package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import kotlin.jvm.internal.Intrinsics;


public final class PrayerTimeExpCalc {

    /* renamed from: A, reason: from kotlin metadata */
    private int Asershift;

    /* renamed from: B, reason: from kotlin metadata */
    private int Maghribshift;

    /* renamed from: C, reason: from kotlin metadata */
    private int Ishashift;

    /* renamed from: D, reason: from kotlin metadata */
    private double JD;

    /* renamed from: E, reason: from kotlin metadata */
    private double Solarnoon;

    /* renamed from: F, reason: from kotlin metadata */
    private double tao;

    /* renamed from: G, reason: from kotlin metadata */
    private double FajerAIT;

    /* renamed from: H, reason: from kotlin metadata */
    private double IshaAIT;

    /* renamed from: a, reason: collision with root package name and from kotlin metadata */
    private int year;

    /* renamed from: b, reason: from kotlin metadata */
    private int month;

    /* renamed from: c, reason: from kotlin metadata */
    private int day;

    /* renamed from: d, reason: from kotlin metadata */
    private double latitude;

    /* renamed from: e, reason: from kotlin metadata */
    private double longitude;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata */
    private double TimeZone;

    /* renamed from: g, reason: from kotlin metadata */
    private int calcMethod;

    /* renamed from: h, reason: from kotlin metadata */
    private int highLatitude;

    /* renamed from: i, reason: from kotlin metadata */
    private int high;

    /* renamed from: j, reason: from kotlin metadata */
    private int umulqura;

    /* renamed from: k, reason: from kotlin metadata */
    private int Hanafia;

    /* renamed from: l, reason: collision with root package name and from kotlin metadata */
    private double jd1;

    /* renamed from: m, reason: from kotlin metadata */
    private double jd2;

    /* renamed from: n, reason: from kotlin metadata */
    private double jd3;

    /* renamed from: o, reason: from kotlin metadata */
    private double jd4;

    /* renamed from: p, reason: from kotlin metadata */
    private double jd5;

    /* renamed from: q, reason: collision with root package name and from kotlin metadata */
    private double jd6;

    /* renamed from: r, reason: collision with root package name and from kotlin metadata */
    private double cm1;

    /* renamed from: s, reason: from kotlin metadata */
    private double cm2;

    /* renamed from: t, reason: from kotlin metadata */
    private double cm3;

    /* renamed from: u, reason: from kotlin metadata */
    private double cm4;

    /* renamed from: v, reason: collision with root package name and from kotlin metadata */
    private double cm5;

    /* renamed from: w, reason: collision with root package name and from kotlin metadata */
    private double cm6;

    /* renamed from: x, reason: from kotlin metadata */
    private int Fajershift;

    /* renamed from: y, reason: collision with root package name and from kotlin metadata */
    private int Shuroqshift;

    /* renamed from: z, reason: from kotlin metadata */
    private int Dhohurshift;

    public PrayerTimeExpCalc(int i, int i2, int i3, double d, double d2, double d3, int i4, int i5, int i6, double d4, double d5) {
        double d6;
        this.year = i;
        this.month = i2;
        this.day = i3;
        this.latitude = d;
        this.longitude = d2;
        this.TimeZone = d3;
        this.calcMethod = i4;
        this.highLatitude = i6;
        this.Hanafia = i5;
        this.Fajershift = 0;
        this.Shuroqshift = 0;
        this.Dhohurshift = 0;
        this.Asershift = 0;
        this.Maghribshift = 0;
        this.Ishashift = 0;
        if (i4 == 0) {
            this.FajerAIT = 18.0d;
            this.IshaAIT = 17.0d;
            this.jd1 = 0.8d;
            this.jd2 = 0.15d;
            this.jd3 = 0.5d;
            this.jd4 = 0.625d;
            this.jd5 = 0.65d;
            this.jd6 = 0.7d;
            this.cm1 = Preference.DEFAULT_SEA_LEVEL;
            this.cm2 = 1.0d;
            this.cm3 = Preference.DEFAULT_SEA_LEVEL;
            this.cm4 = Preference.DEFAULT_SEA_LEVEL;
            this.cm5 = Preference.DEFAULT_SEA_LEVEL;
            this.cm6 = Preference.DEFAULT_SEA_LEVEL;
        }
        if (i4 == 1) { // EgytionGeneralAuthorityofSurvey
            this.FajerAIT = 19.5d;
            this.IshaAIT = 17.5d;
            this.jd1 = 0.1d;
            this.jd2 = 0.15d;
            this.jd3 = 0.5d;
            this.jd4 = 0.6d;
            this.jd5 = 0.65d;
            this.jd6 = 0.7d;
            this.cm1 = 0.5d;
            this.cm2 = 0.6d;
            this.cm3 = 0.5d;
            this.cm4 = 0.5d;
            this.cm5 = 0.4d;
            this.cm6 = 0.5d;
        }
        if (i4 == 2) { // UnivOfIslamicScincesKarachi
            this.FajerAIT = 18.0d;
            this.IshaAIT = 18.0d;
            this.jd1 = 0.8d;
            this.jd2 = 0.15d;
            this.jd3 = 0.5d;
            this.jd4 = 0.625d;
            this.jd5 = 0.65d;
            this.jd6 = 0.7d;
            this.cm1 = Preference.DEFAULT_SEA_LEVEL;
            this.cm2 = 1.0d;
            this.cm3 = Preference.DEFAULT_SEA_LEVEL;
            this.cm4 = Preference.DEFAULT_SEA_LEVEL;
            this.cm5 = Preference.DEFAULT_SEA_LEVEL;
            this.cm6 = Preference.DEFAULT_SEA_LEVEL;
        }
        if (i4 == 3) { //UmmAlQuraUniv
            this.FajerAIT = 18.5d;
            this.IshaAIT = 18.0d;
            this.jd1 = 0.8d;
            this.jd2 = 0.15d;
            this.jd3 = 0.5d;
            this.jd4 = 0.625d;
            this.jd5 = 0.65d;
            this.jd6 = 0.7d;
            this.cm1 = Preference.DEFAULT_SEA_LEVEL;
            this.cm2 = 1.0d;
            this.cm3 = Preference.DEFAULT_SEA_LEVEL;
            this.cm4 = Preference.DEFAULT_SEA_LEVEL;
            this.cm5 = Preference.DEFAULT_SEA_LEVEL;
            this.cm6 = Preference.DEFAULT_SEA_LEVEL;
        }
        if (i4 == 4) {  //IslamicSocietyOfNorthAmerica
            this.FajerAIT = 15.0d;
            this.IshaAIT = 15.0d;
            this.jd1 = 0.8d;
            this.jd2 = 0.15d;
            this.jd3 = 0.5d;
            this.jd4 = 0.625d;
            this.jd5 = 0.65d;
            this.jd6 = 0.7d;
            d6 = Preference.DEFAULT_SEA_LEVEL;
            this.cm1 = Preference.DEFAULT_SEA_LEVEL;
            this.cm2 = 1.0d;
            this.cm3 = Preference.DEFAULT_SEA_LEVEL;
            this.cm4 = Preference.DEFAULT_SEA_LEVEL;
            this.cm5 = Preference.DEFAULT_SEA_LEVEL;
            this.cm6 = Preference.DEFAULT_SEA_LEVEL;
        } else {
            d6 = Preference.DEFAULT_SEA_LEVEL;
        }
        if (i4 == 5) { //Turkey
            this.FajerAIT = 18.0d;
            this.IshaAIT = 17.0d;
            this.jd1 = d6;
            this.jd2 = d6;
            this.jd3 = d6;
            this.jd4 = d6;
            this.jd5 = d6;
            this.jd6 = d6;
            this.cm1 = 0.25d;
            this.cm2 = 0.45d;
            this.cm3 = 0.4d;
            this.cm4 = 0.25d;
            this.cm5 = 0.65d;
            this.cm6 = 0.65d;
            this.Fajershift = -2;
            this.Shuroqshift = -7;
            this.Dhohurshift = 7;
            this.Asershift = 4;
            this.Maghribshift = 9;
            this.Ishashift = 2;
        }
        if (i4 == 6) { //custom
            this.FajerAIT = d4;
            this.IshaAIT = d5;
            this.jd1 = 0.8d;
            this.jd2 = 0.15d;
            this.jd3 = 0.5d;
            this.jd4 = 0.625d;
            this.jd5 = 0.65d;
            this.jd6 = 0.7d;
            this.cm1 = Preference.DEFAULT_SEA_LEVEL;
            this.cm2 = 1.0d;
            this.cm3 = Preference.DEFAULT_SEA_LEVEL;
            this.cm4 = Preference.DEFAULT_SEA_LEVEL;
            this.cm5 = Preference.DEFAULT_SEA_LEVEL;
            this.cm6 = Preference.DEFAULT_SEA_LEVEL;
        }
    }

    private static double a(double d) {
        if (d < -1.0d) {
            d = -1.0d;
        }
        if (d > 1.0d) {
            return 1.0d;
        }
        return d;
    }

    private static double c(double d) {
        double d2 = (long) d;

        return d - d2;
    }

    private static int d(Double d) {
        Intrinsics.checkNotNull(d);
        return (int) Math.floor(d.doubleValue());
    }

    private static double e(double d, double d2) {
        double d3 = d2 - d;
        double d4 = 60;

        double d5 = d3 / d4;
        double floor = d5 - (Math.floor(d5 / 24.0d) * 24.0d);
        if (floor < Preference.DEFAULT_SEA_LEVEL) {
            double d6 = 24;

            return floor + d6;
        }
        return floor;
    }

    private final void b() {
        int i = this.month;
        if (i <= 2) {
            this.month = i + 12;
            this.year--;
        }
        double d = this.year / 100;
        double d2 = 2;

        double d3 = d2 - d;
        double d4 = 4;

        double d5 = d(Double.valueOf(d / d4));

        double d6 = d3 + d5;
        double d7 = this.year + 4716;

        double d8 = d(Double.valueOf(d7 * 365.25d));
        double d9 = this.month + 1;

        double d10 = d(Double.valueOf(d9 * 30.6001d));

        double d11 = d8 + d10;
        double d12 = this.day;

        double d13 = (((d11 + d12) + d6) - 1524.5d) + this.JD;
        this.JD = d13;
        double d14 = 2451545;

        double d15 = d13 - d14;
        double d16 = 36525;

        double d17 = d15 / d16;
        double d18 = (d17 * d17 * 3.032E-4d) + (36000.76983d * d17) + 280.46645d;
        double d19 = (((35999.0503d * d17) - ((1.559E-4d * d17) * d17)) - (((4.8E-7d * d17) * d17) * d17)) + 357.5291d;
        while (d18 > 360.0d) {
            double d20 = 360;

            d18 -= d20;
        }
        while (d19 > 360.0d) {
            double d21 = 360;

            d19 -= d21;
        }
        while (d18 < Preference.DEFAULT_SEA_LEVEL) {
            double d22 = 360;

            d18 += d22;
        }
        while (d19 < Preference.DEFAULT_SEA_LEVEL) {
            double d23 = 360;

            d19 += d23;
        }
        double sin = (Math.sin((d19 * 9.42477796076938d) / 180.0d) * 2.9E-4d) + (Math.sin((d19 * 6.283185307179586d) / 180.0d) * (0.019993d - (1.01E-4d * d17))) + (Math.sin((d19 * 3.141592653589793d) / 180.0d) * ((1.9146d - (0.004817d * d17)) - ((1.4E-5d * d17) * d17))) + d18;
        double d24 = 125.04d - (1934.136d * d17);
        double d25 = (d24 * 3.141592653589793d) / 180.0d;
        double sin2 = (sin - 0.00569d) - (Math.sin(d25) * 0.00478d);
        double cos = (((Math.cos(d25) * 0.00256d) + 23.44023d) * 3.141592653589793d) / 180.0d;
        double d26 = (sin * 3.141592653589793d) / 180.0d;
        double atan = Math.atan((Math.sin(d26) * Math.cos(cos)) / Math.cos(d26));
        double d27 = 180;

        double asin = Math.asin(Math.sin((sin2 * 3.141592653589793d) / 180.0d) * Math.sin(cos));

        this.tao = (asin * d27) / 3.141592653589793d;
        double cos2 = (Math.cos(cos) * ((Math.sin((d24 * 6.283185307179586d) / 180.0d) * 5.8333E-5d) + (((Math.sin(d25) * (-0.004777d)) - (Math.sin((((36000.7698d * d17) + 280.4665d) * 6.283185307179586d) / 180.0d) * 3.666E-4d)) - (Math.sin((((d17 * 481267.8813d) + 218.3165d) * 6.283185307179586d) / 180.0d) * 6.3888E-5d)))) + ((d18 - 0.0057183d) - (((atan * d27) / 3.141592653589793d) + d27));
        if (cos2 < -90.0d) {

            cos2 += d27;
        }
        if (cos2 > 90.0d) {

            cos2 -= d27;
        }

        double d28 = this.TimeZone;
        double d29 = 15;

        double d30 = d28 * d29;
        double d31 = 720;
        double d32 = d30 - this.longitude;

        this.Solarnoon = ((d32 * d4) + d31) - (cos2 * d4);
    }

    /* JADX WARN: Code restructure failed: missing block: B:41:0x0648, code lost:

        if (e(r1, r5) > r12) goto L53;
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x067e, code lost:

        if (e(r3, r5) > r20) goto L73;
     */
    @org.jetbrains.annotations.NotNull
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public final Prayers calc_prayer() {
        b();

        throw new UnsupportedOperationException("Method not decompiled: com.alawail.prayertimes.moazen.PrayerTimeExpCalc.calc_prayer():com.alawail.prayertimes.moazen.Prayers");
    }

    /* renamed from: getAsershift$prayer_time_mobileRelease, reason: from getter */
    public final int getAsershift() {
        return this.Asershift;
    }

    /* renamed from: getCalcMethod$prayer_time_mobileRelease, reason: from getter */
    public final int getCalcMethod() {
        return this.calcMethod;
    }

    /* renamed from: getCm1$prayer_time_mobileRelease, reason: from getter */
    public final double getCm1() {
        return this.cm1;
    }

    /* renamed from: getCm2$prayer_time_mobileRelease, reason: from getter */
    public final double getCm2() {
        return this.cm2;
    }

    /* renamed from: getCm3$prayer_time_mobileRelease, reason: from getter */
    public final double getCm3() {
        return this.cm3;
    }

    /* renamed from: getCm4$prayer_time_mobileRelease, reason: from getter */
    public final double getCm4() {
        return this.cm4;
    }

    /* renamed from: getCm5$prayer_time_mobileRelease, reason: from getter */
    public final double getCm5() {
        return this.cm5;
    }

    /* renamed from: getCm6$prayer_time_mobileRelease, reason: from getter */
    public final double getCm6() {
        return this.cm6;
    }

    /* renamed from: getDay$prayer_time_mobileRelease, reason: from getter */
    public final int getDay() {
        return this.day;
    }

    /* renamed from: getDhohurshift$prayer_time_mobileRelease, reason: from getter */
    public final int getDhohurshift() {
        return this.Dhohurshift;
    }

    /* renamed from: getFajerAIT$prayer_time_mobileRelease, reason: from getter */
    public final double getFajerAIT() {
        return this.FajerAIT;
    }

    /* renamed from: getFajershift$prayer_time_mobileRelease, reason: from getter */
    public final int getFajershift() {
        return this.Fajershift;
    }

    /* renamed from: getHanafia$prayer_time_mobileRelease, reason: from getter */
    public final int getHanafia() {
        return this.Hanafia;
    }

    /* renamed from: getHigh$prayer_time_mobileRelease, reason: from getter */
    public final int getHigh() {
        return this.high;
    }

    /* renamed from: getHighLatitude$prayer_time_mobileRelease, reason: from getter */
    public final int getHighLatitude() {
        return this.highLatitude;
    }

    /* renamed from: getIshaAIT$prayer_time_mobileRelease, reason: from getter */
    public final double getIshaAIT() {
        return this.IshaAIT;
    }

    /* renamed from: getIshashift$prayer_time_mobileRelease, reason: from getter */
    public final int getIshashift() {
        return this.Ishashift;
    }

    /* renamed from: getJD$prayer_time_mobileRelease, reason: from getter */
    public final double getJD() {
        return this.JD;
    }

    /* renamed from: getJd1$prayer_time_mobileRelease, reason: from getter */
    public final double getJd1() {
        return this.jd1;
    }

    /* renamed from: getJd2$prayer_time_mobileRelease, reason: from getter */
    public final double getJd2() {
        return this.jd2;
    }

    /* renamed from: getJd3$prayer_time_mobileRelease, reason: from getter */
    public final double getJd3() {
        return this.jd3;
    }

    /* renamed from: getJd4$prayer_time_mobileRelease, reason: from getter */
    public final double getJd4() {
        return this.jd4;
    }

    /* renamed from: getJd5$prayer_time_mobileRelease, reason: from getter */
    public final double getJd5() {
        return this.jd5;
    }

    /* renamed from: getJd6$prayer_time_mobileRelease, reason: from getter */
    public final double getJd6() {
        return this.jd6;
    }

    /* renamed from: getLatitude$prayer_time_mobileRelease, reason: from getter */
    public final double getLatitude() {
        return this.latitude;
    }

    /* renamed from: getLongitude$prayer_time_mobileRelease, reason: from getter */
    public final double getLongitude() {
        return this.longitude;
    }

    /* renamed from: getMaghribshift$prayer_time_mobileRelease, reason: from getter */
    public final int getMaghribshift() {
        return this.Maghribshift;
    }

    /* renamed from: getMonth$prayer_time_mobileRelease, reason: from getter */
    public final int getMonth() {
        return this.month;
    }

    /* renamed from: getShuroqshift$prayer_time_mobileRelease, reason: from getter */
    public final int getShuroqshift() {
        return this.Shuroqshift;
    }

    /* renamed from: getSolarnoon$prayer_time_mobileRelease, reason: from getter */
    public final double getSolarnoon() {
        return this.Solarnoon;
    }

    /* renamed from: getTao$prayer_time_mobileRelease, reason: from getter */
    public final double getTao() {
        return this.tao;
    }

    /* renamed from: getTimeZone$prayer_time_mobileRelease, reason: from getter */
    public final double getTimeZone() {
        return this.TimeZone;
    }

    /* renamed from: getUmulqura$prayer_time_mobileRelease, reason: from getter */
    public final int getUmulqura() {
        return this.umulqura;
    }

    /* renamed from: getYear$prayer_time_mobileRelease, reason: from getter */
    public final int getYear() {
        return this.year;
    }

    public final void setAsershift$prayer_time_mobileRelease(int i) {
        this.Asershift = i;
    }

    public final void setCalcMethod$prayer_time_mobileRelease(int i) {
        this.calcMethod = i;
    }

    public final void setCm1$prayer_time_mobileRelease(double d) {
        this.cm1 = d;
    }

    public final void setCm2$prayer_time_mobileRelease(double d) {
        this.cm2 = d;
    }

    public final void setCm3$prayer_time_mobileRelease(double d) {
        this.cm3 = d;
    }

    public final void setCm4$prayer_time_mobileRelease(double d) {
        this.cm4 = d;
    }

    public final void setCm5$prayer_time_mobileRelease(double d) {
        this.cm5 = d;
    }

    public final void setCm6$prayer_time_mobileRelease(double d) {
        this.cm6 = d;
    }

    public final void setDay$prayer_time_mobileRelease(int i) {
        this.day = i;
    }

    public final void setDhohurshift$prayer_time_mobileRelease(int i) {
        this.Dhohurshift = i;
    }

    public final void setFajerAIT$prayer_time_mobileRelease(double d) {
        this.FajerAIT = d;
    }

    public final void setFajershift$prayer_time_mobileRelease(int i) {
        this.Fajershift = i;
    }

    public final void setHanafia$prayer_time_mobileRelease(int i) {
        this.Hanafia = i;
    }

    public final void setHigh$prayer_time_mobileRelease(int i) {
        this.high = i;
    }

    public final void setHighLatitude$prayer_time_mobileRelease(int i) {
        this.highLatitude = i;
    }

    public final void setIshaAIT$prayer_time_mobileRelease(double d) {
        this.IshaAIT = d;
    }

    public final void setIshashift$prayer_time_mobileRelease(int i) {
        this.Ishashift = i;
    }

    public final void setJD$prayer_time_mobileRelease(double d) {
        this.JD = d;
    }

    public final void setJd1$prayer_time_mobileRelease(double d) {
        this.jd1 = d;
    }

    public final void setJd2$prayer_time_mobileRelease(double d) {
        this.jd2 = d;
    }

    public final void setJd3$prayer_time_mobileRelease(double d) {
        this.jd3 = d;
    }

    public final void setJd4$prayer_time_mobileRelease(double d) {
        this.jd4 = d;
    }

    public final void setJd5$prayer_time_mobileRelease(double d) {
        this.jd5 = d;
    }

    public final void setJd6$prayer_time_mobileRelease(double d) {
        this.jd6 = d;
    }

    public final void setLatitude$prayer_time_mobileRelease(double d) {
        this.latitude = d;
    }

    public final void setLongitude$prayer_time_mobileRelease(double d) {
        this.longitude = d;
    }

    public final void setMaghribshift$prayer_time_mobileRelease(int i) {
        this.Maghribshift = i;
    }

    public final void setMonth$prayer_time_mobileRelease(int i) {
        this.month = i;
    }

    public final void setShuroqshift$prayer_time_mobileRelease(int i) {
        this.Shuroqshift = i;
    }

    public final void setSolarnoon$prayer_time_mobileRelease(double d) {
        this.Solarnoon = d;
    }

    public final void setTao$prayer_time_mobileRelease(double d) {
        this.tao = d;
    }

    public final void setTimeZone$prayer_time_mobileRelease(double d) {
        this.TimeZone = d;
    }

    public final void setUmulqura$prayer_time_mobileRelease(int i) {
        this.umulqura = i;
    }

    public final void setYear$prayer_time_mobileRelease(int i) {
        this.year = i;
    }
}
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true"

    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="20dp"
        android:gravity="center"
        android:orientation="vertical"
        tools:layout_editor_absoluteX="20dp"
        tools:layout_editor_absoluteY="20dp">

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/loadingann"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:src="@drawable/loadingann" />

        <TextView
            android:id="@+id/dateNow_TextView_MainActivity12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:text="@string/initializing"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_20sdp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/alrabeeaTimes_ImageView_MainActivity"
            android:layout_width="@dimen/alrabeeaTimesWidth"
            android:layout_height="@dimen/alrabeeaTimesHeight"
            android:layout_weight="0"
            android:src="@drawable/img_logo_alrabeea"
            app:tint="#6ac259" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
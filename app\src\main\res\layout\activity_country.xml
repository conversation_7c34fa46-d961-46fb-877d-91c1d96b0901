<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content_ConstraintLayout_CountryActivity"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".UI.Activity.Country.CountryActivity"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:id="@+id/search_LinearLayout_CountryActivity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:padding="@dimen/_10sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <EditText
            android:id="@+id/search_EditText_CountryActivity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/without_corners_bottom_10_background_gray_with"
            android:drawableEnd="@drawable/ic_search_black_28dp"
            android:minHeight="@dimen/_25sdp"
            android:padding="@dimen/_5sdp"
            android:textSize="@dimen/_12sdp" />

        <ImageView
            android:id="@+id/addlocaton"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_35sdp"
            android:layout_weight="1"
            app:srcCompat="@drawable/ic_add"
            app:tint="@color/colorbw" />


    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/optionChoose_RecyclerView_CountryActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/_5sdp"
        android:overScrollMode="never"

        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_LinearLayout_CountryActivity"
        app:layout_constraintVertical_bias="0"
        app:reverseLayout="false"
        tools:listitem="@layout/layout_list_item_country" />


</androidx.constraintlayout.widget.ConstraintLayout>
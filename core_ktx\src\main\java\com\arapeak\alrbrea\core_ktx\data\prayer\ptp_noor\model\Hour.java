package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import kotlin.jvm.internal.Intrinsics;


public final class Hour {

    /* renamed from: a, reason: collision with root package name */
    @Nullable
    private Type f3756a;


    public Hour() {
        this.f3756a = Type.hour12;
    }

    public Hour(@NotNull String hour) {
        Intrinsics.checkNotNullParameter(hour, "hour");
        setHour(hour);
    }

    public Hour(@NotNull Type T) {
        Intrinsics.checkNotNullParameter(T, "T");
        this.f3756a = T;
    }

    @Nullable
    /* renamed from: hourInt, reason: from getter */
    public final Type getF3756a() {
        return this.f3756a;
    }

    @NotNull
    public final String hourString() {
        if (this.f3756a == Type.hour12) {
            return "hour12";
        }
        return "hour24";
    }

    public final void setHour(@NotNull Type hour) {
        Intrinsics.checkNotNullParameter(hour, "hour");
        this.f3756a = hour;
    }

    @Nullable
    public final Type type() {
        return getF3756a();
    }

    public final void setHour(@NotNull String hour) {
        Intrinsics.checkNotNullParameter(hour, "hour");
        if (Intrinsics.areEqual(hour, "hour12")) {
            this.f3756a = Type.hour12;
        } else {
            this.f3756a = Type.hour24;
        }
    }

    public enum Type {
        hour12,
        hour24
    }
}

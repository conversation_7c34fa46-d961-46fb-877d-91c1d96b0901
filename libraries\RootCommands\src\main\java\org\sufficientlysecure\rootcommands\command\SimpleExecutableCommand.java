/*
 * Copyright (C) 2012 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.sufficientlysecure.rootcommands.command;

import android.content.Context;

public class SimpleExecutableCommand extends ExecutableCommand {
    private StringBuilder sb = new StringBuilder();

    public SimpleExecutableCommand(Context context, String executableName, String parameters) {
        super(context, executableName, parameters);
    }

    @Override
    public void output(int id, String line) {
        sb.append(line).append('\n');
    }

    @Override
    public void afterExecution(int id, int exitCode) {
    }

    public String getOutput() {
        return sb.toString();
    }

    public int getExitCode() {
        return exitCode;
    }

}
package com.arapeak.alrbrea.core_ktx.ui.textdesign

import com.intuit.ssp.R


class TextDesignSizes {


    val textSizes = listOf(
        R.dimen._2ssp,
        R.dimen._6ssp,
        R.dimen._10ssp,
        R.dimen._14ssp,
        <PERSON><PERSON>dimen._18ssp,
        R.dimen._22ssp,
        R.dimen._26ssp,
        R.dimen._30ssp,
        R.dimen._34ssp,
        R.dimen._38ssp,
        R.dimen._42ssp,
        R.dimen._46ssp,
        R.dimen._50ssp,
        R.dimen._54ssp,
        R.dimen._58ssp,
        R.dimen._62ssp,
        R.dimen._66ssp,
        R.dimen._70ssp,
        R.dimen._74ssp,
        R.dimen._78ssp,
        R.dimen._82ssp,
        R.dimen._86ssp,
        R.dimen._90ssp,
        R.dimen._94ssp,
        R.dimen._98ssp,
    )

    fun getNextSizeRes(currentSize: Int): Int {
        val i = currentSize + 1

        return if (i >= textSizes.size)
            this.textSizes.last()
        else
            this.textSizes[i]
    }

    fun getNextSize(currentSize: Int): Int {
        val i = currentSize + 1

        return if (i >= textSizes.size)
            this.textSizes.size - 1
        else
            i
    }

    fun getPreviousSizeRes(currentSize: Int): Int {
        val i = currentSize - 1

        return if (i < 0) {
            this.textSizes.first()
        } else
            this.textSizes[i]

    }

    fun getPreviousSize(currentSize: Int): Int {
        val i = currentSize - 1

        return if (i < 0) {
            0
        } else
            i

    }
}
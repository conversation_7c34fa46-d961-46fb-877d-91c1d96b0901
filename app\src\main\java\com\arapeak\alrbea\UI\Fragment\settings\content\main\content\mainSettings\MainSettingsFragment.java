package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.ARG_TYPE_OF_SHOW_BOTH;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ARG_TYPE_OF_SHOW_GREGORIAN;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ARG_TYPE_OF_SHOW_HIJRY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SHOW_SELINT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TYPE_OF_SHOW_DATE_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.AR_LANGUAGE;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerMethod;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.CalcFunction;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimesList;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MainSettingsFragment extends AlrabeeaTimesFragment implements SettingsAdapterCallback {
    private static final String TAG = "MainSettingsFragment";
    public static PrayerApi prayerApi;
    public static int year, month, day;
    private View subSettingsView;
    private RecyclerView settingItemRecyclerView;
    private Dialog loadingDialog;
    // private List<String> typeOfScreenSleep;
    private MainSettingsAdapter mainSettingsAdapter;

    public MainSettingsFragment() {

    }

    public static MainSettingsFragment newInstance() {
        return new MainSettingsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        subSettingsView = inflater.inflate(R.layout.fragment_sub_settings, container, false);

        initView();

        return subSettingsView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        lazyInit();
    }

    private void lazyInit() {
        Handler handler = new Handler();
        Activity context = requireActivity();
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                context.runOnUiThread(() -> {
                    SetParameter();

                });
            }
        }, 100);
    }

    private void initView() {
        settingItemRecyclerView = subSettingsView.findViewById(R.id.settingItem_RecyclerView_SubSettingsFragment);
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<>(), this);

        /*if (getAppCompatActivity() instanceof SettingsActivity) {
            ((SettingsActivity) getAppCompatActivity()).setExit(false);
        } else if (getAppCompatActivity() instanceof SettingsLandscapeActivity) {
            ((SettingsLandscapeActivity) getAppCompatActivity()).setExit(false);
        }*/
    }

    private void SetParameter() {
//        prayerApi = Hawk.get(Utils.getKeyPrayerApi(), null);

        try {
            year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
            month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH));
            day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY));

            List<Object> langList = new ArrayList<>();
            langList.add(getString(R.string.arabic));
            langList.add(getString(R.string.english));
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.application_display_language), ViewsAlrabeeaTimes.SPINNER
                    , langList, HawkSettings.isArabic() ? 0 : 1));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.AppLanguage);


            List<String> timesNumberFormatList = new ArrayList<>();
            timesNumberFormatList.add("٠١٢٣٤٥٦٧٨٩");
            timesNumberFormatList.add("0123456789");
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.times_number_format)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, timesNumberFormatList
                    , HawkSettings.getTypeNumber().equals(AR_LANGUAGE) ? 0 : 1
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.NumberFormat);


            List<String> systemtime = new ArrayList<>();
            systemtime.add(getString(R.string.disactive));
            systemtime.add(getString(R.string.active));
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.silent_mode_alert)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, systemtime
                    , Hawk.get(SHOW_SELINT, 1)
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.SilentMode);


            List<String> typeTime = new ArrayList<>();
            typeTime.add(getString(R.string.am_pm_arabic));
            typeTime.add(getString(R.string.am_pm_english));
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.time_indication_format)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, typeTime
                    , HawkSettings.getTypeAM().equals(AR_LANGUAGE) ? 0 : 1
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.AmPmMode);

            List<String> typeOfShowDate = new ArrayList<>();
            typeOfShowDate.add(getString(R.string.hijry));
            typeOfShowDate.add(getString(R.string.gregorian));
            typeOfShowDate.add(getString(R.string.both));
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.date_view)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, typeOfShowDate
                    , Hawk.get(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_BOTH)
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.DateShowMode);

            List<String> duhaOrSunruse = new ArrayList<>();
            duhaOrSunruse.add(getString(R.string.duha));
            duhaOrSunruse.add(getString(R.string.sunrise));
            duhaOrSunruse.add(getString(R.string.hide));
            duhaOrSunruse.add(getString(R.string.alternate));
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.duha_or_sunrise)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, duhaOrSunruse
                    , HawkSettings.getShowDuhaSetting()
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.DuhaShunriseMode);

            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.display_events)
                    , ViewsAlrabeeaTimes.SPINNER, Arrays.asList(Utils.getStringArray(R.array.events_show_mode))
                    , HawkSettings.getShowEventsOnAllScreens() ? 0 : 1));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.EventsShowMode);

            List<Object> typeSystemNames = new ArrayList<>();
            PrayerMethod def = HawkSettings.getCurrentPrayerMethod();
            for (PrayerMethod method : PrayerMethod.values()) {
                typeSystemNames.add(method.toString());
            }
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.prayer_timing_system), ViewsAlrabeeaTimes.SPINNER
                    , typeSystemNames, def.ordinal()));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.PrayerTimesMode);


        /*List<Object> funcNames = new ArrayList<>();
        CalcFunction defFun = Utils.getCurrentCalcFunction();
        for (CalcFunction method:CalcFunction.values())
            funcNames.add(method.toString());
        mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.prayer_times_equation), ViewsAlrabeeaTimes.SPINNER
                , funcNames, defFun.ordinal()));
        mainSettingsAdapter.getLastItem().setTag(SettingItems.PrayerCalculationMode);*/


            List<Object> dayHijriPlusList = new ArrayList<>();
            dayHijriPlusList.add(-2);
            dayHijriPlusList.add(-1);
            dayHijriPlusList.add(0);
            dayHijriPlusList.add(1);
            dayHijriPlusList.add(2);
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.adjust_hijri_date), ViewsAlrabeeaTimes.SPINNER
                    , dayHijriPlusList, dayHijriPlusList.indexOf(Hawk.get(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, 0))));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.HijriAdjustment);

            List<String> screenOrientation = new ArrayList<>();
            screenOrientation.add(getString(R.string.vertical));
            screenOrientation.add(getString(R.string.horizontal));
            screenOrientation.add(getString(R.string.reversed_vertical));

            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.show_date_in_azkar_screen)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, systemtime
                    , HawkSettings.getEnableTimeWithAzkar() ? 1 : 0 // if true select 1 "enabled"
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.DateInAzkarMode);
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.show_date_in_gallery_screen)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, systemtime
                    , HawkSettings.getEnableTimeWithGallery() ? 1 : 0 // if true select 1 "enabled"
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.DateInGalleryMode);

            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.show_ikama_delay)
                    , ViewsAlrabeeaTimes.RADIO_BUTTON, systemtime
                    , HawkSettings.getEnableShowIkamaDelay() ? 1 : 0 // if true select 1 "enabled"
                    , null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.ShowIkamaDelay);

            int orintation = HawkSettings.getAppOrientation();
            if (orintation < 0)
                orintation = 0;
            mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.app_display_form), ViewsAlrabeeaTimes.RADIO_BUTTON
                    , screenOrientation, orintation, null));
            mainSettingsAdapter.getLastItem().setTag(SettingItems.ScreenOrientation);

            settingItemRecyclerView.setAdapter(mainSettingsAdapter);

            subSettingsView.findViewById(R.id.pb_settings_main).setVisibility(View.GONE);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            Log.e(TAG, "Error: " + e.getMessage());
        }

    }


    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = mainSettingsAdapter.getItem(position);

        switch ((SettingItems) subSettingAlrabeeaTimes.getTag()) {

            case AppLanguage:
                String selectedLang = subPosition == 0 ? AR_LANGUAGE : ConstantsOfApp.EN_LANGUAGE;
                if (selectedLang.equalsIgnoreCase(HawkSettings.getLocaleLanguage()))
                    return;
                HawkSettings.changeLocaleLanguage(getContext(), selectedLang);
                ((SettingsActivity) getAppCompatActivity()).restartActivity();
                break;
            case NumberFormat:
                HawkSettings.setTypeNumber(subPosition == 0 ? AR_LANGUAGE : ConstantsOfApp.EN_LANGUAGE);
                break;
            case SilentMode:
                Hawk.put(SHOW_SELINT, subPosition);
                break;
            case AmPmMode:
                HawkSettings.setTypeAM(subPosition == 0 ? AR_LANGUAGE : ConstantsOfApp.EN_LANGUAGE);
                break;
            case DateShowMode:
                if (subPosition == 0) {
                    Hawk.put(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_HIJRY);
                } else if (subPosition == 1) {
                    Hawk.put(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_GREGORIAN);
                } else if (subPosition == 2) {
                    Hawk.put(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_BOTH);
                }
                break;
            case DuhaShunriseMode:
                HawkSettings.setShowDuhaSetting(subPosition);
                break;
            case EventsShowMode:
                HawkSettings.setShowEventsOnAllScreens(subPosition == 0);
                break;
            case PrayerTimesMode:
                PrayerMethod prayerMethod = PrayerMethod.values()[subPosition];
                PrayerMethod defPrayerMethod = HawkSettings.getCurrentPrayerMethod();
                if (defPrayerMethod == prayerMethod)
                    return;
                switch (prayerMethod) {
                    case customCalendar:
//                        TimingsAlrabeeaTimes timingsAlrabeeaTimes = Utils.getTiming(year,month,day);
//                        PrayerApi prayers = Utils.getPrayerTimesForYear(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
//                        if(timingsAlrabeeaTimes == null)
//                            return;

                        new ChooserDialog(requireContext())
                                .withFilter(false, false, "xls", "csv")
                                .withChosenListener((path, pathFile) -> {
                                    TimingsAlrabeeaTimesList timeList = null;
                                    switch (Utils.getFileExtension(path)) {
                                        case "csv":
                                            timeList = Utils.getTimingFromCsvFile(path);
                                            break;
                                        case "xls":
                                            timeList = Utils.getTimingFromExcelFile(path);
                                            break;
                                    }
                                    if (timeList == null)
                                        return;
                                    List<TimingsAlrabeeaTimes> times = timeList.getTimingsAlrabeeaTimesList();
                                    Utils.deleteAllTimings();
                                    Utils.insertTimings(times);

                                    Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, true);
                                    HawkSettings.setCurrentPrayerMethod(prayerMethod);
                                }).build().show();
                        break;
                    default:
                        HawkSettings.setCurrentPrayerMethod(prayerMethod);
                        Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, false);
                        Utils.deleteAllTimings();
                        break;
                }
                break;
            case PrayerCalculationMode:
                CalcFunction calcFunction = CalcFunction.values()[subPosition];
                CalcFunction defCalcFunction = HawkSettings.getCurrentCalcFunction();
                if (defCalcFunction == calcFunction)
                    return;
                HawkSettings.setCurrentCalcFunction(calcFunction);
                HawkSettings.resetPrayerTimes();
                break;
            case HijriAdjustment:
                List<Object> vals = subSettingAlrabeeaTimes.getObjectList();
                int hijriAdjustment = (int) vals.get(subPosition);
                Hawk.put(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, hijriAdjustment);
                HawkSettings.resetPrayerTimes();
                break;
            case DateInAzkarMode:
                HawkSettings.setEnableTimeWithAzkar(subPosition == 1);
                break;
            case DateInGalleryMode:
                HawkSettings.setEnableTimeWithGallery(subPosition == 1);
                break;
            case ScreenOrientation:
                Log.e("orientation", "clicked " + subPosition);
                HawkSettings.setAppOrientation(subPosition);
                break;
            case ShowIkamaDelay:
                HawkSettings.setEnableShowIkamaDelay(subPosition == 1);
                break;
        }
        /*
        switch (viewsAlrabeeaTimes) {
            case RADIO_BUTTON:
                if (position == 1) {
                    Utils.setTypeNumber(subPosition == 0 ? ConstantsOfApp.AR_LANGUAGE : ConstantsOfApp.EN_LANGUAGE);
                } else if (position == 2) {
                    Hawk.put(SHOW_SELINT, subPosition);
                }else if (position == 3) {
                    Utils.setTypeAM(subPosition == 0 ? ConstantsOfApp.AR_LANGUAGE : ConstantsOfApp.EN_LANGUAGE);
                } else if (position == 4) {
                    if (subPosition == 0) {
                        Hawk.put(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_HIJRY);
                    } else if (subPosition == 1) {
                        Hawk.put(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_GREGORIAN);
                    } else if (subPosition == 2) {
                        Hawk.put(TYPE_OF_SHOW_DATE_KEY, ARG_TYPE_OF_SHOW_BOTH);
                    }
                }
                else if(position == 5){
                    Utils.setDuhaSetting(subPosition == 0);
                }
                else if (position == 9) {
                    Utils.setEnableTimeWithAzkar(subPosition == 1);
                }
                else if (position == 10) {
                    Utils.setEnableTimeWithGallery(subPosition == 1);
                }
                else if (position == 11) {
                    Utils.setAppOrientation(subPosition);
                }
        break;
            case SPINNER:
                if(position == 0){
                    String selectedLang =   subPosition == 0 ? ConstantsOfApp.AR_LANGUAGE : ConstantsOfApp.EN_LANGUAGE;
                    if(selectedLang.equalsIgnoreCase(Utils.getLocaleLanguage()))
                        return;
                    Utils.changeLocaleLanguage(getContext(), selectedLang);
                    ((SettingsActivity) getAppCompatActivity()).restartActivity();
                }
                else if (position == 6) {
                    PrayerMethod prayerMethod = PrayerMethod.values()[subPosition];
                    PrayerMethod defPrayerMethod = Utils.getCurrentPrayerMethod();
                    if(defPrayerMethod == prayerMethod)
                        return;
                    switch (prayerMethod){
                        case custom_calendar:
                            TimingsAlrabeeaTimes timingsAlrabeeaTimes = Utils.getTiming(year,month,day);
                            PrayerApi prayers = Utils.getPrayerTimesForYear(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
                            if(timingsAlrabeeaTimes == null)
                                return;

                            new ChooserDialog(requireContext())
                                    .withFilter(false,false,"xls","csv")
                                    .withChosenListener((path, pathFile) -> {
                                        TimingsAlrabeeaTimesList timeList = null;
                                        switch (Utils.getFileExtension(path)){
                                            case "csv":
                                                timeList = Utils.getTimingFromCsvFile(path);
                                                break;
                                            case"xls":
                                                timeList = Utils.getTimingFromExcelFile(path);
                                                break;
                                        }
                                        if(timeList ==null)
                                            return;
                                        List<TimingsAlrabeeaTimes> times = timeList.getTimingsAlrabeeaTimesList();
                                        PrayerList prayerList = prayers.getPrayerList();
                                        for (TimingsAlrabeeaTimes time:times)
                                            prayerList.getPrayerTimesThisMonth(time.getIntMonth()).get(time.getIntDay()-1).setTimings(time);

                                        prayers.setPrayerList(prayerList);
                                        Utils.putPrayerTimesForYear(prayers);
                                        Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, true);
                                        Utils.setCurrentPrayerMethod(PrayerMethod.values()[subPosition]);
                                    })
                                    .build().show();
                            break;
                        default:
                            Utils.setCurrentPrayerMethod(prayerMethod);
                            Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, false);
                            Utils.resetPrayerTimes();
                            break;


                    }
                }

                else if (position == 7) {

                    CalcFunction calcFunction = CalcFunction.values()[subPosition];
                    CalcFunction defCalcFunction = Utils.getCurrentCalcFunction();
                    if(defCalcFunction == calcFunction)
                        return;
                    Utils.setCurrentCalcFunction(calcFunction);
                    Utils.resetPrayerTimes();
                }
                else if (position == 8) {
                    List<Object> vals = subSettingAlrabeeaTimes.getObjectList();
                    int hijriAdjustment = (int) vals.get(subPosition);
                    Hawk.put(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY,hijriAdjustment);
                    Utils.resetPrayerTimes();
                }
                break;

        }*/


    }

    private enum SettingItems {
        AppLanguage,
        NumberFormat,
        SilentMode,
        AmPmMode,
        DateShowMode,
        DuhaShunriseMode,
        EventsShowMode,
        PrayerTimesMode,
        PrayerCalculationMode,
        HijriAdjustment,
        DateInAzkarMode,
        DateInGalleryMode,
        ScreenOrientation,
        ShowIkamaDelay
    }
}
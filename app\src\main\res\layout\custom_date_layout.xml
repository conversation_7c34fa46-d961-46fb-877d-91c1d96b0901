<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/imageView5"
        style="@style/mosqueIcon"
        android:layout_height="@dimen/_60sdp"
        android:layout_marginTop="0dp"
        android:scaleType="fitCenter"
        app:tint="#653F18" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/hijri_month_container"
            android:layout_width="@dimen/_70sdp"
            android:layout_height="@dimen/_110sdp"
            android:layout_gravity="bottom"
            android:padding="@dimen/_5sdp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/dateNow_TextView_MainActivity"
                    style="@style/Theme_brown_new_3.day_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus5sdp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:text="18"
                    android:textSize="@dimen/_24sdp" />


                <ImageView
                    android:id="@+id/hijri_month_image"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginTop="@dimen/_minus15sdp"
                    android:paddingStart="@dimen/_3sdp"
                    android:paddingEnd="@dimen/_3sdp"
                    android:src="@drawable/theme_brown_3_hijri_month_7"
                    app:tint="#B07A3E" />

                <TextView
                    android:id="@+id/datehm"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus10sdp"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="@string/ramadan"
                    android:textAlignment="center"
                    android:textSize="@dimen/_13sdp" />

                <TextView
                    android:id="@+id/datehy"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus10sdp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:text="1443"
                    android:textSize="@dimen/_18sdp" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/timeNow_TextView_MainActivity"
                    style="@style/BigTime"
                    android:layout_width="wrap_content"
                    android:layout_marginTop="@dimen/_minus4sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:text="11:02"
                    android:textColor="#653F18"
                    android:textSize="@dimen/_30sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/time_seconds"
                    style="@style/BigTime"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginTop="@dimen/_minus5sdp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:text="11"
                    android:textColor="#653F18"
                    android:textSize="@dimen/_20sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </LinearLayout>

            <ImageView
                android:id="@+id/dayimage"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:gravity="center"
                android:src="@drawable/d0"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/timeNow_TextView_MainActivity_"
                app:tint="#653F18" />

            <TextView
                android:id="@+id/day_text"
                style="@style/Theme_brown_new_3.date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:fontFamily="@font/qatar_2022_bold"
                android:text="Saturday"
                android:textColor="#B07A3E"
                android:textSize="@dimen/_14sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/gregorian_month_container"
            android:layout_width="@dimen/_70sdp"
            android:layout_height="@dimen/_110sdp"
            android:layout_gravity="bottom"
            android:padding="@dimen/_5sdp">
            <!--<ImageView
                android:layout_width="match_parent"
                android:layout_height="@dimen/_60sdp"
                android:src="@drawable/theme_brown_3_icon_date"
                style="@style/PrayerTimeLayout.dark_green"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/dateNow1_TextView_MainActivity"
                    style="@style/Theme_brown_new_3.day_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus5sdp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:text="09"
                    android:textSize="@dimen/_24sdp" />


                <ImageView
                    android:id="@+id/gregorian_month_image"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginTop="@dimen/_minus15sdp"
                    android:paddingStart="@dimen/_3sdp"
                    android:paddingEnd="@dimen/_3sdp"
                    android:scaleType="centerInside"
                    android:src="@drawable/theme_brown_3_month_7"
                    app:tint="#B07A3E" />

                <TextView
                    android:id="@+id/datem"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus10sdp"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="April"
                    android:textAlignment="center"
                    android:textSize="@dimen/_13sdp" />

                <TextView
                    android:id="@+id/datey"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus10sdp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:text="2022"
                    android:textSize="@dimen/_18sdp" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</LinearLayout>
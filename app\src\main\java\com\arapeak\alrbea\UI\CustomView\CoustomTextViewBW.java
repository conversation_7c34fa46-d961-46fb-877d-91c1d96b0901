package com.arapeak.alrbea.UI.CustomView;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.TextPaint;
import android.util.AttributeSet;

import com.arapeak.alrbea.R;

public class CoustomTextViewBW extends androidx.appcompat.widget.AppCompatTextView {

    private float strokeWidth;
    private Integer strokeColor;
    private Paint.Join strokeJoin;
    private float strokeMiter;


    public CoustomTextViewBW(Context context) {
        super(context);
        init(null);
    }

    public CoustomTextViewBW(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public CoustomTextViewBW(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(attrs);
    }

    public void init(AttributeSet attrs) {

        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.CoustomTextView);

            if (a.hasValue(R.styleable.CoustomTextView_strokeColor)) {
                float strokeWidth = a.getDimensionPixelSize(R.styleable.CoustomTextView_strokeWidth, 1);
                int strokeColor = a.getColor(R.styleable.CoustomTextView_strokeColor, 0xffffff);
                //  float strokeMiter = a.getDimensionPixelSize(R.styleable.CoustomTextView_strokeMiter, 10);
                Paint.Join strokeJoin = null;
                switch (a.getInt(R.styleable.CoustomTextView_strokeJoinStyle, 0)) {
                    case (0):
                        //  strokeJoin = Paint.Join.MITER;
                        break;
                    case (1):
                        //  strokeJoin = Paint.Join.BEVEL;
                        break;
                    case (2):
                        strokeJoin = Paint.Join.ROUND;
                        break;
                }
                this.setStroke(strokeWidth, strokeColor, strokeJoin, strokeMiter);
            }
        }
    }

    public void setStroke(float width, int color, Paint.Join join, float miter) {
        strokeWidth = width;
        strokeColor = color;
        strokeJoin = join;
        strokeMiter = miter;
    }

    @Override
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int restoreColor = this.getCurrentTextColor();
        if (strokeColor != null) {
            TextPaint paint = this.getPaint();
            paint.setStyle(Paint.Style.STROKE);
            //  paint.setStrokeJoin(strokeJoin);
            //  paint.setStrokeMiter(strokeMiter);
            this.setTextColor(strokeColor);
            paint.setStrokeWidth(strokeWidth);
            super.onDraw(canvas);
            paint.setStyle(Paint.Style.FILL);
            this.setTextColor(restoreColor);
        }
    }
}

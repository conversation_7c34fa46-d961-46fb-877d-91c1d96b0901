<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="@dimen/_15sdp"
    android:layout_marginBottom="@dimen/_15sdp"
    android:fillViewport="true"
    tools:context=".UI.Fragment.settings.content.prayerTimes.content.PrayerTimesSettingsFragment"
    tools:layoutDirection="rtl">
    <!--
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
-->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/settingItem_RecyclerView_PrayerTimesSettingsFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"

                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constrainedHeight="true"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:reverseLayout="false"
                tools:listitem="@layout/layout_list_item_setting" />

            <View
                android:id="@+id/space_View_PrayerTimesSettingsFragment"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_15sdp"
                android:background="#D1D1D1"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/settingItem_RecyclerView_PrayerTimesSettingsFragment" />


            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/isEnableAddHalfHourIsha_CheckBox_PrayerTimesSettingsFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:layout_marginBottom="@dimen/_5sdp"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_14sdp"
                android:visibility="gone"
                app:buttonTint="@color/colorPrimary"
                app:layout_constrainedWidth="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/space_View_PrayerTimesSettingsFragment" />

            <TextView
                android:id="@+id/titleAddHalfHourIsha_TextView_PrayerTimesSettingsFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/edit_isha_in_ramadan"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/isEnableAddHalfHourIsha_CheckBox_PrayerTimesSettingsFragment"
                app:layout_constraintTop_toTopOf="@id/isEnableAddHalfHourIsha_CheckBox_PrayerTimesSettingsFragment" />

            <TextView
                android:id="@+id/descriptionAddHalfHourIsha_TextView_PrayerTimesSettingsFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_5sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="start|top"
                android:includeFontPadding="false"
                android:maxLines="2"
                android:text="@string/add_an_half_hour_for_isha"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_13sdp"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@id/titleAddHalfHourIsha_TextView_PrayerTimesSettingsFragment"
                app:layout_constraintTop_toBottomOf="@id/titleAddHalfHourIsha_TextView_PrayerTimesSettingsFragment" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
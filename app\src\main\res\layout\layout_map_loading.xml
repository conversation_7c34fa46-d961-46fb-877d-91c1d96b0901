<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    app:contentPadding="20dp"
    tools:cardBackgroundColor="@android:color/black">

    <ProgressBar
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:visibility="gone" />


<!--    <com.airbnb.lottie.LottieAnimationView-->
<!--        android:layout_width="100dp"-->
<!--        android:layout_height="100dp"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:lottie_autoPlay="true"-->
<!--        app:lottie_rawRes="@raw/lottielogo" />-->

    <!--<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:padding="15dp">

        &lt;!&ndash;android:orientation="vertical">&ndash;&gt;

        &lt;!&ndash;<TextView&ndash;&gt;
        &lt;!&ndash;android:id="@+id/loadingTxt"&ndash;&gt;
        &lt;!&ndash;android:layout_width="wrap_content"&ndash;&gt;
        &lt;!&ndash;android:layout_height="wrap_content"&ndash;&gt;
        &lt;!&ndash;android:layout_gravity="center"&ndash;&gt;
        &lt;!&ndash;android:padding="25dp"&ndash;&gt;
        &lt;!&ndash;android:gravity="center"&ndash;&gt;
        &lt;!&ndash;android:text="Loading.."&ndash;&gt;
        &lt;!&ndash;android:textStyle="bold"&ndash;&gt;
        &lt;!&ndash;android:visibility="gone"&ndash;&gt;
        &lt;!&ndash;android:textColor="@color/black"&ndash;&gt;
        &lt;!&ndash;android:textSize="20sp"/>&ndash;&gt;

        &lt;!&ndash;<com.wang.avi.AVLoadingIndicatorView
            android:layout_width="65dp"
            android:layout_height="65dp"
            app:indicatorName="BallSpinFadeLoaderIndicator"
            app:indicatorColor="@color/colorAccent"
            />&ndash;&gt;

        <ProgressBar
            android:layout_width="65dp"
            android:layout_height="65dp" />

    </LinearLayout>-->
</androidx.cardview.widget.CardView>

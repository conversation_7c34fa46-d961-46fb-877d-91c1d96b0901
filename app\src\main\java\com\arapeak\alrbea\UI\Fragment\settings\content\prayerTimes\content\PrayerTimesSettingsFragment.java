package com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ADD_HALF_HOUR_ISHA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ADD_HALF_HOUR_ISHA_KEY;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.content.content.PrayerTimesSettingFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.content.content.TarawihAndTahajjudSettingsFragment;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;
import java.util.List;

public class PrayerTimesSettingsFragment extends AlrabeeaTimesFragment implements AdapterCallback {

    public static final String IS_RAMADAN_SETTINGS_ARG = "isRamadanSettings";
    private static final String TAG = "PrayerTimesSettingsF";
    private View prayerTimesSettingsView;
    private RecyclerView settingItemRecyclerView;
    private View spaceView;
    private CheckBox isEnableAddHalfHourIshaCheckBox;
    private TextView titleAddHalfHourIshaTextView, descriptionAddHalfHourIshaTextView;

    private SettingsPrayerAdapter settingsAdapter;
    private boolean isRamadanSettings;

    public PrayerTimesSettingsFragment() {

    }

    public static PrayerTimesSettingsFragment newInstance(boolean isRamadanSettings) {

        Bundle args = new Bundle();

        PrayerTimesSettingsFragment fragment = new PrayerTimesSettingsFragment();
        args.putBoolean(IS_RAMADAN_SETTINGS_ARG, isRamadanSettings);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            isRamadanSettings = getArguments().getBoolean(IS_RAMADAN_SETTINGS_ARG, false);
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        prayerTimesSettingsView = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return prayerTimesSettingsView;
    }

    private void initView() {

        settingItemRecyclerView = prayerTimesSettingsView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        spaceView = prayerTimesSettingsView.findViewById(R.id.space_View_PrayerTimesSettingsFragment);
        isEnableAddHalfHourIshaCheckBox = prayerTimesSettingsView.findViewById(R.id.isEnableAddHalfHourIsha_CheckBox_PrayerTimesSettingsFragment);
        titleAddHalfHourIshaTextView = prayerTimesSettingsView.findViewById(R.id.titleAddHalfHourIsha_TextView_PrayerTimesSettingsFragment);
        descriptionAddHalfHourIshaTextView = prayerTimesSettingsView.findViewById(R.id.descriptionAddHalfHourIsha_TextView_PrayerTimesSettingsFragment);

        settingsAdapter = new SettingsPrayerAdapter(getContext(), setupPrayerTimesSettings(), this);
    }

    private void SetParameter() {
        if (isRamadanSettings) {
            spaceView.setVisibility(View.VISIBLE);
            isEnableAddHalfHourIshaCheckBox.setVisibility(View.VISIBLE);
            titleAddHalfHourIshaTextView.setVisibility(View.VISIBLE);
            descriptionAddHalfHourIshaTextView.setVisibility(View.VISIBLE);

            isEnableAddHalfHourIshaCheckBox.setChecked(Hawk.get(IS_ENABLE_ADD_HALF_HOUR_ISHA_KEY, IS_ENABLE_ADD_HALF_HOUR_ISHA_DEFAULT));
        } else {
            spaceView.setVisibility(View.GONE);
            isEnableAddHalfHourIshaCheckBox.setVisibility(View.GONE);
            titleAddHalfHourIshaTextView.setVisibility(View.GONE);
            descriptionAddHalfHourIshaTextView.setVisibility(View.GONE);
        }
        settingItemRecyclerView.setAdapter(settingsAdapter);
    }

    private void SetAction() {
        isEnableAddHalfHourIshaCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> Hawk.put(IS_ENABLE_ADD_HALF_HOUR_ISHA_KEY, isChecked));

        titleAddHalfHourIshaTextView.setOnClickListener(v -> isEnableAddHalfHourIshaCheckBox.setChecked(!isEnableAddHalfHourIshaCheckBox.isChecked()));

        descriptionAddHalfHourIshaTextView.setOnClickListener(v -> isEnableAddHalfHourIshaCheckBox.setChecked(!isEnableAddHalfHourIshaCheckBox.isChecked()));
    }

    private List<SettingAlrabeeaTimes> setupPrayerTimesSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] prayerTimesSettingsTitleArray, prayerTimesSettingsDescriptionArray;
        if (isRamadanSettings) {
            prayerTimesSettingsTitleArray = getResources().getStringArray(R.array.prayer_times_settings_title_ramadan);
            prayerTimesSettingsDescriptionArray = getResources().getStringArray(R.array.prayer_times_settings_description_ramadan);
        } else {
            prayerTimesSettingsTitleArray = getResources().getStringArray(R.array.prayer_times_settings_title);
            prayerTimesSettingsDescriptionArray = getResources().getStringArray(R.array.prayer_times_settings_description);
        }


        int[] settingsIconArray = setupPrayerTimesSettingsIconArray(prayerTimesSettingsDescriptionArray.length);


        for (int i = 0; i < prayerTimesSettingsTitleArray.length; ++i) {
            settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(prayerTimesSettingsTitleArray[i]
                    , prayerTimesSettingsDescriptionArray[i]
                    , settingsIconArray[i]));
        }

        return settingAlrabeeaTimes;
    }

    private int[] setupPrayerTimesSettingsIconArray(int length) {
        int[] settingsIconArray = new int[length];

        /*settingsIconArray[0] = 0;
        settingsIconArray[1] = 0;
        settingsIconArray[2] = 0;
        settingsIconArray[3] = 0;
        settingsIconArray[4] = 0;
        settingsIconArray[5] = 0;*/

        for (int i = 0; i < settingsIconArray.length; ++i) {
            settingsIconArray[i] = 0;
        }
        return settingsIconArray;
    }

    @Override
    public void onItemClick(int position, String tag) {
        String nameOfPray = "";
        boolean isJomaa = false;
        PrayerType prayerType = PrayerType.Fajr;

        if (position < 6) {
            switch (position) {
                case 0:
                    nameOfPray = ConstantsOfApp.FAJR_KEY;
                    prayerType = PrayerType.Fajr;
                    break;
                case 1:
                    nameOfPray = ConstantsOfApp.DHUHR_KEY;
                    prayerType = PrayerType.Dhuhr;
                    break;
                case 2:
                    nameOfPray = ConstantsOfApp.ASR_KEY;
                    prayerType = PrayerType.Asr;
                    break;
                case 3:
                    nameOfPray = ConstantsOfApp.MAGHRIB_KEY;
                    prayerType = PrayerType.Maghrib;
                    break;
                case 4:
                    nameOfPray = ConstantsOfApp.ISHA_KEY;
                    prayerType = PrayerType.Isha;
                    break;
                case 5:
                    isJomaa = true;
                    nameOfPray = ConstantsOfApp.JOMAA_KEY;
                    prayerType = PrayerType.Dhuhr;
                    break;

            }
            Utils.loadFragment(PrayerTimesSettingFragment.newInstance(prayerType, nameOfPray, isRamadanSettings, isJomaa)
                    , getAppCompatActivity()
                    , 0);
        } else if (position == 6) {
            Utils.loadFragment(TarawihAndTahajjudSettingsFragment.newInstance(getString(R.string.tarawih), isRamadanSettings)
                    , getAppCompatActivity()
                    , 0);
        } else if (position == 7) {
            Utils.loadFragment(TarawihAndTahajjudSettingsFragment.newInstance(getString(R.string.tahajjud), isRamadanSettings)
                    , getAppCompatActivity()
                    , 0);
        }
    }
}

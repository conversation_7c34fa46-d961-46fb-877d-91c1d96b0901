package com.arapeak.alrbea.UI.Fragment.settings.content.ads.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.FUNERAL_MESSAGES_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_FUNERAL_MESSAGES_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_FUNERAL_MESSAGES_KEY;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Model.EventAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapter;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;

public class AddEventFragment extends AlrabeeaTimesFragment {

    private static final String TAG = "AddEventFragment";
    public static int year, month, day;
    public static int yearDate, monthDate, dayDate;
    private View addEventView;
    private TextView templateMessagesTextView;
    private Spinner prayNameSpinner, templateMessagesSpinner;
    private Button saveButton;
    private Dialog loadingDialog;
    private LinearLayout activeCancelLinearLayout;
    private TextView cancelTextView, activeTextView;
    private SwitchCompat activeSwitchCompat;
    private SpinnerAdapter<String> stringSpinnerAdapter, templateMessagesSpinnerAdapter;
    //    private TimingsAlrabeeaTimes timingsAlrabeeaTimes;
//    private DateAlrabeeaTimes dateAlrabeeaTimes;
    private boolean isJomaa;
    private String titleDatePickerDialog;
    private boolean isRamadan;
    private List<String> prayerTagList;
    private String[] templateMessagesTitleArray;
//    public static PrayerApi prayerApi;

    public AddEventFragment() {

    }

    public static AddEventFragment newInstance() {
        return new AddEventFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        addEventView = inflater.inflate(R.layout.fragment_add_event, container, false);

        initView();
        SetParameter();
        SetAction();

        return addEventView;
    }

    private void initView() {
        prayNameSpinner = addEventView.findViewById(R.id.prayName_Spinner_AddEventFragment);
        templateMessagesTextView = addEventView.findViewById(R.id.templateMessages_TextView_AddEventFragment);
        templateMessagesSpinner = addEventView.findViewById(R.id.templateMessages_Spinner_AddEventFragment);
        saveButton = addEventView.findViewById(R.id.save_Button_AddEventFragment);

        activeCancelLinearLayout = addEventView.findViewById(R.id.activeCancel_LinearLayout_AddEventFragment);
        cancelTextView = addEventView.findViewById(R.id.cancel_TextView_AddEventFragment);
        activeSwitchCompat = addEventView.findViewById(R.id.active_SwitchCompat_AddEventFragment);
        activeTextView = addEventView.findViewById(R.id.active_TextView_AddEventFragment);

        loadingDialog = Utils.initLoadingDialog(getContext());

        stringSpinnerAdapter = new SpinnerAdapter<>(getAppCompatActivity()
                , new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.prayer_name))));
        templateMessagesSpinnerAdapter = new SpinnerAdapter<>(getAppCompatActivity()
                , new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.template_messages_body_array))));
        prayerTagList = new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.prayer_tag)));

        templateMessagesTitleArray = getResources().getStringArray(R.array.template_messages_title_array);
        year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
        month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH));
        day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY));

        yearDate = year;
        monthDate = month;
        dayDate = day;


//        prayerApi = MainActivity.prayerApi;
       /* if (Utils.isLandscape(getAppCompatActivity())) {
            prayerApi = MainLandscapeActivity.prayerApi;
        } else {
            prayerApi = MainActivity.prayerApi;
        }*/

//        dateAlrabeeaTimes = prayerApi.getPrayerList().getPrayerTimesThisMonth(month).get(day - 1).getDate();
//        timingsAlrabeeaTimes = prayerApi.getPrayerList().getPrayerTimesThisMonth(month).get(day - 1).getTimings();
//        isJomaa = Utils.isDayJomaa(dateAlrabeeaTimes.getGregorian().getWeekday().getEn());
        isJomaa = Utils.isJomaa();
        isRamadan = Utils.isRamadan();
        titleDatePickerDialog = getString(R.string.start_date);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(getString(R.string.funeral_messages));
//        } else {
//            SettingsActivity.setTextTite(getString(R.string.funeral_messages));
//        }
        SettingsActivity.setTextTite(getString(R.string.funeral_messages));


        Utils.setColorStateListToSwitchCompat(getAppCompatActivity(), activeSwitchCompat);

//        templateMessagesSpinnerAdapter.add(0, "");
        templateMessagesSpinner.setAdapter(templateMessagesSpinnerAdapter);
        prayNameSpinner.setAdapter(stringSpinnerAdapter);

//        isRamadan = dateAlrabeeaTimes.getHijri().getMonth().getNumber() == 9;

        clearAllView();
    }

    private void SetAction() {
        activeSwitchCompat.setOnCheckedChangeListener((buttonView, isChecked) -> {
            Hawk.put(IS_ENABLE_FUNERAL_MESSAGES_KEY, isChecked);
            activeOrDisplay(isChecked);
        });

        saveButton.setOnClickListener(v -> {
            if (isValid()) {

                loadingDialog.show();

                Utils.getInstanceOfRealm().executeTransaction(realm -> {
                    RealmResults<EventAlrabeeaTimes> eventAlrabeeaTimesRealmResults = Utils.getInstanceOfRealm().where(EventAlrabeeaTimes.class)
                            .equalTo("type", ConstantsOfApp.FUNERAL_MESSAGES_KEY)
                            .findAll();
                    if (eventAlrabeeaTimesRealmResults != null && eventAlrabeeaTimesRealmResults.size() > 0) {
                        eventAlrabeeaTimesRealmResults.deleteAllFromRealm();
                    }

                    new Handler(getAppCompatActivity().getMainLooper()).post(() -> saveEventFuneralMessages());
                });
            }
        });
    }

    private void saveEventFuneralMessages() {

        String prayerName, prayerTag, prayerTime;
        switch (prayNameSpinner.getSelectedItemPosition()) {
            case 0:
                prayerTime = PrayerType.Fajr.prayerTime.getWithAdjustment();
//                prayerTime = timingsAlrabeeaTimes.getFajrWithAdjustment(isRamadan);
                prayerName = getString(R.string.fajr);
                prayerTag = ConstantsOfApp.FAJR_KEY;
                break;
            case 1:
                prayerTime = PrayerType.Dhuhr.prayerTime.getWithAdjustment();
//                prayerTime = timingsAlrabeeaTimes.getDhuhrOrJomaaWithAdjustment(isJomaa, isRamadan);
                prayerName = getString(R.string.dhuhr);
                prayerTag = ConstantsOfApp.DHUHR_KEY;
                break;
            case 2:
                prayerTime = PrayerType.Asr.prayerTime.getWithAdjustment();
//                prayerTime = timingsAlrabeeaTimes.getAsrWithAdjustment(isRamadan);
                prayerName = getString(R.string.asr);
                prayerTag = ConstantsOfApp.ASR_KEY;
                break;
            case 3:
                prayerTime = PrayerType.Maghrib.prayerTime.getWithAdjustment();
//                prayerTime = timingsAlrabeeaTimes.getMaghribWithAdjustment(isRamadan);
                prayerName = getString(R.string.maghrib);
                prayerTag = ConstantsOfApp.MAGHRIB_KEY;
                break;
            case 4:
                prayerTime = PrayerType.Isha.prayerTime.getWithAdjustment();
//                prayerTime = timingsAlrabeeaTimes.getIshaWithAdjustment(isRamadan);
                prayerName = getString(R.string.isha);
                prayerTag = ConstantsOfApp.ISHA_KEY;
                break;
            default:
                prayerTime = PrayerType.Dhuhr.prayerTime.getWithAdjustment();
//                prayerTime = timingsAlrabeeaTimes.getJomaaWithAdjustment(isRamadan);
                prayerName = getString(R.string.jomaa);
                prayerTag = ConstantsOfApp.DHUHR_KEY;
                break;
        }

        Utils.getInstanceOfRealm().beginTransaction();
        int eventAlrabeeaTimesId = Hawk.get(ConstantsOfApp.EVENT_ALRABEEA_TIMES_ID_KEY, 1);

        String name = "", message = "";
        if (templateMessagesSpinner.getSelectedItem() != null) {
            message = String.valueOf(templateMessagesSpinner.getSelectedItem()).trim();
            if (templateMessagesSpinner.getSelectedItemPosition() < templateMessagesTitleArray.length) {
                name = templateMessagesTitleArray[templateMessagesSpinner.getSelectedItemPosition()];
            }
        }
        EventAlrabeeaTimes eventAlrabeeaTimes = new EventAlrabeeaTimes(eventAlrabeeaTimesId
                , name
                , message
                , prayerName
                , prayerTag
                , prayerTime
                , 0
                , dayDate + "-" + monthDate + "-" + yearDate
                , ConstantsOfApp.DURATION_OF_FUNERAL_MESSAGES_DEFAULT
                , FUNERAL_MESSAGES_KEY
                , true);

        Utils.getInstanceOfRealm().insertOrUpdate(eventAlrabeeaTimes);

        ++eventAlrabeeaTimesId;
        Hawk.put(ConstantsOfApp.EVENT_ALRABEEA_TIMES_ID_KEY, eventAlrabeeaTimesId);

//        clearAllView();

        Utils.showSuccessAlert(getAppCompatActivity(), getString(R.string.event_added_successfully));

        Utils.getInstanceOfRealm().commitTransaction();
        loadingDialog.dismiss();
    }

    private void setData() {
        final EventAlrabeeaTimes eventFuneralMessages = Utils.getInstanceOfRealm().where(EventAlrabeeaTimes.class)
                .equalTo("type", ConstantsOfApp.FUNERAL_MESSAGES_KEY)
                .equalTo("isActive", true)
                .findFirst();
        if (eventFuneralMessages == null
                || eventFuneralMessages.getPrayerName().isEmpty()) {
            return;
        }

        final String[] prayerNameArray = getResources().getStringArray(R.array.prayer_name);
        final String[] templateMessagesArray = getResources().getStringArray(R.array.template_messages_body_array);

        activeSwitchCompat.setChecked(eventFuneralMessages.isActive());

        final String prayerNameEvent = eventFuneralMessages.getPrayerName();
        final String messageEvent = eventFuneralMessages.getMessageEvent();

        new Thread(() -> {
            int position = 0;
            for (String prayerName : prayerNameArray) {
                if (prayerName.equals(prayerNameEvent)) {
                    final int finalPosition = position;
                    getAppCompatActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            prayNameSpinner.setSelection(finalPosition);
                        }
                    });
//                        return;
                }
                ++position;
            }
            position = 0;
            for (String templateMessages : templateMessagesArray) {
                if (templateMessages.trim().equalsIgnoreCase(messageEvent.trim())) {
                    final int finalPosition1 = position;
                    getAppCompatActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
//                                templateMessagesSpinnerAdapter.remove(0);
                            templateMessagesSpinner.setSelection(finalPosition1);
//                                templateMessagesSpinnerAdapter.notifyDataSetChanged();
                        }
                    });
//                        return;
                }
                ++position;
            }
        }).start();
    }

    private void clearAllView() {
        templateMessagesSpinner.setSelection(0);
        prayNameSpinner.setSelection(0);

        boolean isActive = Hawk.get(IS_ENABLE_FUNERAL_MESSAGES_KEY, IS_ENABLE_FUNERAL_MESSAGES_DEFAULT);
        activeSwitchCompat.setChecked(isActive);
        activeOrDisplay(isActive);
        if (isActive) {
            setData();
        }
    }

    private boolean isValid() {
        boolean isValid = true;
        if (templateMessagesSpinner.getSelectedItem() == null
                || Utils.getValueWithoutNull(String.valueOf(templateMessagesSpinner.getSelectedItem())).isEmpty()) {
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.must_choose_template_message));
            isValid = false;
        }
        return isValid;
    }

    private void activeOrDisplay(boolean isActive) {
        if (isActive) {
            templateMessagesTextView.setVisibility(View.VISIBLE);
            templateMessagesSpinner.setVisibility(View.VISIBLE);
            prayNameSpinner.setVisibility(View.VISIBLE);
            saveButton.setVisibility(View.VISIBLE);
        } else {
            templateMessagesTextView.setVisibility(View.GONE);
            templateMessagesSpinner.setVisibility(View.GONE);
            prayNameSpinner.setVisibility(View.GONE);
            saveButton.setVisibility(View.GONE);
        }
    }
}

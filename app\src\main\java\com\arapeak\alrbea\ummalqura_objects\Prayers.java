package com.arapeak.alrbea.ummalqura_objects;


public class Prayers {
    public static PrayerTimes getPrayerTimes(int year, int month, int day, double latDegree, double longDegree, PrayerTimeType prayerTimeType) {
        double d3;
        double d4;
        double d5;
        double d6;
        double sin;
        PlanetParams planetParams;
        double cos;
        double d7;
        Object obj;
        PrayerTimeType prayerTimeType2 = prayerTimeType;
        PrayerTimes prayerTimes = new PrayerTimes();
        prayerTimes.Rabita = new RabitaTimes();
        RabitaTimes rabitaTimes = new RabitaTimes();
        RabitaTimes rabitaTimes2 = new RabitaTimes();
        PlanetParams SunParam = Core.SunParam(year, month, day, longDegree, latDegree, -prayerTimeType2.TimeZone);
        double sin2 = Math.sin(SunParam.Eqatorial.Decl) * Math.sin(latDegree);
        double cos2 = Math.cos(SunParam.Eqatorial.Decl) * Math.cos(latDegree);
        double d8 = SunParam.Transit;
        if (SunParam.FlagRS != 0 || Math.abs(latDegree) >= 0.79d || (prayerTimeType2.HeightDiffEast == CoreVars.TJ2000 && prayerTimeType2.HeightDiffWest == CoreVars.TJ2000)) {
            d3 = d8;
            d4 = CoreVars.TJ2000;
            d5 = CoreVars.TJ2000;
        } else {
            d4 = Math.acos((Math.sin(-0.014544352255644346d) - sin2) / cos2);
            double acos = (d4 - Math.acos((Math.sin((1.5707963267948966d - Math.asin(6378140.0d / (prayerTimeType2.HeightDiffWest + 6378140.0d))) - 291.4106652870461d) - sin2) / cos2)) * 3.819718634205488d;
            d3 = d8;
            d4 = (d4 - Math.acos((Math.sin((1.5707963267948966d - Math.asin(6378140.0d / (prayerTimeType2.HeightDiffEast + 6378140.0d))) - 291.4106652870461d) - sin2) / cos2)) * 3.819718634205488d;
            d5 = acos;
        }
        if (SunParam.FlagRS != 0 || Math.abs(SunParam.Set - SunParam.Rise) <= 1.0d || Math.abs(SunParam.Set - SunParam.Rise) >= 23.0d) {
            if (latDegree < CoreVars.TJ2000) {
                d6 = -Math.abs(prayerTimeType2.RabitaSuggestedAngle);
            } else {
                d6 = Math.abs(prayerTimeType2.RabitaSuggestedAngle);
            }
            PlanetParams SunParam2 = Core.SunParam(year, month, day, longDegree, d6, -prayerTimeType2.TimeZone);
            sin = Math.sin(SunParam2.Eqatorial.Decl) * Math.sin(d6);
            planetParams = SunParam2;
            d3 = SunParam2.Transit;
            cos = Math.cos(SunParam2.Eqatorial.Decl) * Math.cos(d6);
            d7 = CoreVars.TJ2000;
            obj = 1;
        } else {
            planetParams = SunParam;
            sin = sin2;
            cos = cos2;
            obj = null;
            d6 = CoreVars.TJ2000;
            d7 = CoreVars.TJ2000;
        }
        if (d3 < d7) {
            d3 += 24.0d;
        }
        prayerTimes.Sunrise = planetParams.Rise - d4;
        prayerTimes.Zohar = d3 + prayerTimeType2.SafetyTime;
        prayerTimes.Magreb = (planetParams.Set + d5) + prayerTimeType2.SafetyTime;
        if (obj != null) {
            d7 = prayerTimeType2.Aser + Math.tan(Math.abs(planetParams.Eqatorial.Decl - d6));
        } else {
            d7 = Math.tan(Math.abs(planetParams.Eqatorial.Decl - latDegree)) + prayerTimeType2.Aser;
        }
        d6 = (Math.sin(Math.atan(1.0d / d7)) - sin) / cos;
        if (Math.abs(d6) > 1.0d) {
            d6 = 3.5d;
        } else {
            d6 = Math.acos(d6) * 3.819718634205488d;
        }
        prayerTimes.Aser = (d3 + d6) + prayerTimeType2.SafetyTime;
        d8 = (Math.sin(-prayerTimeType2.FajarAngle) - sin) / cos;
        if (Math.abs(latDegree) >= 0.83776d || Math.abs(d8) > 1.0d) {
            double d9;
            if (latDegree < CoreVars.TJ2000) {
                d9 = d8;
                rabitaTimes2 = GetRatio(year, 12, 21, latDegree, longDegree, prayerTimeType2);
            } else {
                d9 = d8;
                rabitaTimes2 = GetRatio(year, 6, 21, latDegree, longDegree, prayerTimeType2);
            }
            rabitaTimes = rabitaTimes2;
            d6 = d9;
            if (Math.abs(d6) > (prayerTimeType2.FajarAngle * 1.3369d) + 0.45d) {
                d7 = 24.0d - (planetParams.Set - planetParams.Rise);
                if (d7 > 24.0d) {
                    d7 -= 24.0d;
                }
                prayerTimes.Fajer = planetParams.Rise - (d7 * rabitaTimes.FajarExactRabita);
            } else {
                prayerTimes.Fajer = (d3 - ((Math.acos(d6) * 3.819718634205488d) + d4)) + prayerTimeType2.SafetyTime;
            }
            prayerTimes.Rabita.FajarExactRabita = prayerTimes.Fajer;
            if (Math.abs(d6) > 1.0d) {
                rabitaTimes2 = GetRatio(year, month, day, latDegree, longDegree, prayerTimeType);
                sin2 = 24.0d - (planetParams.Set - planetParams.Rise);
                if (sin2 > 24.0d) {
                    sin2 -= 24.0d;
                }
                prayerTimes.Rabita.FajarExactRabita = planetParams.Rise - (sin2 * rabitaTimes2.FajarExactRabita);
            } else {
                d6 = Math.acos(d6) * 3.819718634205488d;
                prayerTimes.Rabita.FajarExactRabita = (d3 - (d6 + d4)) + prayerTimeType2.SafetyTime;
            }
        } else {
            prayerTimes.Fajer = (d3 - ((Math.acos(d8) * 3.819718634205488d) + d4)) + prayerTimeType2.SafetyTime;
            prayerTimes.Rabita.FajarExactRabita = prayerTimes.Fajer;
        }
        if (prayerTimeType2.IshaAngle != CoreVars.TJ2000) {
            d6 = (Math.sin(-prayerTimeType2.IshaAngle) - sin) / cos;
            if (Math.abs(latDegree) < 0.83776d) {
                prayerTimes.Isha = d3 + (((Math.acos(d6) * 3.819718634205488d) + d5) + prayerTimeType2.SafetyTime);
                prayerTimes.Rabita.IshaExactRabita = prayerTimes.Isha;
            } else {
                if (Math.abs(d6) > (prayerTimeType2.FajarAngle * 1.3369d) + 0.45d) {
                    d7 = 24.0d - (planetParams.Set - planetParams.Rise);
                    if (d7 > 24.0d) {
                        d7 -= 24.0d;
                    }
                    prayerTimes.Isha = planetParams.Set + (d7 * rabitaTimes.IshaExactRabita);
                } else {
                    prayerTimes.Isha = d3 + (((Math.acos(d6) * 3.819718634205488d) + d5) + prayerTimeType2.SafetyTime);
                }
                if (Math.abs(d6) > 1.0d) {
                    rabitaTimes2 = GetRatio(year, month, day, latDegree, longDegree, prayerTimeType);
                    sin2 = 24.0d - (planetParams.Set - planetParams.Rise);
                    if (sin2 > 24.0d) {
                        sin2 -= 24.0d;
                    }
                    prayerTimes.Rabita.IshaExactRabita = planetParams.Set + (sin2 * rabitaTimes2.IshaExactRabita);
                } else {
                    d6 = Math.acos(d6) * 3.819718634205488d;
                    prayerTimes.Rabita.IshaExactRabita = d3 + ((d6 + d5) + prayerTimeType2.SafetyTime);
                }
            }
        } else {
            prayerTimes.Isha = prayerTimes.Magreb + prayerTimeType2.IshaFixedSunset;
            prayerTimes.Rabita.IshaExactRabita = prayerTimes.Isha;
        }
        d6 = (Math.sin(prayerTimeType2.EidAngle) - sin) / cos;
        if ((Math.abs(latDegree) < 1.134d || planetParams.FlagRS == 0) && Math.abs(d6) <= 1.0d) {
            prayerTimes.Eid = (d3 - ((Math.acos(d6) * 3.819718634205488d) + d4)) + prayerTimeType2.SafetyTime;
        } else {
            prayerTimes.Eid = prayerTimes.Sunrise + 0.25d;
        }
        return prayerTimes;
    }

    private static RabitaTimes GetRatio(int i, int i2, int i3, double d, double d2, PrayerTimeType prayerTimeType) {
        double d3;
        PrayerTimeType prayerTimeType2 = prayerTimeType;
        PlanetParams planetParams = new PlanetParams();
        RabitaTimes rabitaTimes = new RabitaTimes();
        if (d < CoreVars.TJ2000) {
            d3 = -Math.abs(prayerTimeType2.RabitaSuggestedAngle);
        } else {
            d3 = Math.abs(prayerTimeType2.RabitaSuggestedAngle);
        }
        PlanetParams SunParam = Core.SunParam(i, i2, i3, d2, d3, -prayerTimeType2.TimeZone);
        double sin = Math.sin(SunParam.Eqatorial.Decl) * Math.sin(d3);
        double cos = Math.cos(SunParam.Eqatorial.Decl) * Math.cos(d3);
        d3 = 24.0d - (SunParam.Set - SunParam.Rise);
        double acos = (SunParam.Transit - (Math.acos((Math.sin(-prayerTimeType2.FajarAngle) - sin) / cos) * 3.819718634205488d)) - prayerTimeType2.SafetyTime;
        if (prayerTimeType2.IshaAngle != CoreVars.TJ2000) {
            sin = (SunParam.Transit + (Math.acos((Math.sin(-prayerTimeType2.IshaAngle) - sin) / cos) * 3.819718634205488d)) + prayerTimeType2.SafetyTime;
        } else {
            sin = prayerTimeType2.IshaFixedSunset + SunParam.Set;
        }
        rabitaTimes.IshaExactRabita = (sin - SunParam.Set) / d3;
        rabitaTimes.FajarExactRabita = (SunParam.Rise - acos) / d3;
        return rabitaTimes;
    }

    public static String toStringTime(double d, int i) {
        int i2 = (int) d;
        int i3 = (int) ((d - ((double) i2)) * 60.0d);
        if (i3 == 60) {
            i2++;
            i3 = 0;
        }
        if (i3 < 0) {
            i3 = -i3;
        }
        StringBuilder stringBuilder;
        StringBuilder stringBuilder2;
        String str;
        if (i == 12) {
            stringBuilder = new StringBuilder();
            if (i2 > 12) {
                i2 -= 12;
            }
            stringBuilder.append(i2);
            stringBuilder.append(" : ");
            if (i3 < 10) {
                stringBuilder2 = new StringBuilder();
                str = "0";
            } else {
                stringBuilder2 = new StringBuilder();
                str = "";
            }
            stringBuilder2.append(str);
            stringBuilder2.append(i3);
            stringBuilder.append(stringBuilder2);
            return stringBuilder.toString();
        }
        stringBuilder = new StringBuilder();
        stringBuilder.append(i2);
        stringBuilder.append(" : ");
        if (i3 < 10) {
            stringBuilder2 = new StringBuilder();
            str = "0";
        } else {
            stringBuilder2 = new StringBuilder();
            str = "";
        }
        stringBuilder2.append(str);
        stringBuilder2.append(i3);
        stringBuilder.append(stringBuilder2);
        return stringBuilder.toString();
    }

    public static String toStringTime(double d) {
        return toStringTime(d, 12);
    }
}

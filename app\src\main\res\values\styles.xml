<resources>
    <!--region general -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppThemeNoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <!--        <item name="colorAccent">@color/colorAccent</item>-->
    </style>

    <style name="SplashTheme" parent="AppThemeNoActionBar">
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="SCBSwitch" parent="Theme.AppCompat.Light">
        <!-- active thumb & track color (30% transparency) -->
        <item name="colorControlActivated">@color/colorPrimary</item>

        <!-- inactive thumb color -->
        <item name="colorSwitchThumbNormal">#f1f1f1</item>

        <!-- inactive track color (30% transparency) -->
        <item name="android:colorForeground">#42221f1f</item>
    </style>

    <style name="ConfirmDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@drawable/without_corners_20_background_white</item>
    </style>

    <style name="LoadingDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@drawable/without_corners_20_background_white</item>
    </style>

    <style name="CreateMessageDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@drawable/without_corners_20_background_white</item>
        <item name="android:minWidth">320dp</item>
    </style>

    <style name="prayerTimingStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@drawable/without_corners_20_background_white</item>
        <item name="android:minWidth">620dp</item>
        <item name="android:elevation">0dp</item>
    </style>

    <style name="MovingText" parent="PrayerTime">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginStart">@dimen/_10sdp</item>
        <item name="android:layout_marginEnd">@dimen/_10sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:focusable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">#504e4e</item>
        <item name="android:textSize">@dimen/_14sdp</item>
    </style>

    <style name="MovingTextStyle">
        <item name="android:ellipsize">marquee</item>
        <item name="android:focusable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="AlrabeaIcon">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/_28sdp</item>
        <item name="android:src">@drawable/img_logo_large</item>
        <item name="android:paddingTop">@dimen/_4sdp</item>
        <item name="android:paddingBottom">@dimen/_4sdp</item>
    </style>

    <style name="mosqueIcon">
        <item name="android:layout_width">match_parent</item>
        <!--        <item name="android:visibility">invisible</item>-->
        <item name="android:layout_height">@dimen/mosqueIconHeight</item>
        <item name="android:layout_marginTop">@dimen/_20sdp</item>
        <item name="android:src">@drawable/img_logo_large</item>
        <item name="android:backgroundTint">@color/colorGrayDark</item>
    </style>

    <style name="PrayerTime">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>
        <item name="android:textSize">@dimen/_30sdp</item>
        <!--        <item name="android:fontFamily">@font/jenine</item>-->
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:gravity">center</item>
        <item name="android:text">04:14</item>
    </style>

    <style name="PrayerTime.Time" parent="PrayerTime" />

    <style name="TimeTextView.NameAR" parent="TimeTextView">
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:layout_gravity">right</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:text">04:14</item>
    </style>

    <style name="TimeTextView.TimeType" parent="TimeTextView">
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginEnd">@dimen/_4sdp</item>
        <item name="android:layout_marginStart">@dimen/_4sdp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/_16sdp</item>
        <item name="android:text">AM</item>
    </style>

    <style name="WrappedContentText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/colorblack</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="BigTime" parent="WrappedContentText">
        <item name="android:text">11:25</item>
        <item name="android:textSize">@dimen/_40sdp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TimeTextView" parent="WrappedContentText">

        <!--        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>-->
        <item name="android:includeFontPadding">false</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TimeTextView.Time" parent="TimeTextView">
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:text">04:14</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <!--        <item name="android:background">@drawable/text_view_pray_white_new</item>-->
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="PrayerTimeLayout" parent="PrayerTime">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <!--        <item name="android:layout_marginTop">@dimen/_10sdp</item>-->
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:layout_marginBottom">0dp</item>
        <item name="android:layout_weight">1</item>
        <!--        <item name="android:gravity">center</item>-->
        <item name="android:gravity">left|center_vertical</item>
        <!--        <item name="android:layout_gravity">center</item>-->
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="PrayerTimeLayout.Side" parent="PrayerTimeLayout">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <!--        <item name="android:layout_weight">1.5</item>-->
    </style>

    <style name="PrayerTimeLayout.Side.Firebase" parent="PrayerTimeLayout.Side">
        <item name="android:gravity">start</item>
    </style>

    <style name="PrayerTimeLayout.Side.Custom" parent="PrayerTimeLayout.Side">
        <!--        <item name="android:layoutDirection">ltr</item>-->
        <item name="android:gravity">end</item>
    </style>

    <style name="PrayerTimeLayout.Side.CustomWithIkama" parent="PrayerTimeLayout.Side">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layoutDirection">ltr</item>
        <item name="android:layout_weight">0</item>
        <!--        <item name="android:layout_weight">1.5</item>-->
    </style>
    <!--endregion-->

    <!--region white_new -->
    <style name="LinearLayoutPrayerTimeRow.white_new" parent="LinearLayoutPrayerTimeRow" />

    <style name="TimeTextView.white_new.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">#906e44</item>
    </style>

    <style name="TimeTextView.white_new.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">#3c4754</item>
        <item name="android:textSize">@dimen/_24sdp</item>
    </style>

    <style name="TimeTextView.white_new.Time" parent="TimeTextView.Time">
        <item name="android:textColor">#3c4754</item>
    </style>

    <style name="TimeTextView.white_new.TimeNameEN" parent="TimeTextView">
        <!--        <item name="android:fontFamily">@font/droid_arabic_kufi</item>-->
        <item name="android:textColor">#906e44</item>
        <item name="android:textSize">@dimen/_16sdp</item>
    </style>

    <style name="TimeTextView.white_new.TimeRemain" parent="TimeTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">0</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:visibility">gone</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:focusable">true</item>
        <item name="android:state_selected">true</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:text">متبقي ساعة و3 دقائق</item>
        <item name="android:textColor">#3c4754</item>
    </style>
    <!--endregion-->

    <!--region custom_1 -->
    <style name="LinearLayoutPrayerTimeRow.custom_1" parent="LinearLayoutPrayerTimeRow">
        <item name="android:layoutDirection">locale</item>
    </style>

    <style name="TimeTextView.custom_1.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:fontFamily">@font/qatar_2022_bold</item>
        <!--        <item name="android:fontFamily">@font/teshrin_bold</item>-->
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>
        <!--        <item name="android:layout_marginTop">@dimen/_3sdp</item>-->
        <!--        <item name="android:layout_marginBottom">@dimen/_3sdp</item>-->
        <item name="android:paddingTop">@dimen/_5sdp</item>
        <item name="android:paddingBottom">@dimen/_5sdp</item>
        <item name="android:paddingLeft">@dimen/_3sdp</item>
        <item name="android:paddingRight">@dimen/_3sdp</item>
        <item name="android:textColor">#3c4754</item>
        <item name="android:textSize">@dimen/_28sdp</item>
        <!--        <item name="android:textSize">@dimen/_24sdp</item>-->
    </style>

    <style name="TimeTextView.custom_1.TimeType" parent="TimeTextView.TimeType">
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:layout_marginBottom">0dp</item>
        <item name="android:textColor">#906e44</item>
        <item name="android:fontFamily">@font/qatar_2022_bold</item>
        <!--        <item name="android:fontFamily">@font/teshrin_bold</item>-->
    </style>

    <style name="TimeTextView.custom_1.Time" parent="TimeTextView.Time">
        <item name="android:layout_marginTop">0dp</item>
        <item name="android:layout_marginBottom">0dp</item>
        <item name="android:fontFamily">@font/qatar_2022_bold</item>
        <!--        <item name="android:fontFamily">@font/teshrin_bold</item>-->
        <item name="android:textColor">#3c4754</item>
        <!--        <item name="android:fontFamily">@font/teshrin_bold</item>-->
        <item name="android:textSize">@dimen/_30sdp</item>
        <item name="android:minWidth">@dimen/_65sdp</item>
    </style>

    <style name="TimeTextView.custom_1.TimeRemain" parent="TimeTextView">
        <!--        <item name="android:background">@drawable/dark_green_transperant</item>-->
        <item name="android:background">@drawable/gradient_semi_transperant_custom</item>
        <item name="android:layout_marginTop">@dimen/_5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_5sdp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">0</item>
        <item name="android:includeFontPadding">true</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:visibility">gone</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:focusable">true</item>
        <item name="android:state_selected">true</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:fontFamily">@font/qatar_2022_bold</item>
        <!--        <item name="android:fontFamily">@font/teshrin_bold</item>-->
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:text">متبقي ساعة و3 دقائق</item>
        <item name="android:textColor">#3c4754</item>
    </style>
    <!--endregion-->

    <!--region custom_1_with_ikama -->
    <style name="TimeTextView.custom_1.TimeNameAR.WithIkama" parent="TimeTextView.custom_1.TimeNameAR">
        <item name="android:textSize">@dimen/_22sdp</item>
    </style>

    <style name="TimeTextView.custom_1.Time.WithIkama" parent="TimeTextView.custom_1.Time">
        <item name="android:textSize">@dimen/_24sdp</item>
    </style>

    <style name="TimeTextView.custom_1.TimeRemain.WithIkama" parent="TimeTextView.custom_1.TimeRemain">
        <item name="android:textSize">@dimen/_14sdp</item>
    </style>
    <!--endregion-->

    <!--region custom_1_land -->
    <style name="LinearLayoutPrayerTimeRow.custom_1_land" parent="LinearLayoutPrayerTimeRow.custom_1" />

    <style name="TimeTextView.custom_1_land.TimeNameAR" parent="TimeTextView.custom_1.TimeNameAR">
        <item name="android:textSize">@dimen/_28sdp</item>
        <!--        <item name="android:layout_marginBottom">0dp</item>-->
        <!--        <item name="android:layout_marginTop">0dp</item>-->
        <!--        <item name="android:includeFontPadding">true</item>-->
        <!--        <item name="android:layout_marginBottom">@dimen/_5sdp</item>-->
    </style>

    <style name="TimeTextView.custom_1_land.TimeType" parent="TimeTextView.custom_1.TimeType">
        <item name="android:textSize">@dimen/_20sdp</item>
    </style>

    <style name="TimeTextView.custom_1_land.Time" parent="TimeTextView.custom_1.Time">
        <item name="android:textSize">@dimen/_32sdp</item>
    </style>

    <style name="TimeTextView.custom_1_land.TimeRemain" parent="TimeTextView.custom_1.TimeRemain"></style>
    <!--endregion-->

    <!--region custom_1_land_with_ikama -->
    <style name="TimeTextView.custom_1_land.TimeNameAR.WithIkama" parent="TimeTextView.custom_1_land.TimeNameAR">
        <item name="android:textSize">@dimen/_22sdp</item>
    </style>

    <style name="TimeTextView.custom_1_land.Time.WithIkama" parent="TimeTextView.custom_1_land.Time">
        <item name="android:textSize">@dimen/_24sdp</item>
    </style>
    <!--endregion-->


    <!--region white_new_land -->
    <style name="LinearLayoutPrayerTimeRow.white_new_land" parent="LinearLayoutPrayerTimeRow">
        <!--        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>-->
    </style>

    <style name="TimeTextView.white_new_land.TimeNameAR" parent="TimeTextView.white_new.TimeNameAR">
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus8sdp</item>
    </style>

    <style name="TimeTextView.white_new_land.TimeNameEN" parent="TimeTextView.white_new.TimeNameEN">
        <item name="android:textSize">@dimen/_12sdp</item>
    </style>

    <style name="TimeTextView.white_new_land.Time" parent="TimeTextView.white_new.Time">
        <item name="android:textSize">@dimen/_36sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus16sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
    </style>

    <style name="TimeTextView.white_new_land.TimeType" parent="TimeTextView.white_new.TimeType">
        <item name="android:textSize">@dimen/_12sdp</item>
    </style>
    <!--endregion-->
    <!--region brown_new -->
    <style name="LinearLayoutPrayerTimeRow.brown_new_inside" parent="LinearLayoutPrayerTimeRow">
        <item name="android:background">@drawable/text_view_pray_brown_new</item>
        <item name="android:layout_height">@dimen/_50sdp</item>
        <item name="android:paddingRight">@dimen/_34sdp</item>
        <item name="android:paddingLeft">@dimen/_26sdp</item>
    </style>

    <style name="TimeTextView.brown_new.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textSize">@dimen/_32sdp</item>
        <item name="android:layout_gravity">start</item>
        <item name="android:textColor">@color/datebrownnew</item>
    </style>

    <style name="TimeTextView.brown_new.TimeType" parent="TimeTextView.TimeType">
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:textColor">@color/datebrownnew</item>
    </style>

    <style name="TimeTextView.brown_new.TimeRemain" parent="TimeTextView.brown.TimeRemain">
        <item name="android:textColor">@color/datebrownnew</item>
        <item name="android:background">@drawable/text_view_date_brown_new</item>
        <item name="android:layout_height">@dimen/_30sdp</item>
        <item name="android:paddingRight">@dimen/_35sdp</item>
        <item name="android:paddingLeft">@dimen/_35sdp</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.brown_new" parent="LinearLayoutPrayerTimeRow" />

    <style name="TimeTextView.brown_new.Time" parent="TimeTextView.Time">
        <item name="android:textSize">@dimen/_32sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus19sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus19sdp</item>
        <item name="android:textColor">@color/datebrownnew</item>
    </style>
    <!--endregion-->
    <!--region brown_new_land -->
    <style name="LinearLayoutPrayerTimeRow.brown_new_land_inside" parent="LinearLayoutPrayerTimeRow.brown_new_inside">
        <item name="android:layout_height">@dimen/_40sdp</item>
        <item name="android:paddingRight">@dimen/_26sdp</item>
        <item name="android:paddingLeft">@dimen/_26sdp</item>
    </style>

    <style name="TimeTextView.brown_new_land.Time" parent="TimeTextView.brown_new.Time">
        <item name="android:layout_marginTop">@dimen/_minus16sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:textSize">@dimen/_30sdp</item>
    </style>

    <style name="TimeTextView.brown_new_land.TimeNameAR" parent="TimeTextView.brown_new.TimeNameAR">
        <item name="android:textSize">@dimen/_26sdp</item>
        <!--        <item name="android:textSize">@dimen/_30sdp</item>-->
        <item name="android:layout_marginTop">@dimen/_minus13sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:paddingStart">@dimen/_5sdp</item>
    </style>

    <style name="PrayerTimeLayout.brown_new_land" parent="PrayerTimeLayout.brown">
        <!--        <item name="android:layout_weight">@dimen/_14sdp</item>-->
        <!--        <item name="android:layoutDirection">@dimen/_14sdp</item>-->

        <!--        android:layout_weight="0"-->
        <!--        android:layoutDirection="rtl"-->
    </style>

    <style name="TimeTextView.brown_new_land.TimeType" parent="TimeTextView.brown_new.TimeType">
        <item name="android:textSize">@dimen/_14sdp</item>
    </style>
    <!--endregion-->
    <!--region dark_green -->
    <style name="PrayerTimeLayout.dark_green" parent="PrayerTimeLayout">
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.dark_green" parent="LinearLayoutPrayerTimeRow"></style>

    <style name="TimeTextView.dark_green.Time" parent="TimeTextView.Time">
        <item name="android:textSize">@dimen/_40sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus16sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_green.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_green.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:textSize">@dimen/_32sdp</item>
        <item name="android:layout_gravity">start</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_green.TimeRemain" parent="TimeTextView.white_new.TimeRemain">
        <item name="android:background">@drawable/dark_green_transperant</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <!--endregion-->
    <!--region dark_green_land -->
    <style name="PrayerTimeLayout.dark_green_land" parent="PrayerTimeLayout.dark_green" />

    <style name="LinearLayoutPrayerTimeRow.dark_green_land" parent="LinearLayoutPrayerTimeRow"></style>

    <style name="TimeTextView.dark_green_land.TimeType" parent="TimeTextView.dark_green.TimeType">
        <item name="android:textSize">@dimen/_12sdp</item>
    </style>

    <style name="TimeTextView.dark_green_land.TimeNameAR" parent="TimeTextView.dark_green.TimeNameAR">
        <item name="android:textSize">@dimen/_26sdp</item>
    </style>

    <style name="TimeTextView.dark_green_land.Time" parent="TimeTextView.dark_green.Time">
        <item name="android:layout_marginTop">@dimen/_minus20sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus20sdp</item>
        <item name="android:textSize">@dimen/_36sdp</item>
    </style>
    <!--endregion-->
    <!--region blue -->
    <style name="LinearLayoutPrayerTimeRow.blue" parent="LinearLayoutPrayerTimeRow">
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="TimeTextView.blue.TimeRemain" parent="TimeTextView.brown.TimeRemain">
        <item name="android:background">@drawable/without_corners_bottom_10_background_blue</item>
    </style>

    <style name="TimeTextView.blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/_30sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textStyle">bold</item>
        <item name="android:text">Testing</item>
        <item name="android:textColor">@color/colorBlueMain</item>
    </style>

    <style name="TimeTextView.blue.land2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textStyle">bold</item>
        <item name="android:text">Testing</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:gravity">start</item>
    </style>


    <style name="TimeTextView.blue2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/_30sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textStyle">bold</item>
        <item name="android:text">Testing</item>
        <item name="android:textColor">@color/colorBlueMain</item>
    </style>

    <style name="TimeTextView.blue.TimeNameAR" parent="TimeTextView.blue">
        <item name="android:layout_marginTop">@dimen/_minus13sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">start</item>
    </style>



    <style name="TimeTextView.blue.TimeNameARsmall" parent="TimeTextView.blue">
        <item name="android:layout_marginTop">@dimen/_minus13sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="TimeTextView.blue.Time" parent="TimeTextView.blue">
        <item name="android:text">08:30</item>
    </style>

    <style name="TimeTextView.blue.TimeType" parent="TimeTextView.blue">
        <item name="android:textSize">@dimen/_16sdp</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:text">AM</item>
    </style>
    <!--endregion-->
    <!--region blue_land -->
    <style name="LinearLayoutPrayerTimeRow.blue_land" parent="LinearLayoutPrayerTimeRow.blue">
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="TimeTextView.blue_land.TimeNameAR" parent="TimeTextView.blue.TimeNameAR">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/_26sdp</item>
    </style>


    <style name="TimeTextView.blue_land.Time" parent="TimeTextView.blue.Time">
        <item name="android:textSize">@dimen/_28sdp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.blue_land.TimeType" parent="TimeTextView.blue.TimeType">
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <!--endregion-->
    <!--region blue_lett -->
    <style name="PrayerTimeLayout.blue_lett.center" parent="PrayerTimeLayout">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="PrayerTimeLayout.new_green.center" parent="PrayerTimeLayout">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <!--        <item name="android:layout_width">wrap_content</item>-->
        <item name="android:gravity">center</item>
        <item name="android:background">@color/white</item>
        <item name="android:layout_width">@dimen/_70sdp</item>
        <item name="android:layout_weight">0</item>
        <!--        <item name="android:minWidth">@dimen/_60sdp</item>-->
    </style>

    <style name="PrayerTimeLayout.blue_lett.Left" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <!--        <item name="android:layoutDirection">ltr</item>-->
        <item name="android:gravity">end</item>
    </style>

    <style name="PrayerTimeLayout.blue_lett.Right" parent="PrayerTimeLayout.Side">
        <!--        <item name="android:layout_width">match_parent</item>-->
        <!--        <item name="android:layout_height">match_parent</item>-->
        <item name="android:textColor">@color/colorwt</item>
        <!--        <item name="android:layoutDirection">ltr</item>-->
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.blue_lett" parent="LinearLayoutPrayerTimeRow">
        <item name="android:layout_marginTop">@dimen/_5sdp</item>
        <item name="android:paddingLeft">@dimen/_10sdp</item>
        <item name="android:paddingRight">@dimen/_10sdp</item>
        <item name="android:background">@drawable/background_prays_blue_lett_r</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.new_green" parent="LinearLayoutPrayerTimeRow">
        <item name="android:layout_marginTop">@dimen/_5sdp</item>
        <item name="android:paddingLeft">@dimen/_10sdp</item>
        <item name="android:paddingRight">@dimen/_10sdp</item>
        <item name="android:background">@drawable/background_prays_new_green_r</item>
    </style>

    <style name="TimeTextView.blue_lett">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/_22sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>
        <!--        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>-->
        <!--        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>-->
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:textStyle">bold</item>
        <item name="android:text">Testing</item>
        <item name="android:textColor">@color/colorBlueMain</item>
    </style>

    <style name="TimeTextView.blue_lett.Time" parent="TimeTextView.blue_lett">
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:text">08:30</item>
        <item name="android:textColor">#4b6780</item>
        <item name="android:shadowColor">@color/white</item>
        <item name="android:shadowRadius">5</item>
    </style>

    <style name="TimeTextView.new_green.Time" parent="TimeTextView.blue_lett">
        <item name="android:textSize">@dimen/_18sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:text">08:30</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.blue_lett.TimeType" parent="TimeTextView.blue_lett">
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:text">AM</item>
        <item name="android:paddingEnd">@dimen/_1sdp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#4b6780</item>
        <item name="android:shadowRadius">5</item>
        <!--        <item name="android:minWidth">@dimen/_20sdp</item>-->
    </style>

    <style name="TimeTextView.new_green.TimeType" parent="TimeTextView.blue_lett">
        <item name="android:textSize">@dimen/_10sdp</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:text">AM</item>
        <item name="android:paddingEnd">@dimen/_1sdp</item>
        <item name="android:textColor">@color/white</item>
        <!--        <item name="android:minWidth">@dimen/_20sdp</item>-->
    </style>

    <style name="TimeTextView.blue_lett.TimeNameAR" parent="TimeTextView.blue_lett">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#4b6780</item>
        <item name="android:shadowRadius">5</item>
    </style>

    <style name="TimeTextView.new_green.TimeNameAR" parent="TimeTextView.blue_lett">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">1</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/new_green_2</item>
        <item name="android:textSize">@dimen/_18sdp</item>
    </style>

    <style name="TimeTextView.blue_lett.TimeRemain" parent="TimeTextView.white_new.TimeRemain">
        <item name="android:background">@drawable/background_blue_lett_remain</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#4b6780</item>
        <item name="android:shadowRadius">5</item>
    </style>
    <!--endregion-->
    <!--region blue_lett_land -->
    <!--
    <style name="PrayerTimeLayout.blue_lett_land.center" parent="PrayerTimeLayout.blue_lett.center" />
    <style name="PrayerTimeLayout.blue_lett_land.Left" parent="PrayerTimeLayout.blue_lett.Left" />
    <style name="PrayerTimeLayout.blue_lett_land.Right" parent="PrayerTimeLayout.blue_lett.Right" />
    <style name="LinearLayoutPrayerTimeRow.blue_lett_land" parent="LinearLayoutPrayerTimeRow.blue_lett">

    </style>
    <style name="TimeTextView.blue_lett_land.TimeNameAR" parent="TimeTextView.blue_lett.TimeNameAR">
        <item name="android:textSize">@dimen/_22sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>
    </style>-->
    <style name="TimeTextView.blue_lett_land.Time" parent="TimeTextView.blue_lett">
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:text">08:30</item>
        <item name="android:textColor">#4b6780</item>
        <item name="android:shadowColor">@color/white</item>
        <item name="android:shadowRadius">5</item>
        <item name="android:minWidth">@dimen/_60sdp</item>
    </style>

    <style name="TimeTextView.blue_lett_land.TimeType" parent="TimeTextView.blue_lett">
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:text">AM</item>
        <item name="android:paddingEnd">@dimen/_1sdp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#4b6780</item>
        <item name="android:shadowRadius">5</item>
        <item name="android:minWidth">@dimen/_20sdp</item>
    </style>
    <!--    <style name="TimeTextView.blue_lett_land.Time" parent="TimeTextView.blue_lett.Time">
            <item name="android:textSize">@dimen/_24sdp</item>
            <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
            <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        </style>
        <style name="TimeTextView.blue_lett_land.TimeType" parent="TimeTextView.blue_lett.TimeType">
            <item name="android:textSize">@dimen/_16sdp</item>
            <item name="android:layout_marginTop">@dimen/_minus8sdp</item>
            <item name="android:layout_marginBottom">@dimen/_minus8sdp</item>
        </style>-->
    <!--endregion-->
    <!--region brown_land -->

    <style name="LinearLayoutPrayerTimeRow.brown_land" parent="LinearLayoutPrayerTimeRow.brown" />

    <style name="PrayerTimeLayout.brown_land" parent="PrayerTimeLayout.brown">
        <item name="android:layoutDirection">ltr</item>
    </style>

    <style name="TimeTextView.brown_land.TimeNameAR" parent="TimeTextView.brown.TimeNameAR">
        <item name="android:textSize">@dimen/_25sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
    </style>

    <style name="TimeTextView.brown_land.Time" parent="TimeTextView.brown.Time">
        <item name="android:layout_marginTop">@dimen/_minus13sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:textSize">@dimen/_36sdp</item>
    </style>

    <style name="TimeTextView.brown_land.TimeType" parent="TimeTextView.brown.TimeType">
        <item name="android:textSize">@dimen/_12sdp</item>
    </style>

    <!--endregion-->
    <!--region dark_gray_land -->
    <style name="TimeTextView.dark_gray_land.Time" parent="TimeTextView.Time">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_gray_land.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_gray_land.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:textColor">@color/white</item>
    </style>
    <!--endregion-->
    <!--region dark_gray -->
    <style name="PrayerTimeLayout.dark_gray" parent="PrayerTimeLayout.brown" />

    <style name="LinearLayoutPrayerTimeRow.dark_gray" parent="LinearLayoutPrayerTimeRow">
        <item name="android:background">@drawable/without_corners_bottom_solid_gray</item>
        <item name="android:layout_marginTop">@dimen/_5sdp</item>
        <item name="android:paddingLeft">@dimen/_15sdp</item>
        <item name="android:paddingRight">@dimen/_15sdp</item>
    </style>

    <style name="TimeTextView.dark_gray.Time" parent="TimeTextView.Time">
        <item name="android:textSize">@dimen/_36sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus19sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus19sdp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_gray.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.dark_gray.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_gravity">start</item>
    </style>

    <style name="TimeTextView.dark_gray.TimeRemain" parent="TimeTextView.brown.TimeRemain">
        <item name="android:textColor">@color/colorGrayDark</item>
        <item name="android:background">@drawable/without_corners_bottom_solid_light_gray</item>
    </style>
    <!--endregion-->
    <!--region red_land -->
    <style name="LinearLayoutPrayerTimeRow.red_land" parent="LinearLayoutPrayerTimeRow" />

    <style name="TimeTextView.red_land.Time" parent="TimeTextView.red.Time"></style>

    <style name="TimeTextView.red_land.TimeType" parent="TimeTextView.red.TimeType"></style>

    <style name="TimeTextView.red_land.TimeNameAR" parent="TimeTextView.red.TimeNameAR">
        <item name="android:textSize">@dimen/_25sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus13sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
    </style>

    <style name="TimeTextView.red_land.TimeRemain" parent="TimeTextView.red.TimeRemain" />
    <!--endregion-->
    <!--region red -->

    <style name="TimeTextView.red.Time" parent="TimeTextView.Time">
        <item name="android:textSize">@dimen/_36sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus16sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
        <item name="android:textColor">@color/red_theme</item>
    </style>

    <style name="TimeTextView.red.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">@color/red_theme</item>
    </style>

    <style name="TimeTextView.red.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:textSize">@dimen/_36sdp</item>
        <item name="android:layout_gravity">start</item>
        <item name="android:layout_marginTop">@dimen/_minus16sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus13sdp</item>
        <item name="android:textColor">@color/red_theme</item>
    </style>

    <style name="TimeTextView.red.TimeRemain" parent="TimeTextView.white_new.TimeRemain">
        <item name="android:textColor">@color/red_theme2</item>
        <item name="android:background">@drawable/without_corners_bottom_red_theme</item>
        <item name="android:textSize">@dimen/_15sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginLeft">@dimen/_20sdp</item>
        <item name="android:layout_marginRight">@dimen/_20sdp</item>
    </style>
    <!--endregion-->
    <!--region brown -->
    <style name="PrayerTimeLayout.brown" parent="PrayerTimeLayout">
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.brown" parent="LinearLayoutPrayerTimeRow">
        <item name="android:layout_marginTop">@dimen/_5sdp</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.brown.main" parent="LinearLayoutPrayerTimeRow">
        <!--        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>-->
        <item name="android:layout_marginTop">0dp</item>
    </style>

    <style name="TimeTextView.brown.Time" parent="TimeTextView.Time">
        <item name="android:textSize">@dimen/_40sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus13sdp</item>
        <item name="android:textColor">#704a29</item>
    </style>

    <style name="TimeTextView.brown.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">#a97133</item>
    </style>

    <style name="TimeTextView.brown.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:textSize">@dimen/_30sdp</item>
        <item name="android:layout_gravity">start</item>
        <item name="android:layout_marginTop">@dimen/_minus7sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus7sdp</item>
        <item name="android:textColor">#704a29</item>
    </style>

    <style name="TimeTextView.brown.TimeRemain" parent="TimeTextView.white_new.TimeRemain">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/without_corners_bottom_10_background_brown</item>
        <item name="android:textSize">@dimen/_15sdp</item>
        <item name="android:fontFamily">@font/droid_arabic_kufi</item>
        <item name="android:layout_marginLeft">@dimen/_20sdp</item>
        <item name="android:layout_marginRight">@dimen/_20sdp</item>
    </style>
    <!--endregion-->
    <!--region blue_new_land -->
    <style name="LinearLayoutPrayerTimeRow.blue_new_land" parent="LinearLayoutPrayerTimeRow">
        <item name="android:orientation">vertical</item>
    </style>

    <style name="TimeTextView.blue_new_land.Time" parent="TimeTextView.blue_new.Time">
        <item name="android:textSize">@dimen/_33sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus16sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus16sdp</item>
    </style>

    <style name="TimeTextView.blue_new_land.TimeType" parent="TimeTextView.blue_new.TimeType"></style>

    <style name="TimeTextView.blue_new_land.TimeNameAR" parent="TimeTextView.blue_new.TimeNameAR">
        <item name="android:textSize">@dimen/_26sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>
    </style>

    <style name="TimeTextView.blue_new_land.TimeNameEN" parent="TimeTextView.blue_new.TimeNameEN">
        <item name="android:textSize">@dimen/_18sdp</item>
    </style>
    <!--endregion-->
    <!--region brown_new_3 -->
    <style name="Theme_brown_new_3">
        <item name="android:fontFamily">@font/dinnext_medium</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">#653F18</item>
    </style>

    <style name="Theme_brown_new_3.day_number" parent="Theme_brown_new_3">
        <item name="android:fontFamily">@font/dinnext_bold</item>
    </style>

    <style name="Theme_brown_new_3.date" parent="Theme_brown_new_3">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="PrayerTimeLayout.brown_new_3.Left" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:layout_width">@dimen/_90sdp</item>
        <item name="android:background">@drawable/theme_brown_new_3_bottom_rounded_inactive</item>
        <item name="android:layoutDirection">ltr</item>
        <item name="android:gravity">center</item>
        <!--        <item name="android:gravity">center_vertical|left</item>-->
    </style>

    <style name="PrayerTimeLayout.brown_new_3.top" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:layout_width">@dimen/_90sdp</item>
        <item name="android:background">@drawable/theme_brown_new_3_top_rounded_inactive</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.brown_new_3" parent="LinearLayoutPrayerTimeRow">
        <item name="android:orientation">vertical</item>
    </style>

    <style name="TimeTextView.brown_new_3.TimeNameAR" parent="TimeTextView.blue_new.TimeNameAR">
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <!--        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>-->
        <!--        <item name="android:layout_marginTop">0dp</item>-->
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_marginBottom">0dp</item>
        <item name="android:textSize">@dimen/_18sdp</item>
        <item name="android:fontFamily">@font/dinnext_bold</item>
        <item name="android:textColor">#653F18</item>
    </style>

    <style name="TimeTextView.brown_new_3.TimeType" parent="TimeTextView.blue_new.TimeType">
        <item name="android:textSize">@dimen/_14sdp</item>
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:fontFamily">@font/dinnext_bold</item>
        <item name="android:textColor">#BB8444</item>
    </style>

    <style name="TimeTextView.brown_new_3.Time" parent="TimeTextView.blue_new.Time">
        <item name="android:layout_marginTop">@dimen/_minus5sdp</item>
        <!--        <item name="android:layout_marginBottom">@dimen/_minus5sdp</item>-->
        <!--        <item name="android:layout_marginTop">0dp</item>-->
        <item name="android:layout_width">wrap_content</item>
        <!--        <item name="android:layout_width">0dp</item>-->
        <!--        <item name="android:layout_weight">1</item>-->
        <item name="android:layout_marginBottom">0dp</item>
        <item name="android:textSize">@dimen/_22sdp</item>
        <item name="android:fontFamily">@font/dinnext_bold</item>
        <item name="android:textColor">#653F18</item>
    </style>
    <!--endregion-->
    <!--region green_new -->
    <style name="PrayerTime.green_new" parent="PrayerTime">
        <item name="android:textSize">@dimen/_22sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus8sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus8sdp</item>
    </style>

    <style name="PrayerTime.green_new.Time" parent="PrayerTime.green_new">
        <item name="android:textColor">@color/colordGreen</item>
    </style>

    <style name="PrayerTime.green_new.TimeType" parent="PrayerTime.green_new">
        <item name="android:textSize">@dimen/_16sdp</item>
        <!--        <item name="android:visibility">gone</item>-->
        <item name="android:paddingRight">@dimen/_5sdp</item>
        <item name="android:textColor">@color/colorBrownTheme</item>
        <item name="android:text">AM</item>
    </style>

    <style name="PrayerTime.green_new.TimeName" parent="PrayerTime.green_new">
        <item name="android:textColor">@color/colordGreen</item>
    </style>

    <style name="PrayerTimeLayout.green_new.center" parent="PrayerTimeLayout.main_white.center">
        <item name="android:textColor">@color/colorBrownTheme</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="PrayerTimeLayout.green_new.Left" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorBrownTheme</item>
        <item name="android:layoutDirection">ltr</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:gravity">center_vertical|end</item>
    </style>

    <style name="PrayerTimeLayout.green_new.Right" parent="PrayerTimeLayout.Side">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layoutDirection">ltr</item>
        <item name="android:textColor">@color/colorBrownTheme</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:gravity">right</item>
    </style>
    <!--endregion-->
    <!--region main_white -->
    <style name="PrayerTimeLayout.main_white.center" parent="PrayerTimeLayout">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="PrayerTimeLayout.main_white.End" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:gravity">center_vertical|end</item>
        <item name="android:layout_weight">0</item>

    </style>

    <style name="PrayerTimeLayout.main_white.Start" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <!--        <item name="android:gravity">end</item>-->
        <item name="android:gravity">start</item>
        <item name="android:layout_weight">0</item>
    </style>
    <!--endregion-->
    <!--region blue_new -->
    <style name="PrayerTimeLayout.blue_new.Left" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:layoutDirection">ltr</item>
        <item name="android:gravity">center</item>
        <!--        <item name="android:gravity">center_vertical|left</item>-->
    </style>

    <style name="PrayerTimeLayout.blue_new.Right" parent="PrayerTimeLayout.Side">
        <item name="android:textColor">@color/colorwt</item>
        <item name="android:textSize">@dimen/_20sdp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="LinearLayoutPrayerTimeRow.blue_new" parent="LinearLayoutPrayerTimeRow" />

    <style name="TimeTextView.blue_new.Time" parent="TimeTextView.Time">
        <item name="android:textSize">@dimen/_32sdp</item>
        <item name="android:layout_marginTop">@dimen/_minus10sdp</item>
        <item name="android:layout_marginBottom">@dimen/_minus10sdp</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TimeTextView.blue_new.TimeType" parent="TimeTextView.TimeType">
        <item name="android:textColor">@color/ambluenew</item>
    </style>

    <style name="TimeTextView.blue_new.TimeNameAR" parent="TimeTextView.NameAR">
        <item name="android:fontFamily">@font/droid_arabic_kufi_bold</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/_22sdp</item>
    </style>

    <style name="TimeTextView.blue_new.TimeNameEN" parent="TimeTextView.white_new.TimeNameEN">
        <item name="android:textColor">@color/ambluenew</item>
    </style>

    <style name="TimeTextView.blue_new.TimeRemain" parent="TimeTextView.white_new.TimeRemain">
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_height">@dimen/_26sdp</item>
        <item name="android:background">@drawable/text_view_pray_bluenewnext</item>
    </style>
    <!--endregion-->

    <!--region $name -->
    <!--endregion-->
    <style name="AppTheme.Picker" parent="Theme.AppCompat.Light.NoActionBar">
        <!--        <item name="android:textColorPrimary">@android:color/white</item>-->
        <item name="android:textSize">@dimen/_20sdp</item>
    </style>



    <style name="appTheme.FullScreenDialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowLightStatusBar" >true</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="colorAccent">@color/colorBlue</item>
        <item name="colorPrimary">@color/bluelett</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/white</item>
        <item name="actionMenuTextColor">@color/colorBlueMain</item>
    </style>

    <style name="slide">
        <item name="android:windowEnterAnimation">@anim/slide_up_loyalty</item>
        <item name="android:windowExitAnimation">@anim/slide_down_loyalty</item>
    </style>

</resources>

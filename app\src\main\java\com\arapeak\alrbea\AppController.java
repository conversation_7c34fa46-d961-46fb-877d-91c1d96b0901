package com.arapeak.alrbea;

import android.app.Application;
import android.content.Context;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import com.arapeak.alrbea.UI.Activity.InitialSetupActivity;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.UI.ScreenOrientationEnforcer;
import com.arapeak.alrbea.database.MainDatabase;
import com.arapeak.alrbea.database.OmanCitiesDb;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.downloader.PRDownloader;
import com.downloader.PRDownloaderConfig;
import com.google.firebase.FirebaseApp;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.database.FirebaseDatabase;
import com.nostra13.universalimageloader.cache.disc.impl.UnlimitedDiskCache;
import com.nostra13.universalimageloader.cache.disc.naming.Md5FileNameGenerator;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.ImageLoaderConfiguration;
import com.nostra13.universalimageloader.core.assist.QueueProcessingType;
import com.nostra13.universalimageloader.core.download.BaseImageDownloader;
import com.nostra13.universalimageloader.utils.StorageUtils;
import com.orhanobut.hawk.Hawk;

import org.sufficientlysecure.rootcommands.RootCommands;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import io.realm.Realm;
import io.realm.RealmConfiguration;

public class AppController extends Application {
    private static final String TAG = "AppController";
    public static AppController instance;
    public static Context baseContext;
    public static ScreenOrientationEnforcer screenOrientationEnforcer;
    public static MainDatabase db;
    public static boolean isRooted = false;
    public static String roProductModel = "";
    public static boolean isRockchipDevice1 = false;
    public static boolean isRockchipDevice2 = false;

    private FirebaseCrashlytics mCrashlytics;
    private ExecutorService initializationExecutor;
    private volatile boolean isInitialized = false;

    public static AppController getInstance() {
        return instance;
    }

    public static boolean isLandscapeDevice() {
        try {
            if (baseContext == null) {
                Log.w(TAG, "baseContext is null in isLandscapeDevice");
                return false;
            }

            WindowManager wm = (WindowManager) baseContext.getSystemService(Context.WINDOW_SERVICE);
            if (wm == null) {
                Log.w(TAG, "WindowManager is null");
                return false;
            }

            Display display = wm.getDefaultDisplay();
            if (display == null) {
                Log.w(TAG, "Display is null");
                return false;
            }

            Display.Mode mode = display.getMode();
            if (mode == null) {
                Log.w(TAG, "Display mode is null");
                return false;
            }

            boolean isLandscape = mode.getPhysicalWidth() > mode.getPhysicalHeight();
            Log.d(TAG, "isLandscapeDevice: " + isLandscape +
                    " (width=" + mode.getPhysicalWidth() + ", height=" + mode.getPhysicalHeight() + ")");
            return isLandscape;

        } catch (Exception e) {
            Log.e(TAG, "Error checking landscape orientation", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();

        try {
            instance = this;
            baseContext = getBaseContext();

            // Set up exception handler early
            Thread.setDefaultUncaughtExceptionHandler(new MyExceptionHandler());

            // Initialize critical components synchronously
            initializeCriticalComponents();

            // Initialize non-critical components asynchronously
            initializeNonCriticalComponents();

        } catch (Exception e) {
            Log.e(TAG, "Critical error in onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // Don't crash the app, but log the error
        }
    }

    private void initializeCriticalComponents() {
        try {
            // Device info
            roProductModel = Utils.getSystemProperties("ro.product.model");
            isRockchipDevice1 = roProductModel.equalsIgnoreCase("rk322x_box") ||
                    roProductModel.equalsIgnoreCase("rk322x-box");
            isRockchipDevice2 = roProductModel.equalsIgnoreCase("H96 mini V8");

            Log.i(TAG, "Device model: " + roProductModel);
            Log.i(TAG, "Is Rockchip device: " + (isRockchipDevice1 || isRockchipDevice2));

            // Initialize Realm with better error handling
            initializeRealm();

            // Initialize Firebase
            initializeFirebase();

            // Initialize Hawk for preferences
            Hawk.init(getApplicationContext()).build();

            // Initialize static variables
            InitialSetupActivity.isAddLocation = true;
            // Note: MainActivity variables are now instance variables and will be
            // initialized in MainActivity

        } catch (Exception e) {
            Log.e(TAG, "Error initializing critical components", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initializeRealm() {
        try {
            Realm.init(this);

            RealmConfiguration realmConfiguration = new RealmConfiguration.Builder()
                    .assetFile("realm_alrabeea_times.realm")
                    .allowWritesOnUiThread(false) // Changed to false for better performance
                    .schemaVersion(0)
                    .build();

            Realm.setDefaultConfiguration(realmConfiguration);

        } catch (Exception e) {
            Log.e(TAG, "Error initializing Realm", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Fallback configuration without asset file
            try {
                RealmConfiguration fallbackConfig = new RealmConfiguration.Builder()
                        .allowWritesOnUiThread(false)
                        .schemaVersion(0)
                        .deleteRealmIfMigrationNeeded()
                        .build();
                Realm.setDefaultConfiguration(fallbackConfig);
                Log.i(TAG, "Using fallback Realm configuration");
            } catch (Exception fallbackError) {
                Log.e(TAG, "Failed to initialize fallback Realm configuration", fallbackError);
            }
        }
    }

    private void initializeFirebase() {
        try {
            FirebaseApp.initializeApp(getApplicationContext());
            mCrashlytics = FirebaseCrashlytics.getInstance();
            mCrashlytics.setCrashlyticsCollectionEnabled(true);

            // Enable Firebase persistence with error handling
            try {
                FirebaseDatabase.getInstance().setPersistenceEnabled(true);
            } catch (Exception e) {
                Log.w(TAG, "Firebase persistence already enabled or failed to enable", e);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error initializing Firebase", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initializeNonCriticalComponents() {
        // Use a single thread executor for background initialization
        initializationExecutor = Executors.newSingleThreadExecutor();

        initializationExecutor.execute(() -> {
            try {
                // Initialize image loader
                initializeImageLoader();

                // Initialize downloader
                initializeDownloader();

                // Initialize root commands
                RootCommands.DEFAULT_TIMEOUT = 2 * 60 * 1000; // 2 min

                // Check root permissions
                isRooted = Utils.hasRootPermission();
                Log.i(TAG, "Root permission: " + isRooted);

                // Initialize database
                if (baseContext != null) {
                    db = new MainDatabase(baseContext);
                    OmanCitiesDb.init();
                }

                // Initialize screen orientation enforcer
                screenOrientationEnforcer = new ScreenOrientationEnforcer();

                isInitialized = true;
                Log.i(TAG, "Non-critical components initialized successfully");

            } catch (Exception e) {
                Log.e(TAG, "Error initializing non-critical components", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    private void initializeImageLoader() {
        try {
            if (ImageLoader.getInstance().isInited()) {
                Log.d(TAG, "ImageLoader already initialized");
                return;
            }

            DisplayImageOptions opts = new DisplayImageOptions.Builder()
                    .cacheInMemory(true)
                    .cacheOnDisc(true)
                    .build();

            File cacheDir = StorageUtils.getCacheDirectory(getApplicationContext());
            ImageLoaderConfiguration.Builder config = new ImageLoaderConfiguration.Builder(this);

            config.threadPriority(Thread.NORM_PRIORITY) // Changed from MAX_PRIORITY
                    .denyCacheImageMultipleSizesInMemory()
                    .diskCache(new UnlimitedDiskCache(cacheDir))
                    .defaultDisplayImageOptions(opts)
                    .diskCacheFileNameGenerator(new Md5FileNameGenerator())
                    .diskCacheSize(50 * 1024 * 1024) // Reduced from 100MB to 50MB
                    .tasksProcessingOrder(QueueProcessingType.LIFO)
                    .imageDownloader(new BaseImageDownloader(getApplicationContext()));

            ImageLoader.getInstance().init(config.build());

        } catch (Exception e) {
            Log.e(TAG, "Error initializing ImageLoader", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initializeDownloader() {
        try {
            PRDownloaderConfig config = PRDownloaderConfig.newBuilder()
                    .setDatabaseEnabled(true)
                    .setConnectTimeout(30000)
                    .setReadTimeout(30000)
                    .build();
            PRDownloader.initialize(this, config);

        } catch (Exception e) {
            Log.e(TAG, "Error initializing PRDownloader", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public boolean isInitialized() {
        return isInitialized;
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        cleanup();
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Log.w(TAG, "Low memory warning received");

        // Clear image cache to free memory
        try {
            if (ImageLoader.getInstance().isInited()) {
                ImageLoader.getInstance().clearMemoryCache();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing image cache on low memory", e);
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Log.w(TAG, "Memory trim requested, level: " + level);

        // Clear caches based on trim level
        try {
            if (ImageLoader.getInstance().isInited()) {
                if (level >= TRIM_MEMORY_MODERATE) {
                    ImageLoader.getInstance().clearMemoryCache();
                }
                if (level >= TRIM_MEMORY_COMPLETE) {
                    ImageLoader.getInstance().clearDiskCache();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error trimming memory", e);
        }
    }

    private void cleanup() {
        try {
            if (initializationExecutor != null && !initializationExecutor.isShutdown()) {
                initializationExecutor.shutdown();
                try {
                    if (!initializationExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        initializationExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    initializationExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
        }
    }

    @Override
    public Context getApplicationContext() {
        return super.getApplicationContext();
    }
}
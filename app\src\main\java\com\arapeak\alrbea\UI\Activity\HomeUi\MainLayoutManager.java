//package com.arapeak.alrbea.UI.Activity.HomeUi;
//
//
//import android.app.Activity;
//import android.graphics.Bitmap;
//import android.graphics.BitmapFactory;
//import android.graphics.Color;
//import android.graphics.PorterDuff;
//import android.graphics.Shader;
//import android.graphics.drawable.BitmapDrawable;
//import android.net.Uri;
//import android.util.DisplayMetrics;
//import android.util.Log;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.core.content.ContextCompat;
//import androidx.palette.graphics.Palette;
//
//import com.arapeak.alrbea.APIs.ConstantsOfApp;
//import com.arapeak.alrbea.Enum.UITheme;
//import com.arapeak.alrbea.Model.PremiumUserModel;
//import com.arapeak.alrbea.R;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbea.hawk.HawkSettings;
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//import com.mikhaellopez.circularprogressbar.CircularProgressBar;
//import com.orhanobut.hawk.Hawk;
//import com.squareup.picasso.Callback;
//import com.squareup.picasso.Picasso;
//
//import java.io.File;
//
//// This class will manage the overall layout and theme application
//public class MainLayoutManager {
//
//    private Activity activity;
//    private ViewGroup containerLinearLayout;
//    private UITheme uiTheme;
//    private boolean isLandscape;
//    private boolean logoImageEnabled = false;
//    private boolean moveDateToUp;
//    private int domainColor = 0;
//    private int textColor = 0;
//
//    // References to views that are affected by theme/layout.
//    // Instead of directly holding all TextViews/Imageviews, we can hold parent groups
//    // or set up a way for it to find views when needed. For simplicity, let's keep some direct references.
//    private ImageView backgroundImageView, gregorian_month_image, hijri_month_image, dayimage,
//            containerImageView, alrabeeaTimesImageView, imageViewbrownnew, athkarIcon;
//
//    private ViewGroup gregorian_month_container, hijri_month_container;
//
//
//    public MainLayoutManager(Activity activity, ViewGroup containerLinearLayout, UITheme uiTheme, boolean isLandscape) {
//        this.activity = activity;
//        this.containerLinearLayout = containerLinearLayout;
//        this.uiTheme = uiTheme;
//        this.isLandscape = isLandscape;
//        initViews();
//        setAppTheme();
//        loadCustomTheme();
//        logoImage();
//        loadMainColors();
//        setCustomSettingForThemes();
//    }
//
//    private void initViews() {
//        // Inflate the correct layout based on theme
//        switch (uiTheme) {
//            case BROWN: View.inflate(activity, R.layout.content_main_brown, containerLinearLayout); break;
//            case BLUE: View.inflate(activity, R.layout.content_main_blue, containerLinearLayout); break;
//            case DARK_GREEN: View.inflate(activity, R.layout.content_main_dark_green, containerLinearLayout); break;
//            case DARK_GRAY: View.inflate(activity, R.layout.content_main_drak_gray, containerLinearLayout); break;
//            case GREEN: View.inflate(activity, R.layout.content_main_green, containerLinearLayout); break;
//            case WHITE: View.inflate(activity, R.layout.content_main_white, containerLinearLayout); break;
//            case RED: View.inflate(activity, R.layout.content_main_red, containerLinearLayout); break;
//            case BROWN_NEW: View.inflate(activity, R.layout.content_main_brown_new, containerLinearLayout); break;
//            case BLUE_NEW: View.inflate(activity, R.layout.content_main_blue_new, containerLinearLayout); break;
//            case NEW_GREEN: View.inflate(activity, R.layout.content_main_new_green, containerLinearLayout); break;
//            case BLUE_LET: View.inflate(activity, R.layout.content_main_blue_lett, containerLinearLayout); break;
//            case WHITE_NEW: View.inflate(activity, R.layout.content_main_white_new, containerLinearLayout); break;
//            case BROWN_NEW_3: View.inflate(activity, R.layout.content_main_brown_3, containerLinearLayout); break;
//            case CUSTOM_1:
//                if (Hawk.get(uiTheme.name() + "_show_ikama", false))
//                    View.inflate(activity, R.layout.content_main_custom_1_with_ikama, containerLinearLayout);
//                else
//                    View.inflate(activity, R.layout.content_main_custom_1, containerLinearLayout);
//                break;
//            default: // CUSTOM_FIREBASE
//                if (Hawk.get(uiTheme.name() + "_show_ikama", false))
//                    View.inflate(activity, R.layout.content_main_firebase_with_ikama, containerLinearLayout);
//                else
//                    View.inflate(activity, R.layout.content_main_firebase, containerLinearLayout);
//                break;
//        }
//
//        // Initialize common views
//        backgroundImageView = containerLinearLayout.findViewById(R.id.background_ImageView_MainActivity);
//        gregorian_month_container = containerLinearLayout.findViewById(R.id.gregorian_month_container);
//        hijri_month_container = containerLinearLayout.findViewById(R.id.hijri_month_container);
//        gregorian_month_image = containerLinearLayout.findViewById(R.id.gregorian_month_image);
//        hijri_month_image = containerLinearLayout.findViewById(R.id.hijri_month_image);
//        dayimage = containerLinearLayout.findViewById(R.id.dayimage);
//        containerImageView = containerLinearLayout.findViewById(R.id.container_ImageView_MainActivity1);
//        alrabeeaTimesImageView = containerLinearLayout.findViewById(R.id.alrabeeaTimes_ImageView_MainActivity);
//        imageViewbrownnew = containerLinearLayout.findViewById(R.id.imageView5);
//        athkarIcon = containerLinearLayout.findViewById(R.id.iv_icon);
//
//        // Hide time seconds view
//        View timeSecondsTextView = containerLinearLayout.findViewById(R.id.time_seconds);
//        if (timeSecondsTextView != null) timeSecondsTextView.setVisibility(View.GONE);
//    }
//
//    private void setAppTheme() {
//        // The inflation happened in initViews.
//        // This method can handle other theme-specific initializations if any.
//    }
//
//    private void loadCustomTheme() {
//        if (uiTheme == UITheme.CUSTOM_FIREBASE || uiTheme == UITheme.CUSTOM_1) {
//            String theme = uiTheme.name();
//
//            String path = Hawk.get(theme + (isLandscape ? "_background_landscape" : "_background_portrait"), null);
//            if (path != null && !path.isEmpty() && new File(path).exists()) {
//                DisplayMetrics displayMetrics = new DisplayMetrics();
//                activity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
//                int targetWidth = displayMetrics.widthPixels;
//                int targetHeight = displayMetrics.heightPixels;
//
//                Picasso.get()
//                        .load(new File(path))
//                        .resize(targetWidth, targetHeight)
//                        .centerCrop()
//                        .into(backgroundImageView, new Callback() {
//                            @Override
//                            public void onSuccess() {
//                                loadMainColors();
//                            }
//                            @Override
//                            public void onError(Exception e) {
//                                loadMainColors();
//                                CrashlyticsUtils.INSTANCE.logException(e);
//                            }
//                        });
//            }
//            path = Hawk.get(theme + (isLandscape ? "_date_background_landscape" : "_date_background_portrait"), null);
//            if (path != null && !path.isEmpty() && new File(path).exists()) {
//                Utils.setBackgroundColor(gregorian_month_container, path); // Assuming Utils.setBackgroundColor handles String path
//                Utils.setBackgroundColor(hijri_month_container, path);
//            }
//            int color1 = Hawk.get(theme + "_color1", 0xFF494D32);
//            int color2 = Hawk.get(theme + "_color2", 0xFFAE7C32);
//            moveDateToUp = Hawk.get(theme + "_move_date_to_up", true);
//            setViewsColors(color1, color2);
//        }
//    }
//
//    public void setViewsColors(int color1, int color2) {
//        // This is a repetitive task. Can create a helper function or map view IDs to colors.
//        // For brevity, only showing a few examples.
//        // Many TextViews need to be passed here or accessed directly by ID.
//        // This implies PrayerTimeUIManager and ContentDisplayManager might need to query these colors from here.
//        Utils.setTextColor(activity.findViewById(R.id.timeNow_TextView_MainActivity), color1);
//        Utils.setTextColor(activity.findViewById(R.id.day_text), color1);
//        Utils.setTextColor(activity.findViewById(R.id.dateNow_TextView_MainActivity), color1);
//        // ... (all other TextViews as in original MainActivity)
//
//        // Images
//        Utils.setColorFilter(alrabeeaTimesImageView, color1);
//        Utils.setColorFilter(dayimage, color2);
//        Utils.setColorFilter(hijri_month_image, color2);
//        Utils.setColorFilter(gregorian_month_image, color2);
//
//        // Progress Bar
//        CircularProgressBar circularProgressBar = activity.findViewById(R.id.progressBar);
//        if (circularProgressBar != null) circularProgressBar.setColor(color1);
//
//        // Announcement/Remaining time text colors
//        Utils.setTextColor(activity.findViewById(R.id.tv_remainingOnIkama), color1);
//        Utils.setTextColor(activity.findViewById(R.id.tv_remaining_number), color1);
//        Utils.setTextColor(activity.findViewById(R.id.tv_remaining_text), color1);
//        Utils.setTextColor(activity.findViewById(R.id.tv_message), color1);
//        Utils.setTextColor(activity.findViewById(R.id.tv_description), color1);
//
//        // Prayer Item backgrounds (these are ViewGroup, not TextViews)
//        Utils.setColorFilterView(activity.findViewById(R.id.contentFajr_LinearLayout_MainActivity), color1);
//        Utils.setColorFilterView(activity.findViewById(R.id.contentSunrise_LinearLayout_MainActivity), color1);
//        // ...
//    }
//
//    private void loadMainColors() {
//        domainColor = ContextCompat.getColor(activity, R.color.white);
//        textColor = ContextCompat.getColor(activity, R.color.colorblack);
//        try {
//            BitmapDrawable bitmapDrawable = (BitmapDrawable) backgroundImageView.getDrawable();
//            if (bitmapDrawable == null) return;
//            Bitmap bitmap = bitmapDrawable.getBitmap();
//            if (bitmap == null) return;
//            Palette p = Palette.from(bitmap).generate();
//            Palette.Swatch s = p.getDominantSwatch();
//            if (s == null) return;
//            domainColor = s.getBodyTextColor();
//            textColor = s.getRgb();
//        } catch (Exception e) {
//            domainColor = ContextCompat.getColor(activity, R.color.white);
//            textColor = ContextCompat.getColor(activity, R.color.colorblack);
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    public void logoImage() {
//        PremiumUserModel user = HawkSettings.getPremiumUser();
//
//        Utils.setVisibility(alrabeeaTimesImageView, View.VISIBLE);
//        Utils.setVisibility(athkarIcon, View.VISIBLE);
//        if (user != null) {
//            if (user.iconRemoved) {
//                Utils.setVisibility(alrabeeaTimesImageView, View.INVISIBLE);
//                Utils.setVisibility(athkarIcon, View.GONE);
//            } else {
//                try {
//                    File file = HawkSettings.getPremiumUserCustomIcon();
//                    if (file != null) {
//                        alrabeeaTimesImageView.setImageURI(Uri.fromFile(file));
//                        athkarIcon.setImageURI(Uri.fromFile(file));
//
//                        athkarIcon.setImageTintMode(PorterDuff.Mode.MULTIPLY);
//                        alrabeeaTimesImageView.setImageTintMode(PorterDuff.Mode.MULTIPLY);
//                    }
//                } catch (Exception e) {
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                }
//            }
//        }
//
//        logoImageEnabled = false;
//        if (imageViewbrownnew == null) return;
//        String imgPath = Hawk.get("imgbrown", "");
//        Integer imgColor = Hawk.get("colorlogo", null);
//        if (imgPath != null && !imgPath.isEmpty()) {
//            Utils.setVisibility(imageViewbrownnew, View.VISIBLE);
//            imageViewbrownnew.setImageURI(Uri.parse(imgPath));
//            imageViewbrownnew.setScaleType(ImageView.ScaleType.FIT_CENTER);
//            if (imgColor != null)
//                imageViewbrownnew.setColorFilter(imgColor, PorterDuff.Mode.SRC_IN);
//            else {
//                imageViewbrownnew.setImageTintList(null);
//                imageViewbrownnew.clearColorFilter();
//            }
//            logoImageEnabled = true;
//        } else {
//            Utils.setVisibility(imageViewbrownnew, View.INVISIBLE);
//        }
//    }
//
//    private void setCustomSettingForThemes() {
//        if (imageViewbrownnew == null) return; // Guard
//        switch (uiTheme) {
//            case WHITE: case BROWN_NEW: case DARK_GREEN: case WHITE_NEW: case BROWN:
//                if (isLandscape && !logoImageEnabled) Utils.setVisibility(imageViewbrownnew, View.GONE);
//                break;
//            case BLUE_LET: case BROWN_NEW_3: case CUSTOM_FIREBASE: case CUSTOM_1: // CUSTOM_1 is now included
//                if (!logoImageEnabled)
//                    if (isLandscape) Utils.setVisibility(imageViewbrownnew, View.GONE);
//                    else Utils.setVisibility(imageViewbrownnew, moveDateToUp ? View.GONE : View.INVISIBLE);
//                break;
//            case BLUE:
//                if (!logoImageEnabled) {
//                    if (isLandscape) Utils.setVisibility(imageViewbrownnew, View.GONE);
//                    else setLogoImageHeightToHalf();
//                }
//                break;
//            case NEW_GREEN:
//                if (!logoImageEnabled) Utils.setVisibility(imageViewbrownnew, View.GONE);
//                break;
//        }
//    }
//
//    public void setLogoImageHeightToHalf() {
//        if (imageViewbrownnew == null || imageViewbrownnew.getLayoutParams() == null) return;
//        int imageHeight = imageViewbrownnew.getLayoutParams().height;
//        imageViewbrownnew.getLayoutParams().height = imageHeight / 2;
//        imageViewbrownnew.requestLayout();
//    }
//
//    public void fixBackGroundStretching(ViewGroup tv) {
//        BitmapDrawable background = (BitmapDrawable) tv.getBackground();
//        if (background == null || background.getBitmap() == null) return;
//        BitmapDrawable newBackground = new BitmapDrawable(activity.getResources(), background.getBitmap()) {
//            @Override
//            public int getMinimumWidth() { return 0; }
//            @Override
//            public int getMinimumHeight() { return 0; }
//        };
//        Shader.TileMode modeX = background.getTileModeX();
//        Shader.TileMode modeY = background.getTileModeY();
//        newBackground.setTileModeXY(modeX, modeY);
//        tv.setBackground(newBackground);
//    }
//
//    public void setLayoutDirection(ViewGroup layout) {
//        if (layout == null) return;
//        int direction = HawkSettings.getTypeAM().equals(ConstantsOfApp.AR_LANGUAGE) ? View.LAYOUT_DIRECTION_RTL : View.LAYOUT_DIRECTION_LTR;
//        try {
//            layout.setLayoutDirection(direction);
//        } catch (Exception e) {
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    public int getDomainColor() {
//        return domainColor;
//    }
//
//    public int getTextColor() {
//        return textColor;
//    }
//
//    public UITheme getUiTheme() {
//        return uiTheme;
//    }
//
//    public boolean isLandscape() {
//        return isLandscape;
//    }
//
//    public ViewGroup getContainerLinearLayout() {
//        return containerLinearLayout;
//    }
//}
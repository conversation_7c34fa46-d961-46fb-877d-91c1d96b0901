package com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery;

import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Model.PhotoGalleryImage;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;

public class PhotoGalleryImageAdapter extends RecyclerView.Adapter<PhotoGalleryImageAdapter.ViewHolder> {

    private ArrayList<PhotoGalleryImage> images = new ArrayList<>();

    public PhotoGalleryImageAdapter() {
//        images = dataSet;
    }

    // Create new views (invoked by the layout manager)
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType) {
        // Create a new view, which defines the UI of the list item
        View view = LayoutInflater.from(viewGroup.getContext())
                .inflate(R.layout.photo_gallery_image_list_item, viewGroup, false);

        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder viewHolder, final int position) {

        viewHolder.image = images.get(position);
        viewHolder.loadBackground();
        viewHolder.deleteButton.setOnClickListener(i -> {
            Utils.initConfirmDialog(
                    viewHolder.itemView.getContext(),
                    0, Utils.getString(R.string.delete_photo),
                    Utils.getString(R.string.do_you_sure_want_to_delete_the_photo),
                    true,
                    true,
                    new OnSuccessful() {
                        @Override
                        public void onSuccessful(boolean isSuccessful) {
                            if (isSuccessful) {
                                removeImage(viewHolder.getBindingAdapterPosition());
                            }
                            super.onSuccessful(isSuccessful);
                        }
                    }
            );
        });
    }

    public ArrayList<PhotoGalleryImage> getImages() {
        return images;
    }

    public void setImages(ArrayList<PhotoGalleryImage> images) {
        this.images = images;
        notifyDataSetChanged();
    }

    public void clearImages() {
        images.clear();
        notifyDataSetChanged();
    }

    public void addImage(PhotoGalleryImage image) {
        images.add(image);
        notifyItemInserted(images.size());
    }

    // Return the size of your dataset (invoked by the layout manager)
    @Override
    public int getItemCount() {
        return images.size();
    }

    public void removeImage(int position) {
//        PhotoGalleryImagesRepository.delete(images.get(position).id);
        images.remove(position);
        notifyItemRemoved(position);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView deleteButton;
        private final ImageView backgroundImageView;
        public PhotoGalleryImage image;

        public ViewHolder(View view) {
            super(view);
            deleteButton = view.findViewById(R.id.delete_Button_PhotoGalleryHolder);
            backgroundImageView = view.findViewById(R.id.iv_gallery_photo_list_item);
        }

        public void loadBackground() {
            if (image != null && image.imageUri != null) {
                backgroundImageView.setImageURI(Uri.parse(image.imageUri));
//                backgroundImageView.setImageBitmap(image.getImage());
            }
        }
    }
}

package com.arapeak.alrbea.UI.Fragment.settings.content.athkar.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.AL_AHADETH_DURATION_LONG_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.AL_AHADETH_DURATION_STRING_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_AL_AHADETH_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.MyTimePickerDialog;
import com.arapeak.alrbea.UI.CustomView.TimePicker;
import com.orhanobut.hawk.Hawk;

public class SettingsAlAhadethFragment extends AlrabeeaTimesFragment {

    private static final String TAG = "SettingsAhadethFragment";

    private View settingsAlAhadethView;
    private CheckBox alAhadethCheckBox;
    private TextView alAhadethTextView;
    private TextView alAhadethDurationTextView;

    public SettingsAlAhadethFragment() {

    }

    public static SettingsAlAhadethFragment newInstance() {
        return new SettingsAlAhadethFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        settingsAlAhadethView = inflater.inflate(R.layout.fragment_settings_al_ahadeth, container, false);

        initView();
        SetParameter();
        SetAction();

        return settingsAlAhadethView;
    }

    private void initView() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(getString(R.string.al_ahadeth));
//        } else {
//            SettingsActivity.setTextTite(getString(R.string.al_ahadeth));
//        }
        SettingsActivity.setTextTite(getString(R.string.al_ahadeth));
        alAhadethCheckBox = settingsAlAhadethView.findViewById(R.id.alAhadeth_CheckBox_SettingsAlAhadethFragment);
        alAhadethTextView = settingsAlAhadethView.findViewById(R.id.alAhadeth_TextView_SettingsAlAhadethFragment);
        alAhadethDurationTextView = settingsAlAhadethView.findViewById(R.id.alAhadethDuration_TextView_SettingsAlAhadethFragment);
    }

    private void SetParameter() {
        boolean isEnableAlAhadeth = Hawk.get(IS_ENABLE_AL_AHADETH_KEY, IS_ENABLE_SETTINGS_DEFAULT);
        alAhadethCheckBox.setChecked(isEnableAlAhadeth);
        if (isEnableAlAhadeth) {
            alAhadethDurationTextView.setVisibility(View.VISIBLE);
            alAhadethDurationTextView.setText(Hawk.get(AL_AHADETH_DURATION_STRING_KEY, "00:00:00"));
        } else {
            alAhadethDurationTextView.setVisibility(View.GONE);
        }
    }

    private void SetAction() {
        alAhadethDurationTextView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String[] separated = Hawk.get(AL_AHADETH_DURATION_STRING_KEY, "00:00:00").split(":");
                MyTimePickerDialog mTimePicker = new MyTimePickerDialog(getAppCompatActivity(), new MyTimePickerDialog.OnTimeSetListener() {
                    @Override
                    public void onTimeSet(TimePicker view, int hourOfDay, int minute, int seconds) {
                        alAhadethDurationTextView.setText(String.format("%02d", hourOfDay) +
                                ":" + String.format("%02d", minute) +
                                ":" + String.format("%02d", seconds));
                        Hawk.put(AL_AHADETH_DURATION_STRING_KEY, alAhadethDurationTextView.getText().toString());
                        Hawk.put(AL_AHADETH_DURATION_LONG_KEY, (hourOfDay * 3600000L) + (minute * 60000L) + (seconds * 1000L));
                    }
                }, Integer.valueOf(separated[0]), Integer.valueOf(separated[1]), Integer.valueOf(separated[2]), true);
                mTimePicker.show();
            }
        });

        alAhadethCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Hawk.put(IS_ENABLE_AL_AHADETH_KEY, isChecked);
                if (isChecked) {
                    alAhadethDurationTextView.setVisibility(View.VISIBLE);
                    alAhadethDurationTextView.setText("00:00:00");
                } else {
                    alAhadethDurationTextView.setVisibility(View.GONE);
                    Hawk.delete(AL_AHADETH_DURATION_STRING_KEY);
                    Hawk.delete(AL_AHADETH_DURATION_LONG_KEY);
                }
            }
        });
    }
}

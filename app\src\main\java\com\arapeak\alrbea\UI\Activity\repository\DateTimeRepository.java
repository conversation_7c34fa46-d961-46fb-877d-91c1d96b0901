package com.arapeak.alrbea.UI.Activity.repository;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Repository for handling date and time operations
 * Manages both Gregorian and Hijri calendars
 */
public class DateTimeRepository {

    private static final String TAG = "DateTimeRepository";
    private static DateTimeRepository instance;

    private final ExecutorService executorService;

    // LiveData for time
    private final MutableLiveData<String> currentTimeLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> currentTimeTypeLiveData = new MutableLiveData<>();

    // LiveData for Gregorian date
    private final MutableLiveData<String> gregorianDateLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> gregorianYearLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> gregorianMonthLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> gregorianDayLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> gregorianDayNameLiveData = new MutableLiveData<>();

    // LiveData for Hijri date
    private final MutableLiveData<String> hijriDateLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> hijriYearLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> hijriMonthLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> hijriDayLiveData = new MutableLiveData<>();

    // Calendar instances
    private GregorianCalendar gregorianCalendar;
    private UmmalquraCalendar hijriCalendar;

    // Current date values
    private int currentYear, currentMonth, currentDay;
    private long currentTimeMillis;

    private DateTimeRepository() {
        executorService = Executors.newSingleThreadExecutor();
        initializeCalendars();
    }

    public static synchronized DateTimeRepository getInstance() {
        if (instance == null) {
            instance = new DateTimeRepository();
        }
        return instance;
    }

    private void initializeCalendars() {
        try {
            gregorianCalendar = new GregorianCalendar();
            hijriCalendar = Utils.getUmmalquraCalendar();
            updateCurrentTime();
        } catch (Exception e) {
            Log.e(TAG, "Error initializing calendars", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    // LiveData getters for time
    public LiveData<String> getCurrentTimeLiveData() {
        return currentTimeLiveData;
    }

    public LiveData<String> getCurrentTimeTypeLiveData() {
        return currentTimeTypeLiveData;
    }

    // LiveData getters for Gregorian date
    public LiveData<String> getGregorianDateLiveData() {
        return gregorianDateLiveData;
    }

    public LiveData<String> getGregorianYearLiveData() {
        return gregorianYearLiveData;
    }

    public LiveData<String> getGregorianMonthLiveData() {
        return gregorianMonthLiveData;
    }

    public LiveData<String> getGregorianDayLiveData() {
        return gregorianDayLiveData;
    }

    public LiveData<String> getGregorianDayNameLiveData() {
        return gregorianDayNameLiveData;
    }

    // LiveData getters for Hijri date
    public LiveData<String> getHijriDateLiveData() {
        return hijriDateLiveData;
    }

    public LiveData<String> getHijriYearLiveData() {
        return hijriYearLiveData;
    }

    public LiveData<String> getHijriMonthLiveData() {
        return hijriMonthLiveData;
    }

    public LiveData<String> getHijriDayLiveData() {
        return hijriDayLiveData;
    }

    /**
     * Update current time and date
     */
    public void updateCurrentTime() {
        executorService.execute(() -> {
            try {
                currentTimeMillis = System.currentTimeMillis();

                // Update time
                String time = Utils.getTimeNow();
                String timeType = Utils.getTypeTimeNow();
                currentTimeLiveData.postValue(time);
                currentTimeTypeLiveData.postValue(timeType);

                // Update date values
                currentYear = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR, currentTimeMillis));
                currentMonth = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH, currentTimeMillis));
                currentDay = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY, currentTimeMillis));

                // Update calendars
                updateCalendars();

                // Update date displays
                updateDateDisplays();

            } catch (Exception e) {
                Log.e(TAG, "Error updating current time", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    /**
     * Update calendar instances
     */
    private void updateCalendars() {
        try {
            gregorianCalendar.setTimeInMillis(currentTimeMillis);
            hijriCalendar.setTimeInMillis(currentTimeMillis);
        } catch (Exception e) {
            Log.e(TAG, "Error updating calendars", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update date displays for both calendars
     */
    private void updateDateDisplays() {
        try {
            updateGregorianDateDisplays();
            updateHijriDateDisplays();
        } catch (Exception e) {
            Log.e(TAG, "Error updating date displays", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update Gregorian date displays
     */
    private void updateGregorianDateDisplays() {
        try {
            String language = HawkSettings.getLocaleLanguage();
            boolean isArabic = "ar".equals(language);

            // Year
            String year = String.valueOf(gregorianCalendar.get(Calendar.YEAR));
            gregorianYearLiveData.postValue(year);

            // Month
            String month = getGregorianMonthName(gregorianCalendar.get(Calendar.MONTH), isArabic);
            gregorianMonthLiveData.postValue(month);

            // Day
            String day = String.valueOf(gregorianCalendar.get(Calendar.DAY_OF_MONTH));
            gregorianDayLiveData.postValue(day);

            // Day name
            String dayName = getGregorianDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK), isArabic);
            gregorianDayNameLiveData.postValue(dayName);

            // Full date
            String fullDate = formatGregorianDate(isArabic);
            gregorianDateLiveData.postValue(fullDate);

        } catch (Exception e) {
            Log.e(TAG, "Error updating Gregorian date displays", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update Hijri date displays
     */
    private void updateHijriDateDisplays() {
        try {
            String language = HawkSettings.getLocaleLanguage();
            boolean isArabic = "ar".equals(language);

            // Year
            String year = String.valueOf(hijriCalendar.get(Calendar.YEAR));
            hijriYearLiveData.postValue(year);

            // Month
            String month = getHijriMonthName(hijriCalendar.get(Calendar.MONTH), isArabic);
            hijriMonthLiveData.postValue(month);

            // Day
            String day = String.valueOf(hijriCalendar.get(Calendar.DAY_OF_MONTH));
            hijriDayLiveData.postValue(day);

            // Full date
            String fullDate = formatHijriDate(isArabic);
            hijriDateLiveData.postValue(fullDate);

        } catch (Exception e) {
            Log.e(TAG, "Error updating Hijri date displays", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Get Gregorian month name
     */
    private String getGregorianMonthName(int month, boolean isArabic) {
        if (isArabic) {
            String[] arabicMonths = {
                    "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                    "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
            return month >= 0 && month < arabicMonths.length ? arabicMonths[month] : "";
        } else {
            String[] englishMonths = {
                    "January", "February", "March", "April", "May", "June",
                    "July", "August", "September", "October", "November", "December"
            };
            return month >= 0 && month < englishMonths.length ? englishMonths[month] : "";
        }
    }

    /**
     * Get Hijri month name
     */
    private String getHijriMonthName(int month, boolean isArabic) {
        if (isArabic) {
            String[] arabicMonths = {
                    "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
                    "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
            };
            return month >= 0 && month < arabicMonths.length ? arabicMonths[month] : "";
        } else {
            String[] englishMonths = {
                    "Muharram", "Safar", "Rabi' al-awwal", "Rabi' al-thani", "Jumada al-awwal", "Jumada al-thani",
                    "Rajab", "Sha'ban", "Ramadan", "Shawwal", "Dhu al-Qi'dah", "Dhu al-Hijjah"
            };
            return month >= 0 && month < englishMonths.length ? englishMonths[month] : "";
        }
    }

    /**
     * Get Gregorian day name
     */
    private String getGregorianDayName(int dayOfWeek, boolean isArabic) {
        if (isArabic) {
            String[] arabicDays = {
                    "", "الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"
            };
            return dayOfWeek >= 0 && dayOfWeek < arabicDays.length ? arabicDays[dayOfWeek] : "";
        } else {
            String[] englishDays = {
                    "", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
            };
            return dayOfWeek >= 0 && dayOfWeek < englishDays.length ? englishDays[dayOfWeek] : "";
        }
    }

    /**
     * Format Gregorian date
     */
    private String formatGregorianDate(boolean isArabic) {
        try {
            if (isArabic) {
                return String.format("%s %d %s %d",
                        getGregorianDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK), true),
                        gregorianCalendar.get(Calendar.DAY_OF_MONTH),
                        getGregorianMonthName(gregorianCalendar.get(Calendar.MONTH), true),
                        gregorianCalendar.get(Calendar.YEAR));
            } else {
                return String.format("%s, %s %d, %d",
                        getGregorianDayName(gregorianCalendar.get(Calendar.DAY_OF_WEEK), false),
                        getGregorianMonthName(gregorianCalendar.get(Calendar.MONTH), false),
                        gregorianCalendar.get(Calendar.DAY_OF_MONTH),
                        gregorianCalendar.get(Calendar.YEAR));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error formatting Gregorian date", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Format Hijri date
     */
    private String formatHijriDate(boolean isArabic) {
        try {
            if (isArabic) {
                return String.format("%d %s %d هـ",
                        hijriCalendar.get(Calendar.DAY_OF_MONTH),
                        getHijriMonthName(hijriCalendar.get(Calendar.MONTH), true),
                        hijriCalendar.get(Calendar.YEAR));
            } else {
                return String.format("%s %d, %d AH",
                        getHijriMonthName(hijriCalendar.get(Calendar.MONTH), false),
                        hijriCalendar.get(Calendar.DAY_OF_MONTH),
                        hijriCalendar.get(Calendar.YEAR));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error formatting Hijri date", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Check if it's midnight
     */
    public boolean isMidnight() {
        try {
            Calendar now = Calendar.getInstance();
            return now.get(Calendar.HOUR_OF_DAY) == 0 &&
                    now.get(Calendar.MINUTE) == 0 &&
                    now.get(Calendar.SECOND) < 5; // Allow 5 second window
        } catch (Exception e) {
            Log.e(TAG, "Error checking midnight", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    /**
     * Check if it's Friday (Juma)
     */
    public boolean isFriday() {
        try {
            return gregorianCalendar.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY;
        } catch (Exception e) {
            Log.e(TAG, "Error checking Friday", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    /**
     * Get current year
     */
    public int getCurrentYear() {
        return currentYear;
    }

    /**
     * Get current month
     */
    public int getCurrentMonth() {
        return currentMonth;
    }

    /**
     * Get current day
     */
    public int getCurrentDay() {
        return currentDay;
    }

    /**
     * Get current time in milliseconds
     */
    public long getCurrentTimeMillis() {
        return currentTimeMillis;
    }

    /**
     * Get Gregorian calendar instance
     */
    public GregorianCalendar getGregorianCalendar() {
        return gregorianCalendar;
    }

    /**
     * Get Hijri calendar instance
     */
    public UmmalquraCalendar getHijriCalendar() {
        return hijriCalendar;
    }

    /**
     * Get formatted time for Athkar display
     */
    public String getAthkarTimeDisplay() {
        try {
            String time = currentTimeLiveData.getValue();
            String timeType = currentTimeTypeLiveData.getValue();
            String date = getFormattedDateForAthkar();

            if (time != null && timeType != null && date != null) {
                return date + " | " + time + " " + timeType;
            }
            return "";
        } catch (Exception e) {
            Log.e(TAG, "Error getting Athkar time display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Get formatted date for Athkar display
     */
    private String getFormattedDateForAthkar() {
        try {
            String language = HawkSettings.getLocaleLanguage();
            boolean isArabic = "ar".equals(language);

            if (isArabic) {
                return String.format("%d %s %d",
                        gregorianCalendar.get(Calendar.DAY_OF_MONTH),
                        getGregorianMonthName(gregorianCalendar.get(Calendar.MONTH), true),
                        gregorianCalendar.get(Calendar.YEAR));
            } else {
                return String.format("%s %d, %d",
                        getGregorianMonthName(gregorianCalendar.get(Calendar.MONTH), false),
                        gregorianCalendar.get(Calendar.DAY_OF_MONTH),
                        gregorianCalendar.get(Calendar.YEAR));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error formatting date for Athkar", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Clean up resources
     */
    public void cleanup() {
        try {
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
            }

            gregorianCalendar = null;
            hijriCalendar = null;

        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}
package com.arapeak.alrbea.UI.Fragment.settings.content.remote;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteTool;
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteToolStatus;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.DownloadRequest;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.DownloadResult;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.Downloader;
import com.mikhaellopez.circularprogressbar.CircularProgressBar;
import com.orhanobut.hawk.Hawk;

import org.sufficientlysecure.rootcommands.Shell;
import org.sufficientlysecure.rootcommands.command.SimpleCommand;

import java.io.File;
import java.util.List;

import kotlin.Unit;

@FunctionalInterface
interface RefreshInterface {
    void refresh();
}

public class RemoteToolsListAdapter extends RecyclerView.Adapter<RemoteToolsListAdapter.ViewHolder> {
    private final Activity context;

    private List<RemoteTool> items;
    private RefreshInterface refreshTask = () -> {
    };

    private volatile Boolean isWorking = false;

    public RemoteToolsListAdapter(Activity activity, List<RemoteTool> items) {
        this.context = activity;
        this.items = items;
    }

    public void setList(List<RemoteTool> nw) {
        this.items = nw;
        notifyDataSetChanged();
    }

    public void setOnRefreshTools(RefreshInterface o) {
        this.refreshTask = o;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_remote_tools, parent, false);
        return new ViewHolder(view);
    }

    /**
     * @noinspection t
     */
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        try {

            RemoteTool tool = items.get(position);

            holder.progressBar.setVisibility(View.INVISIBLE);
            holder.tv_name.setText(tool.getName());
            holder.iv_icon.setImageResource(tool.getIconRes());

            if (tool.getStatus() instanceof RemoteToolStatus.NotInstalled) {
                holder.tv_version.setVisibility(View.INVISIBLE);
                holder.btn_open.setVisibility(View.GONE);
                holder.btn_update.setVisibility(View.GONE);
//                holder.progressBar.setVisibility(View.GONE);
                holder.btn_install.setVisibility(View.VISIBLE);
                holder.tv_status.setText(R.string.app_not_installed);
                holder.tv_status.setTextColor(Color.parseColor("#bb393e"));

            } else if (tool.getStatus() instanceof RemoteToolStatus.Installed) {
                RemoteToolStatus.Installed installedStatus = (RemoteToolStatus.Installed) tool.getStatus();
                int versionCode = installedStatus.getVersionCode();
                String versionName = installedStatus.getVersionName();

                holder.tv_version.setVisibility(View.VISIBLE);
                holder.tv_version.setText(versionName);

                holder.btn_open.setVisibility(View.VISIBLE);
                holder.btn_update.setVisibility(View.GONE);
//                holder.progressBar.setVisibility(View.GONE);
                holder.btn_install.setVisibility(View.GONE);

                holder.tv_status.setText(R.string.app_is_installed);
                holder.tv_status.setTextColor(Color.parseColor("#47864a"));


            } else if (tool.getStatus() instanceof RemoteToolStatus.Outdated) {
                RemoteToolStatus.Outdated outdatedStatus = (RemoteToolStatus.Outdated) tool.getStatus();
                int localCode = outdatedStatus.getLocalCode();
                String localName = outdatedStatus.getLocalName();
                int cloudCode = outdatedStatus.getCloudCode();

                holder.tv_version.setVisibility(View.VISIBLE);
                holder.tv_version.setText(localName);

                holder.btn_open.setVisibility(View.VISIBLE);
                holder.btn_update.setVisibility(View.VISIBLE);
//                holder.progressBar.setVisibility(View.GONE);
                holder.btn_install.setVisibility(View.GONE);

                holder.tv_status.setText(R.string.app_outdated);
                holder.tv_status.setTextColor(Color.parseColor("#4372f5"));

            }

            if (!canLaunchApp(tool)) {
                holder.btn_open.setVisibility(View.GONE);
            }

            holder.btn_update.setOnClickListener(
                    v -> {
                        if (isWorking)
                            return;
                        holder.btn_update.setVisibility(View.GONE);
                        holder.btn_open.setVisibility(View.GONE);
                        holder.btn_install.setVisibility(View.GONE);

                        holder.progressBar.setVisibility(View.VISIBLE);
                        holder.progressBar.enableIndeterminateMode(true);

                        Thread main = new Thread(() -> {
                            try {
                                install(tool, holder.progressBar);
                            } catch (Exception e) {
                                CrashlyticsUtils.INSTANCE.logException(e);
                            }
                        });
                        main.start();

                    }
            );

            holder.btn_install.setOnClickListener(
                    v -> {
                        if (isWorking)
                            return;
                        holder.btn_update.setVisibility(View.GONE);
                        holder.btn_open.setVisibility(View.GONE);
                        holder.btn_install.setVisibility(View.GONE);

                        holder.progressBar.setVisibility(View.VISIBLE);
                        holder.progressBar.enableIndeterminateMode(true);
                        Thread main = new Thread(() -> {
                            try {
                                install(tool, holder.progressBar);
                            } catch (Exception e) {
                                CrashlyticsUtils.INSTANCE.logException(e);
                            }
                        });
                        main.start();
                    }
            );

            holder.btn_open.setOnClickListener(
                    v -> {
                        open(tool);
                    }
            );

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void install(RemoteTool tool, CircularProgressBar progressBar) throws InterruptedException {
        isWorking = true;
        
        // Check network connectivity first
        if (!Utils.isNetworkAvailable(context)) {
            context.runOnUiThread(() -> {
                if (progressBar != null) {
                    progressBar.setVisibility(View.GONE);
                }
                Utils.showToast(context, "لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.");
            });
            isWorking = false;
            return;
        }

        DownloadRequest download = new DownloadRequest(
                tool.getId(),
                tool.getDownLink(),
                tool.getFileName(),
                () -> {
                    return Unit.INSTANCE;
                }
        );

        Downloader downloader = new Downloader(context);
        DownloadResult file = null;
        try {
            file = downloader.downloadFile(download, loading -> {
                if (loading >= 0) {
                    context.runOnUiThread(() -> {
                        if (progressBar != null) {
                            progressBar.setVisibility(View.VISIBLE);
                            progressBar.enableIndeterminateMode(false);
                            progressBar.setProgress(loading);
                        }
                    });
                } else {
                    context.runOnUiThread(() -> {
                        if (progressBar != null) {
                            progressBar.setVisibility(View.VISIBLE);
                            progressBar.enableIndeterminateMode(true);
                        }
                    });
                }
                return Unit.INSTANCE;
            });
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            String errorMessage;
            if (e instanceof java.net.UnknownHostException) {
                errorMessage = "لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.";
            } else if (e instanceof java.net.SocketTimeoutException) {
                errorMessage = "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.";
            } else {
                errorMessage = "حدث خطأ أثناء تحميل الملف";
            }

            context.runOnUiThread(() -> {
                if (progressBar != null) {
                    progressBar.setVisibility(View.GONE);
                }
                Utils.showToast(context, errorMessage.toString());
            });
            isWorking = false;
            return;
        }

        if (file == null) {
            context.runOnUiThread(() -> {
                if (progressBar != null) {
                    progressBar.setVisibility(View.GONE);
                }
                Utils.showToast(context, "فشل تحميل الملف. يرجى المحاولة مرة أخرى.");
            });
            isWorking = false;
            return;
        }

        try {
            installApk(file.getFilePath());
            file.component2().invoke();

            int j = 3;
            while (j > 0) {
                Thread.sleep(5000);
                refreshTask.refresh();
                j--;
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            context.runOnUiThread(() -> {
                if (progressBar != null) {
                    progressBar.setVisibility(View.GONE);
                }
                Utils.showToast(context, "حدث خطأ أثناء تثبيت التطبيق. يرجى المحاولة مرة أخرى.");
            });
        } finally {
            isWorking = false;
        }
    }

    private void update(RemoteTool tool) {

    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public void open(RemoteTool tool) {
        try {
            Intent intent = context.getPackageManager().getLaunchIntentForPackage(tool.getAppPackage());
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT);
                Hawk.put("isTeamViewerLaunched", true);
                context.startActivity(intent);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public boolean canLaunchApp(RemoteTool tool) {
        PackageManager packageManager = context.getPackageManager();
        Intent intent = packageManager.getLaunchIntentForPackage(tool.getAppPackage());
        return intent != null;
    }

    private void installApk(String apkPath) {
        if (Utils.hasRootPermission())
            installApkWithRoot(apkPath);
        else
            installApkWithoutRoot(apkPath);
    }

    private void installApkWithRoot(String apkPath) {
        try {
            Shell shell = Shell.startRootShell();
            String runCmd = "am start -n com.alrbea.prayer/com.arapeak.alrbea.ui.Activity.SplashScreen";
            SimpleCommand command = new SimpleCommand("pm install -r " + apkPath + " && " + runCmd);
            shell.add(command).waitForFinish();
            shell.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void installApkWithoutRoot(String apkPath) {
        Intent intent = getIntentForApkInstall(apkPath);
        try {
            Log.i("installAPk", "install apk : " + apkPath);
            context.startActivity(intent);
            Log.i("installAPk", "install " + apkPath);

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private Intent getIntentForApkInstall(String apkPath) {
        File apkFile = new File(apkPath);
        Uri apkUri = FileProvider.getUriForFile(context, context.getApplicationContext().getPackageName() + ".fileprovider", apkFile);
        Intent intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
        intent.setData(apkUri);
        intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        return intent;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public ImageView iv_icon;
        public TextView tv_name, tv_version, tv_status;
        public Button btn_open, btn_install, btn_update;
        public CircularProgressBar progressBar;

        public ViewHolder(View itemView) {
            super(itemView);
            iv_icon = itemView.findViewById(R.id.iv_icon);
            tv_name = itemView.findViewById(R.id.tv_name);
            tv_version = itemView.findViewById(R.id.tv_version);
            btn_open = itemView.findViewById(R.id.btn_open);
            btn_install = itemView.findViewById(R.id.btn_install);
            btn_update = itemView.findViewById(R.id.btn_update);
            progressBar = itemView.findViewById(R.id.progressBar);
            tv_status = itemView.findViewById(R.id.tv_state);
        }
    }
}


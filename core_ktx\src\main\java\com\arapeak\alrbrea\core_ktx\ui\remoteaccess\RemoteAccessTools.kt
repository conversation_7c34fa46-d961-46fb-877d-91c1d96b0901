package com.arapeak.alrbrea.core_ktx.ui.remoteaccess

import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteTool
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteToolStatus


fun RemoteAccessManager.addTeamviewer() {
    val id = 1
    list[id] = RemoteTool(
        id = id,
        name = "TeamViewer",
        appPackage = "com.teamviewer.quicksupport.market",
        fileName = "teamviewer.apk",
        downLink =
        "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/SupportApk%2FTeamViewer15.59.587.apk?alt=media&token=9fdbb542-938b-4eb5-828a-05c4196d108d",
        iconRes = R.drawable.ic_team_viewer,
        status = RemoteToolStatus.NotInstalled,
    )
}

fun RemoteAccessManager.addTeamviewerAddOn() {
    val id = 2
    list[id] = RemoteTool(
        id = id,
        name = "TeamViewer Universal Add On",
        appPackage = "com.teamviewer.quicksupport.addon.universal",
        fileName = "teamvieweraddon.apk",
        downLink =
        "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/SupportApk%2Fcom_teamviewer_quicksupport_addon_universal_v15.57.511.apk.support.apk?alt=media&token=3ccd6f40-26fc-4ba4-8bc9-a2eba39853e0",
        iconRes = R.drawable.ic_team_viewer_dark,
        status = RemoteToolStatus.NotInstalled,
    )
}

fun RemoteAccessManager.addAnyDesk() {
    val id = 3
    list[id] = RemoteTool(
        id = id,
        name = "AnyDesk",
        appPackage = "com.anydesk.anydeskandroid",
        fileName = "anydesk.apk",
        downLink = "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/SupportApk%2Fanydesk.apk?alt=media&token=7858bd7f-ec56-4df4-b0cf-c6940e358df6",
        iconRes = R.drawable.anydesk,
        status = RemoteToolStatus.NotInstalled,
    )
}
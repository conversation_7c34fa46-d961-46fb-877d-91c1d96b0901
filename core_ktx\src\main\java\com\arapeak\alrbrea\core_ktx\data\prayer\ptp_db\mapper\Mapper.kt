package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.mapper

import kotlinx.datetime.LocalTime
import java.util.Calendar

fun LocalTime.toCalendar(): Calendar {
    return Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY, <EMAIL>)
        set(Calendar.MINUTE, <EMAIL>)
    }
}

fun java.time.LocalTime.toCalendar(): Calendar {
    return Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY, <EMAIL>)
        set(Calendar.MINUTE, <EMAIL>)
    }
}
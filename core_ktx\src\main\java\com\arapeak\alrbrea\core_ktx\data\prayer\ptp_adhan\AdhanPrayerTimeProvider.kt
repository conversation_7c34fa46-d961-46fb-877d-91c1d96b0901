package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_adhan

import android.location.Location
import com.arapeak.alrbrea.core_ktx.data.prayer.PrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_adhan.mapper.toAdhanMethod
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper.PrayerTimeMapper
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.arapeak.alrbrea.core_ktx.model.prayer.Prayer
import com.arapeak.alrbrea.core_ktx.model.prayer.PrayerEnum
import com.batoulapps.adhan2.Coordinates
import com.batoulapps.adhan2.HighLatitudeRule
import com.batoulapps.adhan2.Madhab
import com.batoulapps.adhan2.PrayerTimes
import com.batoulapps.adhan2.data.DateComponents
import com.batoulapps.adhan2.model.Rounding
import com.batoulapps.adhan2.model.Shafaq
import timber.log.Timber
import java.util.Calendar

class AdhanPrayerTimeProvider : PrayerTimeProvider() {
    override fun getPrayerTime(location: Location, calculationMethod: CalculationMethod, madhab: Madhab, date: Calendar): DayPrayers {

        val cord = Coordinates(location.latitude, location.longitude)


        val dateComponents = DateComponents(date.get(Calendar.YEAR), date.get(Calendar.MONTH), date.get(Calendar.DAY_OF_MONTH))
        val prayerTimes = PrayerTimes(cord, dateComponents, calculationMethod.toAdhanMethod().parameters)

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, PrayerTimeMapper().map(prayerTimes.fajr)),
            dhuhr = Prayer(PrayerEnum.dhuhr, PrayerTimeMapper().map(prayerTimes.dhuhr)),
            asr = Prayer(PrayerEnum.asr, PrayerTimeMapper().map(prayerTimes.asr)),
            maghrib = Prayer(PrayerEnum.maghrib, PrayerTimeMapper().map(prayerTimes.maghrib)),
            isha = Prayer(PrayerEnum.isha, PrayerTimeMapper().map(prayerTimes.isha)),
            sunrise = Prayer(PrayerEnum.sunrise, PrayerTimeMapper().map(prayerTimes.sunrise)),
            eid = null
        )

//        runTest(cord, dateComponents)


        return dayPrayers
    }

    private fun runTest(cord: Coordinates, dateComponents: DateComponents) {
        val methods = CalculationMethod.entries
        val madhabs = Madhab.entries
        val highLatitudeRules = HighLatitudeRule.entries
        val roundings = Rounding.entries
        val shafaqs = Shafaq.entries

        val fajrAngles = listOf(
//           0.0
            18.0,
//            15.0
        )
        val ishaAngles = listOf(
//            0.0
            18.0,
//            15.0
        )
//        return dayPrayers
        methods.forEach { method ->
            madhabs.forEach { madhab ->
                highLatitudeRules.forEach { hlr ->
                    roundings.forEach { rounding ->
                        shafaqs.forEach { shafaq ->
                            fajrAngles.forEach { fajrAngle ->
                                ishaAngles.forEach { ishaAngle ->
                                    extracted(cord, dateComponents, method, madhab, hlr, rounding, shafaq, fajrAngle, ishaAngle)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun extracted(
        cord: Coordinates, dateComponents: DateComponents, calculationMethod: CalculationMethod, madhab: Madhab,
        hlr: HighLatitudeRule, rounding: Rounding, shafaq: Shafaq, fajrAngle: Double, ishaAngle: Double
    ) {
        val prayerTimes = PrayerTimes(
            cord, dateComponents, calculationMethod.toAdhanMethod().parameters.copy(
                fajrAngle = fajrAngle,
                ishaAngle = ishaAngle,
                madhab = madhab,
                shafaq = shafaq,
                highLatitudeRule = hlr,
                rounding = rounding
            )
        )

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, PrayerTimeMapper().map(prayerTimes.fajr)),
            dhuhr = Prayer(PrayerEnum.dhuhr, PrayerTimeMapper().map(prayerTimes.dhuhr)),
            asr = Prayer(PrayerEnum.asr, PrayerTimeMapper().map(prayerTimes.asr)),
            maghrib = Prayer(PrayerEnum.maghrib, PrayerTimeMapper().map(prayerTimes.maghrib)),
            isha = Prayer(PrayerEnum.isha, PrayerTimeMapper().map(prayerTimes.isha)),
            sunrise = Prayer(PrayerEnum.sunrise, PrayerTimeMapper().map(prayerTimes.sunrise)),
            eid = null
        )

        Timber.tag("TestPray")
            .e(" Adhan   Madhab : " + madhab.name + " ||  method : " + calculationMethod.name + " || HLR : " + hlr.name + " || rounding : " + rounding.name + " || shafaq : " + shafaq.name + " || fajrAngle : " + fajrAngle + " || ishaAngle : " + ishaAngle)
        Timber.tag("TestPray").e(" Adhan " + dayPrayers.toString())
        Timber.tag("TestPray").e(" Adhan " + "\n")

    }
}


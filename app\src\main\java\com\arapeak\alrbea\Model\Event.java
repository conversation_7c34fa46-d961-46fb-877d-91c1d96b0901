package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.Calendar;

public class Event {
    public int id;
    public String text;
    public UmmalquraCalendar sCal;
    public UmmalquraCalendar eCal;
    public boolean enabled;

    public Event() {
        enabled = true;
        text = "";
        sCal = Utils.getUmmalquraCalendar();
        eCal = Utils.getUmmalquraCalendar();
        eCal.add(UmmalquraCalendar.DAY_OF_MONTH, 1);
        sCal.set(Calendar.HOUR_OF_DAY, 0);
        sCal.set(Calendar.MINUTE, 0);
        eCal.set(Calendar.HOUR_OF_DAY, 0);
        eCal.set(Calendar.MINUTE, 0);
    }
}

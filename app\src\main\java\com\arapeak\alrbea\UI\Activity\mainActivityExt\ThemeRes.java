package com.arapeak.alrbea.UI.Activity.mainActivityExt;

import androidx.annotation.DrawableRes;

import com.arapeak.alrbea.R;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.Calendar;
import java.util.GregorianCalendar;

public class ThemeRes {


    @DrawableRes
    public static int getHijriMonthImageRes(UmmalquraCalendar hijriCalendar) {
        int[] images = {
                R.drawable.theme_brown_3_hijri_month_1,
                R.drawable.theme_brown_3_hijri_month_2,
                R.drawable.theme_brown_3_hijri_month_3,
                R.drawable.theme_brown_3_hijri_month_4,
                R.drawable.theme_brown_3_hijri_month_5,
                R.drawable.theme_brown_3_hijri_month_6,
                R.drawable.theme_brown_3_hijri_month_7,
                R.drawable.theme_brown_3_hijri_month_8,
                <PERSON>.drawable.theme_brown_3_hijri_month_9,
                <PERSON>.drawable.theme_brown_3_hijri_month_10,
                R.drawable.theme_brown_3_hijri_month_11,
                R.drawable.theme_brown_3_hijri_month_12,
        };
        return images[hijriCalendar.get(Calendar.MONTH)];
//        return images[dateAlrabeeaTimes.getHijri().getMonth().getNumber()];
    }

    @DrawableRes
    public static int getGregorianMonthImageRes(GregorianCalendar gregorianCalendar) {
        int[] images = {
                R.drawable.theme_brown_3_month_1,
                R.drawable.theme_brown_3_month_2,
                R.drawable.theme_brown_3_month_3,
                R.drawable.theme_brown_3_month_4,
                R.drawable.theme_brown_3_month_5,
                R.drawable.theme_brown_3_month_6,
                R.drawable.theme_brown_3_month_7,
                R.drawable.theme_brown_3_month_8,
                R.drawable.theme_brown_3_month_9,
                R.drawable.theme_brown_3_month_10,
                R.drawable.theme_brown_3_month_11,
                R.drawable.theme_brown_3_month_12,
        };
        return images[gregorianCalendar.get(Calendar.MONTH)];
    }

    @DrawableRes
    public static int getDayImageRes(GregorianCalendar gregorianCalendar) {
        switch (gregorianCalendar.get(Calendar.DAY_OF_WEEK)) {
            case Calendar.SATURDAY:
                return R.drawable.theme_brown_3_day_1;
            case Calendar.SUNDAY:
                return R.drawable.theme_brown_3_day_2;
            case Calendar.MONDAY:
                return R.drawable.theme_brown_3_day_3;
            case Calendar.TUESDAY:
                return R.drawable.theme_brown_3_day_4;
            case Calendar.WEDNESDAY:
                return R.drawable.theme_brown_3_day_5;
            case Calendar.THURSDAY:
                return R.drawable.theme_brown_3_day_6;
            default:
                return R.drawable.theme_brown_3_day_7;
        }
    }
}

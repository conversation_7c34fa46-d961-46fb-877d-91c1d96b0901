package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan

import android.location.Location
import com.arapeak.alrbrea.core_ktx.data.prayer.PrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.Azan
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.ExtremeLatitude
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.Madhhab
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.Method
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.Rounding
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.SimpleDate
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.mapper.dateMapper
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.mapper.timeMapper
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.mapper.toAzanMethod
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.utils.getGmtDiff
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.arapeak.alrbrea.core_ktx.model.prayer.Prayer
import com.arapeak.alrbrea.core_ktx.model.prayer.PrayerEnum
import com.batoulapps.adhan2.Madhab
import timber.log.Timber
import java.util.Calendar


class AzanPrayerTimeProvider : PrayerTimeProvider() {
    override fun getPrayerTime(location: Location, calculationMethod: CalculationMethod, madhab: Madhab, date: Calendar): DayPrayers {
        val day = dateMapper(date)

//        runTest(location, day)

        val l = com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location(
            location.latitude,
            location.longitude,
            getGmtDiff(),
            0
        )
        val azan = Azan(l, calculationMethod.toAzanMethod())
        val prayerTimes = azan.getPrayerTimes(day)
        val imsaak = azan.getImsaak(day)

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, timeMapper(prayerTimes.fajr())),
            dhuhr = Prayer(PrayerEnum.dhuhr, timeMapper(prayerTimes.thuhr())),
            asr = Prayer(PrayerEnum.asr, timeMapper(prayerTimes.assr())),
            maghrib = Prayer(PrayerEnum.maghrib, timeMapper(prayerTimes.maghrib())),
            isha = Prayer(PrayerEnum.isha, timeMapper(prayerTimes.ishaa())),
            sunrise = Prayer(PrayerEnum.sunrise, timeMapper(prayerTimes.shuruq())),
            eid = null
        )

        return dayPrayers
    }

    fun getPrayerTimeJordan(location: Location, date: Calendar): DayPrayers {
        val day = dateMapper(date)

//        runTest(location, day)

        val l = com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location(
            location.latitude,
            location.longitude,
            getGmtDiff(),
            0
        )
        val azan = Azan(l, Method.KARACHI_SHAF.apply {
            fajrAng = 18.0
            ishaaAng = 18.0
            this.madhhab = Madhhab.SHAAFI
            round = Rounding.NORMAL
            offset = true
            thuhrOffset = 1.0
            maghribOffset = 7.0
            shurooqOffset = - 6.0
            extremeLatitude = ExtremeLatitude.LAT_INVALID
        })
        val prayerTimes = azan.getPrayerTimes(day)
        val imsaak = azan.getImsaak(day)

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, timeMapper(prayerTimes.fajr())),
            dhuhr = Prayer(PrayerEnum.dhuhr, timeMapper(prayerTimes.thuhr())),
            asr = Prayer(PrayerEnum.asr, timeMapper(prayerTimes.assr())),
            maghrib = Prayer(PrayerEnum.maghrib, timeMapper(prayerTimes.maghrib())),
            isha = Prayer(PrayerEnum.isha, timeMapper(prayerTimes.ishaa())),
            sunrise = Prayer(PrayerEnum.sunrise, timeMapper(prayerTimes.shuruq())),
            eid = null
        )


        return dayPrayers
    }

    private fun runTest(location: Location, day: SimpleDate) {
        val methods = listOf(
            Method.EGYPT_SURVEY,
            Method.FIXED_ISHAA,
            Method.NONE,
            Method.NORTH_AMERICA,
            Method.KARACHI_HANAF,
            Method.KARACHI_SHAF,
            Method.UMM_ALQURRA,
            Method.MUSLIM_LEAGUE,
        )
        val madhabs = listOf(
//            Madhhab.HANAFI,
            Madhhab.SHAAFI
        )
        val roundings = listOf(
            Rounding.NORMAL,
//            Rounding.SPECIAL,
//            Rounding.NONE,
//            Rounding.AGRESSIVE,
        )
        val elts = listOf(
            ExtremeLatitude.NONE_EX,
//            ExtremeLatitude.LAT_ALL,
//            ExtremeLatitude.LAT_ALWAYS,
            ExtremeLatitude.LAT_INVALID,
//            ExtremeLatitude.GOOD_ALL,
//            ExtremeLatitude.GOOD_INVALID,
//            ExtremeLatitude.SEVEN_NIGHT_ALWAYS,
//            ExtremeLatitude.SEVEN_NIGHT_INVALID,
//            ExtremeLatitude.SEVEN_DAY_ALWAYS,
//            ExtremeLatitude.SEVEN_DAY_INVALID,
//            ExtremeLatitude.HALF_ALWAYS,
//            ExtremeLatitude.HALF_INVALID,
//            ExtremeLatitude.MIN_ALWAYS,
//            ExtremeLatitude.MIN_INVALID,
//            ExtremeLatitude.GOOD_DIF,
        )

        val locations = listOf(
//            com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location(
//                location.latitude,
//                location.longitude,
//                getGmtDiff(),
//                0
//            ),
//            com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location(
//                31.021353,
//                36.364147,
//                getGmtDiff(),
//                0
//            ),
//            com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location(
//                31.0,
//                36.4,
//                getGmtDiff(),
//                0
//            ),
            com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location(
                31.946060,
                35.933583,
                getGmtDiff(),
                0
            )
        )

        val fajrAngles = listOf(
            18.0,
        )
        val ishaAngles = listOf(
            18.0,
        )

        methods.forEach { me ->
            madhabs.forEach { ma ->
                roundings.forEach { ro ->
                    elts.forEach { elt ->
                        fajrAngles.forEach { fajrAngle ->
                            ishaAngles.forEach { ishaAngle ->
                                locations.forEach { locatio ->
                                    extracted(locatio, day, me, ma, ro, elt, fajrAngle, ishaAngle)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun extracted(
        l: com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.astrologicalCalc.Location,
        day: SimpleDate,
        method: Method,
        ma: Madhhab,
        ro: Rounding,
        elt: ExtremeLatitude,
        fajrAngle: Double,
        ishaAngle: Double
    ) {
        method.madhhab = ma
        method.round = ro
        method.extremeLatitude = elt
        method.ishaaAng = ishaAngle
        method.fajrAng = fajrAngle

        val prayerTimes = Azan(l, method).getPrayerTimes(day)

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, timeMapper(prayerTimes.fajr())),
            dhuhr = Prayer(PrayerEnum.dhuhr, timeMapper(prayerTimes.thuhr())),
            asr = Prayer(PrayerEnum.asr, timeMapper(prayerTimes.assr())),
            maghrib = Prayer(PrayerEnum.maghrib, timeMapper(prayerTimes.maghrib())),
            isha = Prayer(PrayerEnum.isha, timeMapper(prayerTimes.ishaa())),
            sunrise = Prayer(PrayerEnum.sunrise, timeMapper(prayerTimes.shuruq())),
            eid = null
        )

        Timber.tag("TestPray")
            .i(" Azan  method : ${method.name} ||  madhhab  : ${ma.name} ||  round : ${ro.name} ||  extremeLatitude : ${elt.name} ||  fajrAngle : $fajrAngle ||  ishaAngle : $ishaAngle || location : ${l.toString()}")
        Timber.tag("TestPray").i(" Azan ${dayPrayers.toString()}")

//        Log.e("TestPray", " Azan  method : ${method.name} ||  madhhab  : ${ma.name} ||  round : ${ro.name} ||  extremeLatitude : ${elt.name} ||  fajrAngle : $fajrAngle ||  ishaAngle : $ishaAngle")
//        Log.e("TestPray", " Azan ${dayPrayers.toString()}")
//        Log.e("TestPray", " Azan \n")
    }
}
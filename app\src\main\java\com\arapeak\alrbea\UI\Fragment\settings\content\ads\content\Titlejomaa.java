package com.arapeak.alrbea.UI.Fragment.settings.content.ads.content;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;
import com.wdullaer.materialdatetimepicker.date.DatePickerDialog;

/**
 * A simple {@link Fragment} subclass.
 */
public class Titlejomaa extends AlrabeeaTimesFragment implements DatePickerDialog.OnDateSetListener, View.OnClickListener {

    private static final String TAG = "CreateMovingMessageFragment";
    public static int startYearDate, startMonthDate, startDayDate, endYearDate, endMonthDate, endDayDate;
    public static int year, month, day;
    private ScrollView confirmMessageScrollView;
    private TextView titleTextView, titleTextView1;
    private TextView cancelTextView;
    private SwitchCompat activeSwitchCompat;
    private TextView activeTextView;
    private EditText messageEditText;
    private LinearLayout dateLinearLayout;
    private TextView startDateTextView;
    private TextView endDateTextView;
    private Button saveButton;
    private Spinner /*prayNameSpinner,*/ timesNumberOfAppearSpinner, moveBetweenImageSpinner;
    private String titleDatePickerDialog;

    public Titlejomaa() {
        // Required empty public constructor
    }


    public static Titlejomaa newInstance() {
        return new Titlejomaa();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_create_moving_message, container, false);

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initView(view);
        setParameter();
        setAction();
    }

    private void initView(View view) {
        confirmMessageScrollView = view.findViewById(R.id.confirm_message_ScrollView_CreateMovingMessageFragment);
        // confirmMessageScrollView.setVisibility(View.GONE);
        moveBetweenImageSpinner = view.findViewById(R.id.moveBetweenImage_Spinner_AddPhotoFragment);
        moveBetweenImageSpinner.setVisibility(View.GONE);

        titleTextView1 = view.findViewById(R.id.moveBetweenImage_TextView_AddPhotoFragment);
        titleTextView1.setVisibility(View.GONE);

        titleTextView = view.findViewById(R.id.title_TextView_CreateMovingMessageFragment);
        titleTextView.setText(Utils.getString(R.string.write_khutba_title));
//       titleTextView.setText("اكتب عنوان الخطبة");

        cancelTextView = view.findViewById(R.id.cancel_TextView_CreateMovingMessageFragment);
        // cancelTextView.setVisibility(View.GONE);
        activeSwitchCompat = view.findViewById(R.id.active_SwitchCompat_CreateMovingMessageFragment);
        //  activeSwitchCompat.setVisibility(View.GONE);

        activeTextView = view.findViewById(R.id.active_TextView_CreateMovingMessageFragment);
        activeTextView.setText(Utils.getString(R.string.enable_khutba_title));
        // activeTextView.setVisibility(View.GONE);

        messageEditText = view.findViewById(R.id.message_EditText_CreateMovingMessageFragment);
        dateLinearLayout = view.findViewById(R.id.date_LinearLayout_CreateMovingMessageFragment);
        dateLinearLayout.setVisibility(View.GONE);

        startDateTextView = view.findViewById(R.id.startDate_TextView_CreateMovingMessageFragment);
        endDateTextView = view.findViewById(R.id.endDate_TextView_CreateMovingMessageFragment);
        saveButton = view.findViewById(R.id.save_Button_CreateMovingMessageFragment);
    }

    private void setParameter() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(Utils.getString(R.string.khutba_title));
//        } else {
//            SettingsActivity.setTextTite(Utils.getString(R.string.khutba_title));
//        }
        SettingsActivity.setTextTite(Utils.getString(R.string.khutba_title));
        activeSwitchCompat.setChecked(Hawk.get("titlejomaaenable", false));
        Utils.setColorStateListToSwitchCompat(getAppCompatActivity(), activeSwitchCompat);

        setData();
    }

    private void setAction() {
        startDateTextView.setOnClickListener(this);
        endDateTextView.setOnClickListener(this);
        saveButton.setOnClickListener(this);


        activeSwitchCompat.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Hawk.put("titlejomaaenable", isChecked);
                activeOrDisplay(isChecked);
            }
        });
    }

    @Override
    public void onClick(View v) {
        Hawk.put(ConstantsOfApp.TITLE_JOMAA, messageEditText.getText().toString());
        Utils.showSuccessAlert(getAppCompatActivity(), getString(R.string.news_added_successfully));
    }

    @Override
    public void onDateSet(DatePickerDialog view, int year, int monthOfYear, int dayOfMonth) {
        String monthOfYearString, dayOfMonthString;

    }

    private boolean isValid() {
        boolean isValid = true;
        if (messageEditText.getText().toString().isEmpty()) {
            messageEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }
        if (startDateTextView.getText().toString().isEmpty()) {
            startDateTextView.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }
        if (endDateTextView.getText().toString().isEmpty()) {
            endDateTextView.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        return isValid;
    }

    private void clearAllView() {
//        startDateTextView.setText(year + "-" + month + "-" + day);

        messageEditText.setText("");
    }


    private void activeOrDisplay(boolean isActive) {
        if (isActive) {
            titleTextView.setVisibility(View.VISIBLE);
            messageEditText.setVisibility(View.VISIBLE);
            //   dateLinearLayout.setVisibility(View.VISIBLE);
            saveButton.setVisibility(View.VISIBLE);
        } else {
            titleTextView.setVisibility(View.GONE);
            messageEditText.setVisibility(View.GONE);
            //  dateLinearLayout.setVisibility(View.GONE);
            saveButton.setVisibility(View.GONE);
        }
    }

    private void setData() {
        clearAllView();

        messageEditText.setText(Hawk.get(ConstantsOfApp.TITLE_JOMAA, ""));

    }
}

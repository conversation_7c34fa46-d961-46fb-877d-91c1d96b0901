package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.alerts.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.CUSTOM_AZAN;

import android.app.Dialog;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.AzanAudio;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.AudioViewHolder;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.downloader.Error;
import com.downloader.OnDownloadListener;
import com.downloader.PRDownloader;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;

import java.io.File;

public class SelectAzan extends Fragment {
    Dialog loadingDialog;
    int currentPlayingAudio = 0;
    private View container;
    private ViewGroup audio_container;
    private SwitchCompat soundEnableSw;
    private RecyclerView rv_audio;
    private SeekBar audioLevel;
    private RecyclerView.Adapter<AudioViewHolder> azanAdapter;
    private MediaPlayer mediaPlayer;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        this.container = inflater.inflate(R.layout.fragment_select_azan, container, false);
        init();
        return this.container;
    }

    private void init() {
        soundEnableSw = container.findViewById(R.id.enableDisablePhoto_SwitchCompat_AddPhotoFragment);
        rv_audio = container.findViewById(R.id.rv_audio);
        audioLevel = container.findViewById(R.id.citationForMorningTime_SeekBar_AthkarFragment);
        audio_container = container.findViewById(R.id.container);

        loadingDialog = Utils.initLoadingDialogWithString(requireContext(), Utils.getString(R.string.downloading_file));
        azanAdapter = new RecyclerView.Adapter<AudioViewHolder>() {
            @NonNull
            @Override
            public AudioViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                LayoutInflater inflater = LayoutInflater.from(requireContext());
                return new AudioViewHolder(inflater.inflate(R.layout.layout_list_item_audio_select, parent, false));
//                return new AzanAudioViewHolder(View.inflate(requireContext(), R.layout.layout_list_item_audio_select, parent));
            }

            @Override
            public void onBindViewHolder(@NonNull AudioViewHolder holder, int position) {
                holder.Bind(position
                        , mediaPlayer != null && mediaPlayer.isPlaying() && currentPlayingAudio == position
                        , HawkSettings.getCurrentAzan() == position
                        , AzanAudio.values()[position]
                        , onPlayAudio -> {
                            if (mediaPlayer != null && mediaPlayer.isPlaying() && currentPlayingAudio == position) {
                                stopAudio();
//                                azanAdapter.notifyItemChanged(position);
                            } else {
                                AzanAudio azan = AzanAudio.values()[holder.position];
                                String path = azan.getPath();
                                if (!azan.isOffline() && !new File(path).exists()) {
                                    if (azan == AzanAudio.CUSTOM) {
                                        selectAudioFile(() -> playAudio(holder.position));
                                    } else
                                        downloadAzan(holder.position, () -> playAudio(holder.position));
                                } else
                                    playAudio(holder.position);
                            }
                        }
                        , onAzanSelected -> {
                            AzanAudio azan = AzanAudio.values()[holder.position];
                            String path = azan.getPath();
                            if (!azan.isOffline() && !new File(path).exists()) {
                                if (azan == AzanAudio.CUSTOM) {
                                    selectAudioFile(() -> selectAzan(holder.position));
                                } else
                                    downloadAzan(holder.position, () -> selectAzan(holder.position));
                            } else {
                                selectAzan(holder.position);
                            }
                        }
                );
            }

            @Override
            public int getItemCount() {
                return AzanAudio.values().length;
            }
        };
        rv_audio.setAdapter(azanAdapter);

        Utils.setColorStateListToSwitchCompat(requireActivity(), soundEnableSw);
        soundEnableSw.setChecked(HawkSettings.getAudioAzanEnabled());
        soundEnableSw.setOnClickListener(i -> {
            HawkSettings.setAudioAzanEnabled(soundEnableSw.isChecked());
            enableDisableAudio();
        });

        enableDisableAudio();

        float level = HawkSettings.getAudioLevel() * 10;
        audioLevel.setProgress((int) level);
        audioLevel.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float prog = progress * 0.1f;
                if (fromUser) {
                    HawkSettings.setAudioLevel(prog);
                    if (mediaPlayer != null)
                        mediaPlayer.setVolume(prog, prog);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }

    private void enableDisableAudio() {
        boolean isEnabled = HawkSettings.getAudioAzanEnabled();
        audio_container.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
    }

    private void selectAudioFile(Runnable onComplete) {
        new ChooserDialog(requireContext())
                .withFilter(false, false, "mp3")
                .withChosenListener((path, pathFile) -> {
                    Hawk.put(CUSTOM_AZAN, path);
                    onComplete.run();
                })
                .build()
                .show();
    }

    private void selectAzan(int position) {
        int lastAzan = HawkSettings.getCurrentAzan();
        HawkSettings.setCurrentAzan(position);
        azanAdapter.notifyItemChanged(lastAzan);
        azanAdapter.notifyItemChanged(position);

    }

    private void downloadAzan(int position, Runnable onComplete) {
        loadingDialog.show();
        AzanAudio azan = AzanAudio.values()[position];
        String url = azan.getURL();

        if (!url.isEmpty()) {
            PRDownloader.download(url, Utils.getDownloadPath() + "/azan/", "" + position)
                    .build()
                    .start(new OnDownloadListener() {
                        @Override
                        public void onDownloadComplete() {
                            loadingDialog.dismiss();
                            onComplete.run();
                        }

                        @Override
                        public void onError(Error error) {
                            Utils.showFailAlert(requireActivity(), "خطأ", "فشل تحميل الملف ");
                            loadingDialog.dismiss();
                        }
                    });
        }
    }

    private void playAudio(int index) {
        AzanAudio azanAudio = AzanAudio.values()[index];
        stopAudio();
        try {
            if (azanAudio.isOffline() && azanAudio.getOfflineResource() != 0)
                mediaPlayer = MediaPlayer.create(requireContext(), azanAudio.getOfflineResource());
            else
                mediaPlayer = MediaPlayer.create(requireContext(), azanAudio.getUri());

            float vol = HawkSettings.getAudioLevel();
            mediaPlayer.setVolume(vol, vol);
            mediaPlayer.setOnCompletionListener(i -> {
                if (azanAdapter != null) {
                    azanAdapter.notifyItemChanged(currentPlayingAudio);
                }
            });
            currentPlayingAudio = index;
            mediaPlayer.start();
            azanAdapter.notifyItemChanged(currentPlayingAudio);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void stopAudio() {
        try {
            if (mediaPlayer != null && mediaPlayer.isPlaying())
                mediaPlayer.stop();
            azanAdapter.notifyItemChanged(currentPlayingAudio);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public void onStop() {
        stopAudio();
        try {
            if (mediaPlayer != null)
                mediaPlayer.release();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        super.onStop();
    }
}
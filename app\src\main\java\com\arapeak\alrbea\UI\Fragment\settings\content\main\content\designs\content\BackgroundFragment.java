package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content;

import static android.app.Activity.RESULT_OK;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.BACKGROUND_IMAGE_ALPHA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.BACKGROUND_IMAGE_KEY;
import static com.arapeak.alrbea.Utils.getOutputMediaImage;

import android.Manifest;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;
import com.skydoves.colorpickerview.ColorEnvelope;
import com.skydoves.colorpickerview.ColorPickerDialog;
import com.skydoves.colorpickerview.listeners.ColorEnvelopeListener;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import pub.devrel.easypermissions.EasyPermissions;

public class BackgroundFragment extends AlrabeeaTimesFragment {

    private static final String TAG = "BackgroundFragment";
    private static final int RESULT_IMG_UPLOAD = 101;
    private static final int PERMISSION_CODE = 103;
    int DefaultColor;
    private View backgroundView;
    private AppCompatCheckBox isEnableCheckBox;
    private Button selectBackgroundButton;
    private ImageView backgroundImageView;
    private TextView transparencyBackgroundTextView;
    private SeekBar backgroundSeekBar;
    private Button saveButton, colorButton;
    private Dialog loadingDialog;
    private float progress;
    private File imgFile = null;
    private Uri imageUri;

    public BackgroundFragment() {

    }

    public static BackgroundFragment newInstance() {
        return new BackgroundFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        backgroundView = inflater.inflate(R.layout.fragment_background, container, false);

        initView();
        setParameter();
        setAction();

        return backgroundView;
    }

    @Override
    public void onActivityResult(int reqCode, int resultCode, Intent data) {
        super.onActivityResult(reqCode, resultCode, data);

        if (data == null) {
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.please_select_photo));
            return;
        }
        if (reqCode == RESULT_IMG_UPLOAD) {
            if (resultCode == RESULT_OK) {
                try {
                    imageUri = data.getData();
                    final InputStream imageStream = getActivity().getContentResolver()
                            .openInputStream(imageUri);
                    final Bitmap selectedImage = BitmapFactory.decodeStream(imageStream);
                    afterSelectingImage(selectedImage);
                } catch (FileNotFoundException e) {
                    Log.e(TAG, "onActivityResult Error: " + e.getMessage());
                    CrashlyticsUtils.INSTANCE.logException(e);
                    Utils.showFailAlert(getAppCompatActivity()
                            , getString(R.string.there_is_a_problem)
                            , e.getMessage());
                } catch (Exception e) {
                    Log.e(TAG, "onActivityResult Error: " + e.getMessage());
                    CrashlyticsUtils.INSTANCE.logException(e);
                    Utils.showFailAlert(getAppCompatActivity()
                            , getString(R.string.there_is_a_problem)
                            , e.getMessage());
                }
            }
        }
    }

    private void initView() {
        isEnableCheckBox = backgroundView.findViewById(R.id.isEnable_CheckBox_BackgroundFragment);
        selectBackgroundButton = backgroundView.findViewById(R.id.selectBackground_Button_BackgroundFragment);
        backgroundImageView = backgroundView.findViewById(R.id.background_ImageView_BackgroundFragment);
        transparencyBackgroundTextView = backgroundView.findViewById(R.id.transparencyBackground_TextView_BackgroundFragment);
        backgroundSeekBar = backgroundView.findViewById(R.id.transparencyBackground_SeekBar_BackgroundFragment);
        saveButton = backgroundView.findViewById(R.id.save_Button_BackgroundFragment);
        colorButton = backgroundView.findViewById(R.id.color_Button_BackgroundFragment);
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());
    }

    private void setParameter() {
//        if (Utils.isLandscape()){
//            SettingsLandscapeActivity.setTextTite(getString(R.string.background));
//        }else {
//            SettingsActivity.setTextTite(getString(R.string.background));
//        }
        SettingsActivity.setTextTite(getString(R.string.background));
//        backgroundImageView.setBackgroundResource(R.drawable.img_athkar_morning);

        isEnableCheckBox.setChecked(Hawk.get(BACKGROUND_IMAGE_KEY + "C", false));
        backgroundImageView.setVisibility(View.GONE);
        selectBackgroundButton.setVisibility(View.GONE);
        transparencyBackgroundTextView.setVisibility(View.GONE);
        backgroundSeekBar.setVisibility(View.GONE);
        colorButton.setVisibility(View.GONE);
        colorButton.setVisibility(View.GONE);


        loadingDialog.show();

        new Thread(new Runnable() {
            @Override
            public void run() {
                final String imageBase64 = Hawk.get(BACKGROUND_IMAGE_KEY, "");
                progress = Hawk.get(BACKGROUND_IMAGE_ALPHA_KEY, 0.3F);
                final Bitmap imageBitmap = Utils.convertImageBase64ToBitmapImage(imageBase64);
                getAppCompatActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (imageBitmap != null) {
                            isEnableCheckBox.setChecked(true);
                            selectBackgroundButton.setVisibility(View.VISIBLE);
                            backgroundImageView.setImageBitmap(imageBitmap);
                            showImageLayout((int) (progress * 100));
                        }
                        loadingDialog.dismiss();
                    }
                });
            }
        }).start();
    }

    private void setAction() {
        isEnableCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Hawk.put(BACKGROUND_IMAGE_KEY + "C", isChecked);
                if (isChecked) {
                    selectBackgroundButton.setVisibility(View.VISIBLE);

                } else {
                    selectBackgroundButton.setVisibility(View.GONE);
                    hideImageLayout();
                    Hawk.delete(BACKGROUND_IMAGE_KEY);
                    Hawk.delete(BACKGROUND_IMAGE_ALPHA_KEY);
                }
            }
        });

        colorButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new ColorPickerDialog.Builder(requireContext())
                        .setTitle(getString(R.string.pick_color))
                        .setPreferenceName("MyColorPickerDialogBackgroundFragment")
                        .setPositiveButton(getString(R.string.ok),
                                new ColorEnvelopeListener() {
                                    @Override
                                    public void onColorSelected(ColorEnvelope envelope, boolean fromUser) {
                                        Hawk.put("colorimage", envelope.getColor());

                                    }
                                })
                        .setNegativeButton(getString(R.string.cancel),
                                new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialogInterface, int i) {
                                        dialogInterface.dismiss();
                                    }
                                })
                        .attachAlphaSlideBar(true) // the default value is true.
                        .attachBrightnessSlideBar(true)  // the default value is true.
                        .setBottomSpace(12) // set a bottom space between the last slidebar and buttons.
                        .show();

            }
        });
        backgroundSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                saveImageSettings(progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        selectBackgroundButton.setOnClickListener(v -> goToGallery());

        saveButton.setOnClickListener(v -> saveImage());
    }

    private void goToGallery() {
        try {
            if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                new ChooserDialog(requireContext())
                        .withFilter(false, false, "png")
                        .withChosenListener((path, pathFile) -> {
                            try {
                                imageUri = Uri.fromFile(pathFile);
                                final InputStream imageStream;
                                imageStream = getActivity().getContentResolver()
                                        .openInputStream(imageUri);
                                final Bitmap selectedImage = BitmapFactory.decodeStream(imageStream);
                                afterSelectingImage(selectedImage);
                            } catch (FileNotFoundException e) {
                                CrashlyticsUtils.INSTANCE.logException(e);
                            }
                        })
                        .build()
                        .show();
                return;
            }

            String[] perms = {Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
            if (EasyPermissions.hasPermissions(getAppCompatActivity(), perms)) {
                getPhotoFromGallery();
            } else {
                EasyPermissions
                        .requestPermissions(getAppCompatActivity(), getString(R.string.get_pic_msg), PERMISSION_CODE, perms);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void getPhotoFromGallery() {

        Intent intent = new Intent();
        intent.setType("image/*");
        intent.setAction(Intent.ACTION_GET_CONTENT);
        startActivityForResult(Intent.createChooser(intent, "Select Picture"), RESULT_IMG_UPLOAD);
        // Intent photoPickerIntent = new Intent(Intent.ACTION_PICK);
        //photoPickerIntent.setType("image/*");
        //startActivityForResult(photoPickerIntent, RESULT_IMG_UPLOAD);
    }

    void afterSelectingImage(Bitmap image) {
        if (image == null) {
            return;
        }
        backgroundImageView.setImageBitmap(image);
        showImageLayout(100);

        getImage(image);
    }

    private void getImage(Bitmap image) {
        if (image == null) {
            return;
        }
        try {
//            imgFile = new File(getAppCompatActivity().getCacheDir(), "image_" + Utils.getDateTimeNowWithFileName() + ".jpg");
            imgFile = getOutputMediaImage(getAppCompatActivity(), "image_" + Utils.getDateTimeNowWithFileName() + ".png");

            if (imgFile == null) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                return;
            }
            if (!imgFile.exists()) {
                imgFile.createNewFile();
            }

            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            image.compress(Bitmap.CompressFormat.PNG, 100 /*ignored for PNG*/, bos);
            byte[] bitmapdata = bos.toByteArray();
            Hawk.put("urlimagebak", imgFile + "");
            FileOutputStream fos = new FileOutputStream(imgFile);
            fos.write(bitmapdata);
            fos.flush();
            fos.close();
            Log.i(TAG, "Done");
        } catch (IOException e) {
            imgFile = null;
            Log.i(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        } catch (Exception e) {
            imgFile = null;
            Log.i(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void showImageLayout(int progress) {
        backgroundImageView.setVisibility(View.VISIBLE);
        transparencyBackgroundTextView.setVisibility(View.VISIBLE);
        backgroundSeekBar.setVisibility(View.VISIBLE);
        saveButton.setVisibility(View.VISIBLE);
        colorButton.setVisibility(View.VISIBLE);
        backgroundSeekBar.setProgress(progress);
        saveImageSettings(progress);
    }

    private void hideImageLayout() {
        backgroundImageView.setVisibility(View.GONE);
        transparencyBackgroundTextView.setVisibility(View.GONE);
        backgroundSeekBar.setVisibility(View.GONE);
        saveButton.setVisibility(View.GONE);
        colorButton.setVisibility(View.GONE);
    }

    private void saveImageSettings(int progress) {
        BackgroundFragment.this.progress = progress / 100.0F;
        backgroundSeekBar.setProgress((int) (BackgroundFragment.this.progress * 100));
        transparencyBackgroundTextView.setText(getString(R.string.transparency_percentage) + " " + BackgroundFragment.this.progress);
        backgroundImageView.setAlpha(BackgroundFragment.this.progress);
    }

    private void saveImage() {
        if (loadingDialog.isShowing()) {
            return;
        }
        loadingDialog.show();
        if (backgroundImageView.getDrawable() == null) {
            loadingDialog.dismiss();
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.try_again));
            return;
        }
        new Thread(() -> {
            Hawk.put(BACKGROUND_IMAGE_KEY, Utils.convertImageBitmapToImageBase64(((BitmapDrawable) backgroundImageView.getDrawable()).getBitmap()));
            Hawk.put(BACKGROUND_IMAGE_ALPHA_KEY, progress);

            getAppCompatActivity().runOnUiThread(() -> {
                Utils.showSuccessAlert(getAppCompatActivity()
                        , getString(R.string.photo_saved_successfully));
                loadingDialog.dismiss();
            });
        }).start();
    }

}

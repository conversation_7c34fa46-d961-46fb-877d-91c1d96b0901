package com.arapeak.alrbea.Service;


import android.content.Context;
import android.location.LocationManager;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.contract.ActivityResultContracts;

import com.arapeak.alrbea.UI.Activity.BaseAppCompatActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.ResolvableApiException;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.LocationSettingsRequest;
import com.google.android.gms.location.LocationSettingsStatusCodes;
import com.google.android.gms.location.SettingsClient;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class GpsUtils {

    public static AtomicInteger resultData = new AtomicInteger(-10);
    public static AtomicBoolean resultReceived = new AtomicBoolean(false);
    //    private Context context;
    private final BaseAppCompatActivity activity;
    private final SettingsClient mSettingsClient;
    private final LocationSettingsRequest mLocationSettingsRequest;
    private final LocationManager locationManager;
    private final LocationRequest locationRequest;
    private ActivityResultLauncher<IntentSenderRequest> resolutionForResult;

    public GpsUtils(BaseAppCompatActivity activity) {
        this.activity = activity;
        locationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
        mSettingsClient = LocationServices.getSettingsClient(activity);

        locationRequest = LocationRequest.create();
        locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        locationRequest.setInterval(10 * 1000);
        LocationSettingsRequest.Builder builder = new LocationSettingsRequest.Builder()
                .addLocationRequest(locationRequest);
//        Task<LocationSettingsResponse> result =
//                LocationServices.getSettingsClient(context).checkLocationSettings(builder.build());
        mLocationSettingsRequest = builder.build();
        //**************************
        builder.setAlwaysShow(true); //this is the key ingredient
        //**************************
    }

    // method for turn on GPS
    public void turnGPSOn(onGpsListener onGpsListener) {
        resolutionForResult = activity.registerForActivityResult(new ActivityResultContracts.StartIntentSenderForResult(), result -> {
            resultData.set(result.getResultCode());
            resultReceived.set(true);
//            if(result.getResultCode() == RESULT_OK){
//                //Granted
//            }else {
//                //Not Granted
//            }
        });
        if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
            if (onGpsListener != null) {
                onGpsListener.gpsStatus(true);
            }
        } else {
            mSettingsClient
                    .checkLocationSettings(mLocationSettingsRequest)
                    .addOnSuccessListener(activity, locationSettingsResponse -> {

                        //  GPS is already enable, callback GPS status through listener
                        if (onGpsListener != null) {
                            onGpsListener.gpsStatus(true);
                        }
                    })
                    .addOnFailureListener(activity, e -> {
                        int statusCode = ((ApiException) e).getStatusCode();
                        switch (statusCode) {
                            case LocationSettingsStatusCodes.RESOLUTION_REQUIRED:
                                try {
                                    IntentSenderRequest intentSenderRequest = new IntentSenderRequest.Builder(((ResolvableApiException) e).getResolution()).build();
                                    resolutionForResult.launch(intentSenderRequest);

//                                    ResolvableApiException rae = (ResolvableApiException) e;
//                                    rae.startResolutionForResult(activity, ConstantsOfApp.GPS_REQUEST);
                                }
//                                catch (IntentSender.SendIntentException sie) {
//                                    Log.i(TAG, "PendingIntent unable to execute request.");
//                                }
                                catch (Exception ex) {
                                    CrashlyticsUtils.INSTANCE.logException(ex);
                                }
                                break;
                        }
                    });
        }
    }


    public interface onGpsListener {
        void gpsStatus(boolean isGPSEnable);
    }
}
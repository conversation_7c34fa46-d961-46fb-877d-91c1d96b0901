<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:layoutDirection="rtl"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/contentFajr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side.Firebase"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/fajr_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR"
                        android:text="@string/fajr" />
                </LinearLayout>
            </LinearLayout>
            <include
                android:id="@+id/tv_prayer_ikama_time_fajr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:visibility="gone"
                tools:visibility="visible" />
            <LinearLayout style="@style/PrayerTimeLayout.Side.Custom">

                <LinearLayout
                    android:id="@+id/fajrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/fajrTime_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.Time" />

                    <TextView
                        android:id="@+id/fajrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeType" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side.Firebase"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR"
                        android:text="@string/dhuhr" />

                </LinearLayout>
            </LinearLayout>
            <include
                android:id="@+id/tv_prayer_ikama_time_dhur"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible" />
            <LinearLayout style="@style/PrayerTimeLayout.Side.Custom">

                <LinearLayout
                    android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/dhuhrTime_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.Time" />

                    <TextView
                        android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeType" />

                </LinearLayout>


            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentAsr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side.Firebase"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/asr_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR"
                        android:text="@string/asr" />

                </LinearLayout>
            </LinearLayout>
            <include
                android:id="@+id/tv_prayer_ikama_time_asr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
            <LinearLayout style="@style/PrayerTimeLayout.Side.Custom">

                <LinearLayout
                    android:id="@+id/asrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/asrTime_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.Time" />

                    <TextView
                        android:id="@+id/asrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeType" />

                </LinearLayout>


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side.Firebase"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/maghrib_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR"
                        android:text="@string/maghrib" />

                </LinearLayout>
            </LinearLayout>
            <include
                android:id="@+id/tv_prayer_ikama_time_maghrib"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
            <LinearLayout style="@style/PrayerTimeLayout.Side.Custom">

                <LinearLayout
                    android:id="@+id/maghribTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/maghribTime_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.Time" />

                    <TextView
                        android:id="@+id/maghribTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeType" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentIsha_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side.Firebase"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/isha_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR"
                        android:text="@string/isha" />
                </LinearLayout>
            </LinearLayout>
            <include
                android:id="@+id/tv_prayer_ikama_time_isha"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
            <LinearLayout style="@style/PrayerTimeLayout.Side.Custom">

                <LinearLayout
                    android:id="@+id/ishaTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/ishaTime_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.Time" />

                    <TextView
                        android:id="@+id/ishaTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeType" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentSunrise_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:layout_marginStart="@dimen/_15sdp"
        android:layout_marginEnd="@dimen/_15sdp"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side.Firebase"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR"
                        android:text="@string/duha"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_22sdp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.Side.Custom">

                <LinearLayout
                    android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/sunriseATime_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.Time"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_26sdp" />

                    <TextView
                        android:id="@+id/sunriseATimeType_TextView_MainActivity"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        style="@style/TimeTextView.custom_1_land.TimeType" />

                </LinearLayout>


            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;

public class SettingAlrabeeaTimes {

    private String title, description;
    private int iconResourcesId;
    private boolean isNew = false;

    public SettingAlrabeeaTimes(String title, String description, int iconResourcesId) {
        this.title = title;
        this.description = description;
        this.iconResourcesId = iconResourcesId;
    }

    public String getTitle() {
        return Utils.getValueWithoutNull(title);
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return Utils.getValueWithoutNull(description);
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getIconResourcesId() {
        return iconResourcesId;
    }

    public void setIconResourcesId(int iconResourcesId) {
        this.iconResourcesId = iconResourcesId;
    }

    public boolean getIsNew() {
        return isNew;
    }

    public void setIsNew(boolean isNew) {
        this.isNew = isNew;
    }
}

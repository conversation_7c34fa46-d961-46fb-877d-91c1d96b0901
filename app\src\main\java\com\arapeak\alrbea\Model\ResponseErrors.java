package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class ResponseErrors {

    @Expose
    @SerializedName("errors")
    private Errors errors;
    @Expose
    @SerializedName(value = "data", alternate = {"message"})
    private String message;

    public Errors getErrors() {
        return errors;
    }

    public void setErrors(Errors errors) {
        this.errors = errors;
    }

    public String getMessage() {
        return Utils.getValueWithoutNull(message);
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isErrorsNotNull() {
        return getErrors().getName().size() != 0
                || getErrors().getEmail().size() != 0
                || getErrors().getPhone().size() != 0
                || getErrors().getSubject().size() != 0
                || getErrors().getText().size() != 0;
    }

    public static class Errors {
        @Expose
        @SerializedName("name")
        private List<String> name;
        @Expose
        @SerializedName("email")
        private List<String> email;
        @Expose
        @SerializedName("phone")
        private List<String> phone;
        @Expose
        @SerializedName("subject")
        private List<String> subject;
        @Expose
        @SerializedName("text")
        private List<String> text;

        public List<String> getName() {
            return name == null ? new ArrayList<String>() : name;
        }

        public void setName(List<String> name) {
            this.name = name;
        }

        public List<String> getEmail() {
            return email == null ? new ArrayList<String>() : email;
        }

        public void setEmail(List<String> email) {
            this.email = email;
        }

        public List<String> getPhone() {
            return phone == null ? new ArrayList<String>() : phone;
        }

        public void setPhone(List<String> phone) {
            this.phone = phone;
        }

        public List<String> getSubject() {
            return subject == null ? new ArrayList<String>() : subject;
        }

        public void setSubject(List<String> subject) {
            this.subject = subject;
        }

        public List<String> getText() {
            return text == null ? new ArrayList<String>() : text;
        }

        public void setText(List<String> text) {
            this.text = text;
        }
    }
}

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import android.util.Log;

import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Date;

import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public class PrayerTime {
    private static double A;   // Shrouk_ikama
    private static double B;   // Zuhr_ikama
    private static double C;   // Asr_ikama
    private static double D;   // Maghrib_ikama
    private static double E;    // Isha_ikama
    private static double F;   // Jomo3a_ikama
    private static double s;    //  Fajr
    private static double t;  //  Shrouk(
    private static double u;   // Zuhr

    /* renamed from: v */
    private static double f3765v;  //  ASR

    /* renamed from: w */
    private static double f3766w;    // Maghrib
    private static double x;   // Isha

    /* renamed from: y */
    private static double f3767y;    //  Jomo3a
    private static double z;     //  Fajr_ikama
    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull

    /* renamed from: q */
    private static double f3763q = 0.017453292d;   // DegToRad
    /* renamed from: r */
    private static double f3764r = 57.29577951d;  // RadToDeg
    /* renamed from: a, reason: from kotlin metadata */
    @NotNull
    private Coordinate m_coordinate;
    /* renamed from: b, reason: from kotlin metadata */
    @NotNull
    private MiladiDate m_date;
    /* renamed from: c, reason: from kotlin metadata */
    @NotNull
    private CalcMethod m_calcMethod;
    /* renamed from: d, reason: from kotlin metadata */
    @NotNull
    private Mazhab m_mazhab;
    /* renamed from: e, reason: from kotlin metadata */
    @Nullable
    private Season m_season;
    /* renamed from: f, reason: from kotlin metadata */
    private int m_highLatitude;
    /* renamed from: g, reason: from kotlin metadata */
    private int m_seasonSummer;
    /* renamed from: h, reason: from kotlin metadata */
    private int m_seasonWinter;
    /* renamed from: i, reason: from kotlin metadata */
    private int m_seasonSummerDay;
    /* renamed from: j, reason: from kotlin metadata */
    private int m_seasonWinterDay;
    /* renamed from: k, reason: from kotlin metadata */
    @NotNull
    private Hour m_hour;
    /* renamed from: l, reason: from kotlin metadata */
    @NotNull
    private String m_fajrAngle;
    /* renamed from: m, reason: from kotlin metadata */
    @NotNull
    private String m_ishaAngle;
    @Nullable
    private String n;
    @Nullable
    private String o;
    @Nullable
    private String p;


    public PrayerTime() {
        this.m_fajrAngle = "18";
        this.m_ishaAngle = "18";
        this.m_coordinate = new Coordinate(Preference.DEFAULT_SEA_LEVEL, Preference.DEFAULT_SEA_LEVEL, 0);
        this.m_date = new MiladiDate(0, 0, 0);
        this.m_calcMethod = new CalcMethod();
        this.m_mazhab = new Mazhab();
        this.m_season = new Season();
        this.m_hour = new Hour();
    }

    public PrayerTime(double longt, double lat, float zone, int i, int i2, int i3) {
        this.m_fajrAngle = "18";
        this.m_ishaAngle = "18";
        this.m_coordinate = new Coordinate(longt, lat, zone);
        this.m_date = new MiladiDate(i, i2, i3);
        this.m_calcMethod = new CalcMethod();
        this.m_mazhab = new Mazhab();
        this.m_season = new Season();
        this.m_hour = new Hour();
    }

    public static /* synthetic */ void addAsr$default(PrayerTime prayerTime, double d, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z2 = true;
            }
            prayerTime.addAsr(d, z2);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addAsr");
    }

    public static /* synthetic */ void addFajr$default(PrayerTime prayerTime, double d, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z2 = true;
            }
            prayerTime.addFajr(d, z2);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addFajr");
    }

    public static /* synthetic */ void addIsha$default(PrayerTime prayerTime, double d, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z2 = true;
            }
            prayerTime.addIsha(d, z2);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addIsha");
    }

    public static /* synthetic */ void addMaghrib$default(PrayerTime prayerTime, double d, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z2 = true;
            }
            prayerTime.addMaghrib(d, z2);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addMaghrib");
    }

    public static /* synthetic */ void addShrouk$default(PrayerTime prayerTime, double d, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z2 = true;
            }
            prayerTime.addShrouk(d, z2);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addShrouk");
    }

    public static /* synthetic */ void addZuhr$default(PrayerTime prayerTime, double d, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z2 = true;
            }
            prayerTime.addZuhr(d, z2);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: addZuhr");
    }

    public static /* synthetic */ Time asrTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.asrTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: asrTime");
    }

    public static /* synthetic */ void calculate$default(PrayerTime prayerTime, boolean z2, boolean z3, int i, Object obj) {
        if (obj == null) {
            if ((i & 2) != 0) {
                z3 = true;
            }
            prayerTime.calculate(z2, z3);
            return;
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: calculate");
    }

    public static /* synthetic */ Time fajrTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.fajrTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: fajrTime");
    }

    public static /* synthetic */ Time ishaTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.ishaTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: ishaTime");
    }

    public static /* synthetic */ Time jomo3aTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.jomo3aTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: jomo3aTime");
    }

    public static /* synthetic */ Time maghribTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.maghribTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: maghribTime");
    }

    public static /* synthetic */ Time shroukTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.shroukTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: shroukTime");
    }

    public static /* synthetic */ Time zuhrTime$default(PrayerTime prayerTime, boolean z2, int i, Object obj) {
        if (obj == null) {
            if ((i & 1) != 0) {
                z2 = true;
            }
            return prayerTime.zuhrTime(z2);
        }
        throw new UnsupportedOperationException("Super calls with default arguments not supported in this target, function: zuhrTime");
    }

    protected final double abs(double myvar) {
        if (myvar < Preference.DEFAULT_SEA_LEVEL) {
            return -myvar;
        }
        return myvar;
    }

    public final void addAsr(double value, boolean isAzan) {
        f3765v += value;
    }

    public final void addFajr(double value, boolean isAzan) {
        if (isAzan) {
            s += value;
        } else {
            z += value;
        }
    }

    public final void addIsha(double value, boolean isAzan) {
        x += value;
    }

    public final void addMaghrib(double value, boolean isAzan) {
        f3766w += value;
    }

    public final void addShrouk(double value, boolean isAzan) {
        t += value;
    }

    public final void addSummer() {
        boolean z2;
        Season season = this.m_season;
        Intrinsics.checkNotNull(season);
        if (season.type() == Season.Type.Summer) {
            s += 60.0d;
            t += 60.0d;
            u += 60.0d;
            f3765v += 60.0d;
            f3766w += 60.0d;
            x += 60.0d;
            f3767y += 60.0d;
            return;
        }
        Season season2 = this.m_season;
        Intrinsics.checkNotNull(season2);
        if (season2.type() == Season.Type.Winter_Summer) {
            boolean z3 = false;
            if (this.m_date.getMonth() >= this.m_seasonWinter && this.m_date.getMonth() < this.m_seasonSummer) {
                z2 = false;
            } else {
                z2 = true;
            }
            if (this.m_seasonSummer < this.m_seasonWinter) {
                if (this.m_date.getMonth() >= this.m_seasonSummer && this.m_date.getMonth() < this.m_seasonWinter) {
                    z2 = true;
                } else {
                    z2 = false;
                }
            }
            if (this.m_date.getMonth() == this.m_seasonSummer) {
                if (this.m_date.getDay() >= this.m_seasonSummerDay) {
                    z2 = true;
                } else {
                    z2 = false;
                }
            }
            if (this.m_date.getMonth() == this.m_seasonWinter) {
                if (this.m_date.getDay() < this.m_seasonWinterDay) {
                    z3 = true;
                }
                z2 = z3;
            }
            if (z2) {
                s += 60.0d;
                t += 60.0d;
                u += 60.0d;
                f3765v += 60.0d;
                f3766w += 60.0d;
                x += 60.0d;
                f3767y += 60.0d;
            }
        }
    }

    public final void addZuhr(double value, boolean isAzan) {
        u += value;
        f3767y += value;
    }

    @NotNull
    public Time asrTime(boolean isAzan) {
        if (isAzan) {
            return new Time(f3765v, true, this.m_hour);
        }
        return new Time(C, true, this.m_hour);
    }

    @NotNull
    public Time asrTimeIkama(double ikama) {
        return new Time(f3765v + ikama, true, this.m_hour);
    }

    public void calculate(boolean is_jomo3a_today, boolean isAzan) {
        int i;
        try {
            if (this.m_mazhab.type() == Mazhab.Type.Hanafi) {
                i = 1;
            } else {
                i = 0;
            }
            Log.e("zonezone", "zone " + this.n);
            Prayers calc_prayer = new PrayerTimeExpCalc(
                    this.m_date.getYear(),
                    this.m_date.getMonth(),
                    this.m_date.getDay(),
                    MyTool.strToFloat(this.o),
                    MyTool.strToFloat(this.p),
                    -999.0f,
                    this.m_calcMethod.toInt_C_SHARB(),
                    i,
                    this.m_highLatitude,
                    MyTool.strToFloat(this.m_fajrAngle),
                    MyTool.strToFloat(this.m_ishaAngle)).calc_prayer();
            s = calc_prayer.getFajr();
            t = calc_prayer.getShrouk();
            u = calc_prayer.getZuhr();
            f3765v = calc_prayer.getAsr();
            f3766w = calc_prayer.getMaghrib();
            x = calc_prayer.getIsha();
            f3767y = u;
            if (!is_jomo3a_today) {
                MiladiDate.Companion companion = MiladiDate.INSTANCE;
                Date NextDay = MyTool.NextDay(this.m_date.ToCalendar(), 6);
                Intrinsics.checkNotNullExpressionValue(NextDay, "NextDay(m_date.ToCalendar(), Calendar.FRIDAY)");
                MiladiDate instanse = companion.getInstanse(NextDay);
                f3767y = new PrayerTimeExpCalc(instanse.getYear(), instanse.getMonth(), instanse.getDay(), MyTool.strToFloat(this.o), MyTool.strToFloat(this.p), MyTool.strToFloat(this.n), this.m_calcMethod.toInt_C_SHARB(), i, this.m_highLatitude, MyTool.strToFloat(this.m_fajrAngle), MyTool.strToFloat(this.m_ishaAngle)).calc_prayer().getZuhr();
            }
            StringBuilder sb = new StringBuilder("");
            Season season = this.m_season;
            Intrinsics.checkNotNull(season);
            sb.append(season.type());
            sb.append(' ');
            Season season2 = this.m_season;
            Intrinsics.checkNotNull(season2);
            sb.append(season2.seasonString());
            MyTool.MyLog("m_season--", sb.toString());
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }


    public final int day() {
        return this.m_date.getDay();
    }

    protected final double equation(double dec, double alt) {
        return Math.acos((Math.sin(alt * f3763q) - (Math.sin(this.m_coordinate.getLatitude() * f3763q) * Math.sin(f3763q * dec))) / (Math.cos(this.m_coordinate.getLatitude() * f3763q) * Math.cos(dec * f3763q))) * f3764r;
    }

    @NotNull
    public Time fajrTime(boolean isAzan) {
        if (isAzan) {
            return new Time(s, true, this.m_hour);
        }
        return new Time(z, true, this.m_hour);
    }

    @NotNull
    public Time fajrTimeIkama(double ikama) {
        return new Time(s + ikama, true, this.m_hour);
    }

    public final double getAsr() {
        return f3765v;
    }

    public final double getFajr() {
        return s;
    }

    public final double getIsha() {
        return x;
    }

    public final double getJomo3a() {
        return f3767y;
    }

    @NotNull
    protected final CalcMethod getM_calcMethod() {
        return this.m_calcMethod;
    }

    public final void setM_calcMethod(@NotNull CalcMethod calcMethod) {
        Intrinsics.checkNotNullParameter(calcMethod, "<set-?>");
        this.m_calcMethod = calcMethod;
    }

    @NotNull
    protected final Coordinate getM_coordinate() {
        return this.m_coordinate;
    }

    protected final void setM_coordinate(@NotNull Coordinate coordinate) {
        Intrinsics.checkNotNullParameter(coordinate, "<set-?>");
        this.m_coordinate = coordinate;
    }

    @NotNull
    public final MiladiDate getM_date() {
        return this.m_date;
    }

    public final void setM_date(@NotNull MiladiDate miladiDate) {
        Intrinsics.checkNotNullParameter(miladiDate, "<set-?>");
        this.m_date = miladiDate;
    }

    @NotNull
    protected final String getM_fajrAngle() {
        return this.m_fajrAngle;
    }

    protected final void setM_fajrAngle(@NotNull String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.m_fajrAngle = str;
    }

    protected final int getM_highLatitude() {
        return this.m_highLatitude;
    }

    protected final void setM_highLatitude(int i) {
        this.m_highLatitude = i;
    }

    @NotNull
    public final Hour getM_hour() {
        return this.m_hour;
    }

    public final void setM_hour(@NotNull Hour hour) {
        Intrinsics.checkNotNullParameter(hour, "<set-?>");
        this.m_hour = hour;
    }

    @NotNull
    protected final String getM_ishaAngle() {
        return this.m_ishaAngle;
    }

    protected final void setM_ishaAngle(@NotNull String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.m_ishaAngle = str;
    }

    @NotNull
    protected final Mazhab getM_mazhab() {
        return this.m_mazhab;
    }

    protected final void setM_mazhab(@NotNull Mazhab mazhab) {
        Intrinsics.checkNotNullParameter(mazhab, "<set-?>");
        this.m_mazhab = mazhab;
    }

    @Nullable
    public final Season getM_season() {
        return this.m_season;
    }

    public final void setM_season(@Nullable Season season) {
        this.m_season = season;
    }

    public final int getM_seasonSummer() {
        return this.m_seasonSummer;
    }

    protected final void setM_seasonSummer(int i) {
        this.m_seasonSummer = i;
    }

    public final int getM_seasonSummerDay() {
        return this.m_seasonSummerDay;
    }

    protected final void setM_seasonSummerDay(int i) {
        this.m_seasonSummerDay = i;
    }

    public final int getM_seasonWinter() {
        return this.m_seasonWinter;
    }

    protected final void setM_seasonWinter(int i) {
        this.m_seasonWinter = i;
    }

    public final int getM_seasonWinterDay() {
        return this.m_seasonWinterDay;
    }

    protected final void setM_seasonWinterDay(int i) {
        this.m_seasonWinterDay = i;
    }

    public final double getMaghrib() {
        return f3766w;
    }

    public final double getShrouk() {
        return t;
    }

    public final double getZuhr() {
        return u;
    }

    @NotNull
    public Time imsakTime(double valBeforeFajr) {
        return new Time(s - valBeforeFajr, true, this.m_hour);
    }

    @NotNull
    public Time ishaTime(boolean isAzan) {
        if (isAzan) {
            return new Time(x, true, this.m_hour);
        }
        return new Time(E, true, this.m_hour);
    }

    @NotNull
    public Time ishaTimeIkama(double ikama) {
        return new Time(x + ikama, true, this.m_hour);
    }

    @NotNull
    public final Time jomo3aTime(boolean isAzan) {
        if (isAzan) {
            return new Time(f3767y, true, this.m_hour);
        }
        return new Time(F, true, this.m_hour);
    }

    @NotNull
    public final Time jomo3aTimeIkama(double ikama) {
        return new Time(f3767y + ikama, true, this.m_hour);
    }

    public final double latitude() {
        return this.m_coordinate.getLatitude();
    }

    public final double longitude() {
        return this.m_coordinate.getLongitude();
    }

    @NotNull
    public Time maghribTime(boolean isAzan) {
        if (isAzan) {
            return new Time(f3766w, true, this.m_hour);
        }
        return new Time(D, true, this.m_hour);
    }

    @NotNull
    public Time maghribTimeIkama(double ikama) {
        return new Time(f3766w + ikama, true, this.m_hour);
    }

    @NotNull
    public final Mazhab mazhab() {
        return this.m_mazhab;
    }

    public final int month() {
        return this.m_date.getMonth();
    }

    @Nullable
    public final Season season() {
        return this.m_season;
    }

    public final void setCalender(@NotNull CalcMethod calcMethod) {
        Intrinsics.checkNotNullParameter(calcMethod, "calcMethod");
        this.m_calcMethod = calcMethod;
    }

    public final void setCoordinate(@NotNull Coordinate coordinate) {
        Intrinsics.checkNotNullParameter(coordinate, "coordinate");
        this.m_coordinate = coordinate;
    }

    public final void setData(@NotNull Coordinate co, @NotNull MiladiDate da, @NotNull CalcMethod ca, @NotNull Mazhab ma, @NotNull Season se) {
        Intrinsics.checkNotNullParameter(co, "co");
        Intrinsics.checkNotNullParameter(da, "da");
        Intrinsics.checkNotNullParameter(ca, "ca");
        Intrinsics.checkNotNullParameter(ma, "ma");
        Intrinsics.checkNotNullParameter(se, "se");
        this.m_coordinate = co;
        this.m_date = da;
        this.m_calcMethod = ca;
        this.m_mazhab = ma;
        this.m_season = se;
    }

    public final void setDate(int day, int r3, int r4) {
        this.m_date.setDay(day);
        this.m_date.setMonth(r3);
        this.m_date.setYear(r4);
    }

    public final void setDay(int day) {
        this.m_date.setDay(day);
    }

    public final void setFajrAngle(@NotNull String val) {
        Intrinsics.checkNotNullParameter(val, "val");
        this.m_fajrAngle = val;
    }

    public final void setHighLatitude(int highLatitude) {
        this.m_highLatitude = highLatitude;
    }

    public final void setHour(@NotNull String hour12_24) {
        Intrinsics.checkNotNullParameter(hour12_24, "hour12_24");
        this.m_hour.setHour(hour12_24);
    }

    public final void setIshaAngle(@NotNull String val) {
        Intrinsics.checkNotNullParameter(val, "val");
        this.m_ishaAngle = val;
    }

    public final void setMazhab(@NotNull Mazhab mazhab) {
        Intrinsics.checkNotNullParameter(mazhab, "mazhab");
        this.m_mazhab = mazhab;
    }

    public final void setMonth(int r2) {
        this.m_date.setMonth(r2);
    }

    public final void setSeason(@NotNull Season season) {
        Intrinsics.checkNotNullParameter(season, "season");
        this.m_season = season;
    }

    public final void setSeasonSummer(int r1) {
        this.m_seasonSummer = r1;
    }

    public final void setSeasonSummerDay(int day) {
        this.m_seasonSummerDay = day;
    }

    public final void setSeasonWinter(int r1) {
        this.m_seasonWinter = r1;
    }

    public final void setSeasonWinterDay(int day) {
        this.m_seasonWinterDay = day;
    }

    public final void setYear(int r2) {
        this.m_date.setYear(r2);
    }

    public final void set_latitude(@NotNull String val) {
        Intrinsics.checkNotNullParameter(val, "val");
        this.o = val;
    }

    public final void set_longitude(@NotNull String val) {
        Intrinsics.checkNotNullParameter(val, "val");
        this.p = val;
    }

    public final void set_timezone(@NotNull String val) {
        Intrinsics.checkNotNullParameter(val, "val");
        this.n = val;
    }

    @NotNull
    public Time shroukTime(boolean isAzan) {
        if (isAzan) {
            return new Time(t, true, this.m_hour);
        }
        return new Time(A, true, this.m_hour);
    }

    @NotNull
    public Time shroukTimeIkama(double ikama) {
        return new Time(t + ikama, true, this.m_hour);
    }

    public final int year() {
        return this.m_date.getYear();
    }

    public final double zone() {
        return this.m_coordinate.getZone();
    }

    @NotNull
    public Time zuhrTime(boolean isAzan) {
        if (isAzan) {
            return new Time(u, true, this.m_hour);
        }
        return new Time(B, true, this.m_hour);
    }

    @NotNull
    public Time zuhrTimeIkama(double ikama) {
        return new Time(u + ikama, true, this.m_hour);
    }

    public final void setCalender(@NotNull CalcMethod.Type calender) {
        Intrinsics.checkNotNullParameter(calender, "calender");
        this.m_calcMethod.setCalender(calender);
    }

    public final void setCoordinate(double lot, double lat, int zone) {
        this.m_coordinate.setLongitude(lot);
        this.m_coordinate.setLatitude(lat);
        this.m_coordinate.setZone(zone);
    }

    public final void setHour(@NotNull Hour.Type hour) {
        Intrinsics.checkNotNullParameter(hour, "hour");
        this.m_hour.setHour(hour);
    }

    public final void setMazhab(@NotNull Mazhab.Type mazhab) {
        Intrinsics.checkNotNullParameter(mazhab, "mazhab");
        this.m_mazhab.setMazhab(mazhab);
    }

    public final void setSeason(@NotNull Season.Type season) {
        Intrinsics.checkNotNullParameter(season, "season");
        Season season2 = this.m_season;
        Intrinsics.checkNotNull(season2);
        season2.setSeason(season);
    }

    public final void setCalender(@NotNull String calender) {
        Intrinsics.checkNotNullParameter(calender, "calender");
        this.m_calcMethod.setCalender(calender);
    }

    public final void setMazhab(@NotNull String mazhab) {
        Intrinsics.checkNotNullParameter(mazhab, "mazhab");
        this.m_mazhab.setMazhab(mazhab);
    }

    public final void setSeason(@NotNull String season) {
        Intrinsics.checkNotNullParameter(season, "season");
        Season season2 = this.m_season;
        Intrinsics.checkNotNull(season2);
        season2.setSeason(season);
    }

    public final void setDate(@NotNull MiladiDate date) {
        Intrinsics.checkNotNullParameter(date, "date");
        this.m_date = date;
    }

    public final void setData(double lot, double lat, int zone, int day, int r8, int r9, @NotNull CalcMethod ca, @NotNull Mazhab ma, @NotNull Season se) {
        Intrinsics.checkNotNullParameter(ca, "ca");
        Intrinsics.checkNotNullParameter(ma, "ma");
        Intrinsics.checkNotNullParameter(se, "se");
        setCoordinate(lot, lat, zone);
        setDate(day, r8, r9);
        setCalender(ca);
        setMazhab(ma);
        setSeason(se);
    }

    public final void setData(double lot, double lat, int zone, int day, int r8, int r9, @NotNull String calender, @NotNull String mazhab, @NotNull String season) {
        Intrinsics.checkNotNullParameter(calender, "calender");
        Intrinsics.checkNotNullParameter(mazhab, "mazhab");
        Intrinsics.checkNotNullParameter(season, "season");
        setCoordinate(lot, lat, zone);
        setDate(day, r8, r9);
        setCalender(calender);
        setMazhab(mazhab);
        setSeason(season);
    }

    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        public final double getAsr() {
            return PrayerTime.f3765v;
        }

        public final void setAsr(double d) {
            PrayerTime.f3765v = d;
        }

        public final double getAsr_ikama() {
            return PrayerTime.C;
        }

        public final void setAsr_ikama(double d) {
            PrayerTime.C = d;
        }

        protected final double getDegToRad() {
            return PrayerTime.f3763q;
        }

        protected final void setDegToRad(double d) {
            PrayerTime.f3763q = d;
        }

        public final double getFajr() {
            return PrayerTime.s;
        }

        public final void setFajr(double d) {
            PrayerTime.s = d;
        }

        public final double getFajr_ikama() {
            return PrayerTime.z;
        }

        public final void setFajr_ikama(double d) {
            PrayerTime.z = d;
        }

        public final double getIsha() {
            return PrayerTime.x;
        }

        public final void setIsha(double d) {
            PrayerTime.x = d;
        }

        public final double getIsha_ikama() {
            return PrayerTime.E;
        }

        public final void setIsha_ikama(double d) {
            PrayerTime.E = d;
        }

        public final double getJomo3a() {
            return PrayerTime.f3767y;
        }

        public final void setJomo3a(double d) {
            PrayerTime.f3767y = d;
        }

        public final double getJomo3a_ikama() {
            return PrayerTime.F;
        }

        public final void setJomo3a_ikama(double d) {
            PrayerTime.F = d;
        }

        public final double getMaghrib() {
            return PrayerTime.f3766w;
        }

        public final void setMaghrib(double d) {
            PrayerTime.f3766w = d;
        }

        public final double getMaghrib_ikama() {
            return PrayerTime.D;
        }

        public final void setMaghrib_ikama(double d) {
            PrayerTime.D = d;
        }

        protected final double getRadToDeg() {
            return PrayerTime.f3764r;
        }

        protected final void setRadToDeg(double d) {
            PrayerTime.f3764r = d;
        }

        public final double getShrouk() {
            return PrayerTime.t;
        }

        public final void setShrouk(double d) {
            PrayerTime.t = d;
        }

        public final double getShrouk_ikama() {
            return PrayerTime.A;
        }

        public final void setShrouk_ikama(double d) {
            PrayerTime.A = d;
        }

        public final double getZuhr() {
            return PrayerTime.u;
        }

        public final void setZuhr(double d) {
            PrayerTime.u = d;
        }

        public final double getZuhr_ikama() {
            return PrayerTime.B;
        }

        public final void setZuhr_ikama(double d) {
            PrayerTime.B = d;
        }

        public final void setIshaExtra(boolean isAzan, int valExtra) {
            if (isAzan) {
                double isha = getIsha();
                double d = valExtra;
                Double.isNaN(d);
                setIsha(isha + d);
                return;
            }
            double isha_ikama = getIsha_ikama();
            double d2 = valExtra;
            Double.isNaN(d2);
            setIsha_ikama(isha_ikama + d2);
        }

        public final void setIshaFromMaghrib(boolean isAzan, int valExtra) {
            if (isAzan) {
                double maghrib = getMaghrib();
                double d = valExtra;
                Double.isNaN(d);
                setIsha(maghrib + d);
                return;
            }
            double maghrib_ikama = getMaghrib_ikama();
            double d2 = valExtra;
            Double.isNaN(d2);
            setIsha_ikama(maghrib_ikama + d2);
        }

        public final void setMaghribExtra(boolean isAzan, int valExtra) {
            if (isAzan) {
                double maghrib = getMaghrib();
                double d = valExtra;
                Double.isNaN(d);
                setMaghrib(maghrib + d);
                return;
            }
            double maghrib_ikama = getMaghrib_ikama();
            double d2 = valExtra;
            Double.isNaN(d2);
            setMaghrib_ikama(maghrib_ikama + d2);
        }
    }
}

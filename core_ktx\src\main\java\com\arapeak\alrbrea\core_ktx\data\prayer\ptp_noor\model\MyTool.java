package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;

import androidx.core.view.ViewCompat;

import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/* loaded from: classes.dex */
public class MyTool {
    public static final String FajrAzan = "azan_fajr01***raw***";
    public static final String GetDataService_TAG_DURATION = "pageTime";
    public static final String GetDataService_TAG_NAME = "videoPath";
    public static final String MsgTCP_BackgroundFiles_SetDefault = "BackgroundFiles_SetDefault";
    public static final String MsgTCP_CANCEL_PREVIEW = "CANCEL_PREVIEW";
    public static final String MsgTCP_REBOOT_TV_BOX = "REBOOT_TV_BOX";
    public static final String MsgTCP_RUN_BLACK_SCREEN = "RUN_BLACK_SCREEN";
    public static final String MsgTCP_RUN_ISLAMIC_SCREEN = "RUN_ISLAMIC_SCREEN";
    public static final String MsgTCP_RUN_MUAQITA = "RUN_MUAQITA";
    public static final String MsgTCP_SCREEN_OFF_PREF = "SCREEN_OFF_PREF";
    public static final String OtherAzan = "azan01***raw***";
    public static final int REQUEST_WRITE_PERMISSION = 786;
    public static final int REQUEST_WRITE_PERMISSION_ANALYSIS_DAILY = 7140;
    public static final int REQUEST_WRITE_PERMISSION_ANALYSIS_MONTHLY = 7141;
    public static final String azan_audio = "azan_audio";
    public static final String azan_none = "azan_none";
    public static final String azan_raw = "***raw***";
    public static final String azan_takbeer = "azan_takbeer";
    public static final String broadcastReceiver_Copy_MSG_Screen_Refresh_Files = "ScreenActivity_Copy_MSG_Refresh_Files";
    public static final String broadcastReceiver_End_Screen_Refresh_Files = "ScreenActivity_End_Refresh_Files";
    public static final String broadcastReceiver_Main_CheckIkamaIsStop = "MainActivity_CheckIkamaIsStop";
    public static final String broadcastReceiver_Main_Finish = "MainActivity_Finish";
    public static final String broadcastReceiver_Main_InitCurrentAlarmTV = "MainActivity_InitCurrentAlarmTV";
    public static final String broadcastReceiver_Main_ReturnToMain = "MainActivity_ReturnToMain";
    public static final String broadcastReceiver_Main_StartAzkar = "MainActivity_StartAzkar";
    public static final String broadcastReceiver_Main_StartBlackScreen = "MainActivity_StartBlackScreen";
    public static final String broadcastReceiver_Main_StartIkama = "MainActivity_StartIkama";
    public static final String broadcastReceiver_Main_StartScreen = "MainActivity_StartScreen";
    public static final String broadcastReceiver_Screen_Finish = "ScreenActivity_Finish";
    public static final String broadcastReceiver_Screen_InitCurrentAlarmTV = "ScreenActivity_InitCurrentAlarmTV";
    public static final String broadcastReceiver_Screen_Refresh_Files = "ScreenActivity_Refresh_Files";
    public static final String broadcastReceiver_Screen_Refresh_News = "ScreenActivity_Refresh_News";
    public static final String broadcastReceiver_Screen_Start_Islamic_Screen = "ScreenActivity_Start_Islamic_Screen";
    public static final String broadcastReceiver_Screen_Start_Muaqita = "ScreenActivity_Start_Muaqita";
    public static final String broadcastReceiver_Start_Screen_Refresh_Files = "ScreenActivity_Start_Refresh_Files";
    public static final String takbeerAzan = "notification_takbeer***raw***";
    /* renamed from: a */
    private static final Hashtable<String, Typeface> f2806a = new Hashtable<>();
    public static String TAG_DURATION = "pageTime";
    public static String TAG_NAME = "videoPath";
    public static boolean checkIkamaIsRunning_IKAMA_Shuruq = false;
    public static int daysToAdd = 0;
    public static int lastMinute = -1;
    public static int secondsToAdd = 12;
    public static boolean secondsToAddInUsing = false;
    public static int secondsToAdd_CONSTANT = 12;
    public static ScreenType alarmScreenType = ScreenType.NONE;
    public static ScreenManagementType alarmScreenManagementType = ScreenManagementType.LOCAL;
    public static String orientaionTV = OrientaionTV.LANDSCAPE.toString();
    public static boolean NECESSARY_12_NIGHT = false;
    public static int ikamaTime_COUNTER = -1;
    public static int ikamaTime_INDEX = -1;
    public static int ikamaTime_INDEX_CURRENT = -1;
    public static boolean isTrialVersion = false;
    public static boolean isRun_IkamaActivity = false;
    public static int PrayerSettingsActivity_IDExplorer = 222;
    public static int EditorActivity_ID = 333;
    public static int UDP_PORT_DEFAULT = 1871;
    public static int SilentActivity_id = 556;
    public static int PrayerSettingsActivity_LayoutDynamicPortraitFiles = 333;
    public static int PrayerSettingsActivity_LayoutDynamicLandscapeFiles = 444;
    public static String fileScreenshots = "Screenshots.jpg";
    public static String fileScreenshotsFastingPDF = "MonthlyFasting.pdf";
    public static int SeekDialog_Val = 0;
    public static int notifyId_Orientation = 1243;
    public static String fileNews = "news.html";
    public static String fileLectures = "lectures.html";
    public static String fileFatawa = "fatawa.html";
    public static String dirProgram = "Al-Awail_Prayer_Time";
    public static String dirVideos = "videos";
    public static String dirVideosScreen = "videos_screen";
    public static String fileScreenBackground = "screen_background.png";
    public static String dirFatawa = "fatawa";
    public static String dirLectures = "lectures";
    public static String dirNews = "news";
    public static String dirLogs = "logs";
    public static String dirImages = "images";
    public static String dirLayouts = "layouts";
    public static String dirLayoutsScreen = "layoutsScreen";
    public static String dirDownloadZip = "DownloadZip";
    public static String dirDownloadImg = "DownloadImg";
    public static String dirLayoutDynamicPortrait = "layout_dynamic_portrait";
    public static String dirLayoutDynamicLandscape = "layout_dynamic_landscape";
    public static String dirBackgroundScreen = "background_screen";
    public static String dirBackground = "background";
    public static String dirMyBackground = "MyBackground";
    public static String dirAudioAlarm = "audio_alarm";
    public static String dirAzan = "azan";
    public static String dirAzkar = "azkar";

    public static String CheckAzan(String str) {
        if (str.contains(":")) {
            return str;
        }
        return "0:".concat(str);
    }

    public static String GetLocalIpAddresses() {
        boolean z2;
        String str = "";
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                Iterator<InterfaceAddress> it = networkInterfaces.nextElement().getInterfaceAddresses().iterator();
                while (it.hasNext()) {
                    String substring = it.next().getAddress().toString().substring(1);
                    if (Pattern.compile("((25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9])\\.(25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1][0-9]{2}|[1-9][0-9]|[0-9]))").matcher(substring).matches()) {
                        z2 = true ^ substring.equals("127.0.0.1");
                    } else {
                        z2 = false;
                    }
                    if (z2) {
                        str = substring;
                    }
                }
            }
        } catch (Exception e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
        }
        return str;
    }

    public static boolean IsRTL(String str) {
        return str.equals("ar") || str.equals("ku") || str.equals("ur");
    }

    public static boolean IsRTL_Other(String str) {
        return str.equals("ku");
    }

    public static void MoveFile(String str, String str2, String str3) {
        try {
            File file = new File(str3);
            if (!file.exists()) {
                file.mkdirs();
            }
            FileInputStream fileInputStream = new FileInputStream(str + str2);
            FileOutputStream fileOutputStream = new FileOutputStream(str3 + str2);
            byte[] bArr = new byte[1024];
            while (true) {
                int read = fileInputStream.read(bArr);
                if (read != -1) {
                    fileOutputStream.write(bArr, 0, read);
                } else {
                    fileInputStream.close();
                    fileOutputStream.flush();
                    fileOutputStream.close();
                    new File(str + str2).delete();
                    return;
                }
            }
        } catch (FileNotFoundException e2) {
            Log.e("tag", e2.getMessage());
        } catch (Exception e3) {
            Log.e("tag", e3.getMessage());
        }
    }

    public static Date NextDay(Calendar calendar, int i2) {
        int i3 = calendar.get(7);
        if (i2 <= i3) {
            i2 += 7;
        }
        calendar.add(5, i2 - i3);
        return calendar.getTime();
    }

    public static String SetFirstCharUpperCase(String str) {
        try {
            String[] split = str.trim().split(" ");
            StringBuilder sb = new StringBuilder();
            for (String str2 : split) {
                if (str2.trim().length() != 0) {
                    sb.append((str2.substring(0, 1).toUpperCase() + str2.substring(1)) + " ");
                }
            }
            return sb.toString();
        } catch (Exception e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
            return str;
        }
    }

//    public static int ConvertStrHMtoMinute(String str) {
//        String[] split = str.split(":");
//        int i2 = 0;
//        int strToInt = strToInt(split[0]);
//        int strToInt2 = strToInt(split[1]);
//        if (str.toLowerCase().contains("pm") && strToInt != 12) {
//            strToInt += 12;
//        }
//        if (!str.toLowerCase().contains("am") || strToInt != 12) {
//            i2 = strToInt;
//        }
//        return (i2 * 60) + strToInt2;
//    }
//
//
//
//
//    public static String DateToStr(int i2, int i3, int i4) {
//        return "" + i2 + str2Int(i3) + str2Int(i4);
//    }
//
//    public static void Do_batteryOptimizationNot(Activity activity) {
//        boolean isIgnoringBatteryOptimizations;
//        try {
//            if (Build.VERSION.SDK_INT >= 23) {
//                Intent intent = new Intent();
//                String packageName = activity.getPackageName();
//                PowerManager powerManager = (PowerManager) activity.getSystemService("power");
//                if (powerManager != null) {
//                    isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(packageName);
//                    if (isIgnoringBatteryOptimizations) {
//                        intent.setAction("android.settings.IGNORE_BATTERY_OPTIMIZATION_SETTINGS");
//                        MyLog("ppp", "no");
//                        activity.startActivityForResult(intent, 100);
//                    }
//                }
//                intent.setAction("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
//                intent.setData(Uri.parse("package:" + packageName));
//                MyLog("ppp", "yes");
//                activity.startActivityForResult(intent, 100);
//            }
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

    public static String ShowAzanVal(String str) {
        try {
            String[] split = str.split(":");
            return split[0] + ":" + split[1];
        } catch (Exception e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
            return "";
        }
    }

    public static boolean check3gp_mp4File(String str) {
        String[] split = str.split("\\.");
        String lowerCase = split[split.length - 1].toLowerCase();
        if (lowerCase.equals("mp4") || lowerCase.equals("3gp")) {
            return true;
        }
        return false;
    }

    public static boolean checkHtmlFile(String str) {
        String[] split = str.split("\\.");
        String lowerCase = split[split.length - 1].toLowerCase();
        if (lowerCase.equals("html") || lowerCase.equals("htm") || lowerCase.equals("mht")) {
            return true;
        }
        return false;
    }

    public static boolean checkIkamaAzkarBlackScreenBackPress() {
        return false;
    }

    public static boolean checkImgFile(String str) {
        String[] split = str.split("\\.");
        String lowerCase = split[split.length - 1].toLowerCase();
        if (lowerCase.equals("png") || lowerCase.equals("jpg") || lowerCase.equals("gif")) {
            return true;
        }
        return false;
    }

    public static String delCityNumberFromCountryName(String str) {
        if (str.contains(" : ")) {
            return str.substring(0, str.indexOf(" : "));
        }
        return str;
    }

    public static void delFile(File file) {
        if (file.isFile() && file.exists()) {
            file.delete();
        }
    }


//    public static void change_NextDate4Request_OnErrorRequest(Preference preference) {
//        MyLog("OnErrorRequest", "change_NextDate4Request_OnErrorRequest 1 ");
//        try {
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss", Locale.ENGLISH);
//            String random_NextDate4Request = preference.getRandom_NextDate4Request();
//            if (random_NextDate4Request.equals("null")) {
//                Calendar calendar = Calendar.getInstance();
//                calendar.add(5, 1);
//                preference.setRandom_NextDate4Request(simpleDateFormat.format(calendar.getTime()));
//                MyLog("OnErrorRequest", "change_NextDate4Request_OnErrorRequest ok00 " + simpleDateFormat.format(calendar.getTime()));
//            } else if (now_Is_bigger_random_NextDate4Request(random_NextDate4Request)) {
//                Date parse = simpleDateFormat.parse(random_NextDate4Request);
//                Calendar calendar2 = Calendar.getInstance();
//                calendar2.setTime(parse);
//                calendar2.add(5, 1);
//                preference.setRandom_NextDate4Request(simpleDateFormat.format(calendar2.getTime()));
//                MyLog("OnErrorRequest", "change_NextDate4Request_OnErrorRequest ok111 " + simpleDateFormat.format(calendar2.getTime()));
//            }
//        } catch (Exception e2) {
//            androidx.fragment.app.q.c(e2, androidx.fragment.app.p.b(e2, "change_NextDate4Request_OnErrorRequest error "), "OnErrorRequest");
//        }
//    }

    public static void delFolderAll(String str) {
        try {
            File file = new File(str);
            if (file.isDirectory()) {
                for (String str2 : file.list()) {
                    new File(file, str2).delete();
                }
            }
        } catch (Exception e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
        }
    }


//    public static void checkDirFound(String str) {
//        File file = new File(getDirProgram());
//        if (!file.exists()) {
//            file.mkdir();
//        }
//        File file2 = new File(getDirProgram() + "/" + getFileName(str));
//        if (!file2.exists()) {
//            file2.mkdir();
//        }
//    }
//
//    public static void checkDirFound2(String str) {
//        File file = new File(getDirProgram());
//        if (!file.exists()) {
//            file.mkdir();
//            MyLog("createFolder00", " " + str);
//        }
//    }
//
//
//    public static String checkHijriEid(Context context, int i2, int i3) {
//        String str;
//        if (i2 == 10 && i3 == 1) {
//            str = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.eid_al_fitr);
//        } else {
//            str = "";
//        }
//        if (i2 == 12) {
//            if (i3 == 10 || i3 == 11 || i3 == 12 || i3 == 13) {
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.eid_al_adha);
//            }
//            return str;
//        }
//        return str;
//    }

    public static void deleteRecursive(File file) {
        if (file.isDirectory()) {
            for (File file2 : file.listFiles()) {
                deleteRecursive(file2);
            }
        }
        file.delete();
    }

//    public static boolean checkIfQuranRun() {
//        return readTextFromFile(getFile_quran_playing_state()).contains("Playing");
//    }

    public static void deleteRecursiveExceptFiles(File file, ArrayList<String> arrayList) {
        MyLog("next_image1100", "deleteRecursiveExceptFiles isDirectory ");
        if (file.isDirectory()) {
            for (File file2 : file.listFiles()) {
                MyLog("next_image1100", "getNextImageSource $( ) del1 " + file2.getName());
                if (!arrayList.contains(file2.getName())) {
                    deleteRecursive(file2);
                    MyLog("next_image1100", "getNextImageSource $( ) del2 " + file2.getName());
                }
            }
        }
    }

    public static Object deserialize(byte[] bArr) {
        try {
            return new ObjectInputStream(new ByteArrayInputStream(bArr)).readObject();
        } catch (IOException e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
            return null;
        } catch (ClassNotFoundException e3) {
            CrashlyticsUtils.INSTANCE.logException(e3);
            return null;
        }
    }


//    public static String checkNoneStr(String str) {
//        if (str.equals(Alarm.NONE_STR)) {
//            return "";
//        }
//        return str;
//    }
//
//    public static boolean checkNotValidDemo(int i2, int i3, int i4) {
//        if ((i2 + HelpFormatter.DEFAULT_OPT_PREFIX + str2Int(i3) + HelpFormatter.DEFAULT_OPT_PREFIX + str2Int(i4)).compareTo(PrayerTimesApplication.demo_until_date) > 0) {
//            return true;
//        }
//        return false;
//    }
//
//    public static boolean checkNotValidDemo__screen(int i2, int i3, int i4) {
//        if ((i2 + HelpFormatter.DEFAULT_OPT_PREFIX + str2Int(i3) + HelpFormatter.DEFAULT_OPT_PREFIX + str2Int(i4)).compareTo(PrayerTimesApplication.demo_until_date__screen) > 0) {
//            return true;
//        }
//        return false;
//    }

//    public static boolean checkPDFFile(String str) {
//        return str.split("\\.")[r1.length - 1].toLowerCase().equals("pdf");
//    }
//
//    public static Boolean checkPowerManagerFound(Context context) {
//        boolean z2 = false;
//        try {
//            Intent[] intentArr = StopAppActivity.POWERMANAGER_INTENTS;
//            int length = intentArr.length;
//            int i2 = 0;
//            while (true) {
//                if (i2 >= length) {
//                    break;
//                }
//                if (context.getPackageManager().resolveActivity(intentArr[i2], 65536) != null) {
//                    z2 = true;
//                    break;
//                }
//                i2++;
//            }
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//        return Boolean.valueOf(z2);
//    }
//
//    public static void check_is_stop_NetServiceBackground() {
//        try {
//            Intent intent = new Intent(PrayerTimesApplication.getAppContext(), (Class<?>) NetService.class);
//            intent.putExtra("action", NetService.ActionType.netService_check_is_stop.getAction());
//            startService(PrayerTimesApplication.getAppContext(), intent);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//        try {
//            Intent intent2 = new Intent(PrayerTimesApplication.getAppContext(), (Class<?>) NetServiceBackground.class);
//            intent2.putExtra("action", NetServiceBackground.ActionType.netServiceBackground_check_is_stop.getAction());
//            startService(PrayerTimesApplication.getAppContext(), intent2);
//        } catch (Exception e3) {
//            e3.printStackTrace();
//        }
//    }
//
//    public static void cityInfoURL_Action(String str, boolean z2) {
//        try {
//            CityFinder.cityInfoURL_response = str;
//            Intent intent = new Intent(PrayerTimesApplication.getAppContext(), (Class<?>) NetServiceBackground.class);
//            intent.putExtra("action", NetServiceBackground.ActionType.cityInfoURL_Action.getAction());
//            intent.putExtra(DataNetAction.IntentService.isMustDownloadPrayerTimes.name(), z2);
//            startService(PrayerTimesApplication.getAppContext(), intent);
//            MyLog("ssss222", "getCityInfoURL_Action ttt7777 3 a NetServiceBackground " + CityFinder.isFirstRun + " " + str.length());
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void cleanDirectory(File file) {
//        File[] listFiles;
//        if (file.exists() && (listFiles = file.listFiles()) != null) {
//            for (File file2 : listFiles) {
//                if (file2.isFile() && file2.exists()) {
//                    q(file2);
//                } else {
//                    cleanDirectory(file2);
//                    q(file2);
//                }
//            }
//        }
//    }
//
//    public static void closeApp(Context context) {
//        new Preference(context).setGoToMain(true);
//        try {
//            Intent mainActivity_Intent = MainActivityControls.INSTANCE.getMainActivity_Intent(context);
//            mainActivity_Intent.setFlags(67108864);
//            mainActivity_Intent.addFlags(268435456);
//            mainActivity_Intent.putExtra("LOGOUT", true);
//            context.startActivity(mainActivity_Intent);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void closeBlackScreenActivity(Context context) {
//        MainActivityControls.INSTANCE.closeBlackScreenActivity(context);
//    }
//
//    public static void closeEditorResultActivity(Context context) {
//        MainActivityControls.INSTANCE.closeEditorResultActivity(context);
//    }
//
//    public static void closeQuranIfRun(Context context) {
//        try {
//            Intent intent = new Intent("android.intent.action.MAIN");
//            intent.setComponent(new ComponentName("com.quran.awail.androidquran", "com.quran.awail.androidquran.service.PlayAudioService"));
//            intent.putExtra("isCloseQuran", Integer.parseInt(SettingsActivity_2.CityType_1));
//            intent.setFlags(268435456);
//            context.startService(intent);
//            MyLog("AndroidAppProcess ", "AndroidAppProcess del 111");
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//        List<AndroidAppProcess> runningAppProcesses = AndroidProcesses.getRunningAppProcesses();
//        for (int i2 = 0; i2 < runningAppProcesses.size(); i2++) {
//            if (runningAppProcesses.get(i2).getPackageName().equals("com.quran.awail.androidquran")) {
//                try {
//                    MyLog("AndroidAppProcess ", "AndroidAppProcess del " + runningAppProcesses.get(i2).getPackageName());
//                    Process.killProcess(runningAppProcesses.get(i2).pid);
//                    Process.killProcess(runningAppProcesses.get(i2).uid);
//                } catch (Exception e3) {
//                    e3.printStackTrace();
//                }
//            }
//        }
//    }
//
//    public static void closeQuranIfRun2(Context context) {
//        List<AndroidAppProcess> runningAppProcesses = AndroidProcesses.getRunningAppProcesses();
//        for (int i2 = 0; i2 < runningAppProcesses.size(); i2++) {
//            if (runningAppProcesses.get(i2).getPackageName().equals("com.quran.awail.androidquran")) {
//                try {
//                    Process.killProcess(runningAppProcesses.get(i2).pid);
//                    Process.killProcess(runningAppProcesses.get(i2).uid);
//                } catch (Exception e2) {
//                    e2.printStackTrace();
//                }
//            }
//        }
//    }
//
//    public static String convertToEnglish(String str) {
//        return str.replaceAll("١", SettingsActivity_2.CityType_1).replaceAll("٢", "2").replaceAll("٣", ExifInterface.GPS_MEASUREMENT_3D).replaceAll("٤", "4").replaceAll("٥", "5").replaceAll("٦", "6").replaceAll("٧", "7").replaceAll("٨", "8").replaceAll("٩", "9").replaceAll("٠", "0");
//    }
//
//    public static void copyClockImgs(String str, String str2, String str3, String str4) {
//        copyFilePath(androidx.concurrent.futures.a.a(str, "/", str2), str2, getDirBackground());
//        copyFilePath(str + "/" + str3, str3, getDirBackground());
//        copyFilePath(str + "/" + str4, str4, getDirBackground());
//    }
//
//    public static void copyClockImgsAzanIkama(String str, boolean z2, boolean z3) {
//        String str2;
//        String str3 = "";
//        if (!z2) {
//            str2 = "";
//        } else {
//            str2 = "_ikama";
//        }
//        if (z3) {
//            str3 = "2_";
//        }
//        try {
//            String str4 = "clock_dial_" + str3 + "fajr" + str2 + ".png";
//            String str5 = "clock_hour_" + str3 + "fajr" + str2 + ".png";
//            String str6 = "clock_minute_" + str3 + "fajr" + str2 + ".png";
//            delClockImgs(str4, str5, str6);
//            String str7 = "clock_dial_" + str3 + "shuruq" + str2 + ".png";
//            String str8 = "clock_hour_" + str3 + "shuruq" + str2 + ".png";
//            String str9 = "clock_minute_" + str3 + "shuruq" + str2 + ".png";
//            delClockImgs(str7, str8, str9);
//            String str10 = "clock_dial_" + str3 + "duhr" + str2 + ".png";
//            String str11 = "clock_hour_" + str3 + "duhr" + str2 + ".png";
//            String str12 = "clock_minute_" + str3 + "duhr" + str2 + ".png";
//            delClockImgs(str10, str11, str12);
//            String str13 = "clock_dial_" + str3 + "asr" + str2 + ".png";
//            String str14 = "clock_hour_" + str3 + "asr" + str2 + ".png";
//            String str15 = "clock_minute_" + str3 + "asr" + str2 + ".png";
//            delClockImgs(str13, str14, str15);
//            String str16 = "clock_dial_" + str3 + "magrib" + str2 + ".png";
//            String str17 = "clock_hour_" + str3 + "magrib" + str2 + ".png";
//            String str18 = "clock_minute_" + str3 + "magrib" + str2 + ".png";
//            delClockImgs(str16, str17, str18);
//            String str19 = "clock_dial_" + str3 + "isha" + str2 + ".png";
//            String str20 = "clock_hour_" + str3 + "isha" + str2 + ".png";
//            String str21 = "clock_minute_" + str3 + "isha" + str2 + ".png";
//            delClockImgs(str19, str20, str21);
//            String str22 = getDirDownloadZip() + "/" + str;
//            copyClockImgs(str22, str4, str5, str6);
//            copyClockImgs(str22, str7, str8, str9);
//            copyClockImgs(str22, str10, str11, str12);
//            copyClockImgs(str22, str13, str14, str15);
//            copyClockImgs(str22, str16, str17, str18);
//            copyClockImgs(str22, str19, str20, str21);
//        } catch (Exception e2) {
//            androidx.fragment.app.q.c(e2, androidx.fragment.app.p.b(e2, "copyClockImgsAzanIkama "), "selectDynamicDesign2");
//        }
//    }

//    public static void copyFile(InputStream inputStream, OutputStream outputStream) {
//        byte[] bArr = new byte[1024];
//        while (true) {
//            int read = inputStream.read(bArr);
//            if (read == -1) {
//                return;
//            } else {
//                outputStream.write(bArr, 0, read);
//            }
//        }
//    }
//
//    public static boolean copyFilePath(String str, String str2, String str3) {
//        try {
//            checkDirFound(str3);
//            File file = new File(str);
//            if (!file.exists()) {
//                return false;
//            }
//            FileInputStream fileInputStream = new FileInputStream(file);
//            FileOutputStream fileOutputStream = new FileOutputStream(str3 + "/" + str2);
//            copyFile(fileInputStream, fileOutputStream);
//            fileInputStream.close();
//            fileOutputStream.close();
//            return true;
//        } catch (Exception e2) {
//            androidx.fragment.app.o.b(e2, androidx.activity.result.c.a("3333333", str, " "), "copyFilePatheevv");
//            return false;
//        }
//    }
//
//    public static void copyFolder(Activity activity, String str, boolean z2) {
//        String[] strArr;
//        boolean z3;
//        try {
//            AssetManager assets = activity.getAssets();
//            String externalStorageState = Environment.getExternalStorageState();
//            if ("mounted".equals(externalStorageState)) {
//                try {
//                    strArr = assets.list(str);
//                } catch (IOException e2) {
//                    Log.e("ERROR", "Failed to get asset file list.", e2);
//                    strArr = null;
//                }
//                for (String str2 : strArr) {
//                    File file = new File(getDirProgram());
//                    if (!file.exists()) {
//                        file.mkdir();
//                    }
//                    File file2 = new File(getDirProgram() + "/" + str);
//                    if (!file2.exists()) {
//                        z3 = file2.mkdir();
//                    } else if (z2) {
//                        return;
//                    } else {
//                        z3 = true;
//                    }
//                    if (z3) {
//                        try {
//                            InputStream open = assets.open(str + "/" + str2);
//                            FileOutputStream fileOutputStream = new FileOutputStream(getDirProgram() + "/" + str + "/" + str2);
//                            MyLog("WEBVIEW", getDirProgram() + "/" + str + "/" + str2);
//                            copyFile(open, fileOutputStream);
//                            open.close();
//                            fileOutputStream.flush();
//                            fileOutputStream.close();
//                        } catch (IOException e3) {
//                            Log.e("ERROR", "Failed to copy asset file: " + str2, e3);
//                        }
//                    }
//                }
//                return;
//            }
//            "mounted_ro".equals(externalStorageState);
//        } catch (Exception e4) {
//            Log.e("ERROR", "copy file ", e4);
//            e4.printStackTrace();
//        }
//    }
//
//    public static void copyFolderWithCheckHasFiles(Activity activity, String str) {
//        String[] strArr;
//        boolean z2;
//        try {
//            AssetManager assets = activity.getAssets();
//            String externalStorageState = Environment.getExternalStorageState();
//            if ("mounted".equals(externalStorageState)) {
//                try {
//                    strArr = assets.list(str);
//                } catch (IOException e2) {
//                    Log.e("ERROR", "Failed to get asset file list.", e2);
//                    strArr = null;
//                }
//                File file = new File(getDirProgram());
//                if (!file.exists()) {
//                    file.mkdir();
//                }
//                File file2 = new File(getDirProgram() + "/" + str);
//                if (!file2.exists()) {
//                    z2 = file2.mkdir();
//                } else {
//                    z2 = true;
//                }
//                if (file2.listFiles().length > 0) {
//                    return;
//                }
//                for (String str2 : strArr) {
//                    if (z2) {
//                        try {
//                            InputStream open = assets.open(str + "/" + str2);
//                            FileOutputStream fileOutputStream = new FileOutputStream(getDirProgram() + "/" + str + "/" + str2);
//                            MyLog("WEBVIEW", getDirProgram() + "/" + str + "/" + str2);
//                            copyFile(open, fileOutputStream);
//                            open.close();
//                            fileOutputStream.flush();
//                            fileOutputStream.close();
//                        } catch (IOException e3) {
//                            Log.e("ERROR", "Failed to copy asset file: " + str2, e3);
//                        }
//                    }
//                }
//                return;
//            }
//            "mounted_ro".equals(externalStorageState);
//        } catch (Exception e4) {
//            Log.e("ERROR", "copy file ", e4);
//            e4.printStackTrace();
//        }
//    }
//
//    public static void copyTakbeer(Activity activity, int i2, String str) {
//        boolean z2;
//        try {
//            File file = new File(getDirProgram() + "/" + dirAudioAlarm);
//            if (!file.exists()) {
//                z2 = file.mkdir();
//            } else {
//                z2 = true;
//            }
//            if (!z2) {
//                return;
//            }
//            InputStream openRawResource = activity.getResources().openRawResource(i2);
//            FileOutputStream fileOutputStream = new FileOutputStream(getDirProgram() + "/" + dirAudioAlarm + "/" + str);
//            byte[] bArr = new byte[1024];
//            while (true) {
//                try {
//                    int read = openRawResource.read(bArr);
//                    if (read > 0) {
//                        fileOutputStream.write(bArr, 0, read);
//                    } else {
//                        openRawResource.close();
//                        fileOutputStream.close();
//                        return;
//                    }
//                } catch (Throwable th) {
//                    openRawResource.close();
//                    fileOutputStream.close();
//                    throw th;
//                }
//            }
//        } catch (IOException e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void copyTakbeer1(Activity activity, int i2, String str) {
//        boolean z2;
//        try {
//            File file = new File(getExternalStorageDir() + "/media/" + dirProgram);
//            if (!file.exists()) {
//                z2 = file.mkdir();
//            } else {
//                z2 = true;
//            }
//            if (!z2) {
//                return;
//            }
//            InputStream openRawResource = activity.getResources().openRawResource(i2);
//            FileOutputStream fileOutputStream = new FileOutputStream(getExternalStorageDir() + "/media/" + dirProgram + "/" + str);
//            byte[] bArr = new byte[1024];
//            while (true) {
//                try {
//                    int read = openRawResource.read(bArr);
//                    if (read > 0) {
//                        fileOutputStream.write(bArr, 0, read);
//                    } else {
//                        openRawResource.close();
//                        fileOutputStream.close();
//                        File file2 = new File(getExternalStorageDir().getAbsolutePath() + "/media/" + dirProgram, str);
//                        ContentResolver contentResolver = activity.getContentResolver();
//                        ContentValues contentValues = new ContentValues();
//                        contentValues.put("_data", file2.getAbsolutePath());
//                        contentValues.put("title", "_" + getFileNameWithoutExt(str));
//                        contentValues.put("mime_type", "audio/mp3");
//                        contentValues.put("_size", Long.valueOf(file2.length()));
//                        contentValues.put("artist", Integer.valueOf(com.alawail.alarm.prayertimes_mobile.R.string.app_name));
//                        contentValues.put("album", dirProgram);
//                        Boolean bool = Boolean.TRUE;
//                        contentValues.put("is_ringtone", bool);
//                        contentValues.put("is_notification", bool);
//                        contentValues.put("is_alarm", bool);
//                        contentValues.put("is_music", Boolean.FALSE);
//                        contentResolver.insert(MediaStore.Audio.Media.getContentUriForPath(file2.getAbsolutePath()), contentValues);
//                        try {
//                            RingtoneManager.getValidRingtoneUri(activity);
//                            return;
//                        } catch (Throwable th) {
//                            Log.e("sanjay in catch", "catch exception" + th.getMessage());
//                            return;
//                        }
//                    }
//                } catch (Throwable th2) {
//                    openRawResource.close();
//                    fileOutputStream.close();
//                    throw th2;
//                }
//            }
//        } catch (IOException e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void createAlarmAll(String str, Context context) {
//        String str2;
//        String str3;
//        if (PrayerTimesApplication.checkMobileEdition()) {
//            return;
//        }
//        try {
//            Database database = Database.getInstance(context);
//            JSONArray jSONArray = new JSONArray(str);
//            if (jSONArray.length() > 0) {
//                database.deleteAlarms(Alarm.Type.NORMAL, Alarm.Type.SCREEN, Alarm.Type.SCREEN_OFF, Alarm.Type.SCREEN_ON, Alarm.Type.SCREEN_OFF_TIME, Alarm.Type.SCREEN_ON_TIME, Alarm.Type.QURAN_ALARM, Alarm.Type.QURAN_KHATMAH_ALARM, Alarm.Type.PRAYER_BEFORE_AFTER_ALARM, Alarm.Type.SUHOOR_ALARM, Alarm.Type.AZKAR_ALARM, Alarm.Type.EVENT_ALARM, Alarm.Type.PRAYER_SCREEN_OFF_ALARM, Alarm.Type.PRAYER_SCREEN_ON_ALARM);
//            }
//            int i2 = 0;
//            while (i2 < jSONArray.length()) {
//                JSONArray jSONArray2 = jSONArray.getJSONArray(i2);
//                String str4 = "";
//                Database database2 = database;
//                JSONArray jSONArray3 = jSONArray;
//                int i3 = i2;
//                String str5 = "";
//                String str6 = str5;
//                String str7 = str6;
//                String str8 = str7;
//                String str9 = str8;
//                String str10 = str9;
//                String str11 = str10;
//                String str12 = str11;
//                String str13 = str12;
//                String str14 = str13;
//                String str15 = str14;
//                String str16 = str15;
//                String str17 = str16;
//                String str18 = str17;
//                String str19 = str18;
//                String str20 = str19;
//                int i4 = 0;
//                boolean z2 = false;
//                boolean z3 = false;
//                boolean z4 = false;
//                while (i4 < jSONArray2.length()) {
//                    JSONObject jSONObject = jSONArray2.getJSONObject(i4);
//                    JSONArray jSONArray4 = jSONArray2;
//                    String next = jSONObject.keys().next();
//                    String str21 = str5;
//                    String str22 = str7;
//                    if (next.toUpperCase().equals("TIME")) {
//                        str12 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("DAYS")) {
//                        str6 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("NAME")) {
//                        str4 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("TYPE")) {
//                        str17 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("TYPE_2")) {
//                        str18 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("PRAYER_TIME_STR")) {
//                        str2 = jSONObject.optString(next);
//                    } else {
//                        str2 = str22;
//                    }
//                    String str23 = str6;
//                    if (next.toUpperCase().equals("DATE_STR")) {
//                        str3 = jSONObject.optString(next);
//                    } else {
//                        str3 = str21;
//                    }
//                    String str24 = str3;
//                    if (next.toUpperCase().equals("QURAN_STR")) {
//                        str20 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("ENABLED_SOUND_TAKBEER")) {
//                        z4 = jSONObject.optString(next).equals(SettingsActivity_2.CityType_1);
//                    }
//                    String str25 = str2;
//                    if (next.toUpperCase().equals("PRAYER_ALARMS_STR")) {
//                        str19 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("SCREEN_FILE")) {
//                        str13 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("SCREEN_TYPE")) {
//                        str14 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("SCREEN_MANAGEMENT_TYPE")) {
//                        str15 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("POWER_SAVE")) {
//                        str16 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("TXT_CONTENT")) {
//                        str9 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("TXT_CONTENT_CUSTOM")) {
//                        str10 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("TXT_WINDOW_SHOW")) {
//                        z2 = jSONObject.optString(next).equals(SettingsActivity_2.CityType_1);
//                    }
//                    if (next.toUpperCase().equals("TXT_WINDOW_CLOSE_AFTER")) {
//                        str11 = jSONObject.optString(next);
//                    }
//                    if (next.toUpperCase().equals("PRIORITY_HIGH")) {
//                        z3 = jSONObject.optString(next).equals(SettingsActivity_2.CityType_1);
//                    }
//                    if (next.toUpperCase().equals("TONE")) {
//                        str8 = jSONObject.optString(next);
//                    }
//                    i4++;
//                    str5 = str24;
//                    jSONArray2 = jSONArray4;
//                    str7 = str25;
//                    str6 = str23;
//                }
//                String str26 = str5;
//                Alarm alarm = new Alarm();
//                alarm.setAlarmName(str4);
//                alarm.setAlarmTonePath(str8);
//                alarm.setAlarmTxtContent(str9);
//                alarm.setAlarmTxtContentCustom(str10);
//                alarm.setAlarmTextWindowShow(Boolean.valueOf(z2));
//                alarm.setAlarmTextWindowCloseAfter(strToInt(str11));
//                alarm.setAlarmPriorityHigh(Boolean.valueOf(z3));
//                alarm.setAlarmTime(str12);
//                alarm.setScreenFile(str13);
//                alarm.setScreenType(ScreenType.values()[strToInt(str14)]);
//                alarm.setScreenManagementType(ScreenManagementType.values()[strToInt(str15)]);
//                alarm.setPowerSave(PowerSave.values()[strToInt(str16)]);
//                alarm.setAlarmEnabledSoundTakbeer(Boolean.valueOf(z4));
//                alarm.setDays(alarm.daysToArray(str6));
//                alarm.setType(Alarm.Type.values()[strToInt(str17)]);
//                alarm.setType_2(Alarm.Type_2.values()[strToInt(str18)]);
//                alarm.setPrayer_time_str(str7);
//                if (!alarm.getPrayer_time_str().equalsIgnoreCase(Alarm.NONE_STR)) {
//                    String[] split = alarm.getPrayer_time_str().split(",");
//                    alarm.setPrayerTimeDlgAlarm(new PrayerTimeDlgAlarm(PrayerTimeDlgAlarm.PrayerTimeDlgEnum.fromInt(strToInt(split[0])), strToInt(split[1])));
//                }
//                alarm.setDate_str(str26);
//                if (!alarm.getDate_str().equalsIgnoreCase(Alarm.NONE_STR)) {
//                    String[] split2 = alarm.getDate_str().split(",");
//                    alarm.setAlarmDate(new AlarmDate(AlarmDate.AlarmDateEnum.fromInt(strToInt(split2[0])), split2[1]));
//                }
//                alarm.setQuran_str(str20);
//                if (!alarm.getQuran_str().equalsIgnoreCase(Alarm.NONE_STR)) {
//                    alarm.setQuranDlgAlarm(new QuranDlgAlarm(alarm.getQuran_str()));
//                }
//                alarm.setPrayerAlarms_str(str19);
//                MyLog("checkAlarmTime", "checkAlarmTime getAll 17");
//                database2.create(database2.checkAlarmTime(alarm));
//                i2 = i3 + 1;
//                database = database2;
//                jSONArray = jSONArray3;
//            }
//            JSONArray jSONArray5 = jSONArray;
//            Database database3 = database;
//            if (jSONArray5.length() > 0) {
//                database3.close();
//                startAlarmOnBootBroadcastReciever(context);
//            }
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void createAlarmPrayerTime12Night(Context context, List<String> list, boolean z2, Calendar calendar, City city) {
//        PrayerTimesApplication.set_jomo3a_today();
//        if (!PrayerTimesApplication.Need_createAlarmPrayerTime && !PrayerTimesApplication.RequiredRestartOnResume_MainActivity) {
//            return;
//        }
//        MyLog("AzkarAlarms", "AzkarAlarms 2");
//        if (PrayerTimesApplication.Need_createAlarmPrayerTime) {
//            PrayerTimesApplication.Need_createAlarmPrayerTime = false;
//        }
//        MyLog("Init Alarm", "Init Alarm createAlarmPrayerTime12Night --- 2");
//        try {
//            Preference preference = new Manager(context).getPreference();
//            preference.fetchCurrentPreferences();
//            setSeasonPrayer(preference, calendar, city, "createAlarmPrayerTime12Night");
//            int powerSaveIsha = preference.getPowerSaveIsha();
//            int powerSaveFajr = preference.getPowerSaveFajr();
//            Database database = Database.getInstance(context);
//            Alarm.Type type = Alarm.Type.IKAMA;
//            List<Alarm> alarms = database.getAlarms(type);
//            Alarm.Type type2 = Alarm.Type.NECESSARY_12_NIGHT;
//            database.deleteAlarms(Alarm.Type.AZAN, type, type2, Alarm.Type.SCREEN_OFF, Alarm.Type.SCREEN_ON, Alarm.Type.FOR_TEST);
//            d(context, database, list, z2);
//            h(context, database, list, z2, alarms);
//            MyLog("checkAlarmTime", "checkAlarmTime getAll 28");
//            database.create(database.checkAlarmTime(Alarm.createAlarm(z2, "00:00:00 AM", context.getString(com.alawail.alarm.prayertimes_mobile.R.string.recreatePrayerTimeAfter12Night), type2, Alarm.Type_2.NORMAL)));
//            List<Alarm> all = database.getAll();
//            Iterator<Alarm> it = database.getAlarms_PrayerTimeDlg(all).iterator();
//            while (it.hasNext()) {
//                updateAlarmAfterBeforeAzan(it.next(), database, context, -999999999, all);
//            }
//            j(powerSaveIsha, powerSaveFajr, context, database, list, z2);
//            if (preference.get_turn_off_prayer_screen().booleanValue()) {
//                DellPrayerTimeScreenTurnAlarms(context);
//                AddPrayerTimeScreenTurnAlarmsAll(preference.get_turn_off_prayer_period(), preference.get_turn_off_prayer_period_jomo3a(), context, false);
//            }
//            k(preference, context, database, calendar);
//            Alarm.Type type3 = Alarm.Type.RESTART_ANDROID_TV_BOX;
//            database.deleteAlarms(type3);
//            if (PrayerTimesApplication.checkTvAgentEdition()) {
//                database.create(database.checkAlarmTime(Alarm.createAlarm(z2, "09:00:00 AM", context.getString(com.alawail.alarm.prayertimes_mobile.R.string.restart_android_tv), type3, Alarm.Type_2.NORMAL)));
//            }
//            e(preference, context, database);
//            i(preference, context, database);
//            c(preference, context, database);
//            f(context, database);
//            database.close();
//            preference.setCurrentAlarmID("null");
//            startAlarmOnBootBroadcastReciever(context);
//        } catch (Exception e2) {
//            androidx.fragment.app.o.b(e2, new StringBuilder(" Init Alarm Init Alarm createAlarmPrayerTime12Night --- "), "createPrayerTime12Night error");
//        }
//    }
//
//    public static void createAlarmPrayerTime12Night_today(Context context) {
//        Manager manager_Database;
//        boolean z2;
//        try {
//            City city = City.getCity(context);
//            if (city.isPrayerTimes_Expression()) {
//                manager_Database = new Manager(context);
//            } else {
//                manager_Database = new Manager_Database(context);
//            }
//            Preference preference = manager_Database.getPreference();
//            if (!city.isPrayerTimes_Expression()) {
//                preference = new Preference_Database(context);
//            }
//            preference.fetchCurrentPreferences();
//            Calendar calendar = Calendar.getInstance();
//            int i2 = calendar.get(5);
//            int i3 = calendar.get(2) + 1;
//            int i4 = calendar.get(1);
//            setSeasonPrayer(preference, calendar, city, "createAlarmPrayerTime12Night_today");
//            PrayerTimesApplication.date4PrayerTime = null;
//            PrayerTimesApplication.set_jomo3a_today();
//            if (preference.getHour12_24().type() == Hour.Type.hour24) {
//                z2 = false;
//            } else {
//                z2 = true;
//            }
//            PrayerTime_Database.INSTANCE.initPrayerTimes();
//            MyLog("date_", "date_ initPrayerTimes AlarmSirvice");
//            ArrayList<String> prayerTimes = manager_Database.getPrayerTimes(city, context, i2, i3, i4, false, PrayerTimesApplication.is_jomo3a_today);
//            PrayerTimesApplication.prayersListToday = prayerTimes;
//            city.ikama.setIsha_ikama(prayerTimes.get(10));
//            PrayerTimesApplication.Need_createAlarmPrayerTime = true;
//            createAlarmPrayerTime12Night(context, PrayerTimesApplication.prayersListToday, z2, calendar, city);
//            MyLog("check_GET_LOCATION_NEXT", "check_GET_LOCATION_NEXT createAlarmPrayerTime12Night ");
//            MyLog("check_GET_LOCATION_NEXT", "check_GET_LOCATION_NEXT updateWidget ");
//        } catch (Exception e2) {
//            androidx.fragment.app.o.b(e2, new StringBuilder(""), "createPrayerTime12Night check_GET_LOCATION_NEXT error");
//        }
//    }
//
//    public static void createAlarmPrayerTime12Night_today_background(boolean z2) {
//        boolean z3;
//        MyLog("check_GET_LOCATION_NEXT", "check_GET_LOCATION_NEXT createAlarmPrayerTime12Night_today_background ");
//        if (z2 && PrayerTimesApplication.MainActivity_Visible && PrayerTimesApplication.checkMobileEdition()) {
//            z3 = true;
//        } else {
//            z3 = false;
//        }
//        if (!z3 && PrayerTimesApplication.checkMobileEdition()) {
//            PrayerTimesApplication.PrayerTime12Night_restartApplication_onPauseMainActivity = true;
//        }
//        try {
//            Intent intent = new Intent(PrayerTimesApplication.getAppContext(), (Class<?>) NetServiceBackground.class);
//            intent.putExtra("action", NetServiceBackground.ActionType.createAlarmPrayerTime12Night_today_background.getAction());
//            intent.putExtra(NetServiceBackground.INSTANCE.isRestartApplication(), z3);
//            startService(PrayerTimesApplication.getAppContext(), intent);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void createAlarmVideoScreen(ArrayList<HashMap<String, String>> arrayList, Context context) {
//        int i2;
//        boolean z2;
//        try {
//            if (arrayList.size() > 0) {
//                Preference preference = new Manager(context).getPreference();
//                Database database = Database.getInstance(context);
//                database.deleteAlarms(Alarm.Type_2.VIDEO_REMOTE, Alarm.Type_2.VIDEO_REMOTE_NET, Alarm.Type_2.VIDEO_REMOTE_REPEAT, Alarm.Type_2.VIDEO_REMOTE_NET_REPEAT);
//                int i3 = 0;
//                for (int i4 = 0; i4 <= arrayList.size() && arrayList.size() != 1; i4++) {
//                    if (i4 == arrayList.size()) {
//                        i2 = 0;
//                        z2 = true;
//                    } else {
//                        i2 = i4;
//                        z2 = false;
//                    }
//                    HashMap<String, String> hashMap = arrayList.get(i2);
//                    String str = hashMap.get(GetDataService_TAG_NAME);
//                    int strToInt = strToInt(hashMap.get(GetDataService_TAG_DURATION));
//                    Alarm alarm = new Alarm();
//                    alarm.setAlarmName(getFileName(str));
//                    Alarm.Type_2 type_2 = Alarm.Type_2.VIDEO_REMOTE;
//                    alarm.setType_2(type_2);
//                    if (z2) {
//                        alarm.setType_2(Alarm.Type_2.VIDEO_REMOTE_REPEAT);
//                    }
//                    alarm.setScreenType(ScreenType.VIDEOS);
//                    ScreenManagementType screenManagementType = preference.getScreenManagementType();
//                    ScreenManagementType screenManagementType2 = ScreenManagementType.REMOTE;
//                    if (screenManagementType == screenManagementType2) {
//                        alarm.setScreenManagementType(screenManagementType2);
//                        alarm.setType_2(type_2);
//                        if (z2) {
//                            alarm.setType_2(Alarm.Type_2.VIDEO_REMOTE_REPEAT);
//                        }
//                    } else {
//                        ScreenManagementType screenManagementType3 = preference.getScreenManagementType();
//                        ScreenManagementType screenManagementType4 = ScreenManagementType.REMOTE_NET;
//                        if (screenManagementType3 == screenManagementType4) {
//                            alarm.setScreenManagementType(screenManagementType4);
//                            alarm.setType_2(Alarm.Type_2.VIDEO_REMOTE_NET);
//                            if (z2) {
//                                alarm.setType_2(Alarm.Type_2.VIDEO_REMOTE_NET_REPEAT);
//                            }
//                        }
//                    }
//                    alarm.setType(Alarm.Type.SCREEN);
//                    alarm.setScreenFile(str);
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.add(13, i3);
//                    alarm.setAlarmTime(calendar);
//                    i3 += strToInt;
//                    MyLog("checkAlarmTime", "checkAlarmTime getAll 16");
//                    database.create(database.checkAlarmTime(alarm));
//                }
//                database.close();
//                startAlarmOnBootBroadcastReciever(context);
//            }
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }
//
//    public static void createFolder(String str) {
//        File file = new File(getDirProgram());
//        if (!file.exists()) {
//            file.mkdir();
//            androidx.fragment.app.c.b(new StringBuilder(" "), dirProgram, "createFolder00");
//        }
//        File file2 = new File(getDirProgram() + "/" + str);
//        if (!file2.exists()) {
//            file2.mkdir();
//            MyLog("createFolder00", " " + dirProgram + "/" + str);
//        }
//    }
//
//    public static String createNextRandomRequestAlarm(Context context, int i2) {
//        String str = "2020-01-22-01:01:01";
//        try {
//            Database database = Database.getInstance(context);
//            Alarm.Type type = Alarm.Type.RANDOM_REQUEST_ALARM;
//            database.deleteAlarms(type);
//            Alarm alarm = new Alarm();
//            alarm.setAlarmName("RANDOM_REQUEST");
//            alarm.setType(type);
//            Calendar calendar = Calendar.getInstance();
//            calendar.add(13, i2);
//            int i3 = calendar.get(11);
//            int i4 = calendar.get(12);
//            int i5 = calendar.get(13);
//            Locale locale = Locale.ENGLISH;
//            String format = new SimpleDateFormat("yyyy-MM-dd", locale).format(calendar.getTime());
//            String[] split = format.split(HelpFormatter.DEFAULT_OPT_PREFIX);
//            alarm.setDateMiladi(strToInt(split[0], 2020), strToInt(split[1], 1), strToInt(split[2], 1));
//            Calendar calendar2 = Calendar.getInstance();
//            calendar2.set(11, i3);
//            calendar2.set(12, i4);
//            calendar2.set(13, i5);
//            calendar2.set(14, 0);
//            alarm.setAlarmTime(calendar2);
//            MyLog("checkAlarmTime", "checkAlarmTime getAll 20");
//            Alarm checkAlarmTime = database.checkAlarmTime(alarm);
//            str = format + HelpFormatter.DEFAULT_OPT_PREFIX + new SimpleDateFormat("HH:mm:ss", locale).format(checkAlarmTime.getAlarmTime().getTime());
//            database.create(checkAlarmTime);
//            database.close();
//            startAlarmOnBootBroadcastReciever(context);
//            MyLog("screen_newszzz", "showNotificationUpdateDataActualCity_background createRandomRequestAlarm " + format + "  -- " + i3 + ":" + i4 + ":" + i5);
//            return str;
//        } catch (Exception e2) {
//            e2.printStackTrace();
//            return str;
//        }
//    }

//    public static JSONArray cur2Json(Cursor cursor) {
//        JSONArray jSONArray = new JSONArray();
//        cursor.moveToFirst();
//        while (!cursor.isAfterLast()) {
//            int columnCount = cursor.getColumnCount();
//            MyLog("doNextRandomRequest", "getJSONArrayFromCursor " + columnCount);
//            JSONObject jSONObject = new JSONObject();
//            for (int i2 = 0; i2 < columnCount; i2++) {
//                if (cursor.getColumnName(i2) != null) {
//                    try {
//                        jSONObject.put(cursor.getColumnName(i2), cursor.getString(i2));
//                        MyLog("doNextRandomRequest", "getJSONArrayFromCursor oooo");
//                    } catch (Exception e2) {
//                        androidx.fragment.app.q.c(e2, new StringBuilder("getJSONArrayFromCursor eeee "), "doNextRandomRequest");
//                    }
//                }
//            }
//            jSONArray.put(jSONObject);
//            cursor.moveToNext();
//        }
//        cursor.close();
//        return jSONArray;
//    }

//    private static void d(Context context, Database database, List<String> list, boolean z2) {
//        MyLog("checkAlarmTime", "checkAlarmTime getAll 27");
//        String str = list.get(0);
//        StringBuilder sb = new StringBuilder();
//        Alarm.Type type = Alarm.Type.AZAN;
//        sb.append(type.toString());
//        sb.append(" ");
//        sb.append(context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerFajr));
//        database.create(database.checkAlarmTime(Alarm.createAlarm(z2, str, sb.toString(), type, Alarm.Type_2.AZAN_Fajr)));
//        if (!PrayerTimesApplication.is_jomo3a_today) {
//            database.create(database.checkAlarmTime(Alarm.createAlarm(z2, list.get(1), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhr), type, Alarm.Type_2.AZAN_Dhuhr)));
//        } else {
//            database.create(database.checkAlarmTime(Alarm.createAlarm(z2, list.get(1), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerJomo3a), type, Alarm.Type_2.AZAN_Jomo3a)));
//        }
//        database.create(database.checkAlarmTime(Alarm.createAlarm(z2, list.get(2), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerAsr), type, Alarm.Type_2.AZAN_Asr)));
//        database.create(database.checkAlarmTime(Alarm.createAlarm(z2, list.get(3), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerMaghrib), type, Alarm.Type_2.AZAN_Maghrib)));
//        database.create(database.checkAlarmTime(Alarm.createAlarm(z2, list.get(4), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerIsha), type, Alarm.Type_2.AZAN_Isha)));
//        database.create(database.checkAlarmTime(Alarm.createAlarm(z2, list.get(5), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerShuruq), type, Alarm.Type_2.AZAN_Shuruq)));
//    }

    public static Calendar getCalendar(int i2, int i3, int i4) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(1, i2);
        calendar.set(2, i3);
        calendar.set(5, i4);
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        return calendar;
    }

//    public static void delClockImgs(String str, String str2, String str3) {
//        try {
//            delFile(new File(getDirBackground() + "/" + str));
//            delFile(new File(getDirBackground() + "/" + str2));
//            delFile(new File(getDirBackground() + "/" + str3));
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

    public static String getDateFromMilliSeconds(long j2, String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str, Locale.ENGLISH);
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(j2);
        return simpleDateFormat.format(calendar.getTime());
    }

    public static String getDeviceName() {
        String str = Build.MANUFACTURER;
        String str2 = Build.MODEL;
        if (str2.startsWith(str)) {
            return l(str2);
        }
        return l(str) + " " + str2;
    }

//    public static void delQuran_KHATMAH(Context context) {
//        Preference preference = new Preference(context);
//        preference.setQuran_date_bagin(Alarm.NONE_STR);
//        preference.setQuran_time(Alarm.NONE_STR);
//        preference.setQuran_times(Alarm.NONE_STR);
//        preference.setQuran_parts(0);
//        preference.setQuran_time_prayer(Alarm.NONE_STR);
//        preference.setQuran_quarters(0);
//        Database database = Database.getInstance(context);
//        database.deleteAlarms(Alarm.Type.QURAN_KHATMAH_ALARM);
//        database.close();
//    }

    public static String getFileName(String str) {
        if (str.lastIndexOf("/") == -1) {
            return str;
        }
        return str.substring(str.lastIndexOf("/") + 1);
    }

    public static String getFileNameWithoutExt(String str) {
        if (str.indexOf(".") > 0) {
            return str.substring(0, str.lastIndexOf("."));
        }
        return str;
    }

    public static String getFileNameWithoutExt2(String str) {
        String fileName = getFileName(str);
        if (fileName.indexOf(".") > 0) {
            return fileName.substring(0, fileName.lastIndexOf("."));
        }
        return fileName;
    }

//    public static void doNextJob(Context context, String str) {
//        MyLog("doNextJob00", "doNextJob mobile " + str);
//        Intent intent = new Intent(PrayerTimesApplication.getAppContext(), (Class<?>) NetServiceBackground.class);
//        intent.putExtra("action", NetServiceBackground.ActionType.doNextJobAlarm.getAction());
//        startService(PrayerTimesApplication.getAppContext(), intent);
//    }
//
//    public static void doNextRandomRequest(Context context, City city) {
//        if (city.isPrayerTimes_Expression()) {
//            return;
//        }
//        MyLog("screen_newszzz", "showNotificationUpdateDataActualCity_background MyTool ");
//        if (((ConnectivityManager) context.getSystemService("connectivity")).getActiveNetworkInfo() != null) {
//            MyLog("update_data_actual_city", " activeNetworkInfo ok ");
//            MyLog("screen_newszzz", " activeNetworkInfo ok ");
//            MyLog("update_data_actual_city", " activeNetworkInfo isInternetAvailable ");
//            MyLog("screen_newszzz", " activeNetworkInfo isInternetAvailable ");
//            MyLog("DataNetService", "start : showNotificationUpdateDataActualCity_background MyTool");
//            MyLog("screen_newszzz", "start : showNotificationUpdateDataActualCity_background MyTool");
//            new DataNetAction(context, DataNetAction.Actions.insert_city_or_city_prayer.name());
//            MyLog("update_data_actual_city", " dataNetService start");
//            MyLog("screen_newszzz", " dataNetService start");
//        }
//    }
//
//    public static void download_file(String str, Activity activity, int i2) {
//        ProgressBar progressBar = (ProgressBar) activity.findViewById(i2);
//        Ion.with(activity).load(androidx.constraintlayout.core.motion.key.a.a("http://192.168.1.101/upload_test/uploads/", str)).progressBar(progressBar).progress(new h0()).write(new File(getDirLayouts() + "/" + str)).setCallback(new g0(progressBar));
//    }
//
//    public static Bitmap drawTextToBitmap(Context context, int i2, int i3, String str) {
//        Resources resources = context.getResources();
//        float f2 = resources.getDisplayMetrics().density;
//        Bitmap decodeResource = BitmapFactory.decodeResource(resources, i2);
//        Bitmap.Config config = decodeResource.getConfig();
//        if (config == null) {
//            config = Bitmap.Config.ARGB_8888;
//        }
//        Bitmap copy = decodeResource.copy(config, true);
//        Canvas canvas = new Canvas(copy);
//        Paint paint = new Paint(1);
//        paint.setColor(-1);
//        paint.setTextSize(i3 * f2);
//        paint.getTextBounds(str, 0, str.length(), new Rect());
//        float width = (copy.getWidth() - r1.width()) / 2.0f;
//        float height = (r1.height() + copy.getHeight()) / 1.5f;
//        MyLog("text1", "" + width + " " + height + " " + i3);
//        Typeface typefaceDefaultApp = getTypefaceDefaultApp(context);
//        paint.setTypeface(Typeface.DEFAULT_BOLD);
//        paint.setTypeface(typefaceDefaultApp);
//        canvas.drawText(str, width, height, paint);
//        return copy;
//    }
//
//    private static void e(Preference preference, Context context, Database database) {
//        database.deleteAlarms(Alarm.Type.AZKAR_BEGIN, Alarm.Type.AZKAR_END);
//        if (preference.isAutoAzkarEnableed()) {
//            preference.getSilentStart();
//            preference.getSilentDuration();
//            preference.getStrPreferenceVal(HelpFormatter.DEFAULT_OPT_PREFIX, "azkar_fajr_img");
//            if (preference.getEnableAzkarFajr()) {
//                int azkarStartFajr = preference.getAzkarStartFajr();
//                int azkarDurationFajr = preference.getAzkarDurationFajr();
//                String strPreferenceVal = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_fajr_img");
//                Alarm alarm = database.getAlarm(Alarm.Type_2.IKAMA_Fajr);
//                String str = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerFajr);
//                if (alarm != null) {
//                    n(context, azkarStartFajr, azkarDurationFajr, alarm.getAlarmTime(), strPreferenceVal, str, database);
//                }
//            }
//            if (preference.getEnableAzkarShuruq()) {
//                int azkarStartShuruq = preference.getAzkarStartShuruq();
//                int azkarDurationShuruq = preference.getAzkarDurationShuruq();
//                String strPreferenceVal2 = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_shuruq_img");
//                Alarm alarm2 = database.getAlarm(Alarm.Type_2.IKAMA_Shuruq);
//                String str2 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.duhaatxt);
//                if (alarm2 != null) {
//                    n(context, azkarStartShuruq, azkarDurationShuruq, alarm2.getAlarmTime(), strPreferenceVal2, str2, database);
//                }
//            }
//            if (preference.getEnableAzkarDhuhr() && !PrayerTimesApplication.is_jomo3a_today) {
//                int azkarStartDhuhr = preference.getAzkarStartDhuhr();
//                int azkarDurationDhuhr = preference.getAzkarDurationDhuhr();
//                String strPreferenceVal3 = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_dhuhr_img");
//                MyLog("azkar_file01", "Dhuhr " + strPreferenceVal3);
//                Alarm alarm3 = database.getAlarm(Alarm.Type_2.IKAMA_Dhuhr);
//                String str3 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhr);
//                if (alarm3 != null) {
//                    n(context, azkarStartDhuhr, azkarDurationDhuhr, alarm3.getAlarmTime(), strPreferenceVal3, str3, database);
//                }
//            }
//            if (preference.getEnableAzkarJomo3a() && PrayerTimesApplication.is_jomo3a_today) {
//                int azkarStartJomo3a = preference.getAzkarStartJomo3a();
//                int azkarDurationJomo3a = preference.getAzkarDurationJomo3a();
//                String strPreferenceVal4 = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_jomo3a_img");
//                Alarm alarm4 = database.getAlarm(Alarm.Type_2.IKAMA_Jomo3a);
//                String str4 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerJomo3a);
//                if (alarm4 != null) {
//                    n(context, azkarStartJomo3a, azkarDurationJomo3a, alarm4.getAlarmTime(), strPreferenceVal4, str4, database);
//                }
//            }
//            if (preference.getEnableAzkarAsr()) {
//                int azkarStartAsr = preference.getAzkarStartAsr();
//                int azkarDurationAsr = preference.getAzkarDurationAsr();
//                String strPreferenceVal5 = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_asr_img");
//                Alarm alarm5 = database.getAlarm(Alarm.Type_2.IKAMA_Asr);
//                String str5 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerAsr);
//                if (alarm5 != null) {
//                    n(context, azkarStartAsr, azkarDurationAsr, alarm5.getAlarmTime(), strPreferenceVal5, str5, database);
//                }
//            }
//            if (preference.getEnableAzkarMaghrib()) {
//                int azkarStartMaghrib = preference.getAzkarStartMaghrib();
//                int azkarDurationMaghrib = preference.getAzkarDurationMaghrib();
//                String strPreferenceVal6 = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_maghrib_img");
//                Alarm alarm6 = database.getAlarm(Alarm.Type_2.IKAMA_Maghrib);
//                String str6 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerMaghrib);
//                if (alarm6 != null) {
//                    n(context, azkarStartMaghrib, azkarDurationMaghrib, alarm6.getAlarmTime(), strPreferenceVal6, str6, database);
//                }
//            }
//            if (preference.getEnableAzkarIsha()) {
//                int azkarStartIsha = preference.getAzkarStartIsha();
//                int azkarDurationIsha = preference.getAzkarDurationIsha();
//                String strPreferenceVal7 = preference.getStrPreferenceVal("azkar_file01***raw***", "azkar_isha_img");
//                Alarm alarm7 = database.getAlarm(Alarm.Type_2.IKAMA_Isha);
//                String str7 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerIsha);
//                if (alarm7 != null) {
//                    n(context, azkarStartIsha, azkarDurationIsha, alarm7.getAlarmTime(), strPreferenceVal7, str7, database);
//                }
//            }
//        }
//    }
//
//    public static void exportDB(Context context, String str) {
//        File externalStorageDir = getExternalStorageDir();
//        Environment.getDataDirectory();
//        File file = new File(DatabaseHelper.DB_PATH + DatabaseHelper.DB_NAME);
//        File file2 = new File(externalStorageDir, str);
//        try {
//            FileChannel channel = new FileInputStream(file).getChannel();
//            FileChannel channel2 = new FileOutputStream(file2).getChannel();
//            channel2.transferFrom(channel, 0L, channel.size());
//            channel.close();
//            channel2.close();
//            MyToast(context, "DB Exported!", 1);
//        } catch (IOException e2) {
//            MyToast(context, "DB Exported! Error \r\n" + e2.getMessage(), 1);
//            e2.printStackTrace();
//        }
//    }
//
//    private static void f(Context context, Database database) {
//        if (PrayerTimesApplication.checkTvAgentEdition()) {
//            List<Alarm> alarms = database.getAlarms(Alarm.Type.NORMAL);
//            Database database2 = Database.getInstance(context);
//            database2.deleteAlarms(Alarm.Type.CLOSE_ALARM_TXT_CONTENT);
//            database2.close();
//            for (Alarm alarm : alarms) {
//                if (alarm.getAlarmTextWindowShow().booleanValue()) {
//                    CreateCloseAlarmTxtContent2(context, alarm.getAlarmTextWindowCloseAfter(), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.CloseAlarmTxtContent), alarm.getAlarmTime(), alarm.getId());
//                }
//            }
//        }
//    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x0056, code lost:

        if (r6.getAlarmType_2() == com.alawail.prayertimes.alarm.Alarm.Type_2.IKAMA_Dhuhr) goto L56;
     */
    /* JADX WARN: Removed duplicated region for block: B:16:0x005b A[Catch: Exception -> 0x00a3, TryCatch #0 {Exception -> 0x00a3, blocks: (B:2:0x0000, B:3:0x0004, B:5:0x000b, B:8:0x001b, B:9:0x0041, B:11:0x0045, B:13:0x004b, B:16:0x005b, B:17:0x005f, B:19:0x0065, B:22:0x0071, B:25:0x0094, B:30:0x0050), top: B:1:0x0000 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
//    private static void g(java.util.List<com.alawail.prayertimes.alarm.Alarm> r5, com.alawail.prayertimes.alarm.Alarm r6, com.alawail.prayertimes.alarm.database.Database r7) {
//        /*
//            java.util.Iterator r0 = r5.iterator()     // Catch: java.lang.Exception -> La3
//        L4:
//            boolean r1 = r0.hasNext()     // Catch: java.lang.Exception -> La3
//            r2 = 1
//            if (r1 == 0) goto L40
//            java.lang.Object r1 = r0.next()     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm r1 = (com.alawail.prayertimes.alarm.Alarm) r1     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r3 = r6.getAlarmType_2()     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r4 = r1.getAlarmType_2()     // Catch: java.lang.Exception -> La3
//            if (r3 != r4) goto L4
//            java.lang.String r0 = r1.getAlarmTxtContent()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTxtContent(r0)     // Catch: java.lang.Exception -> La3
//            java.lang.String r0 = r1.getAlarmTxtContentCustom()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTxtContentCustom(r0)     // Catch: java.lang.Exception -> La3
//            java.lang.Boolean r0 = r1.getAlarmTextWindowShow()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTextWindowShow(r0)     // Catch: java.lang.Exception -> La3
//            int r0 = r1.getAlarmTextWindowCloseAfter()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTextWindowCloseAfter(r0)     // Catch: java.lang.Exception -> La3
//            java.lang.String r0 = r1.getPrayerAlarms_str()     // Catch: java.lang.Exception -> La3
//            r6.setPrayerAlarms_str(r0)     // Catch: java.lang.Exception -> La3
//            r0 = 1
//            goto L41
//        L40:
//            r0 = 0
//        L41:
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r1 = com.alawail.prayertimes.alarm.Alarm.Type_2.IKAMA_Jomo3a     // Catch: java.lang.Exception -> La3
//            if (r0 != 0) goto L4e
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r3 = r6.getAlarmType_2()     // Catch: java.lang.Exception -> La3
//            if (r3 != r1) goto L4e
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r1 = com.alawail.prayertimes.alarm.Alarm.Type_2.IKAMA_Dhuhr     // Catch: java.lang.Exception -> La3
//            goto L58
//        L4e:
//            if (r0 != 0) goto L59
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r3 = r6.getAlarmType_2()     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r4 = com.alawail.prayertimes.alarm.Alarm.Type_2.IKAMA_Dhuhr     // Catch: java.lang.Exception -> La3
//            if (r3 != r4) goto L59
//        L58:
//            r2 = r0
//        L59:
//            if (r2 != 0) goto L94
//            java.util.Iterator r5 = r5.iterator()     // Catch: java.lang.Exception -> La3
//        L5f:
//            boolean r0 = r5.hasNext()     // Catch: java.lang.Exception -> La3
//            if (r0 == 0) goto L94
//            java.lang.Object r0 = r5.next()     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm r0 = (com.alawail.prayertimes.alarm.Alarm) r0     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm$Type_2 r2 = r0.getAlarmType_2()     // Catch: java.lang.Exception -> La3
//            if (r1 != r2) goto L5f
//            java.lang.String r5 = r0.getAlarmTxtContent()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTxtContent(r5)     // Catch: java.lang.Exception -> La3
//            java.lang.String r5 = r0.getAlarmTxtContentCustom()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTxtContentCustom(r5)     // Catch: java.lang.Exception -> La3
//            java.lang.Boolean r5 = r0.getAlarmTextWindowShow()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTextWindowShow(r5)     // Catch: java.lang.Exception -> La3
//            int r5 = r0.getAlarmTextWindowCloseAfter()     // Catch: java.lang.Exception -> La3
//            r6.setAlarmTextWindowCloseAfter(r5)     // Catch: java.lang.Exception -> La3
//            java.lang.String r5 = r0.getPrayerAlarms_str()     // Catch: java.lang.Exception -> La3
//            r6.setPrayerAlarms_str(r5)     // Catch: java.lang.Exception -> La3
//        L94:
//            java.lang.String r5 = "checkAlarmTime"
//            java.lang.String r0 = "checkAlarmTime getAll 18"
//            MyLog(r5, r0)     // Catch: java.lang.Exception -> La3
//            com.alawail.prayertimes.alarm.Alarm r5 = r7.checkAlarmTime(r6)     // Catch: java.lang.Exception -> La3
//            r7.create(r5)     // Catch: java.lang.Exception -> La3
//            goto La7
//        La3:
//            r5 = move-exception
//            r5.printStackTrace()
//        La7:
//            return
//        */
//        throw new UnsupportedOperationException("Method not decompiled: com.alawail.prayertimes.MyTool.g(java.util.List, com.alawail.prayertimes.alarm.Alarm, com.alawail.prayertimes.alarm.database.Database):void");
//    }
//
//    public static void generateNextRandomRequest(Context context, String str, String str2) {
//        if (PrayerTimesApplication.checkTvAgentEdition()) {
//            return;
//        }
//        Preference preference = new Preference(context);
//        preference.setRandom_number4Request(str);
//        preference.setRandom_days4Request(str2);
//        preference.setRandom_NextDate4Request(createNextRandomRequestAlarm(context, (strToInt(preference.getRandom_days4Request(), 1) * 86400) + strToInt(preference.getRandom_number4Request(), 325590)));
//        preference.setRandom_Date4Request(new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss", Locale.ENGLISH).format(Calendar.getInstance().getTime()));
//    }
//
//    public static Calendar getAlarmTimeBeforeAfter(Context context, Alarm.Type_2 type_2) {
//        boolean z2;
//        if (new Preference(context).getHour12_24().type() == Hour.Type.hour24) {
//            z2 = false;
//        } else {
//            z2 = true;
//        }
//        if (type_2 == Alarm.Type_2.AZAN_Jomo3a) {
//            return Alarm.createAlarm(z2, PrayerTimesApplication.prayersListToday.get(13), "---", Alarm.Type.IKAMA, Alarm.Type_2.IKAMA_Fajr).getAlarmTime();
//        }
//        if (type_2 == Alarm.Type_2.IKAMA_Jomo3a) {
//            return Alarm.createAlarm(z2, PrayerTimesApplication.prayersListToday.get(12), "---", Alarm.Type.IKAMA, Alarm.Type_2.IKAMA_Fajr).getAlarmTime();
//        }
//        return null;
//    }
//
//    public static Bundle getAlarmTxtContentBundle(Alarm alarm) {
//        String string = PrayerTimesApplication.context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_activity_alarm_txt_content);
//        if (alarm.getAlarmType() == Alarm.Type.AZKAR_ALARM) {
//            string = PrayerTimesApplication.context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_AzkarAlarm);
//        } else if (alarm.getAlarmType() == Alarm.Type.EVENT_ALARM) {
//            string = PrayerTimesApplication.context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_EventAlarm);
//        }
//        String alarmName = alarm.getAlarmName();
//        String alarmTxtContent = alarm.getAlarmTxtContent();
//        int alarmTextWindowCloseAfter = alarm.getAlarmTextWindowCloseAfter();
//        Bundle bundle = new Bundle();
//        bundle.putString("WindowTitle", string);
//        bundle.putString("Title", alarmName);
//        bundle.putString("Txt", alarmTxtContent);
//        bundle.putInt("Time4Stop", alarmTextWindowCloseAfter);
//        bundle.putInt(HttpHeaders.FROM, alarm.getAlarmType().ordinal());
//        bundle.putString("Group", alarm.getQuran_str());
//        return bundle;
//    }
//
//    public static Alarm getAlarmType2(Alarm.Type_2 type_2, List<Alarm> list) {
//        Alarm alarm = Database.getAlarm(type_2, list);
//        if (alarm == null && type_2 == Alarm.Type_2.IKAMA_Dhuhr) {
//            alarm = Database.getAlarm(Alarm.Type_2.IKAMA_Jomo3a, list);
//        }
//        if (alarm == null && type_2 == Alarm.Type_2.AZAN_Dhuhr) {
//            alarm = Database.getAlarm(Alarm.Type_2.AZAN_Jomo3a, list);
//        }
//        if (alarm == null && type_2 == Alarm.Type_2.IKAMA_Jomo3a) {
//            alarm = Database.getAlarm(Alarm.Type_2.IKAMA_Dhuhr, list);
//        }
//        return (alarm == null && type_2 == Alarm.Type_2.AZAN_Jomo3a) ? Database.getAlarm(Alarm.Type_2.AZAN_Dhuhr, list) : alarm;
//    }
//
//    public static String getApkFileName() {
//        return BuildConfig.apkFileName;
//    }
//
//    public static Locale getAppLocale(Context context) {
//        if (IsRTL(PreferenceManager.getDefaultSharedPreferences(context).getString("LanguageLocale", getCurrentLang()))) {
//            return new Locale("ar");
//        }
//        return Locale.getDefault();
//    }
//
//    public static String getAppNameTV(Context context) {
//        if (PrayerTimesApplication.checkTvAgentEdition()) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.app_name) + " - " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.app_name_tv);
//        }
//        return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.app_name);
//    }
//
//    public static String getAzanDefaultStr(String str, Context context) {
//        if (str.equals("azan01")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan01);
//        }
//        if (str.equals("azan02")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan02);
//        }
//        if (str.equals("azan03")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan03);
//        }
//        if (str.equals("azan04")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan04);
//        }
//        if (str.equals("azan05")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan05);
//        }
//        if (str.equals("azan06")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan06);
//        }
//        if (str.equals("azan07")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan07);
//        }
//        if (str.equals("azan08")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan08);
//        }
//        if (str.equals("azan09")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan09);
//        }
//        if (str.equals("azan10")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan10);
//        }
//        if (str.equals("azan11")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan11);
//        }
//        if (str.equals("azan_fajr01")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr01);
//        }
//        if (str.equals("azan_fajr02")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr02);
//        }
//        if (str.equals("azan_fajr03")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr03);
//        }
//        if (str.equals("azan_fajr04")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr04);
//        }
//        if (str.equals("azan_fajr05")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr05);
//        }
//        if (str.equals("azan_fajr06")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr06);
//        }
//        if (str.equals("azan_fajr07")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr07);
//        }
//        if (str.equals("azan_fajr08")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr08);
//        }
//        if (str.equals("azan_fajr09")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr09);
//        }
//        if (str.equals("azan_fajr10")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr10);
//        }
//        if (str.equals("azan_fajr11")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.azan_fajr11);
//        }
//        if (str.equals("notification_takbeer")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.notification_takbeer);
//        }
//        if (str.equals("ikama01")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.ikama01);
//        }
//        if (str.equals("tone01")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone01);
//        }
//        if (str.equals("tone02")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone02);
//        }
//        if (str.equals("tone03")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone03);
//        }
//        if (str.equals("tone04")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone04);
//        }
//        if (str.equals("tone05")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone05);
//        }
//        if (str.equals("tone_salaty")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone_salaty);
//        }
//        if (str.equals("tone_itistimetoprayer")) {
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.tone_itistimetoprayer);
//        }
//        return str;
//    }
//
//    public static String getBeforeAfterDurationFormat(Context context, int i2, int i3, String str, String str2) {
//        String str3 = i2 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//        if (i2 < 0) {
//            str3 = (i2 * (-1)) + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//        }
//        if (i2 >= 60 || i2 <= -60) {
//            int i4 = i2 / 60;
//            int i5 = i2 - (i4 * 60);
//            if (i4 < 0) {
//                i4 *= -1;
//            }
//            if (i5 < 0) {
//                i5 *= -1;
//            }
//            String str4 = i4 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strHour);
//            if (i5 > 0) {
//                str3 = i4 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strHour) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strAnd) + " " + i5 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//            } else {
//                str3 = str4;
//            }
//        }
//        String str5 = i3 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//        if (i3 < 0) {
//            str5 = (i3 * (-1)) + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//        }
//        if (i3 >= 60 || i3 <= -60) {
//            int i6 = i3 / 60;
//            int i7 = i3 - (i6 * 60);
//            if (i6 < 0) {
//                i6 *= -1;
//            }
//            if (i7 < 0) {
//                i7 *= -1;
//            }
//            str5 = i6 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strHour);
//            if (i7 > 0) {
//                str5 = i6 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strHour) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strAnd) + " " + i7 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//            }
//        }
//        if (i2 >= 0) {
//            return str + "  " + str3 + "  " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.end_silent) + "  " + str5;
//        }
//        return str2 + "  " + str3 + "  " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.end_silent) + "  " + str5;
//    }

    public static String getFirstImgFile(String str) {
        try {
            File[] listFiles = new File(str).listFiles();
            for (int i2 = 0; i2 < listFiles.length; i2++) {
                if (checkImgFile(listFiles[i2].getName())) {
                    return listFiles[i2].getName();
                }
            }
            return "";
        } catch (Exception unused) {
            return "";
        }
    }

//    public static String getCityInfoURL(Preference preference, String str, String str2) {
//        String str3;
//        String str4;
//        String str5 = "";
//        try {
//            str3 = "&z=1&device_date=" + new SimpleDateFormat("yyyy-MM-dd%20HH:mm:ss", Locale.ENGLISH).format(Calendar.getInstance().getTime());
//        } catch (Exception e2) {
//            e2.printStackTrace();
//            str3 = "&z=1&device_date=2020-04-19 16:35:34";
//        }
//        String a2 = androidx.constraintlayout.core.motion.key.a.a("&status_id=", str2);
//        if (PrayerTimesApplication.checkTvAgentEdition()) {
//            if (!str2.isEmpty() && str2.length() > 0) {
//                a2 = "&status_id=2" + str2.substring(1);
//            } else {
//                a2 = "&status_id=0";
//            }
//        }
//        try {
//            if (preference.getParamT4RandomRequest().equals("null")) {
//                preference.setParamT4RandomRequest(MyToolKKt.getRandomID());
//            }
//            String paramT4RandomRequest = preference.getParamT4RandomRequest();
//            str4 = "&t=" + paramT4RandomRequest;
//            try {
//                String md5 = Fn.security.INSTANCE.md5(paramT4RandomRequest + "Mh.Time20");
//                StringBuilder sb = new StringBuilder("&t1=");
//                sb.append(md5);
//                str5 = sb.toString();
//            } catch (Exception e3) {
//                e = e3;
//                e.printStackTrace();
//                int i2 = 0;
//                i2 = PrayerTimesApplication.getAppContext().getPackageManager().getPackageInfo(PrayerTimesApplication.getAppContext().getApplicationContext().getPackageName(), 0).versionCode;
//                String str6 = DataNetAction.serverURLGetCityInfo_11 + "?id=" + str + str4 + str5 + (a2 + "&v=" + i2) + str3;
//                MyLog("doNextRandomRequest", "aaResult 0022266 " + str6 + " status_id " + str2);
//                return str6;
//            }
//        } catch (Exception e4) {
//            e = e4;
//            str4 = "";
//        }
//        int i22 = 0;
//        try {
//            i22 = PrayerTimesApplication.getAppContext().getPackageManager().getPackageInfo(PrayerTimesApplication.getAppContext().getApplicationContext().getPackageName(), 0).versionCode;
//        } catch (PackageManager.NameNotFoundException e5) {
//            e5.printStackTrace();
//        }
//        String str62 = DataNetAction.serverURLGetCityInfo_11 + "?id=" + str + str4 + str5 + (a2 + "&v=" + i22) + str3;
//        MyLog("doNextRandomRequest", "aaResult 0022266 " + str62 + " status_id " + str2);
//        return str62;
//    }
//
//    public static int getColorWidgetTransparent1(String str) {
//        int parseColor = Color.parseColor("#4D000000");
//        str.getClass();
//        char c2 = 65535;
//        switch (str.hashCode()) {
//            case 49:
//                if (str.equals(SettingsActivity_2.CityType_1)) {
//                    c2 = 0;
//                    break;
//                }
//                break;
//            case 50:
//                if (str.equals("2")) {
//                    c2 = 1;
//                    break;
//                }
//                break;
//            case 51:
//                if (str.equals(ExifInterface.GPS_MEASUREMENT_3D)) {
//                    c2 = 2;
//                    break;
//                }
//                break;
//            case 53:
//                if (str.equals("5")) {
//                    c2 = 3;
//                    break;
//                }
//                break;
//            case 54:
//                if (str.equals("6")) {
//                    c2 = 4;
//                    break;
//                }
//                break;
//            case 55:
//                if (str.equals("7")) {
//                    c2 = 5;
//                    break;
//                }
//                break;
//        }
//        switch (c2) {
//            case 0:
//                return Color.parseColor("#0D000000");
//            case 1:
//                return Color.parseColor("#26000000");
//            case 2:
//                return Color.parseColor("#40000000");
//            case 3:
//                return Color.parseColor("#59000000");
//            case 4:
//                return Color.parseColor("#73000000");
//            case 5:
//                return Color.parseColor("#99000000");
//            default:
//                return parseColor;
//        }
//    }
//
//    public static int getColorWidgetTransparent2(String str) {
//        int parseColor = Color.parseColor("#33000000");
//        str.getClass();
//        char c2 = 65535;
//        switch (str.hashCode()) {
//            case 49:
//                if (str.equals(SettingsActivity_2.CityType_1)) {
//                    c2 = 0;
//                    break;
//                }
//                break;
//            case 50:
//                if (str.equals("2")) {
//                    c2 = 1;
//                    break;
//                }
//                break;
//            case 51:
//                if (str.equals(ExifInterface.GPS_MEASUREMENT_3D)) {
//                    c2 = 2;
//                    break;
//                }
//                break;
//            case 53:
//                if (str.equals("5")) {
//                    c2 = 3;
//                    break;
//                }
//                break;
//            case 54:
//                if (str.equals("6")) {
//                    c2 = 4;
//                    break;
//                }
//                break;
//            case 55:
//                if (str.equals("7")) {
//                    c2 = 5;
//                    break;
//                }
//                break;
//        }
//        switch (c2) {
//            case 0:
//                return Color.parseColor("#0D000000");
//            case 1:
//                return Color.parseColor("#1A000000");
//            case 2:
//                return Color.parseColor("#26000000");
//            case 3:
//                return Color.parseColor("#40000000");
//            case 4:
//                return Color.parseColor("#73000000");
//            case 5:
//                return Color.parseColor("#99000000");
//            default:
//                return parseColor;
//        }
//    }
//
//    public static String getCurrentBackground() {
//        String str;
//        String str2 = ImageLayout.IMAGE_FILE_PATH;
//        String str3 = "MyBackground";
//        if (ImageLayout.DESIGN_FILE_PATH.equals("")) {
//            str = "MyBackground";
//        } else {
//            str2 = ImageLayout.DESIGN_FILE_PATH;
//            str = "background";
//        }
//        if (!ImageLayout.IMAGE_FILE_PATH.equals("")) {
//            str2 = ImageLayout.IMAGE_FILE_PATH;
//        } else {
//            str3 = str;
//        }
//        return getDirProgram() + "/" + str3 + "/" + str2;
//    }
//
//    public static int getCurrentHijriMonth(Preference preference, Calendar calendar) {
//        int i2 = calendar.get(5);
//        int i3 = calendar.get(2) + 1;
//        int i4 = calendar.get(1);
//        if (PrayerTimesApplication.date4PrayerTime != null && !PrayerTimesApplication.checkTvAgentEdition()) {
//            i2 = PrayerTimesApplication.date4PrayerTime.getDay();
//            i3 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.MONTH java.lang.String() + 1;
//            i4 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.YEAR java.lang.String();
//        }
//        return preference.getHijriDate(i2, i3, i4, preference.getHijriDatePreference(), preference.getHijriDatePreference_30()).m;
//    }
//
//    public static String getCurrentLang() {
//        String language = Locale.getDefault().getLanguage();
//        String[] stringArray = PrayerTimesApplication.getAppContext().getResources().getStringArray(com.alawail.alarm.prayertimes_mobile.R.array.LanguageLocaleValues);
//        int length = stringArray.length;
//        boolean z2 = false;
//        int i2 = 0;
//        while (true) {
//            if (i2 >= length) {
//                break;
//            }
//            if (language.toLowerCase().equals(stringArray[i2])) {
//                z2 = true;
//                break;
//            }
//            i2++;
//        }
//        if (!z2) {
//            return "en";
//        }
//        return language;
//    }

    public static String getFirstWord(String str) {
        if (str.contains(" ")) {
            return str.substring(0, str.indexOf(" "));
        }
        return str;
    }

//    public static String getDayName(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_sunday);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_monday);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_tuesday);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_wednesday);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_thursday);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_friday);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_saturday);
//            default:
//                return "";
//        }
//    }
//
//    public static String getDayNameFull_en(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_sunday_full_en);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_monday_full_en);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_tuesday_full_en);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_wednesday_full_en);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_thursday_full_en);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_friday_full_en);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_saturday_full_en);
//            default:
//                return "";
//        }
//    }
//
//    public static String getDayName_en(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_sunday_en);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_monday_en);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_tuesday_en);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_wednesday_en);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_thursday_en);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_friday_en);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.day_saturday_en);
//            default:
//                return "";
//        }
//    }

    public static int getItemPos(String[] strArr, String str) {
        if (strArr == null) {
            return -1;
        }
        for (int i2 = 0; i2 < strArr.length; i2++) {
            if (strArr[i2].indexOf(str) >= 0) {
                return i2;
            }
        }
        return -1;
    }

//    public static String getDirAudioAlarm() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirAudioAlarm);
//        return sb.toString();
//    }
//
//    public static String getDirAzan() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirAzan);
//        return sb.toString();
//    }
//
//    public static String getDirAzkar() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirAzkar);
//        return sb.toString();
//    }
//
//    public static String getDirBackground() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirBackground);
//        return sb.toString();
//    }
//
//    public static String getDirBackgroundScreen() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirBackgroundScreen);
//        return sb.toString();
//    }
//
//    public static String getDirDownloadImg() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirBackground);
//        sb.append("/");
//        sb.append(dirDownloadImg);
//        return sb.toString();
//    }
//
//    public static String getDirDownloadZip() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirBackground);
//        sb.append("/");
//        sb.append(dirDownloadZip);
//        return sb.toString();
//    }
//
//    public static String getDirFatawa() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirFatawa);
//        return sb.toString();
//    }
//
//    public static String getDirLayoutDynamicLandscape() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirLayoutDynamicLandscape);
//        return sb.toString();
//    }
//
//    public static String getDirLayoutDynamicPortrait() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirLayoutDynamicPortrait);
//        return sb.toString();
//    }

//    public static String getDirLayouts() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirLayouts);
//        return sb.toString();
//    }

//    public static String getDirLayoutsScreen() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirLayoutsScreen);
//        return sb.toString();
//    }

//    public static String getDirLectures() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirLectures);
//        return sb.toString();
//    }

//    public static String getDirLogs() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirLogs);
//        return sb.toString();
//    }

//    public static String getDirMyBackground() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirMyBackground);
//        return sb.toString();
//    }

//    public static String getDirNews() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirNews);
//        return sb.toString();
//    }

//    public static String getDirProgram() {
//        return getExternalStorageDirStr() + "/" + dirProgram;
//    }

//    public static String getDirVideo() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirVideos);
//        return sb.toString();
//    }

//    public static String getDirVideosScreen() {
//        StringBuilder sb = new StringBuilder();
//        androidx.fragment.app.a.b(sb, "/");
//        sb.append(dirVideosScreen);
//        return sb.toString();
//    }

//    public static File getExternalStorageDir() {
//        if (PrayerTimesApplication.checkTvAgentEdition()) {
//            return Environment.getExternalStorageDirectory();
//        }
//        if (Build.VERSION.SDK_INT >= 30) {
//            return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
//        }
//        return Environment.getExternalStorageDirectory();
//    }

//    public static String getExternalStorageDirStr() {
//        if (PrayerTimesApplication.checkTvAgentEdition()) {
//            return Environment.getExternalStorageDirectory() + "";
//        }
//        int i2 = Build.VERSION.SDK_INT;
//        if (i2 == 29) {
//            ContentResolver contentResolver = PrayerTimesApplication.getAppContext().getContentResolver();
//            ContentValues contentValues = new ContentValues();
//            contentValues.put("relative_path", Environment.DIRECTORY_PICTURES + "");
//            return String.valueOf(contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues));
//        }
//        if (i2 >= 30) {
//            return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "";
//        }
//        return Environment.getExternalStorageDirectory() + "";
//    }

//    public static String getFileLogPath() {
//        return getDirProgram() + "/mylog.txt";
//    }

    public static String getJSONArrayFromCursor(Cursor cursor) {
        JSONArray jSONArray = new JSONArray();
        cursor.moveToFirst();
        while (!cursor.isAfterLast()) {
            int columnCount = cursor.getColumnCount();
            JSONObject jSONObject = new JSONObject();
            MyLog("doNextRandomRequest", "getJSONArrayFromCursor " + columnCount);
            for (int i2 = 0; i2 < columnCount; i2++) {
                if (cursor.getColumnName(i2) != null) {
                    try {
                        if (cursor.getString(i2) != null) {
                            cursor.getString(i2);
                            jSONObject.put(cursor.getColumnName(i2), cursor.getString(i2));
                        } else {
                            jSONObject.put(cursor.getColumnName(i2), "");
                        }
                    } catch (Exception e2) {
                        e2.getMessage();
                    }
                }
            }
            jSONArray.put(jSONObject);
            cursor.moveToNext();
        }
        cursor.close();
        return jSONArray.toString();
    }

    public static String[] getLayoutExt() {
        return new String[]{"json"};
    }

    public static ArrayList<String> getListHtmlFiles(String str) {
        ArrayList<String> arrayList = new ArrayList<>();
        try {
            File[] listFiles = new File(str).listFiles();
            for (int i2 = 0; i2 < listFiles.length; i2++) {
                if (checkHtmlFile(listFiles[i2].getName())) {
                    arrayList.add(listFiles[i2].getName());
                }
            }
        } catch (Exception unused) {
        }
        return arrayList;
    }

//    public static String getFile_PDF_Temp() {
//        return getDirProgram() + "/temp.pdf";
//    }

//    public static String getFile_android_preferences() {
//        return getDirProgram() + "/android_preferences.json";
//    }

//    public static String getFile_quran_playing_state() {
//        return getDirProgram() + "/quran_playing_state.txt";
//    }

//    public static String getFirstFatawaFile() {
//        try {
//            File[] listFiles = new File(getDirFatawa()).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }
//
//    public static String getFirstFatawaFilePath() {
//        String dirFatawa2 = getDirFatawa();
//        try {
//            File[] listFiles = new File(dirFatawa2).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return dirFatawa2 + "/" + listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }

    public static String getLocalIpAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                Enumeration<InetAddress> inetAddresses = networkInterfaces.nextElement().getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress nextElement = inetAddresses.nextElement();
                    if (nextElement.isLoopbackAddress()) {
                        return nextElement.getHostAddress();
                    }
                }
            }
            return null;
        } catch (SocketException e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
            return null;
        }
    }

//    public static String getFirstLecturesFile() {
//        try {
//            File[] listFiles = new File(getDirLectures()).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }
//
//    public static String getFirstLecturesFilePath() {
//        String dirLectures2 = getDirLectures();
//        try {
//            File[] listFiles = new File(dirLectures2).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return dirLectures2 + "/" + listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }
//
//    public static String getFirstNewsFile() {
//        try {
//            File[] listFiles = new File(getDirNews()).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }
//
//    public static String getFirstNewsFilePath() {
//        String dirNews2 = getDirNews();
//        try {
//            File[] listFiles = new File(dirNews2).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return dirNews2 + "/" + listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }
//
//    public static String getFirstVideoFile() {
//        try {
//            File[] listFiles = new File(getDirVideo()).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkHtmlFile(listFiles[i2].getName())) {
//                    return listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }
//
//    public static String getFirstVideoFilePath() {
//        String dirVideo = getDirVideo();
//        try {
//            File[] listFiles = new File(dirVideo).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (check3gp_mp4File(listFiles[i2].getName())) {
//                    return dirVideo + "/" + listFiles[i2].getName();
//                }
//            }
//            return "";
//        } catch (Exception unused) {
//            return "";
//        }
//    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x005a  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x005f  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0064  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x006c  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x0074  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static java.lang.String getMultiSelectList(java.util.Set<java.lang.String> r4, android.content.Context r5) {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.Iterator r4 = r4.iterator()
        L9:
            boolean r1 = r4.hasNext()
            if (r1 == 0) goto L83
            java.lang.Object r1 = r4.next()
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.trim()
            r1.getClass()
            int r2 = r1.hashCode()
            r3 = -1
            switch(r2) {
                case -1097222349: goto L49;
                case -217366722: goto L3d;
                case 345929542: goto L31;
                case 350453432: goto L25;
                default: goto L24;
            }
        L24:
            goto L54
        L25:
            java.lang.String r2 = "widget_visible_hijri"
            boolean r1 = r1.equals(r2)
            if (r1 != 0) goto L2f
            goto L54
        L2f:
            r3 = 3
            goto L54
        L31:
            java.lang.String r2 = "widget_visible_clock"
            boolean r1 = r1.equals(r2)
            if (r1 != 0) goto L3b
            goto L54
        L3b:
            r3 = 2
            goto L54
        L3d:
            java.lang.String r2 = "widget_visible_fasting"
            boolean r1 = r1.equals(r2)
            if (r1 != 0) goto L47
            goto L54
        L47:
            r3 = 1
            goto L54
        L49:
            java.lang.String r2 = "widget_visible_city"
            boolean r1 = r1.equals(r2)
            if (r1 != 0) goto L53
            goto L54
        L53:
            r3 = 0
        L54:
            r1 = 2132018908(0x7f1406dc, float:1.9676136E38)
            switch(r3) {
                case 0: goto L74;
                case 1: goto L6c;
                case 2: goto L64;
                case 3: goto L5f;
                default: goto L5a;
            }
        L5a:
            java.lang.String r1 = r5.getString(r1)
            goto L7b
        L5f:
            java.lang.String r1 = r5.getString(r1)
            goto L7b
        L64:
            r1 = 2132018906(0x7f1406da, float:1.9676132E38)
            java.lang.String r1 = r5.getString(r1)
            goto L7b
        L6c:
            r1 = 2132018907(0x7f1406db, float:1.9676134E38)
            java.lang.String r1 = r5.getString(r1)
            goto L7b
        L74:
            r1 = 2132018905(0x7f1406d9, float:1.967613E38)
            java.lang.String r1 = r5.getString(r1)
        L7b:
            java.lang.String r1 = r1.trim()
            r0.add(r1)
            goto L9
        L83:
            int r4 = r0.size()
            if (r4 != 0) goto L91
            r4 = 2132018901(0x7f1406d5, float:1.9676122E38)
            java.lang.String r4 = r5.getString(r4)
            return r4
        L91:
            r4 = 2132017574(0x7f1401a6, float:1.967343E38)
            java.lang.String r4 = r5.getString(r4)
            java.lang.String r4 = android.text.TextUtils.join(r4, r0)
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: com.alawail.prayertimes.MyTool.getMultiSelectList(java.util.Set, android.content.Context):java.lang.String");
    }

//    public static String getHMSDurationFormat(Context context, int i2) {
//        String str = i2 + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Second);
//        if (i2 >= 60) {
//            int i3 = i2 / 60;
//            int i4 = i2 - (i3 * 60);
//            String str2 = i3 + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Minute);
//            if (i4 > 0) {
//                str = i3 + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Minute) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strAnd) + " " + i4 + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Second);
//            } else {
//                str = str2;
//            }
//        }
//        if (i2 >= 3600) {
//            int i5 = i2 / 3600;
//            int i6 = i5 * 3600;
//            int i7 = (i2 - i6) / 60;
//            int i8 = i2 - ((i7 * 60) + i6);
//            String str3 = i5 + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Hour);
//            if (i7 > 0) {
//                StringBuilder b2 = android.support.v4.media.i.b(str3, " ");
//                b2.append(context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strAnd));
//                b2.append(" ");
//                b2.append(i7);
//                b2.append(" ");
//                b2.append(context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Minute));
//                str3 = b2.toString();
//            }
//            if (i8 > 0) {
//                StringBuilder b3 = android.support.v4.media.i.b(str3, " ");
//                b3.append(context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strAnd));
//                b3.append(" ");
//                b3.append(i8);
//                b3.append(" ");
//                b3.append(context.getString(com.alawail.alarm.prayertimes_mobile.R.string.str_Second));
//                return b3.toString();
//            }
//            return str3;
//        }
//        return str;
//    }

//    public static String getHijriDateString(int i2, String str) {
//        Calendar calendar = Calendar.getInstance();
//        if (PrayerTimesApplication.date4PrayerTime != null && !PrayerTimesApplication.checkTvAgentEdition()) {
//            int day = PrayerTimesApplication.date4PrayerTime.getDay();
//            int i3 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.MONTH java.lang.String() + 1;
//            int i4 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.YEAR java.lang.String();
//            calendar.set(5, day);
//            calendar.set(2, i3 - 1);
//            calendar.set(1, i4);
//        }
//        calendar.add(5, i2);
//        HijriCalendar hijriCalendar = new HijriCalendar(calendar.get(1), calendar.get(2) + 1, calendar.get(5));
//        int hijriDay = hijriCalendar.getHijriDay();
//        int hijriMonth = hijriCalendar.getHijriMonth();
//        Calendar calendar2 = Calendar.getInstance();
//        if (DateToStr(calendar2.get(1), calendar2.get(2) + 1, calendar2.get(5)).equals(str)) {
//            hijriDay = 30;
//        }
//        String a2 = androidx.fragment.app.d.a(hijriMonth, "");
//        if (a2.length() == 1) {
//            a2 = "0".concat(a2);
//        }
//        String a3 = androidx.fragment.app.d.a(hijriDay, "");
//        if (a3.length() == 1) {
//            a3 = "0".concat(a3);
//        }
//        return hijriCalendar.getHijriYear() + "/" + a2 + "/" + a3;
//    }

//    public static String[] getHijriMonthNames(Context context) {
//        return new String[]{context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h1), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h2), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h3), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h4), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h5), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h6), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h7), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h8), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h9), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h10), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h11), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h12)};
//    }

//    public static String getHijriName2(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs1);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs2);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs3);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs4);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs5);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs6);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs7);
//            case 8:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs8);
//            case 9:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs9);
//            case 10:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs10);
//            case 11:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs11);
//            case 12:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_hs12);
//            default:
//                return "";
//        }
//    }

//    public static String getHijriName_en(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h1_en);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h2_en);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h3_en);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h4_en);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h5_en);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h6_en);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h7_en);
//            case 8:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h8_en);
//            case 9:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h9_en);
//            case 10:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h10_en);
//            case 11:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h11_en);
//            case 12:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_h12_en);
//            default:
//                return "";
//        }
//    }

//    public static String[] getHijriTextList(Context context) {
//        String[] strArr = {SettingsActivity_2.CityType_1, "2", ExifInterface.GPS_MEASUREMENT_3D, "4", "5", "6"};
//        strArr[0] = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.hijri_day_30th);
//        String hijriDateString = getHijriDateString(-2, HelpFormatter.DEFAULT_OPT_PREFIX);
//        strArr[1] = hijriDateString;
//        hijriDateString.split("/");
//        strArr[0] = strArr[0];
//        strArr[2] = getHijriDateString(-1, HelpFormatter.DEFAULT_OPT_PREFIX);
//        strArr[3] = getHijriDateString(0, HelpFormatter.DEFAULT_OPT_PREFIX);
//        strArr[4] = getHijriDateString(1, HelpFormatter.DEFAULT_OPT_PREFIX);
//        strArr[5] = getHijriDateString(2, HelpFormatter.DEFAULT_OPT_PREFIX);
//        return strArr;
//    }

//    public static String[] getHijriValList() {
//        return new String[]{"0", SettingsActivity_2.CityType_1, "2", ExifInterface.GPS_MEASUREMENT_3D, "4", "5"};
//    }

//    public static String getIkamaValsString(int i2, int i3, int i4, Context context) {
//        String str;
//        if (i2 == -1 || i3 == -1) {
//            str = "";
//        } else {
//            str = str2Int(i2) + ":" + str2Int(i3);
//        }
//        if (!str.equals("")) {
//            return str;
//        }
//        return i4 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//    }

//    public static int getIntPrefVal(int i2, String str, Context context) {
//        int i3;
//        String str2 = "0";
//        try {
//            String string = PreferenceManager.getDefaultSharedPreferences(context).getString(str, context.getString(i2));
//            try {
//                if (!string.equals("")) {
//                    str2 = string;
//                }
//            } catch (Exception e2) {
//                e = e2;
//                str2 = string;
//                e.printStackTrace();
//                String num = Integer.toString(strToInt(str2));
//                i3 = 0;
//                i3 = Integer.parseInt(context.getString(i2));
//                return Integer.parseInt(num);
//            }
//        } catch (Exception e3) {
//            e = e3;
//        }
//        String num2 = Integer.toString(strToInt(str2));
//        i3 = 0;
//        try {
//            i3 = Integer.parseInt(context.getString(i2));
//            return Integer.parseInt(num2);
//        } catch (NumberFormatException e4) {
//            e4.printStackTrace();
//            return i3;
//        }
//    }

    public static int getNegativeColorDLG() {
        return Color.rgb(117, 117, 117);
    }

    public static int getNeutralColorDLG() {
        return Color.rgb(255, 0, 0);
    }

    public static int getPositiveColorDLG() {
        return Color.rgb(255, 255, 255);
    }

//    public static ArrayList<String> getListFiles(String str, String[] strArr) {
//        ArrayList<String> arrayList = new ArrayList<>();
//        try {
//            File[] listFiles = new File(str).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (checkFile(listFiles[i2].getName(), strArr)) {
//                    arrayList.add(listFiles[i2].getName());
//                }
//            }
//        } catch (Exception unused) {
//        }
//        return arrayList;
//    }

    public static Bitmap getScreenShot(View view) {
        View rootView = view.getRootView();
        rootView.setDrawingCacheEnabled(true);
        Bitmap createBitmap = Bitmap.createBitmap(rootView.getDrawingCache());
        rootView.setDrawingCacheEnabled(false);
        return createBitmap;
    }

//    public static ArrayList<String> getListImageFiles(String str) {
//        return getListFiles(str, new String[]{"png", "jpg", "gif"});
//    }
//
//    public static ArrayList<String> getListLayoutFiles(String str) {
//        return getListFiles(str, getLayoutExt());
//    }

//    public static ArrayList<String> getListRaw() {
//        ArrayList<String> arrayList = new ArrayList<>();
//        for (Field field : R.raw.class.getFields()) {
//            arrayList.add(field.getName());
//        }
//        return arrayList;
//    }

//    public static ArrayList<String> getListVideoFiles(String str) {
//        ArrayList<String> arrayList = new ArrayList<>();
//        try {
//            File[] listFiles = new File(str).listFiles();
//            for (int i2 = 0; i2 < listFiles.length; i2++) {
//                if (isFileVideoDep(listFiles[i2].getName())) {
//                    arrayList.add(listFiles[i2].getName());
//                }
//            }
//        } catch (Exception unused) {
//        }
//        return arrayList;
//    }

    public static Spannable getTitleFalseCheck(String str) {
        SpannableString spannableString = new SpannableString(str);
        spannableString.setSpan(new ForegroundColorSpan(ViewCompat.MEASURED_STATE_MASK), 0, spannableString.length(), 0);
        return spannableString;
    }

//    public static String getMacAddress() {
//        try {
//            return loadFileAsString("/sys/class/net/eth0/address").toUpperCase().substring(0, 17);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//            return null;
//        }
//    }

//    public static MainActivityType getMainActivityType(Activity activity) {
//        String str;
//        MainActivityType mainActivityType = MainActivityType.MOBILE;
//        if (((UiModeManager) activity.getSystemService("uimode")).getCurrentModeType() == 4) {
//            str = "TV_DEVICE";
//        } else {
//            str = "NO_TV_DEVICE";
//        }
//        if (str.equals("TV_DEVICE")) {
//            return MainActivityType.TV;
//        }
//        return mainActivityType;
//    }

//    public static String getMiladiName2(int i2, Context context) {
//        int miladi_month = new Manager(context).getPreference().getMiladi_month();
//        switch (i2) {
//            case 1:
//                String string = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me1);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms1);
//                }
//                return string;
//            case 2:
//                String string2 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me2);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms2);
//                }
//                return string2;
//            case 3:
//                String string3 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me3);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms3);
//                }
//                return string3;
//            case 4:
//                String string4 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me4);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms4);
//                }
//                return string4;
//            case 5:
//                String string5 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me5);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms5);
//                }
//                return string5;
//            case 6:
//                String string6 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me6);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms6);
//                }
//                return string6;
//            case 7:
//                String string7 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me7);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms7);
//                }
//                return string7;
//            case 8:
//                String string8 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me8);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms8);
//                }
//                return string8;
//            case 9:
//                String string9 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me9);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms9);
//                }
//                return string9;
//            case 10:
//                String string10 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me10);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms10);
//                }
//                return string10;
//            case 11:
//                String string11 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me11);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms11);
//                }
//                return string11;
//            case 12:
//                String string12 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me12);
//                if (miladi_month == 0) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_ms12);
//                }
//                return string12;
//            default:
//                return "";
//        }
//    }

//    public static String getMiladiNameFull_en(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me1_full_en);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me2_full_en);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me3_full_en);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me4_full_en);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me5_full_en);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me6_full_en);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me7_full_en);
//            case 8:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me8_full_en);
//            case 9:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me9_full_en);
//            case 10:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me10_full_en);
//            case 11:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me11_full_en);
//            case 12:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me12_full_en);
//            default:
//                return "";
//        }
//    }

//    public static String getMiladiName_en(int i2, Context context) {
//        switch (i2) {
//            case 1:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me1_en);
//            case 2:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me2_en);
//            case 3:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me3_en);
//            case 4:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me4_en);
//            case 5:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me5_en);
//            case 6:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me6_en);
//            case 7:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me7_en);
//            case 8:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me8_en);
//            case 9:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me9_en);
//            case 10:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me10_en);
//            case 11:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me11_en);
//            case 12:
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.month_me12_en);
//            default:
//                return "";
//        }
//    }

//    public static String getMinuteFromMinuteSecond(String str, boolean z2) {
//        String str2;
//        Exception e2;
//        String str3;
//        MyLog("updateRemaining", "updateRemainingTime_remainingTime00 getMinuteFromMinuteSecond " + str);
//        try {
//            if (str.indexOf(":") != -1) {
//                str2 = str.split(":")[0];
//                try {
//                    int parseInt = Integer.parseInt(str2);
//                    if (parseInt != 0) {
//                        int i2 = parseInt + 1;
//                        String str2Int = str2Int(i2);
//                        if (Integer.parseInt(str.split(":")[1]) == 0) {
//                            str3 = str2Int(parseInt);
//                        } else {
//                            str3 = str2Int;
//                        }
//                        if (!z2) {
//                            if (i2 == 60) {
//                                return "01:00";
//                            }
//                            return "00:" + str3;
//                        }
//                        return str3;
//                    }
//                    return str.split(":")[1];
//                } catch (Exception e3) {
//                    e2 = e3;
//                    e2.printStackTrace();
//                    return str2;
//                }
//            }
//            return str;
//        } catch (Exception e4) {
//            str2 = str;
//            e2 = e4;
//        }
//    }

    public static Spannable getTitleTrueCheck(String str) {
        SpannableString spannableString = new SpannableString(str);
        spannableString.setSpan(new ForegroundColorSpan(-16776961), 0, spannableString.length(), 0);
        return spannableString;
    }

    public static Typeface getTypeface(Context context, String str) {
        Typeface typeface;
        Hashtable<String, Typeface> hashtable = f2806a;
        synchronized (hashtable) {
            if (!hashtable.containsKey(str)) {
                try {
                    hashtable.put(str, Typeface.createFromAsset(context.getAssets(), str));
                } catch (Exception unused) {
                    return null;
                }
            }
            typeface = hashtable.get(str);
        }
        return typeface;
    }

    public static String intToDate(int i2, int i3) {
        if (i2 >= 0 && i3 >= 0) {
            return String.format(Locale.ENGLISH, "%02d-%02d", Integer.valueOf(i2), Integer.valueOf(i3));
        }
        return "";
    }

//    public static Alarm getNext(Context context) {
//        TreeSet treeSet = new TreeSet(new v());
//        for (Alarm alarm : Database.getInstance(context).getAll()) {
//            if (alarm.getAlarmActive().booleanValue()) {
//                treeSet.add(alarm);
//            }
//        }
//        if (treeSet.iterator().hasNext()) {
//            return (Alarm) treeSet.iterator().next();
//        }
//        return null;
//    }

    public static String intToTime(int i2, int i3) {
        return (i2 < 0 || i3 < 0) ? "" : String.format(Locale.ENGLISH, "%02d:%02d", Integer.valueOf(i2), Integer.valueOf(i3));
    }

//    public static String getPrayerTimeName(Context context, int i2, boolean z2) {
//        if (i2 != 0) {
//            if (i2 != 1) {
//                if (i2 != 2) {
//                    if (i2 != 3) {
//                        if (i2 != 4) {
//                            if (i2 != 5) {
//                                return "";
//                            }
//                            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerIsha);
//                        }
//                        return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerMaghrib);
//                    }
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerAsr);
//                }
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhr);
//            }
//            if (!z2) {
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhr);
//            }
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerShuruq);
//        }
//        return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerFajr);
//    }

//    public static String getPrayerTimeName4Widget(Context context, int i2, boolean z2, boolean z3) {
//        if (i2 != 0) {
//            if (i2 != 1) {
//                if (i2 != 2) {
//                    if (i2 != 3) {
//                        if (i2 != 4) {
//                            if (i2 != 5) {
//                                return "";
//                            }
//                            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerIshaAfter);
//                        }
//                        return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerMaghribAfter);
//                    }
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerAsrAfter);
//                }
//                if (z3 && PrayerTimesApplication.is_jomo3a_today) {
//                    return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerJomo3aAfter);
//                }
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhrAfter);
//            }
//            if (!z2) {
//                return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhrAfter);
//            }
//            return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerShuruqAfter);
//        }
//        return context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerFajrAfter);
//    }

//    public static String getPrayer_News_txt() {
//        String str = "";
//        try {
//            DB_Cursor visibleNewsList = DatabaseHelper.getInstance(PrayerTimesApplication.getAppContext()).news.getVisibleNewsList();
//            visibleNewsList.cur.moveToFirst();
//            while (!visibleNewsList.cur.isAfterLast()) {
//                StringBuilder sb = new StringBuilder();
//                sb.append(str);
//                Cursor cursor = visibleNewsList.cur;
//                sb.append(cursor.getString(cursor.getColumnIndex("news_txt")).trim());
//                str = sb.toString();
//                visibleNewsList.cur.moveToNext();
//                if (!visibleNewsList.cur.isAfterLast()) {
//                    str = str + " <> ";
//                }
//            }
//            visibleNewsList.destroy();
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//        return str;
//    }

//    public static int getRawFile(String str) {
//        if (OtherAzan.equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan01;
//        }
//        if ("azan02***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan02;
//        }
//        if ("azan03***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan03;
//        }
//        if ("azan04***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan04;
//        }
//        if ("azan05***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan05;
//        }
//        if ("azan06***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan06;
//        }
//        if ("azan07***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan07;
//        }
//        if ("azan08***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan08;
//        }
//        if ("azan09***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan09;
//        }
//        if ("azan10***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan10;
//        }
//        if ("azan11***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan11;
//        }
//        if (FajrAzan.equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr01;
//        }
//        if ("azan_fajr02***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr02;
//        }
//        if ("azan_fajr03***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr03;
//        }
//        if ("azan_fajr04***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr04;
//        }
//        if ("azan_fajr05***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr05;
//        }
//        if ("azan_fajr06***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr06;
//        }
//        if ("azan_fajr07***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr07;
//        }
//        if ("azan_fajr08***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr08;
//        }
//        if ("azan_fajr09***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr09;
//        }
//        if ("azan_fajr10***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr10;
//        }
//        if ("azan_fajr11***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.azan_fajr11;
//        }
//        if (takbeerAzan.equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.notification_takbeer;
//        }
//        if ("ikama01***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.ikama01;
//        }
//        if ("tone01***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone01;
//        }
//        if ("tone02***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone02;
//        }
//        if ("tone03***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone03;
//        }
//        if ("tone04***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone04;
//        }
//        if ("tone05***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone05;
//        }
//        if ("tone_salaty***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone_salaty;
//        }
//        if ("tone_itistimetoprayer***raw***".equals(str)) {
//            return com.alawail.alarm.prayertimes_mobile.R.raw.tone_itistimetoprayer;
//        }
//        return -1;
//    }

//    public static Bitmap getRecyclerViewScreenshot(RecyclerView recyclerView, int i2) {
//        int itemCount = recyclerView.getAdapter().getItemCount();
//        RecyclerView.ViewHolder createViewHolder = recyclerView.getAdapter().createViewHolder(recyclerView, i2);
//        recyclerView.getAdapter().onBindViewHolder(createViewHolder, i2);
//        createViewHolder.itemView.measure(View.MeasureSpec.makeMeasureSpec(recyclerView.getWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(0, 0));
//        View view = createViewHolder.itemView;
//        view.layout(0, 0, view.getMeasuredWidth(), createViewHolder.itemView.getMeasuredHeight());
//        Bitmap createBitmap = Bitmap.createBitmap(recyclerView.getMeasuredWidth(), createViewHolder.itemView.getMeasuredHeight() * itemCount, Bitmap.Config.ARGB_8888);
//        Canvas canvas = new Canvas(createBitmap);
//        canvas.drawColor(-1);
//        Paint paint = new Paint();
//        createViewHolder.itemView.setDrawingCacheEnabled(true);
//        createViewHolder.itemView.buildDrawingCache();
//        canvas.drawBitmap(createViewHolder.itemView.getDrawingCache(), BitmapDescriptorFactory.HUE_RED, 0, paint);
//        createViewHolder.itemView.setDrawingCacheEnabled(false);
//        createViewHolder.itemView.destroyDrawingCache();
//        int measuredHeight = createViewHolder.itemView.getMeasuredHeight() + 0;
//        for (int i3 = 1; i3 < itemCount; i3++) {
//            try {
//                recyclerView.getAdapter().onBindViewHolder(createViewHolder, i3);
//                createViewHolder.itemView.setDrawingCacheEnabled(true);
//                createViewHolder.itemView.buildDrawingCache();
//                canvas.drawBitmap(createViewHolder.itemView.getDrawingCache(), BitmapDescriptorFactory.HUE_RED, measuredHeight, paint);
//                measuredHeight += createViewHolder.itemView.getMeasuredHeight();
//                createViewHolder.itemView.setDrawingCacheEnabled(false);
//                createViewHolder.itemView.destroyDrawingCache();
//            } catch (Exception e2) {
//                e2.printStackTrace();
//            }
//        }
//        return createBitmap;
//    }

//    public static String getScreenInfo(Activity activity) {
//        String str;
//        String str2;
//        String str3;
//        String str4;
//        Display defaultDisplay = activity.getWindowManager().getDefaultDisplay();
//        Point point = new Point();
//        defaultDisplay.getSize(point);
//        int i2 = point.x;
//        int i3 = point.y;
//        int i4 = activity.getResources().getDisplayMetrics().densityDpi;
//        if (i4 != 120) {
//            if (i4 != 160) {
//                if (i4 != 213) {
//                    if (i4 != 240) {
//                        if (i4 != 320) {
//                            if (i4 != 480) {
//                                if (i4 != 640) {
//                                    str = android.support.v4.media.a.a("Unknown ", i4);
//                                } else {
//                                    str = "XXXHDPI";
//                                }
//                            } else {
//                                str = "XXHDPI";
//                            }
//                        } else {
//                            str = "XHDPI";
//                        }
//                    } else {
//                        str = "HDPI";
//                    }
//                } else {
//                    str = "TV";
//                }
//            } else {
//                str = "MDPI";
//            }
//        } else {
//            str = "LDPI";
//        }
//        if (((UiModeManager) activity.getSystemService("uimode")).getCurrentModeType() == 4) {
//            str2 = "Running on a TV Device ";
//        } else {
//            str2 = "Running on a non-TV Device ";
//        }
//        if (activity.getPackageManager().hasSystemFeature("android.hardware.touchscreen")) {
//            str3 = "OK";
//        } else {
//            str3 = "NO";
//        }
//        if ((activity.getResources().getConfiguration().screenLayout & 15) == 3) {
//            str4 = "\r\nScreen Size: SCREEN_LAYOUT_SIZE_LARGE";
//        } else if ((activity.getResources().getConfiguration().screenLayout & 15) == 2) {
//            str4 = "\r\nScreen Size: SCREEN_LAYOUT_SIZE_NORMAL";
//        } else if ((activity.getResources().getConfiguration().screenLayout & 15) == 1) {
//            str4 = "\r\nScreen Size: SCREEN_LAYOUT_SIZE_SMALL";
//        } else if ((activity.getResources().getConfiguration().screenLayout & 15) == 4) {
//            str4 = "\r\nScreen Size: SCREEN_LAYOUT_SIZE_XLARGE";
//        } else {
//            str4 = "\r\nScreen size is neither large, normal or small";
//        }
//        StringBuilder sb = new StringBuilder("width:");
//        sb.append(i2);
//        sb.append(" height:");
//        sb.append(i3);
//        sb.append(" ; Density:");
//        sb.append(str);
//        sb.append("\r\n");
//        sb.append(str2);
//        sb.append(" ; FEATURE_TOUCH_SCREEN ");
//        return android.support.v4.media.b.a(sb, str3, str4);
//    }

    public static boolean isFileFound(String str) {
        return new File(str).exists();
    }

//    public static Bitmap getScreenshotFromRecyclerView(RecyclerView recyclerView) {
//        RecyclerView.Adapter adapter = recyclerView.getAdapter();
//        if (adapter != null) {
//            int itemCount = adapter.getItemCount();
//            Paint paint = new Paint();
//            LruCache lruCache = new LruCache(((int) (Runtime.getRuntime().maxMemory() / PlaybackStateCompat.ACTION_PLAY_FROM_MEDIA_ID)) / 8);
//            int i2 = 0;
//            for (int i3 = 0; i3 < itemCount; i3++) {
//                RecyclerView.ViewHolder createViewHolder = adapter.createViewHolder(recyclerView, adapter.getItemViewType(i3));
//                adapter.onBindViewHolder(createViewHolder, i3);
//                createViewHolder.itemView.measure(View.MeasureSpec.makeMeasureSpec(recyclerView.getWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(0, 0));
//                View view = createViewHolder.itemView;
//                view.layout(0, 0, view.getMeasuredWidth(), createViewHolder.itemView.getMeasuredHeight());
//                createViewHolder.itemView.setDrawingCacheEnabled(true);
//                createViewHolder.itemView.buildDrawingCache();
//                Bitmap drawingCache = createViewHolder.itemView.getDrawingCache();
//                if (drawingCache != null) {
//                    lruCache.put(String.valueOf(i3), drawingCache);
//                }
//                i2 += createViewHolder.itemView.getMeasuredHeight();
//            }
//            Bitmap createBitmap = Bitmap.createBitmap(recyclerView.getMeasuredWidth(), i2, Bitmap.Config.ARGB_8888);
//            Canvas canvas = new Canvas(createBitmap);
//            canvas.drawColor(-1);
//            int i4 = 0;
//            for (int i5 = 0; i5 < itemCount; i5++) {
//                Bitmap bitmap = (Bitmap) lruCache.get(String.valueOf(i5));
//                canvas.drawBitmap(bitmap, BitmapDescriptorFactory.HUE_RED, i4, paint);
//                i4 += bitmap.getHeight();
//                bitmap.recycle();
//            }
//            return createBitmap;
//        }
//        return null;
//    }

//    public static String getSeasonVal(Context context, City city) {
//        String str = city.seasonCity.season;
//        String[] stringArray = context.getResources().getStringArray(com.alawail.alarm.prayertimes_mobile.R.array.season);
//        Season.Companion companion = Season.INSTANCE;
//        if (companion.getSeason(city.seasonCity.season) == Season.Type.Winter_Summer) {
//            StringBuilder b2 = android.support.v4.media.i.b(stringArray[2], "\r\n ");
//            b2.append(stringArray[1]);
//            b2.append(" ");
//            b2.append(city.seasonCity.seasonWinterDay);
//            b2.append("/");
//            b2.append(city.seasonCity.seasonWinterMonth);
//            b2.append("\t\t");
//            b2.append(stringArray[0]);
//            b2.append(" ");
//            b2.append(city.seasonCity.seasonSummerDay);
//            b2.append("/");
//            b2.append(city.seasonCity.seasonSummerMonth);
//            return b2.toString();
//        }
//        if (companion.getSeason(city.seasonCity.season) == Season.Type.Summer) {
//            return stringArray[0];
//        }
//        if (companion.getSeason(city.seasonCity.season) == Season.Type.Winter) {
//            return stringArray[1];
//        }
//        return str;
//    }

//    public static ArrayList<String> getSendedFiles(boolean z2, boolean z3) {
//        ArrayList<String> arrayList = new ArrayList<>();
//        new ArrayList();
//        if (z2 && !ImageLayout.IMAGE_FILE_PATH.equals("")) {
//            arrayList.add(getDirMyBackground() + "/" + ImageLayout.IMAGE_FILE_PATH + ";;" + dirProgram + "/" + dirMyBackground);
//        }
//        if (z3) {
//            Iterator<String> it = getListFiles(getDirLayouts()).iterator();
//            while (it.hasNext()) {
//                arrayList.add(getDirLayouts() + "/" + it.next() + ";;" + dirProgram + "/" + dirLayouts);
//            }
//            Iterator<String> it2 = getListFiles(getDirLayoutDynamicPortrait()).iterator();
//            while (it2.hasNext()) {
//                arrayList.add(getDirLayoutDynamicPortrait() + "/" + it2.next() + ";;" + dirProgram + "/" + dirLayoutDynamicPortrait);
//            }
//            Iterator<String> it3 = getListFiles(getDirLayoutDynamicLandscape()).iterator();
//            while (it3.hasNext()) {
//                arrayList.add(getDirLayoutDynamicLandscape() + "/" + it3.next() + ";;" + dirProgram + "/" + dirLayoutDynamicLandscape);
//            }
//            Iterator<String> it4 = getListFiles(getDirVideo()).iterator();
//            while (it4.hasNext()) {
//                arrayList.add(getDirVideo() + "/" + it4.next() + ";;" + dirProgram + "/" + dirVideos);
//            }
//            Iterator<String> it5 = getListFiles(getDirNews()).iterator();
//            while (it5.hasNext()) {
//                arrayList.add(getDirNews() + "/" + it5.next() + ";;" + dirProgram + "/" + dirNews);
//            }
//            Iterator<String> it6 = getListFiles(getDirFatawa()).iterator();
//            while (it6.hasNext()) {
//                arrayList.add(getDirFatawa() + "/" + it6.next() + ";;" + dirProgram + "/" + dirFatawa);
//            }
//            Iterator<String> it7 = getListFiles(getDirLectures()).iterator();
//            while (it7.hasNext()) {
//                arrayList.add(getDirLectures() + "/" + it7.next() + ";;" + dirProgram + "/" + dirLectures);
//            }
//            Iterator<String> it8 = getListFiles(getDirAzan()).iterator();
//            while (it8.hasNext()) {
//                arrayList.add(getDirAzan() + "/" + it8.next() + ";;" + dirProgram + "/" + dirAzan);
//            }
//            Iterator<String> it9 = getListFiles(getDirAzkar()).iterator();
//            while (it9.hasNext()) {
//                arrayList.add(getDirAzkar() + "/" + it9.next() + ";;" + dirProgram + "/" + dirAzkar);
//            }
//            Iterator<String> it10 = getListFiles(getDirAudioAlarm()).iterator();
//            while (it10.hasNext()) {
//                arrayList.add(getDirAudioAlarm() + "/" + it10.next() + ";;" + dirProgram + "/" + dirAudioAlarm);
//            }
//        }
//        return arrayList;
//    }

//    public static String getShiftAzanValsString(int i2, Context context) {
//        return i2 + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.strMinute);
//    }

//    public static String getTimeToStringHM(Calendar calendar, boolean z2) {
//        String str;
//        int i2 = calendar.get(11);
//        if (z2 && i2 > 12) {
//            i2 -= 12;
//        }
//        if (z2 && i2 == 0) {
//            i2 = 12;
//        }
//        if (i2 <= 9) {
//            str = "0";
//        } else {
//            str = "";
//        }
//        StringBuilder b2 = android.support.v4.media.c.b(str);
//        b2.append(String.valueOf(i2));
//        String b3 = android.support.v4.media.a.b(b2.toString(), ":");
//        if (calendar.get(12) <= 9) {
//            b3 = android.support.v4.media.a.b(b3, "0");
//        }
//        StringBuilder b4 = android.support.v4.media.c.b(b3);
//        b4.append(String.valueOf(calendar.get(12)));
//        return b4.toString();
//    }

    public static boolean isGooglePlayInstalled(Context context) {
        PackageManager packageManager = context.getPackageManager();
        try {
            String str = (String) packageManager.getPackageInfo("com.android.vending", 1).applicationInfo.loadLabel(packageManager);
            if (str == null) {
                return false;
            }
            if (str.equals("Market")) {
                return false;
            }
            return true;
        } catch (PackageManager.NameNotFoundException unused) {
            return false;
        }
    }

    public static boolean isTabletNotTvBox(Activity activity) {
        boolean z2;
        boolean z3;
        boolean z4;
        boolean z5;
        boolean z6 = true;
        if ((activity.getResources().getConfiguration().screenLayout & 15) == 4) {
            z2 = true;
        } else {
            z2 = false;
        }
        if ((activity.getResources().getConfiguration().screenLayout & 15) == 3) {
            z3 = true;
        } else {
            z3 = false;
        }
        if (!z2 && !z3) {
            z4 = false;
        } else {
            z4 = true;
        }
        DisplayMetrics displayMetrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int i2 = displayMetrics.widthPixels;
        int i3 = displayMetrics.heightPixels;
        float f2 = displayMetrics.density;
        float f3 = i2 / f2;
        float f4 = i3 / f2;
        float min = Math.min(f3, f4);
        float max = Math.max(f3, f4);
        if (min > 720.0f || min > 600.0f) {
            z5 = true;
        } else {
            z5 = false;
        }
        if (!z4 && !z5) {
            z6 = false;
        }
        if (max >= 1280.0f) {
            return false;
        }
        return z6;
    }

//    public static Point getTouchPositionFromDragEvent(View view, DragEvent dragEvent) {
//        Rect rect = new Rect();
//        view.getGlobalVisibleRect(rect);
//        return new Point((PrayerTimesApplication.ImageLayoutWidth * (Math.round(dragEvent.getX()) + rect.left)) / PrayerTimesApplication.ScreenWidth, (PrayerTimesApplication.ImageLayoutHeight * (Math.round(dragEvent.getY()) + rect.top)) / PrayerTimesApplication.ScreenHeight);
//    }

    public static boolean isTouchInsideOfRect(Point point, Rect rect) {
        int i2;
        int i3 = point.x;
        if (i3 > rect.left && i3 < rect.right && (i2 = point.y) > rect.top && i2 < rect.bottom) {
            return true;
        }
        return false;
    }

//    public static Typeface getTypefaceDefaultApp(Context context) {
//        return Typeface.createFromAsset(context.getAssets(), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.default_font_full_app));
//    }

//    public static String getURLImg(String str) {
//        if (!str.contains(azan_raw)) {
//            return v0.a("<div style='text-align: center;'><img src='file://", str, "' alt='Azkar' height='100%' width='100%'  style='background-color:#353535;' ></div>");
//        }
//        return "<div style='text-align: center;'><img src='file:///android_asset/azkar/" + MainActivityControls.INSTANCE.getDrawableAzkarFileTxt(str) + "' alt='Azkar' height='100%' width='100%'  style='background-color:#353535;' ></div>";
//    }

//    public static int get_current_volume_level(Context context, int i2) {
//        return ((AudioManager) context.getSystemService("audio")).getStreamVolume(i2);
//    }

//    public static int get_max_volume_level(Context context, int i2) {
//        return ((AudioManager) context.getSystemService("audio")).getStreamMaxVolume(i2);
//    }

//    private static void h(Context context, Database database, List<String> list, boolean z2, List<Alarm> list2) {
//        String str = list.get(6);
//        StringBuilder sb = new StringBuilder();
//        Alarm.Type type = Alarm.Type.IKAMA;
//        sb.append(type.toString());
//        sb.append(" ");
//        sb.append(context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerFajr));
//        g(list2, Alarm.createAlarm(z2, str, sb.toString(), type, Alarm.Type_2.IKAMA_Fajr), database);
//        if (!PrayerTimesApplication.is_jomo3a_today) {
//            g(list2, Alarm.createAlarm(z2, list.get(7), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhr), type, Alarm.Type_2.IKAMA_Dhuhr), database);
//        } else {
//            g(list2, Alarm.createAlarm(z2, list.get(7), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerJomo3a), type, Alarm.Type_2.IKAMA_Jomo3a), database);
//        }
//        g(list2, Alarm.createAlarm(z2, list.get(8), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerAsr), type, Alarm.Type_2.IKAMA_Asr), database);
//        g(list2, Alarm.createAlarm(z2, list.get(9), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerMaghrib), type, Alarm.Type_2.IKAMA_Maghrib), database);
//        g(list2, Alarm.createAlarm(z2, list.get(10), type.toString() + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerIsha), type, Alarm.Type_2.IKAMA_Isha), database);
//        g(list2, Alarm.createAlarm(z2, list.get(11), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.duhaatxt), type, Alarm.Type_2.IKAMA_Shuruq), database);
//    }

//    private static void i(Preference preference, Context context, Database database) {
//        database.deleteAlarms(Alarm.Type.SCREEN_BEGIN, Alarm.Type.SCREEN_END, Alarm.Type.CLOSE_ALARM_SCREEN_BEGIN);
//        if (preference.isAutoScreenEnableed()) {
//            preference.getSilentStart();
//            preference.getSilentDuration();
//            if (preference.getEnableScreenFajr()) {
//                int screenStartFajr = preference.getScreenStartFajr();
//                int screenDurationFajr = preference.getScreenDurationFajr();
//                Alarm alarm = database.getAlarm(Alarm.Type_2.AZAN_Fajr);
//                String str = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerFajr);
//                if (alarm != null) {
//                    o(context, screenStartFajr, screenDurationFajr, alarm.getAlarmTime(), str, database);
//                }
//            }
//            if (preference.getEnableScreenShuruq()) {
//                int screenStartShuruq = preference.getScreenStartShuruq();
//                int screenDurationShuruq = preference.getScreenDurationShuruq();
//                Alarm alarm2 = database.getAlarm(Alarm.Type_2.AZAN_Shuruq);
//                String str2 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.duhaatxt);
//                if (alarm2 != null) {
//                    o(context, screenStartShuruq, screenDurationShuruq, alarm2.getAlarmTime(), str2, database);
//                }
//            }
//            if (preference.getEnableScreenDhuhr() && !PrayerTimesApplication.is_jomo3a_today) {
//                int screenStartDhuhr = preference.getScreenStartDhuhr();
//                int screenDurationDhuhr = preference.getScreenDurationDhuhr();
//                Alarm alarm3 = database.getAlarm(Alarm.Type_2.AZAN_Dhuhr);
//                String str3 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerDhuhr);
//                if (alarm3 != null) {
//                    o(context, screenStartDhuhr, screenDurationDhuhr, alarm3.getAlarmTime(), str3, database);
//                }
//            }
//            if (preference.getEnableScreenJomo3a() && PrayerTimesApplication.is_jomo3a_today) {
//                int screenStartJomo3a = preference.getScreenStartJomo3a();
//                int screenDurationJomo3a = preference.getScreenDurationJomo3a();
//                Alarm alarm4 = database.getAlarm(Alarm.Type_2.AZAN_Jomo3a);
//                String str4 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerJomo3a);
//                if (alarm4 != null) {
//                    o(context, screenStartJomo3a, screenDurationJomo3a, alarm4.getAlarmTime(), str4, database);
//                }
//            }
//            if (preference.getEnableScreenAsr()) {
//                int screenStartAsr = preference.getScreenStartAsr();
//                int screenDurationAsr = preference.getScreenDurationAsr();
//                Alarm alarm5 = database.getAlarm(Alarm.Type_2.AZAN_Asr);
//                String str5 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerAsr);
//                if (alarm5 != null) {
//                    o(context, screenStartAsr, screenDurationAsr, alarm5.getAlarmTime(), str5, database);
//                }
//            }
//            if (preference.getEnableScreenMaghrib()) {
//                int screenStartMaghrib = preference.getScreenStartMaghrib();
//                int screenDurationMaghrib = preference.getScreenDurationMaghrib();
//                Alarm alarm6 = database.getAlarm(Alarm.Type_2.AZAN_Maghrib);
//                String str6 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerMaghrib);
//                if (alarm6 != null) {
//                    o(context, screenStartMaghrib, screenDurationMaghrib, alarm6.getAlarmTime(), str6, database);
//                }
//            }
//            if (preference.getEnableScreenIsha()) {
//                int screenStartIsha = preference.getScreenStartIsha();
//                int screenDurationIsha = preference.getScreenDurationIsha();
//                Alarm alarm7 = database.getAlarm(Alarm.Type_2.AZAN_Isha);
//                String str7 = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.title_ScreenAlarm_name) + " " + context.getString(com.alawail.alarm.prayertimes_mobile.R.string.iqamaPrayerIsha);
//                if (alarm7 != null) {
//                    o(context, screenStartIsha, screenDurationIsha, alarm7.getAlarmTime(), str7, database);
//                }
//            }
//        }
//    }

    public static boolean isTouchInsideOfView(View view, Point point) {
        Rect rect = new Rect();
        view.getGlobalVisibleRect(rect);
        return isTouchInsideOfRect(point, rect);
    }

    public static boolean isTvBox() {
        return true;
    }

//    public static boolean isContains(String[] strArr, String str) {
//        CustomStringList3 customStringList3 = new CustomStringList3();
//        customStringList3.addAll(Arrays.asList(strArr));
//        return customStringList3.contains(str);
//    }

    public static String joinStrs(String str, String[] strArr) {
        ArrayList arrayList = new ArrayList();
        for (String str2 : strArr) {
            String trim = str2.trim();
            if (!trim.equals("")) {
                arrayList.add(trim.trim());
            }
        }
        return TextUtils.join(str, arrayList);
    }

//    public static boolean isFileVideoDep(String str) {
//        if (!check3gp_mp4File(str) && !checkImgFile(str) && !checkPDFFile(str)) {
//            return false;
//        }
//        return true;
//    }

    private static String l(String str) {
        if (TextUtils.isEmpty(str)) {
            return str;
        }
        char[] charArray = str.toCharArray();
        StringBuilder sb = new StringBuilder();
        boolean z2 = true;
        for (char c2 : charArray) {
            if (z2 && Character.isLetter(c2)) {
                sb.append(Character.toUpperCase(c2));
                z2 = false;
            } else {
                if (Character.isWhitespace(c2)) {
                    z2 = true;
                }
                sb.append(c2);
            }
        }
        return sb.toString();
    }

//    public static boolean isSilent(String str) {
//        if (str != null && !str.isEmpty() && !str.equals(ScreenFileListActivity.SILENT_SELECTED)) {
//            return false;
//        }
//        return true;
//    }

    public static String msToString2(long j2) {
        Locale locale = Locale.ENGLISH;
        TimeUnit timeUnit = TimeUnit.MILLISECONDS;
        return String.format(locale, "%02d:%02d:%02d", Long.valueOf(timeUnit.toHours(j2)), Long.valueOf(timeUnit.toMinutes(j2) - TimeUnit.HOURS.toMinutes(timeUnit.toHours(j2))), Long.valueOf(timeUnit.toSeconds(j2) - TimeUnit.MINUTES.toSeconds(timeUnit.toMinutes(j2))));
    }

    public static boolean now_Is_bigger_random_NextDate4Request(String str) {
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss", Locale.ENGLISH);
            try {
                if (simpleDateFormat.parse(simpleDateFormat.format(calendar.getTime())).getTime() - simpleDateFormat.parse(str).getTime() < 1) {
                    return false;
                }
                return true;
            } catch (Exception e2) {
                CrashlyticsUtils.INSTANCE.logException(e2);
                return false;
            }
        } catch (Exception e3) {
            CrashlyticsUtils.INSTANCE.logException(e3);
            return false;
        }
    }

    public static boolean now_Is_bigger_random_NextDate4Request_1Hour(String str) {
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss", Locale.ENGLISH);
            try {
                long time = simpleDateFormat.parse(simpleDateFormat.format(calendar.getTime())).getTime() - simpleDateFormat.parse(str).getTime();
                long j2 = (time / 1000) % 60;
                long j3 = (time / 60000) % 60;
                long j4 = (time / 3600000) % 24;
                if (time / 86400000 < 0 || j4 < 1) {
                    return false;
                }
                return true;
            } catch (Exception e2) {
                CrashlyticsUtils.INSTANCE.logException(e2);
                return false;
            }
        } catch (Exception e3) {
            CrashlyticsUtils.INSTANCE.logException(e3);
            return false;
        }
    }

    public static String readTextFromFile(String str) {
        try {
            FileInputStream fileInputStream = new FileInputStream(new File(str));
            byte[] bArr = new byte[fileInputStream.available()];
            fileInputStream.read(bArr);
            fileInputStream.close();
            return new String(bArr);
        } catch (IOException e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
            return "";
        }
    }

//    private static void j(int i2, int i3, Context context, Database database, List<String> list, boolean z2) {
//        if (i2 != 0 && i3 != 0) {
//            Hour hour = new Hour();
//            if (z2) {
//                hour.setHour(Hour.Type.hour12);
//            } else {
//                hour.setHour(Hour.Type.hour24);
//            }
//            String f3783a = new Time_Database(((i2 * 60) + TimeHelper.getSecFromStr(list.get(4))) / 60, true, hour).getF3783a();
//            String string = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.powerSaveOff);
//            Alarm.Type type = Alarm.Type.SCREEN_OFF;
//            Alarm.Type_2 type_2 = Alarm.Type_2.NORMAL;
//            Alarm createAlarm = Alarm.createAlarm(z2, f3783a, string, type, type_2);
//            createAlarm.setQuran_str("alarmOff");
//            MyLog("checkAlarmTime", "checkAlarmTime getAll 25");
//            database.create(database.checkAlarmTime(createAlarm));
//            int secFromStr = TimeHelper.getSecFromStr(list.get(0)) - (i3 * 60);
//            if (secFromStr < 0) {
//                secFromStr += 86400;
//            }
//            Alarm createAlarm2 = Alarm.createAlarm(z2, new Time_Database(secFromStr / 60, true, hour).getF3783a(), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.powerSaveOn), Alarm.Type.SCREEN_ON, type_2);
//            createAlarm2.setQuran_str("alarmOn");
//            MyLog("checkAlarmTime", "checkAlarmTime getAll 26");
//            database.create(database.checkAlarmTime(createAlarm2));
//        }
//    }

    public static double round_double(double d2, int i2) {
        if (i2 >= 0) {
            double pow = (long) Math.pow(10.0d, i2);
            Double.isNaN(pow);
            double round = Math.round(d2 * pow);
            Double.isNaN(round);
            Double.isNaN(pow);
            return round / pow;
        }
        throw new IllegalArgumentException();
    }

//    private static void k(Preference preference, Context context, Database database, Calendar calendar) {
//        database.deleteAlarms(Alarm.Type.SILENT_BEGIN, Alarm.Type.SILENT_END);
//        if (preference.isAutoSilentEnableed()) {
//            preference.getSilentStart();
//            preference.getSilentDuration();
//            if (preference.getEnableSilentFajr()) {
//                int silentStartFajr = preference.getSilentStartFajr();
//                int silentDurationFajr = preference.getSilentDurationFajr();
//                Alarm alarm = database.getAlarm(Alarm.Type_2.AZAN_Fajr);
//                if (alarm != null) {
//                    p(context, silentStartFajr, silentDurationFajr, alarm.getAlarmTime(), database);
//                }
//            }
//            if (preference.getEnableSilentDhuhr() && !PrayerTimesApplication.is_jomo3a_today) {
//                int silentStartDhuhr = preference.getSilentStartDhuhr();
//                int silentDurationDhuhr = preference.getSilentDurationDhuhr();
//                Alarm alarm2 = database.getAlarm(Alarm.Type_2.AZAN_Dhuhr);
//                if (alarm2 != null) {
//                    p(context, silentStartDhuhr, silentDurationDhuhr, alarm2.getAlarmTime(), database);
//                }
//            }
//            if (preference.getEnableSilentJomo3a() && PrayerTimesApplication.is_jomo3a_today) {
//                int silentStartJomo3a = preference.getSilentStartJomo3a();
//                int silentDurationJomo3a = preference.getSilentDurationJomo3a();
//                Alarm alarm3 = database.getAlarm(Alarm.Type_2.AZAN_Jomo3a);
//                if (alarm3 != null) {
//                    p(context, silentStartJomo3a, silentDurationJomo3a, alarm3.getAlarmTime(), database);
//                }
//            }
//            if (preference.getEnableSilentAsr()) {
//                int silentStartAsr = preference.getSilentStartAsr();
//                int silentDurationAsr = preference.getSilentDurationAsr();
//                Alarm alarm4 = database.getAlarm(Alarm.Type_2.AZAN_Asr);
//                if (alarm4 != null) {
//                    p(context, silentStartAsr, silentDurationAsr, alarm4.getAlarmTime(), database);
//                }
//            }
//            if (preference.getEnableSilentMaghrib()) {
//                int silentStartMaghrib = preference.getSilentStartMaghrib();
//                int silentDurationMaghrib = preference.getSilentDurationMaghrib();
//                Alarm alarm5 = database.getAlarm(Alarm.Type_2.AZAN_Maghrib);
//                if (alarm5 != null) {
//                    p(context, silentStartMaghrib, silentDurationMaghrib, alarm5.getAlarmTime(), database);
//                }
//            }
//            if (preference.getEnableSilentTarawih() && getCurrentHijriMonth(preference, calendar) == 9) {
//                int silentStartTarawih = preference.getSilentStartTarawih();
//                int silentDurationTarawih = preference.getSilentDurationTarawih();
//                Alarm alarm6 = database.getAlarm(Alarm.Type_2.AZAN_Isha);
//                if (alarm6 != null) {
//                    p(context, silentStartTarawih, silentDurationTarawih, alarm6.getAlarmTime(), database);
//                    return;
//                }
//                return;
//            }
//            if (preference.getEnableSilentIsha()) {
//                int silentStartIsha = preference.getSilentStartIsha();
//                int silentDurationIsha = preference.getSilentDurationIsha();
//                Alarm alarm7 = database.getAlarm(Alarm.Type_2.AZAN_Isha);
//                if (alarm7 != null) {
//                    p(context, silentStartIsha, silentDurationIsha, alarm7.getAlarmTime(), database);
//                }
//            }
//        }
//    }

    public static byte[] serialize(Object obj) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            new ObjectOutputStream(byteArrayOutputStream).writeObject(obj);
        } catch (IOException e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
        }
        return byteArrayOutputStream.toByteArray();
    }

//    public static String loadFileAsString(String str) {
//        try {
//            StringBuffer stringBuffer = new StringBuffer(1000);
//            BufferedReader bufferedReader = new BufferedReader(new FileReader(str));
//            char[] cArr = new char[1024];
//            while (true) {
//                int read = bufferedReader.read(cArr);
//                if (read != -1) {
//                    stringBuffer.append(String.valueOf(cArr, 0, read));
//                } else {
//                    bufferedReader.close();
//                    return stringBuffer.toString();
//                }
//            }
//        } catch (Exception e2) {
//            androidx.fragment.app.q.c(e2, androidx.fragment.app.p.b(e2, ""), "eee0000");
//            return "";
//        }
//    }

//    public static ArrayList<HashMap<String, String>> loadIslamicScreenImgsFromLocal(Context context) {
//        ArrayList<HashMap<String, String>> arrayList = new ArrayList<>();
//        ArrayList arrayList2 = null;
//        try {
//            Gson gson = new Gson();
//            String videoList_Screen = new Preference(context).getVideoList_Screen();
//            MyLog("VideoList_Screen", " R " + videoList_Screen);
//            if (videoList_Screen != null) {
//                arrayList2 = (ArrayList) gson.fromJson(videoList_Screen, new k().getType());
//            }
//        } catch (Exception e2) {
//            androidx.fragment.app.q.c(e2, androidx.fragment.app.p.b(e2, " error "), "VideoList_Screen");
//        }
//        ArrayList arrayList3 = new ArrayList();
//        arrayList3.clear();
//        if (arrayList2 != null) {
//            for (int i2 = 0; i2 < arrayList2.size(); i2++) {
//                arrayList3.add((HashMap) arrayList2.get(i2));
//                MyLog("VideoList_Screen", " productsList " + arrayList2.get(i2));
//            }
//            MyLog("VideoList_Screen", " productsList " + arrayList2.size());
//            for (int i3 = 0; i3 < arrayList3.size(); i3++) {
//                HashMap hashMap = (HashMap) arrayList3.get(i3);
//                String str = (String) hashMap.get(GetDataService_TAG_NAME);
//                String str2 = (String) hashMap.get(GetDataService_TAG_DURATION);
//                if (str.contains(";")) {
//                    str = getFileName(str).split(";")[0];
//                }
//                HashMap<String, String> hashMap2 = new HashMap<>();
//                hashMap2.put(GetDataService_TAG_NAME, str + ";" + str2);
//                hashMap2.put(GetDataService_TAG_DURATION, str2);
//                arrayList.add(hashMap2);
//            }
//        }
//        return arrayList;
//    }

//    private static void m(Context context, int i2, Calendar calendar, Database database, List<Alarm> list) {
//        int i3 = calendar.get(11);
//        int i4 = calendar.get(12);
//        int i5 = calendar.get(13);
//        Calendar calendar2 = Calendar.getInstance();
//        calendar2.set(11, i3);
//        calendar2.set(12, i4);
//        calendar2.set(13, i5);
//        calendar2.set(14, 0);
//        calendar2.add(12, i2);
//        MyLog("checkAlarmTime", "checkAlarmTime getAll 19");
//        database.create(database.checkAlarmTime(Alarm.createAlarm(calendar2, context.getString(com.alawail.alarm.prayertimes_mobile.R.string.autoHideLastNotification), Alarm.Type.AUTO_HIDE_ALARM, Alarm.Type_2.NORMAL), list));
//    }

//    public static void moveToSilentVibration(Preference preference, AudioManager audioManager) {
//        if (preference.getEnableSilentVibration()) {
//            audioManager.setRingerMode(1);
//        } else {
//            audioManager.setRingerMode(0);
//        }
//        preference.setStartingSilentVibration(true);
//    }

//    public static long msToHMS(long j2) {
//        Calendar.getInstance().setTimeInMillis(j2);
//        return (r0.get(11) * 60 * 60) + (r0.get(12) * 60) + r0.get(13);
//    }

//    public static String msToString(long j2) {
//        String a2;
//        long j3 = j2 / 1000;
//        long j4 = j3 / 3600;
//        long j5 = (j3 / 60) % 60;
//        long j6 = j3 % 60;
//        String str = "00";
//        if (j5 == 0) {
//            a2 = "00";
//        } else if (j5 < 10) {
//            a2 = android.support.v4.media.j.a("0", j5);
//        } else {
//            a2 = android.support.v4.media.j.a("", j5);
//        }
//        if (j6 != 0) {
//            if (j6 < 10) {
//                str = android.support.v4.media.j.a("0", j6);
//            } else {
//                str = android.support.v4.media.j.a("", j6);
//            }
//        }
//        if (j4 > 0) {
//            return j4 + ":" + a2 + ":" + str;
//        }
//        if (j5 > 0) {
//            return j5 + ":" + str;
//        }
//        return androidx.constraintlayout.core.motion.key.a.a(":", str);
//    }

    public static Float strToFloat(String str) {
        return Float.parseFloat(str);
    }

//    public static String msToString3(long j2) {
//        String str;
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTimeInMillis(j2);
//        if (calendar.get(11) <= 9) {
//            str = "0";
//        } else {
//            str = "";
//        }
//        StringBuilder b2 = android.support.v4.media.c.b(str);
//        b2.append(String.valueOf(calendar.get(11)));
//        String b3 = android.support.v4.media.a.b(b2.toString(), ":");
//        if (calendar.get(12) <= 9) {
//            b3 = android.support.v4.media.a.b(b3, "0");
//        }
//        StringBuilder b4 = android.support.v4.media.c.b(b3);
//        b4.append(String.valueOf(calendar.get(12)));
//        String b5 = android.support.v4.media.a.b(b4.toString(), ":");
//        if (calendar.get(13) <= 9) {
//            b5 = android.support.v4.media.a.b(b5, "0");
//        }
//        StringBuilder b6 = android.support.v4.media.c.b(b5);
//        b6.append(String.valueOf(calendar.get(13)));
//        return b6.toString();
//    }

//    private static void n(Context context, int i2, int i3, Calendar calendar, String str, String str2, Database database) {
//        try {
//            int i4 = calendar.get(11);
//            int i5 = calendar.get(12);
//            int i6 = calendar.get(13);
//            Calendar calendar2 = Calendar.getInstance();
//            calendar2.set(11, i4);
//            calendar2.set(12, i5);
//            calendar2.set(13, i6);
//            calendar2.set(14, 0);
//            calendar2.add(12, i2);
//            MyLog("checkAlarmTime", "checkAlarmTime getAll 21");
//            Alarm checkAlarmTime = database.checkAlarmTime(Alarm.createAlarm(calendar2, str2, Alarm.Type.AZKAR_BEGIN, Alarm.Type_2.NORMAL));
//            checkAlarmTime.setAlarmTextWindowShow(Boolean.TRUE);
//            checkAlarmTime.setAlarmTextWindowCloseAfter(i3);
//            String uRLImg = getURLImg(str);
//            checkAlarmTime.setAlarmTxtContentCustom(uRLImg);
//            MyLog("get_TxtContentCustom", "" + uRLImg);
//            MyLog("Azkar_screen", "createAzkarBeginEndAlarm " + str2 + "  " + uRLImg);
//            database.create(checkAlarmTime);
//            a(context, checkAlarmTime.getAlarmTextWindowCloseAfter(), checkAlarmTime.getAlarmTime().getTimeInMillis(), calendar2, checkAlarmTime.getId());
//        } catch (Exception e2) {
//            androidx.fragment.app.q.c(e2, androidx.fragment.app.p.b(e2, "createAzkarBeginEndAlarm error "), "Azkar_screen");
//        }
//    }

    public static void shareTxt(Context context, String str) {
        Intent intent = new Intent("android.intent.action.SEND");
        intent.putExtra("android.intent.extra.TEXT", str);
        intent.setType("text/plain");
        context.startActivity(Intent.createChooser(intent, "Share via"));
    }

    public static void showAppInfo(Activity activity) {
        try {
            Intent intent = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS");
            intent.setData(Uri.parse("package:com.alawail.alarm.prayertimes_mobile"));
            activity.startActivity(intent);
        } catch (Exception e2) {
            CrashlyticsUtils.INSTANCE.logException(e2);
        }
    }

//    private static void o(Context context, int i2, int i3, Calendar calendar, String str, Database database) {
//        try {
//            int i4 = calendar.get(11);
//            int i5 = calendar.get(12);
//            int i6 = calendar.get(13);
//            Calendar calendar2 = Calendar.getInstance();
//            calendar2.set(11, i4);
//            calendar2.set(12, i5);
//            calendar2.set(13, i6);
//            calendar2.set(14, 0);
//            calendar2.add(12, i2);
//            MyLog("checkAlarmTime", "checkAlarmTime getAll 22");
//            Alarm checkAlarmTime = database.checkAlarmTime(Alarm.createAlarm(calendar2, str, Alarm.Type.SCREEN_BEGIN, Alarm.Type_2.NORMAL));
//            checkAlarmTime.setAlarmTextWindowCloseAfter(i3);
//            MyLog("Azkar_screen1", "createScreenBeginEndAlarm  " + str);
//            database.create(checkAlarmTime);
//            CreateCloseAlarmScreenBegin(context, checkAlarmTime.getAlarmTextWindowCloseAfter(), context.getString(com.alawail.alarm.prayertimes_mobile.R.string.CloseScreen_TimerPlay), checkAlarmTime.getAlarmTime().getTimeInMillis(), calendar2);
//        } catch (Exception e2) {
//            androidx.fragment.app.q.c(e2, androidx.fragment.app.p.b(e2, "createScreenBeginEndAlarm error "), "Azkar_screen1");
//        }
//    }

//    public static void openPowerManager(Context context) {
//        try {
//            for (Intent intent : StopAppActivity.POWERMANAGER_INTENTS) {
//                if (context.getPackageManager().resolveActivity(intent, 65536) != null) {
//                    context.startActivity(intent);
//                    return;
//                }
//            }
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

//    private static void p(Context context, int i2, int i3, Calendar calendar, Database database) {
//        int i4 = calendar.get(11);
//        int i5 = calendar.get(12);
//        int i6 = calendar.get(13);
//        Calendar calendar2 = Calendar.getInstance();
//        calendar2.set(11, i4);
//        calendar2.set(12, i5);
//        calendar2.set(13, i6);
//        calendar2.set(14, 0);
//        calendar2.add(12, i2);
//        Calendar calendar3 = Calendar.getInstance();
//        calendar3.set(11, i4);
//        calendar3.set(12, i5);
//        calendar3.set(13, i6);
//        calendar3.set(14, 0);
//        calendar3.add(12, i2 + i3);
//        MyLog("checkAlarmTime", "checkAlarmTime getAll 23");
//        String string = context.getString(com.alawail.alarm.prayertimes_mobile.R.string.silentBegin);
//        Alarm.Type type = Alarm.Type.SILENT_BEGIN;
//        Alarm.Type_2 type_2 = Alarm.Type_2.NORMAL;
//        database.create(database.checkAlarmTime(Alarm.createAlarm(calendar2, string, type, type_2)));
//        database.create(database.checkAlarmTime(Alarm.createAlarm(calendar3, context.getString(com.alawail.alarm.prayertimes_mobile.R.string.silentEnd), Alarm.Type.SILENT_END, type_2)));
//    }

//    private static void q(File file) {
//        if (file.exists() && !file.delete()) {
//            throw new IOException(String.format("File %s can't be deleted", file.getAbsolutePath()));
//        }
//    }

    public static void startService(Context context, Intent intent) {
        MyLog("not null", "startService " + intent);
        if (Build.VERSION.SDK_INT >= 26) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

//    public static void refreshAllWidgets(Context context) {
//        NECESSARY_12_NIGHT = true;
//        PrayerTimesApplication.isNECESSARY_12_NIGHT__ScreenActivity = true;
//        PrayerTimesApplication.isNECESSARY_12_NIGHT__ScreenActivity_2 = true;
//        PrayerTimesApplication.date4PrayerTime = null;
//        try {
//            new MainActivityControls(context).updateWidget();
//            MyLog("GPS_INFO555", "refreshAllWidgets isNECESSARY_12_NIGHT__Widget3 111 refreshAllWidgets " + PrayerTimesApplication.isNECESSARY_12_NIGHT__Widget3);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

//    public static boolean refreshLocale(Context context) {
//        String string = PreferenceManager.getDefaultSharedPreferences(context).getString("LanguageLocale", getCurrentLang());
//        boolean IsRTL = IsRTL(string);
//        Locale locale = new Locale(string);
//        Locale locale2 = new Locale("ar");
//        Resources resources = context.getResources();
//        DisplayMetrics displayMetrics = resources.getDisplayMetrics();
//        Configuration configuration = resources.getConfiguration();
//        configuration.locale = locale;
//        if (IsRTL_Other(string)) {
//            configuration.setLayoutDirection(locale2);
//        } else {
//            configuration.setLayoutDirection(configuration.locale);
//        }
//        resources.updateConfiguration(configuration, displayMetrics);
//        Resources resources2 = context.getApplicationContext().getResources();
//        DisplayMetrics displayMetrics2 = resources2.getDisplayMetrics();
//        Configuration configuration2 = resources2.getConfiguration();
//        configuration2.locale = locale;
//        if (IsRTL_Other(string)) {
//            configuration.setLayoutDirection(locale2);
//        } else {
//            configuration2.setLayoutDirection(configuration2.locale);
//        }
//        resources2.updateConfiguration(configuration2, displayMetrics2);
//        PrayerTimesApplication.isRTL = IsRTL;
//        return IsRTL;
//    }

    public static Calendar toCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

//    public static void saveLogcat() {
//        try {
//            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(Runtime.getRuntime().exec("logcat -d").getInputStream()));
//            StringBuilder sb = new StringBuilder();
//            while (true) {
//                String readLine = bufferedReader.readLine();
//                if (readLine == null) {
//                    break;
//                } else {
//                    sb.append(readLine);
//                }
//            }
//            File file = new File(getExternalStorageDir() + File.separator + "LogTest.txt");
//            if (!file.exists()) {
//                file.createNewFile();
//            }
//            FileOutputStream fileOutputStream = new FileOutputStream(file, true);
//            fileOutputStream.write(sb.toString().getBytes());
//            fileOutputStream.close();
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0027. Please report as an issue. */
//    public static void saveMultiSelectListLockWidget(Set<String> set, Preference preference) {
//        preference.set_hijri_date_widget_lock_visible(false);
//        preference.set_city_widget_lock_visible(false);
//        preference.set_fasting_widget_lock_visible(false);
//        Iterator<String> it = set.iterator();
//        while (it.hasNext()) {
//            String trim = it.next().trim();
//            trim.getClass();
//            char c2 = 65535;
//            switch (trim.hashCode()) {
//                case -1097222349:
//                    if (trim.equals("widget_visible_city")) {
//                        c2 = 0;
//                        break;
//                    }
//                    break;
//                case -217366722:
//                    if (trim.equals("widget_visible_fasting")) {
//                        c2 = 1;
//                        break;
//                    }
//                    break;
//                case 350453432:
//                    if (trim.equals("widget_visible_hijri")) {
//                        c2 = 2;
//                        break;
//                    }
//                    break;
//            }
//            switch (c2) {
//                case 0:
//                    preference.set_city_widget_lock_visible(true);
//                    break;
//                case 1:
//                    preference.set_fasting_widget_lock_visible(true);
//                    break;
//                case 2:
//                    preference.set_hijri_date_widget_lock_visible(true);
//                    break;
//            }
//        }
//    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0027. Please report as an issue. */
//    public static void saveMultiSelectListWidget(Set<String> set, Preference preference) {
//        preference.set_clock_widget_visible(false);
//        preference.set_city_widget_visible(false);
//        preference.set_fasting_widget_visible(false);
//        Iterator<String> it = set.iterator();
//        while (it.hasNext()) {
//            String trim = it.next().trim();
//            trim.getClass();
//            char c2 = 65535;
//            switch (trim.hashCode()) {
//                case -1097222349:
//                    if (trim.equals("widget_visible_city")) {
//                        c2 = 0;
//                        break;
//                    }
//                    break;
//                case -217366722:
//                    if (trim.equals("widget_visible_fasting")) {
//                        c2 = 1;
//                        break;
//                    }
//                    break;
//                case 345929542:
//                    if (trim.equals("widget_visible_clock")) {
//                        c2 = 2;
//                        break;
//                    }
//                    break;
//            }
//            switch (c2) {
//                case 0:
//                    preference.set_city_widget_visible(true);
//                    break;
//                case 1:
//                    preference.set_fasting_widget_visible(true);
//                    break;
//                case 2:
//                    preference.set_clock_widget_visible(true);
//                    break;
//            }
//        }
//    }

    public static String intToTime(int i2) {
        int i3 = i2 / 60;
        int i4 = i2 - (i3 * 60);
        return (i3 < 0 || i4 < 0) ? "" : String.format(Locale.ENGLISH, "%02d:%02d", Integer.valueOf(i3), Integer.valueOf(i4));
    }

//    public static void setActionBar(AppCompatActivity appCompatActivity, int i2) {
//        try {
//            Toolbar toolbar = (Toolbar) appCompatActivity.findViewById(com.alawail.alarm.prayertimes_mobile.R.id.toolbar);
//            appCompatActivity.setSupportActionBar(toolbar);
//            appCompatActivity.getSupportActionBar().setDisplayHomeAsUpEnabled(true);
//            appCompatActivity.getSupportActionBar().setDisplayShowHomeEnabled(true);
//            appCompatActivity.setTitle("");
//            appCompatActivity.getSupportActionBar().setTitle(i2);
//            appCompatActivity.getSupportActionBar().setDisplayShowTitleEnabled(true);
//            if (!IsRTL(appCompatActivity) || appCompatActivity.findViewById(com.alawail.alarm.prayertimes_mobile.R.id.toolbar) == null) {
//                return;
//            }
//            toolbar.setNavigationIcon(com.alawail.alarm.prayertimes_mobile.R.drawable.m_back_arrow_right);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

//    public static void setBtnDlgProps(MaterialDialog.Builder builder) {
//        builder.typeface(getTypefaceDefaultApp(builder.getContext()), getTypefaceDefaultApp(builder.getContext())).positiveColor(getPositiveColorDLG()).negativeColor(getNegativeColorDLG()).buttonsGravity(GravityEnum.CENTER);
//    }

//    public static void setBtnDlgWidth(MaterialDialog materialDialog, boolean z2) {
//        MyLog("setBtnDlgWidth", "setBtnDlgWidth");
//        materialDialog.setOnShowListener(new i0(materialDialog));
//        if (z2) {
//            materialDialog.show();
//        }
//    }

//    public static boolean setCityDatabase(Context context, long j2, City city) {
//        DatabaseHelper databaseHelper = DatabaseHelper.getInstance(context);
//        databaseHelper.actualCity.getCityActual(j2, city);
//        Manager manager = new Manager(context);
//        Preference preference = manager.getPreference();
//        boolean z2 = true;
//        if (databaseHelper.actualCityData.checkDataCityActual(city)) {
//            preference.setCity_Selected(true);
//            preference.setMy_City_Selected(true);
//            manager.updateCity(city);
//            city.clearPrayerTimeShiftAndIkamaDB();
//            city.setPrayerTimes_Expression(false);
//            if (city instanceof City_2) {
//                try {
//                    City.Exp exp = city.exp;
//                    exp.timezone_preferen = String.format(Locale.US, "%.2f", Double.valueOf(strToFloat(exp.timezone_preferen, 3.0f)));
//                    preference.set_time_zone_city_2(city.exp.timezone_preferen);
//                    MyLog("isFirstRun", "setCityDatabase City_2 " + city.exp.timezone_preferen);
//                } catch (Exception e2) {
//                    e2.printStackTrace();
//                }
//            }
//            if (city.azanDB.year_type.equals(City.YEAR_ONE_REPEAT_LOOP_UAE)) {
//                city.seasonCity.setSeason(new Season(Season.Type.Summer));
//            }
//            MyLog("_name_city", "setCityDatabase " + city.exp.timezone_preferen);
//            MyLog("isFirstRun", "setCityDatabase " + city.exp.timezone_preferen);
//            MyLog("is_Makkah_default_city", "2017-03-08");
//        } else {
//            MyToast(context, com.alawail.alarm.prayertimes_mobile.R.string.error_actual_data, 1);
//            z2 = false;
//        }
//        DatabaseHelper.Close();
//        return z2;
//    }

    public static void initCheckIkamaAzkarBlackScreenBackPress() {
    }

//    public static void setCityExpression(Context context, City city) {
//        Manager manager = new Manager(context);
//        city.ikametDB.setNotFound4Exp();
//        city.clearPrayerTimeShiftAndIkamaDB();
//        city.setPrayerTimes_Expression(true);
//        manager.getPreference().setCity_Selected(true);
//        manager.getPreference().setMy_City_Selected(true);
//        City.Exp exp = city.exp;
//        exp.set_longitude_preferen(exp.longitude);
//        City.Exp exp2 = city.exp;
//        exp2.setLongitude(exp2.longitude);
//        City.Exp exp3 = city.exp;
//        exp3.set_latitude_preferen(exp3.latitude);
//        City.Exp exp4 = city.exp;
//        exp4.setLatitude(exp4.latitude);
//        City.Exp exp5 = city.exp;
//        exp5.set_timezone_preferen(String.format(Locale.US, "%.2f", Float.valueOf(exp5.timeZone)));
//        City.Exp exp6 = city.exp;
//        exp6.setTimeZone(exp6.timeZone);
//        CalcMethod calcMethod = new CalcMethod();
//        calcMethod.setCalender(strToInt(city.exp.calcMethod, 0));
//        city.exp.set_calc_method(calcMethod.calenderString());
//        String str = city.seasonCity.season;
//        Season.Type type = Season.Type.Summer;
//        if (str.equals(type.name())) {
//            city.seasonCity.setSeason(new Season(type));
//        } else {
//            city.seasonCity.setSeason(new Season(Season.Type.Winter));
//        }
//        MyLog("m_season--", "db1 exp " + city.seasonCity.season + " " + city.exp.calcMethod + " " + calcMethod.calenderString() + " " + (city instanceof City_2));
//        Mazhab mazhab = new Mazhab();
//        mazhab.setMazhab(strToInt(city.exp.mazhab, 0));
//        city.exp.set_mazhab(mazhab.mazhabString());
//    }


//    public static void setSeasonPrayer(Preference preference, Calendar calendar, City city, String str) {
//        try {
//            boolean z2 = false;
//            if (!city.isPrayerTimes_Expression()) {
//                if (city.azanDB.year_type.equals(City.YEAR_ONE_REPEAT_LOOP_UAE)) {
//                    city.seasonCity.setSeason(new Season(Season.Type.Summer));
//                    MyLog("changeSeasonPrayer1 ", "YEAR_ONE_REPEAT_LOOP_UAE Summer " + city.seasonCity.season + " " + str);
//                    z2 = true;
//                }
//                if (city.country.id.toLowerCase().equals("tr") && city.actual.getTurkey_Actual_Settings().booleanValue()) {
//                    city.seasonCity.setSeason(new Season(Season.Type.Summer));
//                    MyLog("changeSeasonPrayer1 ", "tr Summer " + city.seasonCity.season + " " + str);
//                    z2 = true;
//                }
//                if (city.country.id.toLowerCase().equals("ae") && city.actual.getArab_Emirates_Actual_Settings().booleanValue()) {
//                    city.seasonCity.setSeason(new Season(Season.Type.Summer));
//                    MyLog("changeSeasonPrayer1 ", "ae Summer " + city.seasonCity.season + " " + str);
//                    z2 = true;
//                }
//                if (city.country.id.toLowerCase().equals("iq") && city.actual.getIraq_Actual_Settings().booleanValue()) {
//                    city.seasonCity.setSeason(new Season(Season.Type.Winter));
//                    z2 = true;
//                }
//                if (city.country.id.toLowerCase().equals("ma") && city.actual.getMorocco_Actual_Settings().booleanValue()) {
//                    city.seasonCity.setSeason(new Season(Season.Type.Summer));
//                    MyLog("changeSeasonPrayer1 ", "ma Summer " + city.seasonCity.season + " " + str);
//                    if (getCurrentHijriMonth(preference, calendar) == 9) {
//                        city.seasonCity.setSeason(new Season(Season.Type.Winter));
//                        MyLog("changeSeasonPrayer1 ", "ma Winter " + city.seasonCity.season + " " + str);
//                    }
//                    z2 = true;
//                }
//            }
//            preference.setSeasonPrayerNotChange(Boolean.valueOf(z2));
//            MyLog("setSeasonPrayerNotChange", "" + z2);
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }

    public static void write_preference(Context context) {
    }

    public static void MyLog(String str, String str2) {
    }


    /* loaded from: classes.dex */
    public enum CityDataType {
        DATABASE,
        EXPRESSION,
        NONE
    }

//    public static void storeScreenShot(Bitmap bitmap) {
//        String dirProgram2 = getDirProgram();
//        File file = new File(dirProgram2);
//        if (!file.exists()) {
//            file.mkdirs();
//        }
//        try {
//            FileOutputStream fileOutputStream = new FileOutputStream(new File(dirProgram2, fileScreenshots));
//            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream);
//            fileOutputStream.flush();
//            fileOutputStream.close();
//        } catch (Exception e2) {
//            e2.printStackTrace();
//        }
//    }


//    public static String strToMinutes(String str) {
//        String str2;
//        if (str == null || str == "") {
//            return "0";
//        }
//        try {
//            if (str.isEmpty()) {
//                return "0";
//            }
//            if (str.length() == 5) {
//                str2 = ((strToInt(str.substring(0, 2)) * 60) + strToInt(str.substring(3, 5))) + "";
//            } else {
//                str2 = strToInt(str) + "";
//            }
//            return str2;
//        } catch (Exception e2) {
//            e2.printStackTrace();
//            return "0";
//        }
//    }

//    public static String strToNum(String str, int i2, int i3) {
//        if (str == null || str == "") {
//            return "0";
//        }
//        try {
//            if (str.isEmpty() || str.length() < i3) {
//                return "0";
//            }
//            return strToInt(str.substring(i2, i3)) + "";
//        } catch (Exception e2) {
//            e2.printStackTrace();
//            return "0";
//        }
//    }

    /* loaded from: classes.dex */
    public enum MainActivityType {
        MOBILE,
        TV,
        NONE
    }


    /* loaded from: classes.dex */
    public enum OrientaionTV {
        LANDSCAPE,
        PORTRAIT;

        public static String[] getOrientaionTVs() {
            return new String[]{"LANDSCAPE", "PORTRAIT"};
        }

        @Override // java.lang.Enum
        public String toString() {
            int ordinal = ordinal();
            if (ordinal != 0) {
                if (ordinal != 1) {
                    return super.toString();
                }
                return "PORTRAIT";
            }
            return "LANDSCAPE";
        }
    }


    /* loaded from: classes.dex */
    public enum PowerSave {
        SCREEN_ON,
        SCREEN_OFF,
        NONE;

        public static String[] getPowerSave() {
            return new String[]{"SCREEN_ON", "SCREEN_OFF", "NONE"};
        }

        @Override // java.lang.Enum
        public String toString() {
            int ordinal = ordinal();
            if (ordinal != 0) {
                if (ordinal != 1) {
                    if (ordinal != 2) {
                        return super.toString();
                    }
                    return "NONE";
                }
                return "SCREEN_OFF";
            }
            return "SCREEN_ON";
        }
    }

    /* loaded from: classes.dex */
    public enum ScreenManagementType {
        LOCAL,
        REMOTE,
        REMOTE_NET;

        @Override // java.lang.Enum
        public String toString() {
            int ordinal = ordinal();
            if (ordinal != 0) {
                if (ordinal != 1) {
                    if (ordinal != 2) {
                        return super.toString();
                    }
                    return "REMOTE_NET";
                }
                return "REMOTE";
            }
            return "LOCAL";
        }
    }

//    public static PrayerTime setSeasonPrayer(City city, Preference preference, PrayerTime prayerTime, Calendar calendar, boolean z2) {
//        if (!city.isPrayerTimes_Expression) {
//            if (city.country.id.toLowerCase().equals("sa") && city.actual.isSaudi_Actual_Settings && getCurrentHijriMonth(preference, calendar) == 9) {
//                PrayerTime.INSTANCE.setIshaFromMaghrib(z2, 120);
//            }
//            if (city.country.id.toLowerCase().equals("qa") && city.actual.isQatar_Actual_Settings && getCurrentHijriMonth(preference, calendar) == 9) {
//                PrayerTime.Companion companion = PrayerTime.INSTANCE;
//                companion.setMaghribExtra(z2, 1);
//                companion.setIshaExtra(z2, 1);
//            }
//        }
//        City.FixedIshaMagrib fixedIshaMagrib = city.fixedIshaMagrib;
//        if (fixedIshaMagrib.isIsha_Magrib_Between) {
//            PrayerTime.INSTANCE.setIshaFromMaghrib(z2, strToInt(fixedIshaMagrib.Isha_Magrib_Between_value_minutes, 90));
//        }
//        return prayerTime;
//    }

//    public static void copyFolder(Activity activity, String str, String str2, boolean z2) {
//        String[] strArr;
//        boolean z3;
//        try {
//            AssetManager assets = activity.getAssets();
//            String externalStorageState = Environment.getExternalStorageState();
//            if ("mounted".equals(externalStorageState)) {
//                try {
//                    strArr = assets.list(str);
//                } catch (IOException e2) {
//                    Log.e("ERROR", "Failed to get asset file list.", e2);
//                    strArr = null;
//                }
//                for (String str3 : strArr) {
//                    File file = new File(getDirProgram());
//                    if (!file.exists()) {
//                        file.mkdir();
//                    }
//                    File file2 = new File(getDirProgram() + "/" + str2);
//                    if (!file2.exists()) {
//                        z3 = file2.mkdir();
//                    } else if (z2) {
//                        return;
//                    } else {
//                        z3 = true;
//                    }
//                    if (z3) {
//                        try {
//                            InputStream open = assets.open(str + "/" + str3);
//                            FileOutputStream fileOutputStream = new FileOutputStream(getDirProgram() + "/" + str2 + "/" + str3);
//                            MyLog("WEBVIEW", getDirProgram() + "/" + str2 + "/" + str3);
//                            copyFile(open, fileOutputStream);
//                            open.close();
//                            fileOutputStream.flush();
//                            fileOutputStream.close();
//                        } catch (IOException e3) {
//                            Log.e("ERROR", "Failed to copy asset file: " + str3, e3);
//                        }
//                    }
//                }
//                return;
//            }
//            "mounted_ro".equals(externalStorageState);
//        } catch (Exception e4) {
//            Log.e("ERROR", "copy file ", e4);
//            e4.printStackTrace();
//        }
//    }


    /* loaded from: classes.dex */
    public enum ScreenType {
        NEWS,
        VIDEOS,
        FATAWA,
        LECTURES,
        NONE;

        public static String[] getScreenTypes() {
            return new String[]{"NEWS", "VIDEOS", "FATAWA", "LECTURES", "NONE"};
        }

        @Override // java.lang.Enum
        public String toString() {
            int ordinal = ordinal();
            if (ordinal != 0) {
                if (ordinal != 1) {
                    if (ordinal != 2) {
                        if (ordinal != 3) {
                            if (ordinal != 4) {
                                return super.toString();
                            }
                            return "NONE";
                        }
                        return "LECTURES";
                    }
                    return "FATAWA";
                }
                return "VIDEOS";
            }
            return "NEWS";
        }
    }

}

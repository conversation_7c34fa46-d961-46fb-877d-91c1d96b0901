# 40-Minute Restart After Athan Feature Implementation

## Overview
This document describes the implementation of the automatic app restart feature that triggers 40 minutes after each Athan (prayer call) is displayed.

## Feature Description
- **Trigger**: Automatically restart the app 40 minutes after each Athan is displayed
- **Purpose**: Refresh the app state and ensure optimal performance after prayer times
- **Scope**: Applies to all 5 daily prayers (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)

## Implementation Details

### 1. **New Variables Added**
```java
// Handler for restart after Athan functionality
private Handler restartAfterAthanHandler;
private Runnable restartAfterAthanRunnable;
private volatile boolean isRestartAfterAthanScheduled = false;

// Constants
private static final long RESTART_DELAY_AFTER_ATHAN = 40 * 60 * 1000; // 40 minutes
private static final String RESTART_AFTER_ATHAN_KEY = "restart_after_athan_enabled";
private static final String LAST_ATHAN_RESTART_KEY = "last_athan_restart_time";
```

### 2. **Handler Initialization**
```java
// In initializeHandlers() method
restartAfterAthanHandler = new Handler(Looper.getMainLooper());

// In initializeRunnables() method
restartAfterAthanRunnable = new Runnable() {
    @Override
    public void run() {
        try {
            Log.i(TAG, "40 minutes have passed after Athan, restarting app");
            
            saveLastAthanRestartTime();
            isRestartAfterAthanScheduled = false;
            restartAppAfterAthan();
            
        } catch (Exception e) {
            Log.e(TAG, "Error in restart after Athan runnable", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            isRestartAfterAthanScheduled = false;
        }
    }
};
```

### 3. **Trigger Integration**
The restart is scheduled when Athan is displayed:
```java
// In the main handler logic where Athan is displayed
AnnouncementMessage message = new AnnouncementMessage();
message.type = AnnouncementType.AZAN;
message.prayer = prayerType;
displayAnnouncement(message);

// Schedule app restart 40 minutes after Athan
scheduleRestartAfterAthan(prayerType);
```

### 4. **Core Methods**

#### **scheduleRestartAfterAthan()**
```java
private void scheduleRestartAfterAthan(PrayerType prayerType) {
    // Check if feature is enabled
    if (!isRestartAfterAthanEnabled()) return;
    
    // Prevent duplicate scheduling
    if (isRestartAfterAthanScheduled) return;
    
    // Check if recently restarted
    if (hasRecentlyRestarted()) return;
    
    // Schedule the restart
    isRestartAfterAthanScheduled = true;
    restartAfterAthanHandler.postDelayed(restartAfterAthanRunnable, RESTART_DELAY_AFTER_ATHAN);
    
    Log.i(TAG, "App restart scheduled for 40 minutes after " + prayerType.getName() + " Athan");
}
```

#### **restartAppAfterAthan()**
```java
private void restartAppAfterAthan() {
    // Check if activity is in foreground
    if (!isActivityInForeground(this, MainActivity.class)) return;
    
    // Create restart intent
    Intent restartIntent = new Intent(this, MainActivity.class);
    restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
    restartIntent.putExtra("restarted_after_athan", true);
    
    startActivity(restartIntent);
    finish();
}
```

#### **cancelRestartAfterAthan()**
```java
private void cancelRestartAfterAthan() {
    if (isRestartAfterAthanScheduled && restartAfterAthanHandler != null) {
        restartAfterAthanHandler.removeCallbacks(restartAfterAthanRunnable);
        isRestartAfterAthanScheduled = false;
        Log.d(TAG, "Cancelled scheduled restart after Athan");
    }
}
```

### 5. **Safety Mechanisms**

#### **Duplicate Prevention**
- Uses `isRestartAfterAthanScheduled` flag to prevent multiple scheduling
- Checks if app was recently restarted within last 30 minutes

#### **Recent Restart Check**
```java
private boolean hasRecentlyRestarted() {
    long lastRestartTime = Hawk.get(LAST_ATHAN_RESTART_KEY, 0L);
    long currentTime = System.currentTimeMillis();
    long timeSinceLastRestart = currentTime - lastRestartTime;
    
    // Consider "recent" as within the last 30 minutes
    return timeSinceLastRestart < (30 * 60 * 1000);
}
```

#### **Foreground Check**
- Only restarts if MainActivity is in foreground
- Prevents unnecessary restarts when app is in background

### 6. **Lifecycle Management**

#### **Cleanup Integration**
```java
// In stopAllHandlers()
cancelRestartAfterAthan();

// In cleanupResources()
restartAfterAthanHandler = null;
restartAfterAthanRunnable = null;
```

#### **State Persistence**
- Saves last restart time using Hawk preferences
- Maintains restart scheduling state across app lifecycle

## Feature Flow

### 1. **Athan Display**
```
Prayer Time Reached → Display Athan → Schedule 40-min Restart
```

### 2. **Restart Scheduling**
```
Check Enabled → Check Not Duplicate → Check Not Recent → Schedule Handler
```

### 3. **Restart Execution**
```
40 Minutes Pass → Check Foreground → Save Restart Time → Restart App
```

### 4. **Safety Checks**
```
Multiple Checks → Prevent Duplicates → Ensure Proper Cleanup
```

## Configuration Options

### **Enable/Disable Feature**
```java
private boolean isRestartAfterAthanEnabled() {
    // Currently always enabled, can be made configurable
    return true;
    // Future: return Hawk.get(RESTART_AFTER_ATHAN_KEY, true);
}
```

### **Customizable Delay**
```java
// Can be made configurable in settings
private static final long RESTART_DELAY_AFTER_ATHAN = 40 * 60 * 1000; // 40 minutes
```

## Logging and Monitoring

### **Key Log Messages**
- `"Scheduling app restart 40 minutes after [Prayer] Athan"`
- `"40 minutes have passed after Athan, restarting app"`
- `"App restart scheduled for 40 minutes after [Prayer] Athan"`
- `"Cancelled scheduled restart after Athan"`

### **Error Handling**
- Comprehensive try-catch blocks in all methods
- Firebase Crashlytics logging for all exceptions
- Graceful degradation on errors
- State reset on error conditions

## Benefits

### **Performance**
- ✅ Refreshes app state after prayer times
- ✅ Clears any accumulated memory usage
- ✅ Ensures optimal performance

### **Reliability**
- ✅ Automatic refresh without user intervention
- ✅ Prevents app state issues
- ✅ Maintains consistent behavior

### **User Experience**
- ✅ Seamless restart process
- ✅ No user interaction required
- ✅ Maintains app functionality

## Testing Scenarios

### **Normal Operation**
1. Wait for any Athan to be displayed
2. Verify restart is scheduled (check logs)
3. Wait 40 minutes
4. Verify app restarts automatically

### **Edge Cases**
1. **Multiple Athans**: Verify no duplicate scheduling
2. **Background App**: Verify no restart when app in background
3. **Recent Restart**: Verify no restart if recently restarted
4. **App Lifecycle**: Verify proper cleanup on app destroy

### **Error Scenarios**
1. **Handler Null**: Verify graceful handling
2. **Activity Destroyed**: Verify no restart attempt
3. **Exception in Restart**: Verify error logging and recovery

## Future Enhancements

### **Configurable Settings**
- Add setting to enable/disable feature
- Add setting to customize restart delay
- Add setting for specific prayers only

### **Advanced Features**
- Different delays for different prayers
- User notification before restart
- Restart only during specific hours

### **Monitoring**
- Analytics for restart frequency
- Performance metrics before/after restart
- User behavior analysis

## Conclusion

The 40-minute restart after Athan feature has been successfully implemented with:
- ✅ **Robust error handling** and safety mechanisms
- ✅ **Proper lifecycle management** and cleanup
- ✅ **Comprehensive logging** for monitoring
- ✅ **Configurable architecture** for future enhancements
- ✅ **Seamless integration** with existing prayer time logic

The feature automatically maintains app performance by refreshing the application state 40 minutes after each prayer time, ensuring optimal user experience throughout the day.

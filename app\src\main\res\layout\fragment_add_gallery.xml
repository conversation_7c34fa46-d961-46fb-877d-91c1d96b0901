<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".UI.Fragment.settings.content.photoGallery.AddGalleryFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_10sdp"
        android:gravity="center"
        android:orientation="vertical">

        <EditText
            android:id="@+id/galleryName_EditText_AddPhotoFragment"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_30sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:hint="@string/gallery_name"
            android:textAlignment="center"
            android:textColor="#474747"
            android:textSize="@dimen/contactUsView" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#D1D1D1" />

        <TextView
            android:id="@+id/tv_gallery_image_resolution"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:text="@string/upload_a_photo"
            android:textColor="@android:color/darker_gray"
            android:textSize="18sp" />

        <LinearLayout
            android:id="@+id/uploadPhoto_LinearLayout_AddPhotoFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_dark_gray"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:scaleType="fitCenter"
                app:srcCompat="@drawable/ic_camera_alt_black_24dp"
                app:tint="@android:color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/add_photo"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/photo_RecyclerView_AddPhotoFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:overScrollMode="never"

            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:reverseLayout="false"
            app:spanCount="3"
            tools:itemCount="3"
            tools:listitem="@layout/photo_gallery_image_list_item" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/_5sdp"
            android:background="#D1D1D1" />


        <Button
            android:id="@+id/btn_expand_setting"
            android:layout_width="@dimen/_200sdp"
            android:layout_height="wrap_content"
            android:background="@drawable/without_corners_50_background_blue"
            android:drawableLeft="@drawable/ic_baseline_expand_more_24"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:text="@string/advanced_setting"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sdp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/advanced_setting_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/duration_of_prayer_times_minutes"
                android:textColor="@color/colorblack"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_duration_of_prayer_times"
                android:layout_width="@dimen/_200sdp"
                android:layout_height="wrap_content"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/edit"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/duration_of_photo_gallery_minutes"
                android:textColor="@color/colorblack"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_duration_of_single_image"
                android:layout_width="@dimen/_200sdp"
                android:layout_height="wrap_content"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/edit"
                android:textColor="@color/white"
                android:textSize="@dimen/_12sdp" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/enable_gallery_button_between_azan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/enable_photo_gallery_with_announcement"
                android:textSize="@dimen/_12sdp"
                android:visibility="gone" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/enable_gallery_button_between_azan_jomaa"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/enable_photo_gallery_with_announcement_jomaa"
                android:textSize="@dimen/_12sdp"
                android:visibility="gone" />

        </LinearLayout>

        <Button
            android:id="@+id/save_Button_AddPhotoFragment"
            android:layout_width="@dimen/_200sdp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/save"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:includeFontPadding="false"
            android:text="@string/gallery_close"
            android:textColor="@color/red"
            android:textSize="@dimen/_14sdp"
            tools:text="@string/gallery_close" />

        <Button
            android:id="@+id/delete_Button_AddPhotoFragment"
            android:layout_width="@dimen/_200sdp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_red"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/delete"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold" />
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
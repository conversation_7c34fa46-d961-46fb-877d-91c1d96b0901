package com.arapeak.alrbrea.core_ktx.model.time

import com.arapeak.alrbrea.core_ktx.model.AppLanguageEnum

data class TimeDateShowFormat(
    val numberFormat: TimeNumbersFormatEnum,
    val language: AppLanguageEnum,
    val calenderType: DateCalendarEnum,
    val timeFormat: TimeFormatEnum
) {
    constructor(numbersLang: String, language: String, calenderType: Int) : this(
        parseNumberFormat(numbersLang), parseLanguage(language), parseCalendarType(calenderType), TimeFormatEnum.H12
    )

    companion object {
        private fun parseNumberFormat(numbersLang: String): TimeNumbersFormatEnum {
            return when (numbersLang) {
                "en" -> TimeNumbersFormatEnum.Western
                "ar" -> TimeNumbersFormatEnum.Arabic
                else -> TimeNumbersFormatEnum.Western
            }
        }

        private fun parseLanguage(language: String): AppLanguageEnum {
            return when (language) {
                "en" -> AppLanguageEnum.En
                "ar" -> AppLanguageEnum.Ar
                else -> AppLanguageEnum.Ar
            }
        }

        private fun parseCalendarType(calenderType: Int): DateCalendarEnum {
//            public static final int ARG_TYPE_OF_SHOW_HIJRY = 0;
//            public static final int ARG_TYPE_OF_SHOW_GREGORIAN = 1;
//            public static final int ARG_TYPE_OF_SHOW_BOTH = 2;

            return when (calenderType) {
                0 -> DateCalendarEnum.Hijri
                1 -> DateCalendarEnum.Miladi
                2 -> DateCalendarEnum.Both
                else -> DateCalendarEnum.Both
            }
        }
    }

}



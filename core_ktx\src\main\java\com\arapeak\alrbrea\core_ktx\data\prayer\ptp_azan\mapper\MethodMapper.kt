package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.mapper

import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.batoulapps.adhan2.CalculationMethod.DUBAI
import com.batoulapps.adhan2.CalculationMethod.EGYPTIAN
import com.batoulapps.adhan2.CalculationMethod.KARACHI
import com.batoulapps.adhan2.CalculationMethod.KUWAIT
import com.batoulapps.adhan2.CalculationMethod.MOON_SIGHTING_COMMITTEE
import com.batoulapps.adhan2.CalculationMethod.MUSLIM_WORLD_LEAGUE
import com.batoulapps.adhan2.CalculationMethod.NORTH_AMERICA
import com.batoulapps.adhan2.CalculationMethod.OTHER
import com.batoulapps.adhan2.CalculationMethod.QATAR
import com.batoulapps.adhan2.CalculationMethod.SINGAPORE
import com.batoulapps.adhan2.CalculationMethod.TURKEY
import com.batoulapps.adhan2.CalculationMethod.UMM_AL_QURA
import com.batoulapps.adhan2.Madhab
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib.Method as AzanMethod
import com.batoulapps.adhan2.CalculationMethod as AdhanCalculationMethod

fun mapMethod(m1: AdhanCalculationMethod, madhab: Madhab): AzanMethod {
    return when (m1) {
        MUSLIM_WORLD_LEAGUE -> AzanMethod.MUSLIM_LEAGUE
        EGYPTIAN -> AzanMethod.EGYPT_SURVEY
        KARACHI -> if (madhab == Madhab.HANAFI) AzanMethod.KARACHI_HANAF else AzanMethod.KARACHI_SHAF
        UMM_AL_QURA -> AzanMethod.UMM_ALQURRA
        DUBAI -> AzanMethod.UMM_ALQURRA
        MOON_SIGHTING_COMMITTEE -> AzanMethod.FIXED_ISHAA
        NORTH_AMERICA -> AzanMethod.NORTH_AMERICA
        KUWAIT -> AzanMethod.KARACHI_SHAF
        QATAR -> AzanMethod.MUSLIM_LEAGUE
        SINGAPORE -> AzanMethod.MUSLIM_LEAGUE
        TURKEY -> AzanMethod.MUSLIM_LEAGUE
        OTHER -> AzanMethod.MUSLIM_LEAGUE
    }

}


fun CalculationMethod.toAzanMethod(): AzanMethod {
    val default = AzanMethod.UMM_ALQURRA
    return when (this) {
        CalculationMethod.automatic -> default
        CalculationMethod.ummAlQurra -> AzanMethod.UMM_ALQURRA
        CalculationMethod.egyptianSurvey -> AzanMethod.EGYPT_SURVEY
        CalculationMethod.karachi -> AzanMethod.KARACHI_SHAF
        CalculationMethod.muslimLeague -> AzanMethod.MUSLIM_LEAGUE
        CalculationMethod.northAmerica -> AzanMethod.NORTH_AMERICA
        CalculationMethod.gulf_region -> default
        CalculationMethod.kuwait -> default
        CalculationMethod.singapore -> default
        CalculationMethod.france -> default
        CalculationMethod.turkey -> default
        CalculationMethod.russia -> default
        CalculationMethod.customCalendar -> default
    }
}
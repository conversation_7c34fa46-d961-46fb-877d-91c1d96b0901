// Top-level build files where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        maven { url "https://jcenter.bintray.com"}
        maven { url "https://maven.google.com" }
        maven { url 'https://jitpack.io' }

        google()
        mavenCentral()
        jcenter()
        maven {
            url = "https://jcenter.bintray.com"
        }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }

    //    maven { url "http://code.newtronlabs.com:8081/artifactory/libs-release-local" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.1'
        classpath 'com.google.gms:google-services:4.4.1'
        classpath "io.realm:realm-gradle-plugin:10.15.1"
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.1'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0'
        //classpath 'com.google.firebase:firebase-crashlytics-gradle:2.5.2'
      //  classpath 'com.newtronlabs.android:plugin:4.0.0'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}
allprojects {
    repositories {
        maven { url "https://jcenter.bintray.com"}
        maven { url "https://maven.google.com" }
        maven { url "https://jitpack.io" }
        google()
        mavenCentral()
        jcenter()
        maven {
            url = "https://jcenter.bintray.com"
        }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }

       // maven { url "http://code.newtronlabs.com:8081/artifactory/libs-release-local" }


    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper

import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.ASER_ID_HANAFI
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.ASER_ID_SHAFI
import com.batoulapps.adhan2.Madhab
import com.batoulapps.adhan2.Madhab.HANAFI
import com.batoulapps.adhan2.Madhab.SHAFI

class MadehabMapper {
    fun map(madhab: Madhab): Int {
        return when (madhab) {
            SHAFI -> ASER_ID_SHAFI
            HANAFI -> ASER_ID_HANAFI
        }
    }
}
package com.arapeak.alrbea.deleted;


import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;

import com.arapeak.alrbea.Enum.News;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapter;

/**
 * A simple {@link Fragment} subclass.
 */
//public class CreateMovingMessageFragment extends AlrabeeaTimesFragment implements DatePickerDialog.OnDateSetListener, View.OnClickListener {
public class CreateMovingMessageFragment extends AlrabeeaTimesFragment {

    private static final String TAG = "CreateMovingMessageFragment";
    public static int startYearDate, startMonthDate, startDayDate, endYearDate, endMonthDate, endDayDate;
    public static int year, month, day;
    News news;
    private ScrollView confirmMessageScrollView;
    private TextView titleTextView;
    private TextView cancelTextView;
    private SwitchCompat activeSwitchCompat;
    private TextView activeTextView;
    private EditText messageEditText;
    private LinearLayout dateLinearLayout;
    private TextView startDateTextView;
    private TextView endDateTextView;
    private Button saveButton;
    private Spinner moveBetweenImageSpinner;
    private SpinnerAdapter<String> moveBetweenImageSpinnerAdapter;
    private String titleDatePickerDialog;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_create_moving_message, container, false);

    }
/*

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        news = Utils.getCurrentNews();
        initView(view);
        setParameter();
        setAction();
    }
    private void initView(View view) {
        confirmMessageScrollView = view.findViewById(R.id.confirm_message_ScrollView_CreateMovingMessageFragment);
        titleTextView = view.findViewById(R.id.title_TextView_CreateMovingMessageFragment);
        cancelTextView = view.findViewById(R.id.cancel_TextView_CreateMovingMessageFragment);
        moveBetweenImageSpinner = view.findViewById(R.id.moveBetweenImage_Spinner_AddPhotoFragment);

        activeSwitchCompat = view.findViewById(R.id.active_SwitchCompat_CreateMovingMessageFragment);
        activeTextView = view.findViewById(R.id.active_TextView_CreateMovingMessageFragment);
        messageEditText = view.findViewById(R.id.message_EditText_CreateMovingMessageFragment);
        dateLinearLayout = view.findViewById(R.id.date_LinearLayout_CreateMovingMessageFragment);
        startDateTextView = view.findViewById(R.id.startDate_TextView_CreateMovingMessageFragment);
        endDateTextView = view.findViewById(R.id.endDate_TextView_CreateMovingMessageFragment);
        saveButton = view.findViewById(R.id.save_Button_CreateMovingMessageFragment);


      moveBetweenImageSpinnerAdapter = new SpinnerAdapter<>(getAppCompatActivity()
              , new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.movingtext))));
    }

    private void setParameter() {
        activeSwitchCompat.setChecked(news.isActive);
//        activeSwitchCompat.setChecked(Hawk.get(ConstantsOfApp.NEWS_TICKER_KEY+"C", false));

        Utils.setColorStateListToSwitchCompat(getAppCompatActivity(),activeSwitchCompat);
        moveBetweenImageSpinner.setAdapter(moveBetweenImageSpinnerAdapter);

        setData();
    }
    private void setAction() {
        startDateTextView.setOnClickListener(this);
        endDateTextView.setOnClickListener(this);
        saveButton.setOnClickListener(this);


        activeSwitchCompat.setOnCheckedChangeListener((buttonView, isChecked) -> {
            NewsTicker newsTicker = Hawk.get(ConstantsOfApp.NEWS_TICKER_KEY, null);
            if (newsTicker != null) {
                newsTicker.setActive(isChecked);
                Hawk.put(ConstantsOfApp.NEWS_TICKER_KEY, newsTicker);
            } else if (!isChecked) {
                Hawk.delete(ConstantsOfApp.NEWS_TICKER_KEY);
            }
            Hawk.put(ConstantsOfApp.NEWS_TICKER_KEY+"C", isChecked);
            activeOrDisplay(isChecked);
        });


        moveBetweenImageSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                NewsTicker newsTicker = Hawk.get(ConstantsOfApp.NEWS_TICKER_KEY, null);
                if(position==0){
                    messageEditText.setText(newsTicker!=null?newsTicker.getMessageEvent():" ");
                    Hawk.put(ConstantsOfApp.NEWS_TICKER_KEY+"d", 0);


                }else {
                    messageEditText.setText(parent.getItemAtPosition(position).toString().trim());
                   // Hawk.put(ConstantsOfApp.NEWS_TICKER_KEY+"d", position);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    @Override
    public void onClick(View v) {
        if (v.equals(startDateTextView)) {
            setupDatePicker(false);
        } else if (v.equals(endDateTextView)) {
            setupDatePicker(true);
        } else if (v.equals(saveButton)) {
            if (isValid()) {
                NewsTicker newsTicker = new NewsTicker(messageEditText.getText().toString()
                        , startDayDate + "-" + startMonthDate + "-" + startYearDate
                        , endDayDate + "-" + endMonthDate + "-" + endYearDate
                        , activeSwitchCompat.isChecked());

                Hawk.put(ConstantsOfApp.NEWS_TICKER_KEY, newsTicker);
                Utils.showSuccessAlert(getAppCompatActivity(), getString(R.string.news_added_successfully));
            }
        }
    }

    @Override
    public void onDateSet(DatePickerDialog view, int year, int monthOfYear, int dayOfMonth) {
        String monthOfYearString, dayOfMonthString;
        if (((monthOfYear + 1) + "").length() == 1) {
            monthOfYearString = "0" + (monthOfYear + 1);
        } else {
            monthOfYearString = "" + (monthOfYear + 1);
        }

        if ((dayOfMonth + "").length() == 1) {
            dayOfMonthString = "0" + dayOfMonth;
        } else {
            dayOfMonthString = "" + dayOfMonth;
        }
        if (titleDatePickerDialog.equalsIgnoreCase(getString(R.string.start_date))) {
            startDateTextView.setError(null);

            startDateTextView.setText(year + "-" + monthOfYearString + "-" + dayOfMonthString);

            startYearDate = year;
            startMonthDate = monthOfYear + 1;
            startDayDate = dayOfMonth;

        } else if (titleDatePickerDialog.equalsIgnoreCase(getString(R.string.end_date))) {
            endDateTextView.setError(null);

            endDateTextView.setError(null);

            endDateTextView.setText(year + "-" + monthOfYearString + "-" + dayOfMonthString);

            endYearDate = year;
            endMonthDate = monthOfYear + 1;
            endDayDate = dayOfMonth;

        }
    }

    private boolean isValid() {
        boolean isValid = true;
        if (messageEditText.getText().toString().isEmpty()) {
            messageEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }
        if (startDateTextView.getText().toString().isEmpty()) {
            startDateTextView.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }
        if (endDateTextView.getText().toString().isEmpty()) {
            endDateTextView.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        return isValid;
    }

    private void clearAllView() {
//        startDateTextView.setText(year + "-" + month + "-" + day);
        startDateTextView.setText("");
        endDateTextView.setText("");

        activeSwitchCompat.setChecked(false);
        messageEditText.setText("");
    }

    private void setupDatePicker(boolean isEndDate) {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog datePickerDialog;
        datePickerDialog = DatePickerDialog.newInstance(
                CreateMovingMessageFragment.this,
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));

        if (!isEndDate) {
            if (!startDateTextView.getText().toString().isEmpty()) {
                Long timeMillis = Utils.GetDateFromTimestampMillis(startDateTextView.getText().toString());
                if (timeMillis > 0) {
                    calendar.setTimeInMillis(timeMillis);
                    datePickerDialog = DatePickerDialog.newInstance(
                            CreateMovingMessageFragment.this,
                            calendar.get(Calendar.YEAR),
                            calendar.get(Calendar.MONTH),
                            calendar.get(Calendar.DAY_OF_MONTH));
                }
            }

            datePickerDialog.setMinDate(Calendar.getInstance());
            if (endDayDate > 0 && endMonthDate > 0 && endYearDate > 0) {
                Calendar calendarMax = Calendar.getInstance();
                calendarMax.set(Calendar.YEAR, endYearDate);
                calendarMax.set(Calendar.MONTH, endMonthDate - 1);
                calendarMax.set(Calendar.DAY_OF_MONTH, endDayDate);
                datePickerDialog.setMaxDate(calendarMax);
            }
            titleDatePickerDialog = getString(R.string.start_date);

        } else {
            if (!endDateTextView.getText().toString().isEmpty()) {
                Long timeMillis = Utils.GetDateFromTimestampMillis(endDateTextView.getText().toString());
                if (timeMillis > 0) {
                    calendar.setTimeInMillis(timeMillis);
                    datePickerDialog = DatePickerDialog.newInstance(
                            CreateMovingMessageFragment.this,
                            calendar.get(Calendar.YEAR),
                            calendar.get(Calendar.MONTH),
                            calendar.get(Calendar.DAY_OF_MONTH));
                }
            }

            datePickerDialog.setMinDate(Calendar.getInstance());
            if (startDayDate > 0 && startMonthDate > 0 && startYearDate > 0) {
                Calendar calendarMin = Calendar.getInstance();
                calendarMin.set(Calendar.YEAR, startYearDate);
                calendarMin.set(Calendar.MONTH, startMonthDate - 1);
                calendarMin.set(Calendar.DAY_OF_MONTH, startDayDate);
                datePickerDialog.setMinDate(calendarMin);
            }
            titleDatePickerDialog = getString(R.string.end_date);

        }

        datePickerDialog.setLocale(Locale.ENGLISH);
        datePickerDialog.dismissOnPause(true);
        datePickerDialog.setAccentColor(ContextCompat.getColor(getAppCompatActivity(), R.color.colorPrimary));
        datePickerDialog.setOkColor(ContextCompat.getColor(getAppCompatActivity(), R.color.colorPrimary));
        datePickerDialog.setCancelColor(ContextCompat.getColor(getAppCompatActivity(), R.color.colorPrimary));
        datePickerDialog.show(getAppCompatActivity().getFragmentManager(), titleDatePickerDialog);

    }

    private void activeOrDisplay(boolean isActive) {
        if (isActive) {
            titleTextView.setVisibility(View.VISIBLE);
            messageEditText.setVisibility(View.VISIBLE);
            dateLinearLayout.setVisibility(View.VISIBLE);
            saveButton.setVisibility(View.VISIBLE);
        } else {
            titleTextView.setVisibility(View.GONE);
            messageEditText.setVisibility(View.GONE);
            dateLinearLayout.setVisibility(View.GONE);
            saveButton.setVisibility(View.GONE);
        }
    }

    private void setData() {
        NewsTicker newsTicker = Hawk.get(ConstantsOfApp.NEWS_TICKER_KEY, null);
        if (newsTicker != null) {
            activeSwitchCompat.setChecked(newsTicker.isActive());
            startDateTextView.setText(newsTicker.getStartDate());
            endDateTextView.setText(newsTicker.getEndDate());
            messageEditText.setText(newsTicker.getMessageEvent());
            activeOrDisplay(true);
//            activeOrDisplay(newsTicker.isActive());
        } else {
            activeOrDisplay(false);
        }
    }*/
    /*
    private void setData() {
     //   clearAllView();

        NewsTicker newsTicker = Hawk.get(ConstantsOfApp.NEWS_TICKER_KEY, null);
        if (newsTicker != null) {
            activeSwitchCompat.setChecked(newsTicker.isActive());
            startDateTextView.setText(newsTicker.getStartDate());
            endDateTextView.setText(newsTicker.getEndDate());
            messageEditText.setText(newsTicker.getMessageEvent());
            activeOrDisplay(true);
//            activeOrDisplay(newsTicker.isActive());
        } else {
            activeOrDisplay(false);
        }
    }*/
}

package com.arapeak.alrbea.UI.Fragment.settings.content;

import android.app.Dialog;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.InfoOfCode;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class AboutApp extends AlrabeeaTimesFragment implements SettingsAdapterCallback {
    public final static int RESULT_LOAD_FILE_ON_ACTIVITY_RESULT = 210;
    private static final String TAG = "GeneralSettingsFragment";
    private static final int PERMISSION_CODE = 103;

    private View generalSettingsView;
    private RecyclerView settingItemRecyclerView;

    //    private SettingsAdapter settingsAdapter;
    private MainSettingsAdapter mainSettingsAdapter;
    private Dialog loadingDialog;
    private Uri fileUri;

    public AboutApp() {

    }

    public static AboutApp newInstance() {
        return new AboutApp();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        generalSettingsView = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return generalSettingsView;
    }

    private void initView() {
        settingItemRecyclerView = generalSettingsView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(getString(R.string.about_app));
//        } else {
//            SettingsActivity.setTextTite(getString(R.string.about_app));
//        }
        SettingsActivity.setTextTite(getString(R.string.about_app));
        settingItemRecyclerView.setAdapter(mainSettingsAdapter);

        setupGeneralSettings();
    }

    private void SetAction() {


    }

    private void setupGeneralSettings() {
        InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.general_settings);
        try {
            PackageInfo pInfo = getAppCompatActivity().getPackageManager().getPackageInfo(getAppCompatActivity().getPackageName(), 0);
            final String versionName = pInfo.versionName;
            final int versionCode = pInfo.versionCode;
            SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.app_version)
                    , ViewsAlrabeeaTimes.TEXT_VIEW
                    , versionName /*generalSettingsTitleArray[0]*/
                    , true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.app_code)
                    , ViewsAlrabeeaTimes.TEXT_VIEW
                    , infoOfCode.getCode() /*generalSettingsTitleArray[1]*/
                    , true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.contact_us)
                    , ViewsAlrabeeaTimes.TEXT_VIEW
                    , "<EMAIL>  -  +966556227729"/*generalSettingsTitleArray[2]*/
                    , true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.use_conditions)
                    , ViewsAlrabeeaTimes.TEXT_VIEW
                    , ""/*generalSettingsTitleArray[2]*/
                    , true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);


            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.privacy_policy)
                    , ViewsAlrabeeaTimes.TEXT_VIEW
                    , ""/*generalSettingsTitleArray[2]*/
                    , true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        } catch (PackageManager.NameNotFoundException e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }

    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        if (position == 3) {
            ScrollView scrollView = new ScrollView(requireContext());
            TextView textView = new TextView(requireContext());
            textView.setText(getString(com.arapeak.alrbrea.core_ktx.R.string.use_conditions_content));
            scrollView.addView(textView);

            AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
            builder.setTitle(getString(R.string.use_conditions));
            builder.setView(scrollView);
            AlertDialog dialog = builder.create();
            dialog.show();


//            Toast.makeText(requireContext(), "Conditions", Toast.LENGTH_SHORT).show();
        }
        if (position == 4) {
            ScrollView scrollView = new ScrollView(requireContext());
            TextView textView = new TextView(requireContext());
            textView.setText(getString(com.arapeak.alrbrea.core_ktx.R.string.privacy_policy_content));
            scrollView.addView(textView);

            AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
            builder.setTitle(getString(R.string.privacy_policy));
            builder.setView(scrollView);
            AlertDialog dialog = builder.create();
            dialog.show();

        }
    }

}

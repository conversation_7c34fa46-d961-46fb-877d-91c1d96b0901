<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:layoutDirection="rtl"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:layoutDirection="locale"
        android:orientation="horizontal">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.brown_new_3">

            <LinearLayout
                android:id="@+id/fajrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.top">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/fajr_TextView_MainActivity"
                        style="@style/TimeTextView.brown_new_3.TimeNameAR"
                        android:text="@string/fajr" />

                    <ImageView
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:paddingEnd="@dimen/_5sdp"
                        android:src="@drawable/theme_brown_3_icon_pray_fajr" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.Left">


                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.Time" />

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.TimeType" />

            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_fajr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.brown_new_3">

            <LinearLayout
                android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.top"

                android:background="#00000000">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity"
                        style="@style/TimeTextView.brown_new_3.TimeNameAR"
                        android:text="@string/duha"

                        android:textSize="@dimen/_16sdp" />

                    <ImageView
                        android:layout_width="@dimen/_23sdp"
                        android:layout_height="@dimen/_23sdp"
                        android:paddingEnd="@dimen/_5sdp"
                        android:src="@drawable/theme_brown_3_icon_pray_duha" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.Left"
                android:background="#00000000">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.Time"
                    android:textSize="@dimen/_20sdp" />

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.TimeType"
                    android:textSize="@dimen/_12sdp" />


            </LinearLayout>
        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.brown_new_3">

            <LinearLayout
                android:id="@+id/dhuhrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.top">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivity"
                        style="@style/TimeTextView.brown_new_3.TimeNameAR"
                        android:text="@string/dhuhr" />

                    <ImageView
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:layout_marginEnd="@dimen/_5sdp"
                        android:src="@drawable/theme_brown_3_icon_pray_dhuhr" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.Left">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.Time" />

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.TimeType" />
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_dhur"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:layoutDirection="locale"
        android:orientation="horizontal">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.brown_new_3">

            <LinearLayout
                android:id="@+id/asrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.top">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/asr_TextView_MainActivity"
                        style="@style/TimeTextView.brown_new_3.TimeNameAR"
                        android:text="@string/asr" />

                    <ImageView
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:paddingEnd="@dimen/_5sdp"
                        android:src="@drawable/theme_brown_3_icon_pray_asr" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.Left">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.Time" />

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.TimeType" />
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_asr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.brown_new_3">

            <LinearLayout
                android:id="@+id/maghribATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.top">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/maghrib_TextView_MainActivity"
                        style="@style/TimeTextView.brown_new_3.TimeNameAR"

                        android:ellipsize="marquee"
                        android:focusable="true"
                        android:gravity="center"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:text="@string/maghrib"
                        android:textAlignment="center" />

                    <ImageView
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:paddingEnd="@dimen/_5sdp"
                        android:src="@drawable/theme_brown_3_icon_pray_maghrib" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.Left">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.Time" />

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.TimeType" />


            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_maghrib"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.brown_new_3">

            <LinearLayout
                android:id="@+id/ishaATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.top">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/isha_TextView_MainActivity"
                        style="@style/TimeTextView.brown_new_3.TimeNameAR"
                        android:text="@string/isha" />

                    <ImageView
                        android:layout_width="@dimen/_25sdp"
                        android:layout_height="@dimen/_25sdp"
                        android:paddingEnd="@dimen/_5sdp"
                        android:src="@drawable/theme_brown_3_icon_pray_isha" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.brown_new_3.Left">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.Time" />

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.brown_new_3.TimeType" />

            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_isha"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
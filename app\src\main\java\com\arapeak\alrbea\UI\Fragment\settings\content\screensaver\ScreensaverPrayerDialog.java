package com.arapeak.alrbea.UI.Fragment.settings.content.screensaver;

import android.graphics.Point;
import android.os.Bundle;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.arapeak.alrbea.R;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.repo.ScreenSaverRepo;

import java.util.ArrayList;
import java.util.List;

public class ScreensaverPrayerDialog extends DialogFragment {

    ScreenSaverRepo repo;
    String TAG = "ScreensaverPrayerDialog";
    private List<Boolean> list = new ArrayList<>();
    private int delay = 0;
    private int preDelay = 0;

    private ScreensaverPrayerDialog(ScreenSaverRepo repo) {
        super();
        this.repo = repo;
    }

    static ScreensaverPrayerDialog newInstance(ScreenSaverRepo repo) {
        return new ScreensaverPrayerDialog(repo);
    }

    void display(FragmentManager fragmentManager) {
        show(fragmentManager, "ScreensaverPrayerDialog");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.screensaver_prayers_config_dialog, container, false);

        CheckBox cbFajr = view.findViewById(R.id.ss_prayers_after_fajr);
        CheckBox cbSunrise = view.findViewById(R.id.ss_prayers_after_sunrise);
        CheckBox cbDhur = view.findViewById(R.id.ss_prayers_after_dhur);
        CheckBox cbAsr = view.findViewById(R.id.ss_prayers_after_asr);
        CheckBox cbMaghreb = view.findViewById(R.id.ss_prayers_after_maghreb);
        CheckBox cbIsha = view.findViewById(R.id.ss_prayers_after_isha);

        Button btnAddMin = view.findViewById(R.id.ss_add_Button_SubSettingsHolder);
        TextView tvMin = view.findViewById(R.id.ss_edit_TextView_SubSettingsHolder);
        Button btnMinMin = view.findViewById(R.id.ss_minus_Button_SubSettingsHolder);

        Button btnAddMin2 = view.findViewById(R.id.ss_add_Button_SubSettingsHolder2);
        TextView tvMin2 = view.findViewById(R.id.ss_edit_TextView_SubSettingsHolder2);
        Button btnMinMin2 = view.findViewById(R.id.ss_minus_Button_SubSettingsHolder2);

        Button btndissmisn = view.findViewById(R.id.btn_dismmis);


        list = repo.getTimingPrayers(getContext());
        delay = repo.getTimingPrayersDelay(getContext());
        preDelay = repo.getTimingPrayersPreDelay(getContext());

        cbFajr.setChecked(list.get(0));
        cbSunrise.setChecked(list.get(1));
        cbDhur.setChecked(list.get(2));
        cbAsr.setChecked(list.get(3));
        cbMaghreb.setChecked(list.get(4));
        cbIsha.setChecked(list.get(5));

        tvMin.setText(delay + "");
        tvMin2.setText(preDelay + "");

        cbFajr.setOnCheckedChangeListener((buttonView, isChecked) -> {
            list.set(0, isChecked);
            repo.updateTimingPrayers(getContext(), list);
        });

        cbSunrise.setOnCheckedChangeListener((buttonView, isChecked) -> {
            list.set(1, isChecked);
            repo.updateTimingPrayers(getContext(), list);
        });

        cbDhur.setOnCheckedChangeListener((buttonView, isChecked) -> {
            list.set(2, isChecked);
            repo.updateTimingPrayers(getContext(), list);
        });

        cbAsr.setOnCheckedChangeListener((buttonView, isChecked) -> {
            list.set(3, isChecked);
            repo.updateTimingPrayers(getContext(), list);
        });

        cbMaghreb.setOnCheckedChangeListener((buttonView, isChecked) -> {
            list.set(4, isChecked);
            repo.updateTimingPrayers(getContext(), list);
        });

        cbIsha.setOnCheckedChangeListener((buttonView, isChecked) -> {
            list.set(5, isChecked);
            repo.updateTimingPrayers(getContext(), list);
        });

        btndissmisn.setOnClickListener(v -> {
            dismiss();
        });
        btnAddMin.setOnClickListener(v -> {
            int dalue = 0;
            try {
                String current = tvMin.getText().toString();
                dalue = Integer.parseInt(current) + 1;
                if (dalue < 0)
                    dalue = 0;
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            tvMin.setText(dalue + "");
            repo.updateTimingPrayersDelay(getContext(), dalue);
        });

        btnMinMin.setOnClickListener(v -> {
            int dalue = 0;
            try {
                String current = tvMin.getText().toString();
                dalue = Integer.parseInt(current) - 1;
                if (dalue < 0)
                    dalue = 0;
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            tvMin.setText(dalue + "");
            repo.updateTimingPrayersDelay(getContext(), dalue);
        });
        btnAddMin2.setOnClickListener(v -> {
            int dalue = 0;
            try {
                String current = tvMin2.getText().toString();
                dalue = Integer.parseInt(current) + 1;
                if (dalue < 0)
                    dalue = 0;
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            tvMin2.setText(dalue + "");
            repo.updateTimingPrayersPreDelay(getContext(), dalue);
        });

        btnMinMin2.setOnClickListener(v -> {
            int dalue = 0;
            try {
                String current = tvMin2.getText().toString();
                dalue = Integer.parseInt(current) - 1;
                if (dalue < 0)
                    dalue = 0;
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            tvMin2.setText(dalue + "");
            repo.updateTimingPrayersPreDelay(getContext(), dalue);
        });


        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.prayerTimingStyle);

    }

    @Override
    public void onResume() {
        super.onResume();

        Window window = getDialog().getWindow();
        Point size = new Point();

        Display display = window.getWindowManager().getDefaultDisplay();
        display.getSize(size);

        int width = size.x;
        int height = size.y;

        window.setLayout((int) (width * 0.8), (int) (height * 0.8));
        window.setGravity(Gravity.CENTER);

    }


}

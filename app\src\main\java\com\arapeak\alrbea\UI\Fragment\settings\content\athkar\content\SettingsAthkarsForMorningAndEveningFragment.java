package com.arapeak.alrbea.UI.Fragment.settings.content.athkar.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_EVNING_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_EVNING_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_MORNING_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_MORNING_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.DEFAULT_FOR_IS_THERE_ATHKER;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class SettingsAthkarsForMorningAndEveningFragment extends AlrabeeaTimesFragment implements SettingsAdapterCallback {

    private static final String TAG = "SettingsAthkarsForMorningAndEveningFragment";
    public static int year, month, day;
    public static int yearDate, monthDate, dayDate;

    private View athkarView;
    private CheckBox citationForMorningCheckBox;
    private TextView citationForMorningTextView;
    private TextView citationForMorningDescribeTextView;
    private View space1View;
    private TextView citationForMorningTimeTextView;
    private SeekBar citationForMorningTimeSeekBar, citationForEveningTimeSeekBar;
    private CheckBox citationForEveningCheckBox;
    private TextView citationForEveningTextView;
    private TextView citationForEveningDescribeTextView;
    private View space2View;
    private TextView citationForEveningTimeTextView;
    private Spinner spinnerMorning, spinnerEvening;
    private MainSettingsAdapter mainSettingsAdapter, mainSettingsAdapter1;
    private Button b1, b2;
    private RecyclerView settingItemRecyclerView, settingItemRecyclerViewe;
    private int citationForMorningTime, citationForEveningTime;
    //    private TimingsAlrabeeaTimes timingsAlrabeeaTimes;
    private Context context;
//    public static PrayerApi prayerApi;

    public SettingsAthkarsForMorningAndEveningFragment() {

    }

    public static SettingsAthkarsForMorningAndEveningFragment newInstance() {
        return new SettingsAthkarsForMorningAndEveningFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        athkarView = inflater.inflate(R.layout.fragment_settings_athkar, container, false);

        initView();
        SetParameter();
        SetAction();

        return athkarView;
    }

    private void initView() {
        citationForMorningCheckBox = athkarView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment);
        //  spinnerMorning =  athkarView.findViewById(R.id.spinnerMorning);
        //spinnerEvening =  athkarView.findViewById(R.id.spinnerEvening);
        citationForMorningTextView = athkarView.findViewById(R.id.citationForMorning_TextView_AthkarFragment);
        citationForMorningDescribeTextView = athkarView.findViewById(R.id.citationForMorningDescribe_TextView_AthkarFragment);
        space1View = athkarView.findViewById(R.id.space1_View_AthkarFragment);
        citationForMorningTimeTextView = athkarView.findViewById(R.id.citationForMorningTime_TextView_AthkarFragment);
        citationForMorningTimeSeekBar = athkarView.findViewById(R.id.citationForMorningTime_SeekBar_AthkarFragment);
        citationForEveningCheckBox = athkarView.findViewById(R.id.citationForEvening_CheckBox_AthkarFragment);
        citationForEveningTextView = athkarView.findViewById(R.id.citationForEvening_TextView_AthkarFragment);
        citationForEveningDescribeTextView = athkarView.findViewById(R.id.citationForEveningDescribe_TextView_AthkarFragment);
        space2View = athkarView.findViewById(R.id.space2_View_AthkarFragment);
        citationForEveningTimeTextView = athkarView.findViewById(R.id.citationForEveningTime_TextView_AthkarFragment);
        citationForEveningTimeSeekBar = athkarView.findViewById(R.id.citationForEveningTime_SeekBar_AthkarFragment);
        settingItemRecyclerView = athkarView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingFragment);
        settingItemRecyclerViewe = athkarView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingFragmentE);
        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);
        mainSettingsAdapter1 = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);

        b1 = athkarView.findViewById(R.id.b1);
        b2 = athkarView.findViewById(R.id.b2);

    }

    @SuppressLint("SetTextI18n")
    private void SetParameter() {
        SettingsActivity.setTextTite(getString(R.string.athkar));

        b1.setOnClickListener(v -> {
            Utils.initAzkarTimeDialog(getAppCompatActivity(), AthkarType.MorningAthkar, (position, duration) -> {
                SetParameter();
            });
        });

        b2.setOnClickListener(v -> {
            Utils.initAzkarTimeDialog(getAppCompatActivity(), AthkarType.EveningAthkar, (position, duration) -> {
                SetParameter();
            });
        });

        year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
        month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH));
        day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY));

        yearDate = year;
        monthDate = month;
        dayDate = day;


//        citationForMorningTimeTextView.setText(""
//                +  getString(R.string.remind_time_before_sunrise)+":"+Hawk.get(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY,ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT)
//                +"\n"
//                + getString(R.string.remind_time)+":" + Utils.getTimeFormatted(PrayerTime.getTimeToAnnounceAthkar(AthkarType.MorningAthkar),"HH:mm"));
//
//        citationForEveningTimeTextView.setText(getString(R.string.remind_time_after__sunset)+ ":"+Hawk.get(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY,ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT)
//                +"\n"
//                +getString(R.string.remind_time)+":" + Utils.getTimeFormatted(PrayerTime.getTimeToAnnounceAthkar(AthkarType.MorningAthkar),"HH:mm"));
        citationForMorningTimeTextView.setText(Utils.getAzkarTimeTypeText(AthkarType.MorningAthkar) + ":" + HawkSettings.getAzkarTimeDuration(AthkarType.MorningAthkar)
                + "\n"
                + getString(R.string.remind_time) + ":" + Utils.getTimeFormatted(PrayerTime.getTimeToAnnounceAthkar(AthkarType.MorningAthkar), "hh:mm a"));

        citationForEveningTimeTextView.setText(Utils.getAzkarTimeTypeText(AthkarType.EveningAthkar) + ":" + HawkSettings.getAzkarTimeDuration(AthkarType.EveningAthkar)
                + "\n"
                + getString(R.string.remind_time) + ":" + Utils.getTimeFormatted(PrayerTime.getTimeToAnnounceAthkar(AthkarType.EveningAthkar), "hh:mm a"));

        mainSettingsAdapter.clear();
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.citation_for_morning)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_MORNING_PRAYER_KEY, ATHKARS_MORNING_PRAYER_DEFAULT)
                , false);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);
        settingItemRecyclerView.setAdapter(mainSettingsAdapter);


        mainSettingsAdapter1.clear();
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimese = new SubSettingAlrabeeaTimes(getString(R.string.citation_for_evening)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , getString(R.string.duration_of_after_prayer) + "."
                , Hawk.get(ATHKARS_EVNING_PRAYER_KEY, ATHKARS_EVNING_PRAYER_DEFAULT)
                , false);
        mainSettingsAdapter1.add(subSettingAlrabeeaTimese);
        settingItemRecyclerViewe.setAdapter(mainSettingsAdapter1);
        citationForMorningTime = Hawk.get(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT);
        citationForEveningTime = Hawk.get(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT);

        boolean isEnableCitationForMorning = Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_MORNING_KEY, DEFAULT_FOR_IS_THERE_ATHKER);
        citationForMorningCheckBox.setChecked(isEnableCitationForMorning);

        citationForMorningTimeTextView.setEnabled(isEnableCitationForMorning);

        boolean isEnableCitationForEvening = Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_EVENING_KEY, true);
        citationForEveningCheckBox.setChecked(isEnableCitationForEvening);

        citationForEveningTimeTextView.setEnabled(isEnableCitationForEvening);
        citationForEveningTimeSeekBar.setEnabled(isEnableCitationForEvening);

    }

    private void SetAction() {

        citationForMorningCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            citationForMorningTimeTextView.setEnabled(isChecked);
            citationForMorningTimeSeekBar.setEnabled(isChecked);


            Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_MORNING_KEY, isChecked);
            Hawk.put(ConstantsOfApp.IS_SHOW_CITATION_FOR_MORNING_KEY, isChecked);

            boolean isEnableCitationForMorning = Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_MORNING_KEY, DEFAULT_FOR_IS_THERE_ATHKER);
            Log.i(TAG, "SetAction: " + isEnableCitationForMorning);
        });

        citationForEveningCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            citationForEveningTimeTextView.setEnabled(isChecked);
            citationForEveningTimeSeekBar.setEnabled(isChecked);


            Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_EVENING_KEY, isChecked);
            Hawk.put(ConstantsOfApp.IS_SHOW_CITATION_FOR_EVENING_KEY, isChecked);
        });
    }

    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = mainSettingsAdapter.getItem(position);
        System.out.println(tag);
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes1 = mainSettingsAdapter1.getItem(position);

        boolean isEnable = subPosition == 1;

        switch (position) {
            case 0:

                if (tag.trim().equals(getString(R.string.citation_for_evening))) {
                    Hawk.put(ATHKARS_EVNING_PRAYER_KEY, subPosition);
                } else
                    Hawk.put(ATHKARS_MORNING_PRAYER_KEY, subPosition);

//                Hawk.put(ConstantsOfApp.IS_SHOW_CITATION_FOR_MORNING_KEY, true);
//                Hawk.put(ConstantsOfApp.IS_SHOW_CITATION_FOR_EVENING_KEY, true);


        }
    }
}


package com.arapeak.alrbea.UI.Activity;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

/**
 * Helper class for MainActivity UI operations
 * Handles UI updates, visibility changes, and text setting operations
 */
public class MainActivityUIHelper {
    private static final String TAG = "MainActivityUIHelper";
    private final Context context;

    public MainActivityUIHelper(Context context) {
        this.context = context;
    }

    /**
     * Safely sets text to a TextView with null checks and error handling
     */
    public void setText(TextView textView, String text) {
        try {
            if (textView != null && text != null) {
                textView.setText(text);
            } else {
                Log.w(TAG, "setText: TextView or text is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting text: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely sets text to a TextView with CharSequence
     */
    public void setText(TextView textView, CharSequence text) {
        try {
            if (textView != null && text != null) {
                textView.setText(text);
            } else {
                Log.w(TAG, "setText: TextView or text is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting text: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely sets visibility of a view with null checks and error handling
     */
    public void setVisibility(View view, int visibility) {
        try {
            if (view != null) {
                view.setVisibility(visibility);
            } else {
                Log.w(TAG, "setVisibility: View is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting visibility: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely sets background resource with null checks and error handling
     */
    public void setBackgroundResource(View view, int resourceId) {
        try {
            if (view != null) {
                view.setBackgroundResource(resourceId);
            } else {
                Log.w(TAG, "setBackgroundResource: View is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting background resource: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely sets image resource with null checks and error handling
     */
    public void setImageResource(android.widget.ImageView imageView, int resourceId) {
        try {
            if (imageView != null) {
                imageView.setImageResource(resourceId);
            } else {
                Log.w(TAG, "setImageResource: ImageView is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting image resource: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely sets selected state with null checks and error handling
     */
    public void setSelected(View view) {
        try {
            if (view != null) {
                view.setSelected(true);
            } else {
                Log.w(TAG, "setSelected: View is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting selected state: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely sets layout direction with null checks and error handling
     */
    public void safeSetLayoutDirection(View view) {
        try {
            if (view != null) {
                view.setLayoutDirection(View.LAYOUT_DIRECTION_LOCALE);
            } else {
                Log.w(TAG, "safeSetLayoutDirection: View is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting layout direction: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Shows a toast message safely with error handling
     */
    public void showToast(String message, int duration) {
        try {
            if (context != null && message != null) {
                Toast.makeText(context, message, duration).show();
            } else {
                Log.w(TAG, "showToast: Context or message is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing toast: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Shows a short toast message
     */
    public void showToast(String message) {
        showToast(message, Toast.LENGTH_SHORT);
    }

    /**
     * Safely runs code on UI thread with error handling
     */
    public void safeRunOnUi(Runnable runnable) {
        try {
            if (context instanceof MainActivity && runnable != null) {
                ((MainActivity) context).runOnUiThread(runnable);
            } else {
                Log.w(TAG, "safeRunOnUi: Context is not MainActivity or runnable is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error running on UI thread: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safely finds view by ID with error handling
     */
    public <T extends View> T findViewById(int id) {
        try {
            if (context instanceof MainActivity) {
                return ((MainActivity) context).findViewById(id);
            } else {
                Log.w(TAG, "findViewById: Context is not MainActivity");
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error finding view by ID: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            return null;
        }
    }

    /**
     * Logs debug messages with consistent formatting
     */
    public void log(String message) {
        try {
            Log.d(TAG, message);
        } catch (Exception e) {
            // Fallback logging
            System.out.println(TAG + ": " + message);
        }
    }

    /**
     * Logs error messages with consistent formatting
     */
    public void logError(String message, Throwable throwable) {
        try {
            Log.e(TAG, message, throwable);
            CrashlyticsUtils.INSTANCE.logException(throwable);
        } catch (Exception e) {
            // Fallback logging
            System.err.println(TAG + ": " + message);
            if (throwable != null) {
                throwable.printStackTrace();
            }
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_5sdp"
    android:layoutDirection="locale"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#CFECEA"
        app:cardCornerRadius="@dimen/_5sdp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_8sdp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/active"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_14sdp" />

            <TextView
                android:id="@+id/tv_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/active"
                android:textColor="#8A8686"
                android:textSize="@dimen/_16sdp" />

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/active"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_13sdp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>
package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


public final class Prayers {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata */
    private double fajr;

    /* renamed from: b, reason: from kotlin metadata */
    private double shrouk;

    /* renamed from: c, reason: from kotlin metadata */
    private double zuhr;

    /* renamed from: d, reason: from kotlin metadata */
    private double asr;

    /* renamed from: e, reason: from kotlin metadata */
    private double maghrib;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata */
    private double isha;

    public final void checkZeroVals() {
        double d = this.fajr;
        double d2 = 12.0d;
        if (((int) d) <= 0) {
            d = 12.0d;
        }
        this.fajr = d;
        double d3 = this.shrouk;
        if (((int) d3) <= 0) {
            d3 = 12.0d;
        }
        this.shrouk = d3;
        double d4 = this.zuhr;
        if (((int) d4) <= 0) {
            d4 = 12.0d;
        }
        this.zuhr = d4;
        double d5 = this.asr;
        if (((int) d5) <= 0) {
            d5 = 12.0d;
        }
        this.asr = d5;
        double d6 = this.maghrib;
        if (((int) d6) <= 0) {
            d6 = 12.0d;
        }
        this.maghrib = d6;
        double d7 = this.isha;
        if (((int) d7) > 0) {
            d2 = d7;
        }
        this.isha = d2;
    }

    public final double getAsr() {
        return this.asr;
    }

    public final void setAsr(double d) {
        this.asr = d;
    }

    public final double getFajr() {
        return this.fajr;
    }

    public final void setFajr(double d) {
        this.fajr = d;
    }

    public final double getIsha() {
        return this.isha;
    }

    public final void setIsha(double d) {
        this.isha = d;
    }

    public final double getMaghrib() {
        return this.maghrib;
    }

    public final void setMaghrib(double d) {
        this.maghrib = d;
    }

    public final double getShrouk() {
        return this.shrouk;
    }

    public final void setShrouk(double d) {
        this.shrouk = d;
    }

    public final double getZuhr() {
        return this.zuhr;
    }

    public final void setZuhr(double d) {
        this.zuhr = d;
    }
}

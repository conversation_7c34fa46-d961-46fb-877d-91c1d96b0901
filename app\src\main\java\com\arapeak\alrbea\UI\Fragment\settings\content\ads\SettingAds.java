package com.arapeak.alrbea.UI.Fragment.settings.content.ads;


import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.AddEventFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.NewsFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.Titlejomaa;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.events.EventsFragment;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.List;

public class SettingAds extends AlrabeeaTimesFragment implements AdapterCallback {
    private static final String TAG = "DesignFolderSettingsFragment";

    private View mainSettingsFragment;
    private RecyclerView settingItemRecyclerView;
    private Dialog loadingDialog;

    //    private SettingsAdapter settingsAdapter;
    private SettingsAdapter settingsAdapter;

    public SettingAds() {

    }

    public static SettingAds newInstance() {
        return new SettingAds();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mainSettingsFragment = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return mainSettingsFragment;
    }

    private void initView() {
        settingItemRecyclerView = mainSettingsFragment.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        if (Utils.isLandscape()) {
            settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 3));
        } else {
            settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));

        }
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        settingsAdapter = new SettingsAdapter(getContext(), setupSettings(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()){
//            SettingsLandscapeActivity.setTextTite("الاعلانات");
//        }else {
//            SettingsActivity.setTextTite("الاعلانات");
//        }
        SettingsActivity.setTextTite("الاعلانات");
        settingItemRecyclerView.setAdapter(settingsAdapter);

    }

    private void SetAction() {


    }

    @Override
    public void onItemClick(int position, String tag) {
        switch (position) {
            case 0:
                Utils.loadFragment(new NewsFragment()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 1:
                Utils.loadFragment(new EventsFragment()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 2:
                Utils.loadFragment(AddEventFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 3:
                Utils.loadFragment(Titlejomaa.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
        }

    }

    private List<SettingAlrabeeaTimes> setupSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.settings_ads);
        String[] generalSettingsDescriptionArray = getResources().getStringArray(R.array.settings_ads_des);

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[0]
                , generalSettingsDescriptionArray[0]
                , R.drawable.news));
        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[1]
                , generalSettingsDescriptionArray[1]
                , R.drawable.calendar_app_96px));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[2]
                , generalSettingsDescriptionArray[2]
                , R.drawable.tabot));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[3]
                , generalSettingsDescriptionArray[3]
                , R.drawable.manbere));


        return settingAlrabeeaTimes;
    }
}


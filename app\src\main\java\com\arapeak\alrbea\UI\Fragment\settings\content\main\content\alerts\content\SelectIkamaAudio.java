package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.alerts.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.CUSTOM_IKAMA;

import android.app.Dialog;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.SeekBar;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.IkamaAudio;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.AudioViewHolder;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.downloader.Error;
import com.downloader.OnDownloadListener;
import com.downloader.PRDownloader;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;

import java.io.File;

public class SelectIkamaAudio extends Fragment {

    Dialog loadingDialog;
    int currentPlayingAudio = 0;
    private View container;
    private ViewGroup audio_container;
    private SwitchCompat soundEnableSw;
    private RecyclerView rv_audio;
    private SeekBar audioLevel;
    private RecyclerView.Adapter<AudioViewHolder> ikamaAdapter;
    private MediaPlayer mediaPlayer;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        this.container = inflater.inflate(R.layout.fragment_select_ikama_audio, container, false);
        init();
        return this.container;
    }

    private void init() {
        soundEnableSw = container.findViewById(R.id.enableDisablePhoto_SwitchCompat_AddPhotoFragment);
        rv_audio = container.findViewById(R.id.rv_audio);
        audioLevel = container.findViewById(R.id.citationForMorningTime_SeekBar_AthkarFragment);
        audio_container = container.findViewById(R.id.container);

        loadingDialog = Utils.initLoadingDialogWithString(requireContext(), Utils.getString(R.string.downloading_file));
        ikamaAdapter = new RecyclerView.Adapter<AudioViewHolder>() {
            @NonNull
            @Override
            public AudioViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                LayoutInflater inflater = LayoutInflater.from(requireContext());
                return new AudioViewHolder(inflater.inflate(R.layout.layout_list_item_audio_select, parent, false));
            }

            @Override
            public void onBindViewHolder(@NonNull AudioViewHolder holder, int position) {
                holder.Bind(position
                        , mediaPlayer != null && mediaPlayer.isPlaying() && currentPlayingAudio == position
                        , HawkSettings.getCurrentIkama() == position
                        , IkamaAudio.values()[position]
                        , onPlayAudio -> {
                            if (mediaPlayer != null && mediaPlayer.isPlaying() && currentPlayingAudio == position) {
                                stopAudio();
                            } else {
                                IkamaAudio ikamaAudio = IkamaAudio.values()[holder.position];
                                String path = ikamaAudio.getPath();
                                if (ikamaAudio != IkamaAudio.ALERT && !new File(path).exists()) {
                                    if (ikamaAudio == IkamaAudio.CUSTOM) {
                                        selectAudioFile(() -> playAudio(holder.position));
                                    } else
                                        downloadIkama(holder.position, () -> playAudio(holder.position));
                                } else
                                    playAudio(holder.position);
                            }
                        }
                        , onAudioSelected -> {
                            IkamaAudio ikama = IkamaAudio.values()[holder.position];
                            String path = ikama.getPath();
                            if (ikama != IkamaAudio.ALERT && !new File(path).exists()) {
                                if (ikama == IkamaAudio.CUSTOM) {
                                    selectAudioFile(() -> selectIkama(holder.position));
                                } else
                                    downloadIkama(holder.position, () -> selectIkama(holder.position));
                            } else {
                                selectIkama(holder.position);
                            }
                        }
                );
            }

            @Override
            public int getItemCount() {
                return IkamaAudio.values().length;
            }
        };
        rv_audio.setAdapter(ikamaAdapter);

        Utils.setColorStateListToSwitchCompat(requireActivity(), soundEnableSw);
        soundEnableSw.setChecked(HawkSettings.getAudioIkamaEnabled());
        soundEnableSw.setOnClickListener(i -> {
            HawkSettings.setAudioIkamaEnabled(soundEnableSw.isChecked());
            enableDisableAudio();
        });

        enableDisableAudio();

        float level = HawkSettings.getAudioLevel() * 10;
        audioLevel.setProgress((int) level);
        audioLevel.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float prog = progress * 0.1f;
                if (fromUser) {
                    HawkSettings.setAudioLevel(prog);
                    if (mediaPlayer != null)
                        mediaPlayer.setVolume(prog, prog);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }

    private void enableDisableAudio() {
        boolean isEnabled = HawkSettings.getAudioIkamaEnabled();
        audio_container.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
    }

    private void selectAudioFile(Runnable onComplete) {
        new ChooserDialog(requireContext())
                .withFilter(false, false, "mp3")
                .withChosenListener((path, pathFile) -> {
                    Hawk.put(CUSTOM_IKAMA, path);
                    onComplete.run();
                })
                .build()
                .show();
    }

    private void selectIkama(int position) {
        int lastIkama = HawkSettings.getCurrentIkama();
        HawkSettings.setCurrentIkama(position);
        ikamaAdapter.notifyItemChanged(lastIkama);
        ikamaAdapter.notifyItemChanged(position);

    }

    private void downloadIkama(int position, Runnable onComplete) {
        loadingDialog.show();
        IkamaAudio ikama = IkamaAudio.values()[position];
        String url = ikama.getURL();
        if (!url.isEmpty()) {
            PRDownloader.download(url, Utils.getDownloadPath() + "/ikama/", "" + position)
                    .build()
                    .start(new OnDownloadListener() {
                        @Override
                        public void onDownloadComplete() {
                            loadingDialog.dismiss();
                            onComplete.run();
                        }

                        @Override
                        public void onError(Error error) {
                            Utils.showFailAlert(requireActivity(), "خطأ", "فشل تحميل الملف ");
                            loadingDialog.dismiss();
                        }
                    });
        }
    }

    private void playAudio(int index) {
        IkamaAudio ikamaAudio = IkamaAudio.values()[index];
        stopAudio();
        try {
            if (ikamaAudio == IkamaAudio.ALERT)
                mediaPlayer = MediaPlayer.create(requireContext(), R.raw.a);
            else
                mediaPlayer = MediaPlayer.create(requireContext(), ikamaAudio.getUri());

            float vol = HawkSettings.getAudioLevel();
            mediaPlayer.setVolume(vol, vol);
            mediaPlayer.setOnCompletionListener(i -> {
                if (ikamaAdapter != null) {
                    ikamaAdapter.notifyItemChanged(currentPlayingAudio);
                }
            });
            currentPlayingAudio = index;
            mediaPlayer.start();
            ikamaAdapter.notifyItemChanged(currentPlayingAudio);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void stopAudio() {
        try {
            if (mediaPlayer != null && mediaPlayer.isPlaying())
                mediaPlayer.stop();
            ikamaAdapter.notifyItemChanged(currentPlayingAudio);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public void onStop() {
        stopAudio();
        try {
            if (mediaPlayer != null)
                mediaPlayer.release();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        super.onStop();
    }
}
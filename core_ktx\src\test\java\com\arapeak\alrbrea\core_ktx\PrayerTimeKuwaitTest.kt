package com.arapeak.alrbrea.core_ktx

import android.content.Context
import android.location.Location
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum
import com.arapeak.alrbrea.core_ktx.repo.PrayerTimeRepo
import com.batoulapps.adhan2.CalculationMethod
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import java.util.Calendar
import java.util.TimeZone


@RunWith(RobolectricTestRunner::class)
class PrayerTimeKuwaitTest {
    private lateinit var repo: PrayerTimeRepo
    val timezone = "GMT-1:00"
    lateinit var context: Context


    @Before
    fun setUp() {
        val location = Location("mock").apply {
            latitude = 29.076944
            longitude = 48.083889
        }
        repo = PrayerTimeRepo(location, CalculationMethod.KUWAIT)
        context = RuntimeEnvironment.getApplication();
    }


    @Test
    fun test_date_1() {
        val expectedFajr = "03:19"
        val expectedSunrise = "04:54"
        val expectedDhur = "11:53"
        val expectedAsr = "15:27"
        val expectedMaghreb = "18:51"
        val expectedIsha = "20:22"

        val dateToTest = "6/7/2024"


        val date = Calendar.getInstance().apply {
            set(Calendar.YEAR, dateToTest.split("/")[2].toInt())
            set(Calendar.MONTH, dateToTest.split("/")[1].toInt() - 1)
            set(Calendar.DAY_OF_MONTH, dateToTest.split("/")[0].toInt())
        }
        val res = repo.getPrayerTimes(date, CountriesSupportedEnum.Kuwait, context)

        res.fajr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.sunrise.time.timeZone = TimeZone.getTimeZone(timezone)
        res.dhuhr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.asr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.maghrib.time.timeZone = TimeZone.getTimeZone(timezone)
        res.isha.time.timeZone = TimeZone.getTimeZone(timezone)
        res.eid?.time?.timeZone = TimeZone.getTimeZone(timezone)

        Assert.assertEquals(expectedFajr.split(":")[0].toInt(), res.fajr.time.time.hours)
        Assert.assertEquals(expectedFajr.split(":")[1].toInt(), res.fajr.time.time.minutes)

        Assert.assertEquals(expectedSunrise.split(":")[0].toInt(), res.sunrise.time.time.hours)
        Assert.assertEquals(expectedSunrise.split(":")[1].toInt(), res.sunrise.time.time.minutes)

        Assert.assertEquals(expectedDhur.split(":")[0].toInt(), res.dhuhr.time.time.hours)
        Assert.assertEquals(expectedDhur.split(":")[1].toInt(), res.dhuhr.time.time.minutes)

        Assert.assertEquals(expectedAsr.split(":")[0].toInt(), res.asr.time.time.hours)
        Assert.assertEquals(expectedAsr.split(":")[1].toInt(), res.asr.time.time.minutes)

        Assert.assertEquals(expectedMaghreb.split(":")[0].toInt(), res.maghrib.time.time.hours)
        Assert.assertEquals(expectedMaghreb.split(":")[1].toInt(), res.maghrib.time.time.minutes)

        Assert.assertEquals(expectedIsha.split(":")[0].toInt(), res.isha.time.time.hours)
        Assert.assertEquals(expectedIsha.split(":")[1].toInt(), res.isha.time.time.minutes)
    }
}
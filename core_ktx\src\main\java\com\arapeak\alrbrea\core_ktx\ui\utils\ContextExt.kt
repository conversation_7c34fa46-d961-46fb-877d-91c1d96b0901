package com.arapeak.alrbrea.core_ktx.ui.utils

import android.app.ActivityManager
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

fun Context.saveImageToCache(bitmap: Bitmap?, path: String, name: String) {
    if (bitmap == null)
        return

    try {
        File(cacheDir, path).mkdirs()
        val f = File(cacheDir, "$path$name")
        f.setReadable(true, false)
        val out = FileOutputStream(
            f
        )
        bitmap?.compress(
            Bitmap.CompressFormat.JPEG,
            90, out
        )
        out.flush()
        out.close()

        Log.d("Files", "saveImageToCache: ${f.absoluteFile}")
    } catch (e: FileNotFoundException) {
        CrashlyticsUtils.logException(e)
    } catch (e: IOException) {
        CrashlyticsUtils.logException(e)
    }

}


fun Context.isActivityInForeground(activityClass: Class<*>): Boolean {
    try {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = activityManager.getRunningTasks(1)
        if (runningTasks.isNotEmpty()) {
            val topActivity = runningTasks[0].topActivity
            if (topActivity?.className == activityClass.name)
                return true
        }
    } catch (e: Exception) {
        CrashlyticsUtils.logException(e)
    }

    try {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val tasks = activityManager.appTasks
        if (tasks.isNotEmpty()) {
            val topActivity = tasks[0].taskInfo.topActivity
            if (topActivity?.className == activityClass.name)
                return true
        }
    } catch (e: Exception) {
        CrashlyticsUtils.logException(e)
    }

    return false
}

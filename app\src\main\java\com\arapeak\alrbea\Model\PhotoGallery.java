package com.arapeak.alrbea.Model;

import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class PhotoGallery {
    public String name = "";
    public long id = 0;
    public ArrayList<PhotoGalleryImage> images = new ArrayList<>();

    public TimeAmount getPrayerTimesDuration() {
        return Hawk.get("prayerTimesGalleryDuration" + id, new TimeAmount(0, 0, 15));
    }

    public void setPrayerTimesDuration(TimeAmount amount) {
        Hawk.put("prayerTimesGalleryDuration" + id, amount);
    }

    public TimeAmount getSingleImageDuration() {
        return Hawk.get("singleImageGalleryDuration" + id, new TimeAmount(0, 0, 15));
    }

    public void setSingleImageDuration(TimeAmount amount) {
        Hawk.put("singleImageGalleryDuration" + id, amount);
    }

    public boolean isEnabledBetweenAzanAndIkama() {
        return Hawk.get("galleryEnabledBetweenAzanAndIkama" + id, false);
    }

    public void setEnabledBetweenAzanAndIkama(boolean enabled) {
        Hawk.put("galleryEnabledBetweenAzanAndIkama" + id, enabled);
    }

    public boolean isEnabledBetweenAzanAndIkamaJomaa() {
        return Hawk.get("galleryEnabledBetweenAzanAndIkamaJomaa" + id, false);
    }

    public void setEnabledBetweenAzanAndIkamaJomaa(boolean enabled) {
        Hawk.put("galleryEnabledBetweenAzanAndIkamaJomaa" + id, enabled);
    }
}

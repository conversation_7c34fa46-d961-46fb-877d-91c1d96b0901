package com.arapeak.alrbea.ummalqura_objects;

import android.content.Context;
import android.location.Location;

import com.arapeak.alrbea.R;


public class City {
    public static final City[] cities;

    static {
        City[] cityArr = new City[androidx.appcompat.R.styleable.AppCompatTheme_windowMinWidthMinor];
        int i = 0;
        cityArr[0] = new City(18.22d, 42.51d);
        cityArr[1] = new City(25.93d, 49.66d);
        cityArr[2] = new City(16.97d, 42.83d);
        cityArr[3] = new City(24.74d, 46.58d);
        cityArr[4] = new City(23.91d, 42.92d);
        cityArr[5] = new City(16.7d, 42.95d);
        cityArr[6] = new City(18.17d, 42.84d);
        cityArr[7] = new City(27.56d, 47.7d);
        cityArr[8] = new City(22.22d, 46.656d);
        cityArr[9] = new City(25.408d, 49.6132d);
        cityArr[10] = new City(17.24d, 42.95d);
        cityArr[11] = new City(20.27d, 41.66d);
        cityArr[12] = new City(17.04d, 43.09d);
        cityArr[13] = new City(26.77d, 44.22d);
        cityArr[14] = new City(26.0d, 43.73d);
        cityArr[15] = new City(20.01d, 41.47d);
        cityArr[16] = new City(26.15d, 43.67d);
        cityArr[17] = new City(17.34d, 43.14d);
        cityArr[18] = new City(26.03d, 44.95d);
        cityArr[19] = new City(26.79d, 41.32d);
        cityArr[20] = new City(31.33d, 37.34d);
        cityArr[21] = new City(24.05d, 45.25d);
        cityArr[22] = new City(23.62d, 46.52d);
        cityArr[23] = new City(16.78d, 43.22d);
        cityArr[24] = new City(24.88d, 40.52d);
        cityArr[25] = new City(21.63d, 39.7d);
        cityArr[26] = new City(22.26d, 39.79d);
        cityArr[27] = new City(24.15d, 47.32d);
        cityArr[28] = new City(21.91d, 42.02d);
        cityArr[29] = new City(20.15d, 40.28d);
        cityArr[30] = new City(23.52d, 40.89d);
        cityArr[31] = new City(19.78d, 41.43d);
        cityArr[32] = new City(25.87d, 44.21d);
        cityArr[33] = new City(19.13d, 41.93d);
        cityArr[34] = new City(20.16d, 41.29d);
        cityArr[35] = new City(19.14d, 42.14d);
        cityArr[36] = new City(27.47d, 48.47d);
        cityArr[37] = new City(26.55d, 37.96d);
        cityArr[38] = new City(17.71d, 42.09d);
        cityArr[39] = new City(20.25d, 41.36d);
        cityArr[40] = new City(17.6d, 42.86d);
        cityArr[41] = new City(25.86d, 43.48d);
        cityArr[42] = new City(27.17d, 42.44d);
        cityArr[43] = new City(26.23d, 36.47d);
        cityArr[44] = new City(25.03d, 37.26d);
        cityArr[45] = new City(25.94d, 42.95d);
        cityArr[46] = new City(30.99d, 41.02d);
        cityArr[47] = new City(20.47d, 45.57d);
        cityArr[48] = new City(26.3d, 44.82d);
        cityArr[49] = new City(23.78d, 38.8d);
        cityArr[50] = new City(17.88d, 43.72d);
        cityArr[51] = new City(17.378d, 42.538d);
        cityArr[52] = new City(19.86d, 41.57d);
        cityArr[53] = new City(27.9d, 42.4d);
        cityArr[54] = new City(19.57d, 41.96d);
        cityArr[55] = new City(20.0d, 42.6d);
        cityArr[56] = new City(26.35d, 43.96d);
        cityArr[57] = new City(26.44d, 50.1d);
        cityArr[58] = new City(27.35d, 35.69d);
        cityArr[59] = new City(17.69d, 43.51d);
        cityArr[60] = new City(17.1d, 42.78d);
        cityArr[61] = new City(24.6d, 46.13d);
        cityArr[62] = new City(29.81d, 39.87d);
        cityArr[63] = new City(24.5d, 44.38d);
        cityArr[64] = new City(16.71d, 42.12d);
        cityArr[65] = new City(19.13d, 41.08d);
        cityArr[66] = new City(17.85d, 43.02d);
        cityArr[67] = new City(28.4d, 45.97d);
        cityArr[68] = new City(27.52d, 41.7d);
        cityArr[69] = new City(29.3d, 34.95d);
        cityArr[70] = new City(23.49d, 46.86d);
        cityArr[71] = new City(25.11d, 46.1d);
        cityArr[72] = new City(16.89d, 42.54d);
        cityArr[73] = new City(21.5d, 39.17d);
        cityArr[74] = new City(27.0d, 49.66d);
        cityArr[75] = new City(28.42d, 48.49d);
        cityArr[76] = new City(25.71d, 39.28d);
        cityArr[77] = new City(18.31d, 42.73d);
        cityArr[78] = new City(18.85d, 51.819d);
        cityArr[79] = new City(26.26d, 50.21d);
        cityArr[80] = new City(17.6d, 45.066d);
        cityArr[81] = new City(22.11d, 39.31d);
        cityArr[82] = new City(24.54d, 39.63d);
        cityArr[83] = new City(18.54d, 42.05d);
        cityArr[84] = new City(25.9d, 45.34d);
        cityArr[85] = new City(21.426666d, 39.831666d);
        cityArr[86] = new City(24.48d, 46.29d);
        cityArr[87] = new City(17.52d, 44.2d);
        cityArr[88] = new City(26.09d, 43.97d);
        cityArr[89] = new City(26.51d, 43.62d);
        cityArr[90] = new City(26.56d, 50.0d);
        cityArr[91] = new City(19.95d, 41.24d);
        cityArr[92] = new City(22.79d, 39.03d);
        cityArr[93] = new City(29.63d, 43.5d);
        cityArr[94] = new City(25.56d, 47.17d);
        cityArr[95] = new City(21.28d, 42.82d);
        cityArr[96] = new City(26.71d, 50.07d);
        cityArr[97] = new City(18.23d, 42.28d);
        cityArr[98] = new City(24.67d, 46.69d);
        cityArr[99] = new City(26.04d, 43.55d);
        cityArr[100] = new City(17.15d, 42.62d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_textAppearanceSearchResultTitle] = new City(29.97d, 40.2d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_textAppearanceSmallPopupMenu] = new City(16.59d, 42.94d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_textColorAlertDialogListItem] = new City(18.08d, 43.14d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_textColorSearchUrl] = new City(25.26d, 45.26d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_toolbarNavigationButtonStyle] = new City(17.49d, 47.11d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_toolbarStyle] = new City(26.32d, 44.26d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_tooltipForegroundColor] = new City(28.4d, 36.58d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_tooltipFrameBackground] = new City(21.25d, 40.4d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_viewInflaterClass] = new City(19.55d, 43.52d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowActionBar] = new City(28.63d, 38.55d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowActionBarOverlay] = new City(25.28d, 45.86d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowActionModeOverlay] = new City(17.97d, 44.11d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowFixedHeightMajor] = new City(21.23d, 41.65d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowFixedHeightMinor] = new City(31.68d, 38.66d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowFixedWidthMajor] = new City(20.4722d, 44.783d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowFixedWidthMinor] = new City(18.53d, 44.21d);
        cityArr[androidx.appcompat.R.styleable.AppCompatTheme_windowMinWidthMajor] = new City(24.09d, 38.07d);
        cities = cityArr;
        while (i < cities.length) {
            cities[i].nameResourceID = i;
            i++;
        }
    }

    private final Location location = new Location("");
    private String cityName;
    private int nameResourceID = -1;

    public City(double d, double d2) {
        this.location.setLongitude(d2);
        this.location.setLatitude(d);
    }

    public static City getCityByName(Location location, String str, Context context) {
        for (City city : cities) {
            if (city.getCityName(context).equals(str)) {
                return city;
            }
        }
        City city2 = new City(location.getLatitude(), location.getLongitude());
        city2.cityName = str;
        return city2;
    }

    public static City getDefaultCity() {
        return cities[0];
    }

    public String getCityName(Context context) {
        if (this.nameResourceID == -1) {
            return this.cityName;
        }
        if (context != null) {
            this.cityName = this.cityName = context.getResources().getStringArray(R.array.cities)[this.nameResourceID];
        } else {
            this.cityName = "";
        }
        return this.cityName;
    }

    public Location getCityLocation() {
        return this.location;
    }
}

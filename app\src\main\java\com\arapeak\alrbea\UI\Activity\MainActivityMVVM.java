package com.arapeak.alrbea.UI.Activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsoluteLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.palette.graphics.Palette;

import com.arapeak.alrbea.AnnouncementMessage;
import com.arapeak.alrbea.Enum.AnnouncementType;
import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.AzkarTheme;
import com.arapeak.alrbea.Enum.News;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.ResourcesLocale;
import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.AnnouncementManager;
import com.arapeak.alrbea.UI.Activity.repository.DateTimeRepository;
import com.arapeak.alrbea.UI.Activity.repository.PrayerRepository;
import com.arapeak.alrbea.UI.Activity.viewmodel.MainViewModel;
import com.arapeak.alrbea.UI.Fragment.settings.content.textDesigner.FontMapper;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.ui.screensaver.ScreensaverScheduler;
import com.arapeak.alrbrea.core_ktx.ui.textdesign.TextDesignUiManager;
import com.google.android.material.navigation.NavigationView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.mikhaellopez.circularprogressbar.CircularProgressBar;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;
import com.tapadoo.alerter.Alert;

import java.io.File;
import java.util.Calendar;
import java.util.List;

/**
 * MainActivity following MVVM architecture
 * This is a clean, maintainable version of the original MainActivity
 */
public class MainActivityMVVM extends BaseAppCompatActivity
        implements NavigationView.OnNavigationItemSelectedListener, AdapterCallback {

    public static final String TAG = "MainActivityMVVM";

    // ViewModel and Repositories
    private MainViewModel viewModel;
    private PrayerRepository prayerRepository;
    private DateTimeRepository dateTimeRepository;

    // UI Components
    private LinearLayout containerLinearLayout;
    private Dialog loadingDialog;
    private Alert alert = null;

    // Time and Date Views
    private TextView timeNowTextView, timeNowTypeTextView;
    private TextView dateNowTextView, dateNowTextView2, dateHTextView;
    private TextView datehm, datehy, datey, datem, dayText;

    // Prayer Time Views
    private ViewGroup prayerTimeContainer;
    private TextView remainingPrayerTextView;
    private TextView fajrTextView, fajrTimeTextView, remainingFajrTextView;
    private TextView sunriseTextView, sunriseTimeTextView, remainingSunriseTextView;
    private TextView dhuhrTextView, dhuhrTimeTextView, remainingDhuhrTextView;
    private TextView asrTextView, asrTimeTextView, remainingAsrTextView;
    private TextView maghribTextView, maghribTimeTextView, remainingMaghribTextView;
    private TextView ishaTextView, ishaTimeTextView, remainingIshaTextView;

    // Prayer Time Type Views
    private TextView fajrTimeTypeTextView, sunriseTimeTypeTextView, dhuhrTimeTypeTextView;
    private TextView asrTimeTypeTextView, maghribTimeTypeTextView, ishaTimeTypeTextView;

    // Prayer Time Layout Views
    private ViewGroup fajrTimeLinearLayout, sunriseTimeLinearLayout, dhuhrTimeLinearLayout;
    private ViewGroup asrTimeLinearLayout, maghribTimeLinearLayout, ishaTimeLinearLayout;
    private ViewGroup fajrATimeLinearLayout, sunriseATimeLinearLayout, dhuhrATimeLinearLayout;
    private ViewGroup asrATimeLinearLayout, maghribATimeLinearLayout, ishaATimeLinearLayout;

    // Content Layout Views
    private LinearLayout contentFajrLayout, contentSunriseLayout, contentDhuhrLayout;
    private LinearLayout contentAsrLayout, contentMaghribLayout, contentIshaLayout;

    // Image Views
    private ImageView backgroundImageView, containerImageView, alrabeeaTimesImageView;
    private ImageView dayimage, gregorian_month_image, hijri_month_image;
    private ImageView imageSunriseView, imagefajrView, imagedhuhrView;
    private ImageView imageasrView, imagemaghribView, imageishaView;

    // Athkar Views
    private ViewGroup athkarContainer;
    private TextView athkarTextView, athkarTimeTextView;
    private ImageView athkarImageView, athkarIcon;
    private LinearLayout athkarTime;

    // Announcement Views
    private ViewGroup announcementContainer;
    private TextView movingMessageTextView;

    // Month Container Views
    private ViewGroup gregorian_month_container, hijri_month_container;
    private ViewGroup timeNowLinearLayout;

    // Ikama Time Views
    private TextView tvIkamaDelayFajr, tvIkamaDelayDhur, tvIkamaDelayAsr;
    private TextView tvIkamaDelayMaghreb, tvIkamaDelayIsha;

    // Other Components
    private AnnouncementManager announcementManager;
    private ScreensaverScheduler screensaver;
    private TextDesignUiManager textDesign;

    // State Variables
    private boolean isLandscape;
    private boolean isShowingAthkar = false;
    private boolean isShowingAnouncement = false;
    private boolean isShowingPhotoGallery = false;
    private UITheme uiTheme = null;
    private String lan = "ar";
    private int domainColor = 0;
    private int textColor = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "Creating MainActivityMVVM");

            // Set up crash handler
            setupCrashHandler();

            // Start monitoring service
            startAppMonitorService();

            // Initialize basic settings
            initializeBasicSettings();

            // Set content view
            setContentView(R.layout.activity_main);

            // Initialize ViewModel and Repositories
            initializeArchitectureComponents();

            // Initialize UI
            initializeUI();

            // Set up observers
            setupObservers();

            // Initialize other components
            initializeOtherComponents();

            Log.d(TAG, "MainActivityMVVM created successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            handleCriticalError("Failed to initialize app", e);
        }
    }

    /**
     * Initialize basic settings
     */
    private void initializeBasicSettings() {
        try {
            adjustDisplayScale();
            uiTheme = HawkSettings.getCurrentTheme();
            lan = HawkSettings.getLocaleLanguage();
            Utils.initActivity(this);
            isLandscape = isLandscape();

            // Set orientation based on settings
            setOrientation();

        } catch (Exception e) {
            Log.e(TAG, "Error initializing basic settings", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set orientation based on settings
     */
    private void setOrientation() {
        try {
            switch (HawkSettings.getAppOrientation()) {
                case 1:
                    Log.d(TAG, "Setting orientation to LANDSCAPE");
                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                    break;
                case 2:
                    Log.d(TAG, "Setting orientation to REVERSE_PORTRAIT");
                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
                    break;
                default:
                    Log.d(TAG, "Setting orientation to PORTRAIT");
                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting orientation", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Initialize ViewModel and Repository components
     */
    private void initializeArchitectureComponents() {
        try {
            Log.d(TAG, "Initializing architecture components");

            // Initialize repositories
            prayerRepository = PrayerRepository.getInstance();
            dateTimeRepository = DateTimeRepository.getInstance();

            // Initialize ViewModel
            viewModel = new ViewModelProvider(this).get(MainViewModel.class);

            Log.d(TAG, "Architecture components initialized");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing architecture components", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            throw e; // Re-throw as this is critical
        }
    }

    /**
     * Initialize UI components
     */
    private void initializeUI() {
        try {
            Log.d(TAG, "Initializing UI");

            // Find views
            findViews();

            // Set up theme
            setAppTheme();

            // Initialize other UI components
            initializeUIComponents();

            Log.d(TAG, "UI initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing UI", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Find all views
     */
    private void findViews() {
        try {
            // Main container
            containerLinearLayout = findViewById(R.id.container_LinearLayout_MainActivity);

            // Background and images
            backgroundImageView = findViewById(R.id.background_ImageView_MainActivity);
            containerImageView = findViewById(R.id.container_ImageView_MainActivity1);
            alrabeeaTimesImageView = findViewById(R.id.alrabeeaTimes_ImageView_MainActivity);

            // Time views
            timeNowLinearLayout = findViewById(R.id.timeNow_LinearLayout_MainActivity);
            timeNowTextView = findViewById(R.id.timeNow_TextView_MainActivity);
            timeNowTypeTextView = findViewById(R.id.timeNowType_TextView_MainActivity);

            // Date views
            dateNowTextView = findViewById(R.id.dateNow_TextView_MainActivity);
            dateNowTextView2 = findViewById(R.id.dateNow2_TextView_MainActivity);
            dateHTextView = findViewById(R.id.dateNow1_TextView_MainActivity);
            datehm = findViewById(R.id.datehm);
            datehy = findViewById(R.id.datehy);
            datey = findViewById(R.id.datey);
            datem = findViewById(R.id.datem);
            dayText = findViewById(R.id.day_text);

            // Month and day images
            gregorian_month_container = findViewById(R.id.gregorian_month_container);
            hijri_month_container = findViewById(R.id.hijri_month_container);
            gregorian_month_image = findViewById(R.id.gregorian_month_image);
            hijri_month_image = findViewById(R.id.hijri_month_image);
            dayimage = findViewById(R.id.dayimage);

            // Prayer time views
            findPrayerTimeViews();

            // Athkar views
            findAthkarViews();

            // Announcement views
            findAnnouncementViews();

            // Ikama time views
            findIkamaTimeViews();

        } catch (Exception e) {
            Log.e(TAG, "Error finding views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Find prayer time related views
     */
    private void findPrayerTimeViews() {
        try {
            // Prayer container
            prayerTimeContainer = findViewById(R.id.prayerTime_container);
            remainingPrayerTextView = findViewById(R.id.remainingPrayer_TextView_MainActivity);

            // Fajr views
            contentFajrLayout = findViewById(R.id.contentFajr_LinearLayout_MainActivity);
            fajrTextView = findViewById(R.id.fajr_TextView_MainActivity);
            fajrTimeTextView = findViewById(R.id.fajrTime_TextView_MainActivity);
            remainingFajrTextView = findViewById(R.id.remainingFajr_TextView_MainActivity);
            fajrTimeTypeTextView = findViewById(R.id.fajrTimeType_TextView_MainActivity);
            fajrTimeLinearLayout = findViewById(R.id.fajrTime_LinearLayout_MainActivity);
            fajrATimeLinearLayout = findViewById(R.id.fajrATime_LinearLayout_MainActivity);
            imagefajrView = findViewById(R.id.imageFajr_View_MainActivity);

            // Sunrise views
            contentSunriseLayout = findViewById(R.id.contentSunrise_LinearLayout_MainActivity);
            sunriseTextView = findViewById(R.id.sunrise_TextView_MainActivity);
            sunriseTimeTextView = findViewById(R.id.sunriseTime_TextView_MainActivity);
            remainingSunriseTextView = findViewById(R.id.remainingSunrise_TextView_MainActivity);
            sunriseTimeTypeTextView = findViewById(R.id.sunriseTimeType_TextView_MainActivity);
            sunriseTimeLinearLayout = findViewById(R.id.sunriseTime_LinearLayout_MainActivity);
            sunriseATimeLinearLayout = findViewById(R.id.sunriseATime_LinearLayout_MainActivity);
            imageSunriseView = findViewById(R.id.imageSunrise_View_MainActivity);

            // Dhuhr views
            contentDhuhrLayout = findViewById(R.id.contentDhuhr_LinearLayout_MainActivity);
            dhuhrTextView = findViewById(R.id.dhuhr_TextView_MainActivity);
            dhuhrTimeTextView = findViewById(R.id.dhuhrTime_TextView_MainActivity);
            remainingDhuhrTextView = findViewById(R.id.remainingDhuhr_TextView_MainActivity);
            dhuhrTimeTypeTextView = findViewById(R.id.dhuhrTimeType_TextView_MainActivity);
            dhuhrTimeLinearLayout = findViewById(R.id.dhuhrTime_LinearLayout_MainActivity);
            dhuhrATimeLinearLayout = findViewById(R.id.dhuhrATime_LinearLayout_MainActivity);
            imagedhuhrView = findViewById(R.id.imageDhuhr_View_MainActivity);

            // Asr views
            contentAsrLayout = findViewById(R.id.contentAsr_LinearLayout_MainActivity);
            asrTextView = findViewById(R.id.asr_TextView_MainActivity);
            asrTimeTextView = findViewById(R.id.asrTime_TextView_MainActivity);
            remainingAsrTextView = findViewById(R.id.remainingAsr_TextView_MainActivity);
            asrTimeTypeTextView = findViewById(R.id.asrTimeType_TextView_MainActivity);
            asrTimeLinearLayout = findViewById(R.id.asrTime_LinearLayout_MainActivity);
            asrATimeLinearLayout = findViewById(R.id.asrATime_LinearLayout_MainActivity);
            imageasrView = findViewById(R.id.imageAsr_View_MainActivity);

            // Maghrib views
            contentMaghribLayout = findViewById(R.id.contentMaghrib_LinearLayout_MainActivity);
            maghribTextView = findViewById(R.id.maghrib_TextView_MainActivity);
            maghribTimeTextView = findViewById(R.id.maghribTime_TextView_MainActivity);
            remainingMaghribTextView = findViewById(R.id.remainingMaghrib_TextView_MainActivity);
            maghribTimeTypeTextView = findViewById(R.id.maghribTimeType_TextView_MainActivity);
            maghribTimeLinearLayout = findViewById(R.id.maghribTime_LinearLayout_MainActivity);
            maghribATimeLinearLayout = findViewById(R.id.maghribATime_LinearLayout_MainActivity);
            imagemaghribView = findViewById(R.id.imageMaghrib_View_MainActivity);

            // Isha views
            contentIshaLayout = findViewById(R.id.contentIsha_LinearLayout_MainActivity);
            ishaTextView = findViewById(R.id.isha_TextView_MainActivity);
            ishaTimeTextView = findViewById(R.id.ishaTime_TextView_MainActivity);
            remainingIshaTextView = findViewById(R.id.remainingIsha_TextView_MainActivity);
            ishaTimeTypeTextView = findViewById(R.id.ishaTimeType_TextView_MainActivity);
            ishaTimeLinearLayout = findViewById(R.id.ishaTime_LinearLayout_MainActivity);
            ishaATimeLinearLayout = findViewById(R.id.ishaATime_LinearLayout_MainActivity);
            imageishaView = findViewById(R.id.imageIsha_View_MainActivity);

        } catch (Exception e) {
            Log.e(TAG, "Error finding prayer time views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Find Athkar related views
     */
    private void findAthkarViews() {
        try {
            athkarContainer = findViewById(R.id.athkar_container);
            athkarTextView = findViewById(R.id.athkar_TextView);
            athkarTimeTextView = findViewById(R.id.athkarTime_TextView);
            athkarImageView = findViewById(R.id.athkar_ImageView);
            athkarIcon = findViewById(R.id.athkar_icon);
            athkarTime = findViewById(R.id.athkarTime);
        } catch (Exception e) {
            Log.e(TAG, "Error finding Athkar views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Find announcement related views
     */
    private void findAnnouncementViews() {
        try {
            announcementContainer = findViewById(R.id.announcement_container);
            movingMessageTextView = findViewById(R.id.movingMessage_TextView);
        } catch (Exception e) {
            Log.e(TAG, "Error finding announcement views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Find Ikama time views
     */
    private void findIkamaTimeViews() {
        try {
            tvIkamaDelayFajr = findViewById(R.id.tv_prayer_ikama_time_fajr);
            tvIkamaDelayDhur = findViewById(R.id.tv_prayer_ikama_time_dhur);
            tvIkamaDelayAsr = findViewById(R.id.tv_prayer_ikama_time_asr);
            tvIkamaDelayMaghreb = findViewById(R.id.tv_prayer_ikama_time_maghrib);
            tvIkamaDelayIsha = findViewById(R.id.tv_prayer_ikama_time_isha);
        } catch (Exception e) {
            Log.e(TAG, "Error finding Ikama time views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Initialize UI components
     */
    private void initializeUIComponents() {
        try {
            // Initialize loading dialog
            loadingDialog = Utils.initLoadingDialog(this);

            // Set up layout direction
            setLayoutDirection();

            // Initialize custom theme
            loadCustomTheme();

            // Set up text selection
            setupTextSelection();

        } catch (Exception e) {
            Log.e(TAG, "Error initializing UI components", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set up observers for ViewModel LiveData
     */
    private void setupObservers() {
        try {
            Log.d(TAG, "Setting up observers");

            // Time observers
            setupTimeObservers();

            // Date observers
            setupDateObservers();

            // Prayer time observers
            setupPrayerTimeObservers();

            // Athkar observers
            setupAthkarObservers();

            // Event observers
            setupEventObservers();

            // State observers
            setupStateObservers();

            Log.d(TAG, "Observers set up successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up observers", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set up time-related observers
     */
    private void setupTimeObservers() {
        // Current time observer
        viewModel.getCurrentTime().observe(this, time -> {
            if (time != null) {
                safeSetText(timeNowTextView, time);
            }
        });

        // Current time type observer
        viewModel.getCurrentTimeType().observe(this, timeType -> {
            if (timeType != null) {
                safeSetText(timeNowTypeTextView, timeType);
            }
        });
    }

    /**
     * Set up date-related observers
     */
    private void setupDateObservers() {
        // Current date observer
        viewModel.getCurrentDate().observe(this, date -> {
            if (date != null) {
                safeSetText(dateNowTextView, date);
            }
        });

        // Hijri date observer
        viewModel.getHijriDate().observe(this, hijriDate -> {
            if (hijriDate != null) {
                safeSetText(dateHTextView, hijriDate);
            }
        });

        // Gregorian date observer
        viewModel.getGregorianDate().observe(this, gregorianDate -> {
            if (gregorianDate != null) {
                safeSetText(dateNowTextView2, gregorianDate);
            }
        });
    }

    /**
     * Set up prayer time observers
     */
    private void setupPrayerTimeObservers() {
        // Prayer times observer
        viewModel.getPrayerTimes().observe(this, this::updatePrayerTimesDisplay);

        // Next prayer observer
        viewModel.getNextPrayer().observe(this, this::updateNextPrayerDisplay);

        // Remaining time observer
        viewModel.getRemainingTime().observe(this, remainingTime -> {
            if (remainingTime != null) {
                safeSetText(remainingPrayerTextView, remainingTime);
            }
        });
    }

    /**
     * Set up Athkar observers
     */
    private void setupAthkarObservers() {
        // Current Athkar observer
        viewModel.getCurrentAthkar().observe(this, athkar -> {
            if (athkar != null && athkarTextView != null) {
                safeSetText(athkarTextView, athkar);
            }
        });
    }

    /**
     * Set up event observers
     */
    private void setupEventObservers() {
        // Active events observer
        viewModel.getActiveEvents().observe(this, this::updateEventsDisplay);
    }

    /**
     * Set up state observers
     */
    private void setupStateObservers() {
        // Loading state observer
        viewModel.getIsLoading().observe(this, isLoading -> {
            if (isLoading != null) {
                if (isLoading) {
                    showLoadingDialog();
                } else {
                    hideLoadingDialog();
                }
            }
        });

        // Error observer
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !TextUtils.isEmpty(error)) {
                showError(error);
            }
        });

        // Theme observer
        viewModel.getCurrentTheme().observe(this, theme -> {
            if (theme != null && theme != uiTheme) {
                uiTheme = theme;
                updateThemeDisplay();
            }
        });
    }

    /**
     * Initialize other components
     */
    private void initializeOtherComponents() {
        try {
            // Initialize announcement manager
            announcementManager = new AnnouncementManager(MainActivityMVVM.this);

            // Initialize screensaver
            screensaver = new ScreensaverScheduler(this);

            // Initialize text design
            textDesign = new TextDesignUiManager();
            initTextDesign();

            // Check Duha views
            checkDuhaViews();

            // Set sunrise or Duha names
            setSunriseOrDuhaNamesTextView();

            // Initialize Ikama time views
            initIkamaTimeViews();

            // Set custom settings for themes
            setCustomSettingForThemes();

        } catch (Exception e) {
            Log.e(TAG, "Error initializing other components", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update prayer times display
     */
    private void updatePrayerTimesDisplay(TimingsAlrabeeaTimes prayerTimes) {
        if (prayerTimes == null) {
            return;
        }

        try {
            runOnUiThread(() -> {
                // Update Fajr
                if (prayerTimes.getFajr() != null) {
                    safeSetText(fajrTimeTextView, prayerTimes.getFajr());
                }

                // Update Sunrise
                if (prayerTimes.getSunrise() != null) {
                    safeSetText(sunriseTimeTextView, prayerTimes.getSunrise());
                }

                // Update Dhuhr
                if (prayerTimes.getDhuhr() != null) {
                    safeSetText(dhuhrTimeTextView, prayerTimes.getDhuhr());
                }

                // Update Asr
                if (prayerTimes.getAsr() != null) {
                    safeSetText(asrTimeTextView, prayerTimes.getAsr());
                }

                // Update Maghrib
                if (prayerTimes.getMaghrib() != null) {
                    safeSetText(maghribTimeTextView, prayerTimes.getMaghrib());
                }

                // Update Isha
                if (prayerTimes.getIsha() != null) {
                    safeSetText(ishaTimeTextView, prayerTimes.getIsha());
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error updating prayer times display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update next prayer display
     */
    private void updateNextPrayerDisplay(PrayerType nextPrayer) {
        if (nextPrayer == null) {
            return;
        }

        try {
            runOnUiThread(() -> {
                // Reset all prayer backgrounds
                resetPrayerBackgrounds();

                // Highlight next prayer
                highlightNextPrayer(nextPrayer);
            });

        } catch (Exception e) {
            Log.e(TAG, "Error updating next prayer display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Reset all prayer backgrounds
     */
    private void resetPrayerBackgrounds() {
        try {
            setBackgroundToTextViewPray(contentFajrLayout, false);
            setBackgroundToTextViewPray(contentSunriseLayout, false);
            setBackgroundToTextViewPray(contentDhuhrLayout, false);
            setBackgroundToTextViewPray(contentAsrLayout, false);
            setBackgroundToTextViewPray(contentMaghribLayout, false);
            setBackgroundToTextViewPray(contentIshaLayout, false);
        } catch (Exception e) {
            Log.e(TAG, "Error resetting prayer backgrounds", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Highlight next prayer
     */
    private void highlightNextPrayer(PrayerType nextPrayer) {
        try {
            switch (nextPrayer) {
                case Fajr:
                    setBackgroundToTextViewPray(contentFajrLayout, true);
                    break;
                case Dhuhr:
                    setBackgroundToTextViewPray(contentDhuhrLayout, true);
                    break;
                case Asr:
                    setBackgroundToTextViewPray(contentAsrLayout, true);
                    break;
                case Maghrib:
                    setBackgroundToTextViewPray(contentMaghribLayout, true);
                    break;
                case Isha:
                    setBackgroundToTextViewPray(contentIshaLayout, true);
                    break;
                default:
                    // Handle Sunrise or other cases
                    if (contentSunriseLayout != null && contentSunriseLayout.getVisibility() == View.VISIBLE) {
                        setBackgroundToTextViewPray(contentSunriseLayout, true);
                    }
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error highlighting next prayer", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update events display
     */
    private void updateEventsDisplay(List<Event> events) {
        if (events == null || events.isEmpty()) {
            return;
        }

        try {
            StringBuilder eventText = new StringBuilder();
            for (Event event : events) {
                eventText.append("                ").append(event.text).append("                ");
            }
            showEvent(eventText.toString());

        } catch (Exception e) {
            Log.e(TAG, "Error updating events display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Show event message
     */
    private void showEvent(String eventText) {
        try {
            if (movingMessageTextView != null) {
                safeSetText(movingMessageTextView, eventText);
                safeSetVisibility(movingMessageTextView, View.VISIBLE);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing event", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update theme display
     */
    private void updateThemeDisplay() {
        try {
            setAppTheme();
            loadCustomTheme();
            setCustomSettingForThemes();
        } catch (Exception e) {
            Log.e(TAG, "Error updating theme display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Show error message
     */
    private void showError(String error) {
        try {
            runOnUiThread(() -> {
                Toast.makeText(this, error, Toast.LENGTH_LONG).show();
                Log.e(TAG, "Error: " + error);
            });
        } catch (Exception e) {
            Log.e(TAG, "Error showing error message", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Handle critical error
     */
    private void handleCriticalError(String message, Exception e) {
        try {
            Log.e(TAG, message, e);
            CrashlyticsUtils.INSTANCE.logException(e);

            runOnUiThread(() -> {
                Toast.makeText(this, message, Toast.LENGTH_LONG).show();
            });

            // Restart app after critical error
            Handler handler = new Handler(Looper.getMainLooper());
            handler.postDelayed(() -> {
                Intent intent = new Intent(this, MainActivityMVVM.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
                finish();
            }, 3000);

        } catch (Exception ex) {
            Log.e(TAG, "Error handling critical error", ex);
            CrashlyticsUtils.INSTANCE.logException(ex);
        }
    }

    /**
     * Safe set text to TextView
     */
    private void safeSetText(TextView textView, String text) {
        try {
            if (textView != null && text != null) {
                runOnUiThread(() -> textView.setText(text));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting text", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safe set visibility
     */
    private void safeSetVisibility(View view, int visibility) {
        try {
            if (view != null) {
                runOnUiThread(() -> view.setVisibility(visibility));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting visibility", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set layout direction for RTL support
     */
    private void setLayoutDirection() {
        try {
            safeSetLayoutDirection(timeNowLinearLayout);
            safeSetLayoutDirection(fajrTimeLinearLayout);
            safeSetLayoutDirection(sunriseTimeLinearLayout);
            safeSetLayoutDirection(dhuhrTimeLinearLayout);
            safeSetLayoutDirection(asrTimeLinearLayout);
            safeSetLayoutDirection(maghribTimeLinearLayout);
            safeSetLayoutDirection(ishaTimeLinearLayout);
            safeSetLayoutDirection(fajrATimeLinearLayout);
            safeSetLayoutDirection(sunriseATimeLinearLayout);
            safeSetLayoutDirection(dhuhrATimeLinearLayout);
            safeSetLayoutDirection(asrATimeLinearLayout);
            safeSetLayoutDirection(maghribATimeLinearLayout);
            safeSetLayoutDirection(ishaATimeLinearLayout);
        } catch (Exception e) {
            Log.e(TAG, "Error setting layout direction", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safe set layout direction
     */
    private void safeSetLayoutDirection(ViewGroup layout) {
        try {
            if (layout != null) {
                runOnUiThread(() -> {
                    if ("ar".equals(lan)) {
                        layout.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                    } else {
                        layout.setLayoutDirection(View.LAYOUT_DIRECTION_LTR);
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting layout direction for view", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Load custom theme
     */
    private void loadCustomTheme() {
        try {
            String customThemePath = HawkSettings.getCustomThemePath();
            if (!TextUtils.isEmpty(customThemePath)) {
                File themeFile = new File(customThemePath);
                if (themeFile.exists() && backgroundImageView != null) {
                    int w = getResources().getDisplayMetrics().widthPixels;
                    int h = getResources().getDisplayMetrics().heightPixels;

                    Picasso.get().load(themeFile).resize(w, h).into(backgroundImageView, new Callback() {
                        @Override
                        public void onSuccess() {
                            Log.d(TAG, "Custom theme loaded successfully");
                        }

                        @Override
                        public void onError(Exception e) {
                            Log.e(TAG, "Error loading custom theme", e);
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    });
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading custom theme", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set up text selection for scrolling text
     */
    private void setupTextSelection() {
        try {
            setSelected(movingMessageTextView);
            setSelected(dateNowTextView);
            setSelected(remainingPrayerTextView);
            setSelected(datehm);
            setSelected(remainingFajrTextView);

            if (remainingSunriseTextView != null && remainingSunriseTextView.getVisibility() == View.VISIBLE) {
                setSelected(remainingSunriseTextView);
            }

            setSelected(remainingDhuhrTextView);
            setSelected(remainingAsrTextView);
            setSelected(remainingMaghribTextView);
            setSelected(remainingIshaTextView);
            setSelected(datem);

        } catch (Exception e) {
            Log.e(TAG, "Error setting up text selection", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safe set selected for TextView
     */
    public void setSelected(TextView textView) {
        try {
            if (textView != null) {
                runOnUiThread(() -> textView.setSelected(true));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting selected", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set app theme
     */
    private void setAppTheme() {
        try {
            if (uiTheme != null) {
                // Apply theme-specific settings
                switch (uiTheme) {
                    case BROWN_NEW_3:
                        // Brown theme specific settings
                        break;
                    case BLUE_NEW:
                        // Blue theme specific settings
                        break;
                    default:
                        // Default theme settings
                        break;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting app theme", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set background to prayer view
     */
    private void setBackgroundToTextViewPray(ViewGroup layoutPray, boolean isNextPray) {
        try {
            if (layoutPray == null) {
                return;
            }

            runOnUiThread(() -> {
                if (isNextPray) {
                    // Highlight next prayer
                    layoutPray.setBackgroundColor(ContextCompat.getColor(this, R.color.next_prayer_highlight));
                } else {
                    // Normal prayer background
                    layoutPray.setBackgroundColor(ContextCompat.getColor(this, android.R.color.transparent));
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error setting background to prayer view", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Check Duha views visibility
     */
    private void checkDuhaViews() {
        try {
            if (HawkSettings.getShowDuhaSetting() == 2) {
                safeSetVisibility(sunriseTimeTextView, View.GONE);
                safeSetVisibility(sunriseTimeTypeTextView, View.GONE);
                safeSetVisibility(sunriseTextView, View.GONE);
                safeSetVisibility(contentSunriseLayout, View.GONE);
                safeSetVisibility(remainingSunriseTextView, View.GONE);
                safeSetVisibility(sunriseTimeLinearLayout, View.GONE);
                safeSetVisibility(sunriseATimeLinearLayout, View.GONE);

                View tvSunrise = findViewById(R.id.sunrise_TextView_MainActivity_en);
                if (tvSunrise != null) {
                    safeSetVisibility(tvSunrise, View.GONE);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking Duha views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set sunrise or Duha names
     */
    private void setSunriseOrDuhaNamesTextView() {
        try {
            // Implementation for setting sunrise/duha names based on settings
            // This should be adapted from your existing logic
        } catch (Exception e) {
            Log.e(TAG, "Error setting sunrise/duha names", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Initialize Ikama time views
     */
    private void initIkamaTimeViews() {
        try {
            if (!HawkSettings.getEnableShowIkamaDelay()) {
                return;
            }

            // Implementation for Ikama time views
            // This should be adapted from your existing logic

        } catch (Exception e) {
            Log.e(TAG, "Error initializing Ikama time views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Initialize text design
     */
    private void initTextDesign() {
        try {
            AbsoluteLayout abs = findViewById(R.id.abs_text_design);

            if (textDesign != null && !textDesign.isEnabled(this)) {
                if (abs != null) {
                    abs.setVisibility(View.GONE);
                }
                return;
            }

            // Implementation for text design initialization
            // This should be adapted from your existing logic

        } catch (Exception e) {
            Log.e(TAG, "Error initializing text design", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Set custom settings for themes
     */
    private void setCustomSettingForThemes() {
        try {
            // Implementation for theme-specific custom settings
            // This should be adapted from your existing logic
        } catch (Exception e) {
            Log.e(TAG, "Error setting custom settings for themes", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Show loading dialog
     */
    public void showLoadingDialog() {
        try {
            if (loadingDialog != null && !loadingDialog.isShowing()) {
                runOnUiThread(() -> loadingDialog.show());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing loading dialog", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Hide loading dialog
     */
    public void hideLoadingDialog() {
        try {
            if (loadingDialog != null && loadingDialog.isShowing()) {
                runOnUiThread(() -> loadingDialog.dismiss());
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding loading dialog", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Setup crash handler
     */
    private void setupCrashHandler() {
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread thread, Throwable throwable) {
                Log.e(TAG, "Uncaught exception", throwable);
                CrashlyticsUtils.INSTANCE.logException(throwable);

                // Restart the app
                Intent intent = new Intent(MainActivityMVVM.this, MainActivityMVVM.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                        | Intent.FLAG_ACTIVITY_CLEAR_TASK
                        | Intent.FLAG_ACTIVITY_NEW_TASK);

                startActivity(intent);

                // Kill the current process
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(10);
            }
        });
    }

    /**
     * Start app monitor service
     */
    private void startAppMonitorService() {
        try {
            // Request notification permission for Android 13+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                requestNotificationPermission();
            }

            Intent serviceIntent = new Intent(this, AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent);
            } else {
                startService(serviceIntent);
            }
            Log.d(TAG, "AppMonitorService started");

        } catch (Exception e) {
            Log.e(TAG, "Error starting app monitor service", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Request notification permission
     */
    private void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (checkSelfPermission(
                    android.Manifest.permission.POST_NOTIFICATIONS) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                        new String[] { android.Manifest.permission.POST_NOTIFICATIONS },
                        100);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        try {
            Log.d(TAG, "onResume");

            // Refresh data
            if (viewModel != null) {
                viewModel.refreshData();
            }

            // Handle news display
            handleNewsDisplay();

        } catch (Exception e) {
            Log.e(TAG, "Error in onResume", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Handle news display
     */
    private void handleNewsDisplay() {
        try {
            News news = HawkSettings.getCurrentNews();
            if (news != null && news.isActive) {
                Calendar start = Utils.getCalendar(news.sDate);
                Calendar end = Utils.getCalendar(news.eDate);
                Calendar now = Calendar.getInstance();

                if (now.after(start) && now.before(end)) {
                    safeSetText(movingMessageTextView, news.text);
                    safeSetVisibility(movingMessageTextView, View.VISIBLE);
                } else {
                    safeSetVisibility(movingMessageTextView, View.GONE);
                }
            } else {
                safeSetVisibility(movingMessageTextView, View.GONE);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling news display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        try {
            Log.d(TAG, "onPause");

            if (announcementManager != null) {
                announcementManager.onStop();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in onPause", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        try {
            Log.d(TAG, "onDestroy started");

            // Clean up resources
            cleanupResources();

            // Ensure service keeps running
            startAppMonitorService();

            Log.d(TAG, "onDestroy completed");

        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Clean up resources
     */
    private void cleanupResources() {
        try {
            // Clean up announcement manager
            if (announcementManager != null) {
                announcementManager.onStop();
                announcementManager = null;
            }

            // Clean up repositories
            if (prayerRepository != null) {
                prayerRepository.cleanup();
                prayerRepository = null;
            }

            if (dateTimeRepository != null) {
                dateTimeRepository.cleanup();
                dateTimeRepository = null;
            }

            // Clean up other components
            screensaver = null;
            textDesign = null;

            // Clean up dialog
            if (loadingDialog != null && loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }
            loadingDialog = null;

            // Clean up alert
            if (alert != null) {
                alert.setVisibility(View.GONE);
                alert = null;
            }

            // Force garbage collection
            System.gc();

            Log.d(TAG, "Resources cleaned up successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up resources", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Implementation for navigation item selection
        return false;
    }

    @Override
    public void onSuccess() {
        // Implementation for adapter callback
    }

    @Override
    public void onFailure(String error) {
        // Implementation for adapter callback
        showError(error);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        // Handle back press - minimize app instead of closing
        moveTaskToBack(true);
    }

    /**
     * Get resources with locale
     */
    public ResourcesLocale getResources(String localeTarget) {
        return new ResourcesLocale(this, localeTarget);
    }

    /**
     * Check if landscape orientation
     */
    public boolean isLandscape() {
        Resources resources = getResources();
        Configuration config = resources.getConfiguration();
        return config.orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    /**
     * Adjust display scale
     */
    public void adjustDisplayScale() {
        try {
            // Implementation for display scale adjustment
            // This should be adapted from your existing logic
        } catch (Exception e) {
            Log.e(TAG, "Error adjusting display scale", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public void onItemClick(int position, String tag) {
        // Implementation for item click
    }
}
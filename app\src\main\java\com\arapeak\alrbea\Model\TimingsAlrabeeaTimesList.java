package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class TimingsAlrabeeaTimesList {


    @Expose
    @SerializedName("timings")
    private List<TimingsAlrabeeaTimes> timingsAlrabeeaTimesList;

    public List<TimingsAlrabeeaTimes> getTimingsAlrabeeaTimesList() {
        return timingsAlrabeeaTimesList == null ? new ArrayList<TimingsAlrabeeaTimes>() : timingsAlrabeeaTimesList;
    }

    public void setTimingsAlrabeeaTimesList(List<TimingsAlrabeeaTimes> timingsAlrabeeaTimesList) {
        this.timingsAlrabeeaTimesList = timingsAlrabeeaTimesList;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clipChildren="true"
    android:clipToPadding="true"
    app:cardBackgroundColor="@android:color/black"
    app:cardCornerRadius="20dp"
    app:cardElevation="3dp"
    tools:layoutDirection="rtl">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#F3F3F3"
        android:clipChildren="true"
        android:clipToPadding="true">

        <Button
            android:id="@+id/settings_Button_PhotoGalleryHolder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#F3F3F3"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/settings"
            android:textColor="#14A4C2"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/photo_ImageView_PhotoGalleryHolder"
            app:layout_constraintWidth_percent="0.495" />

        <Button
            android:id="@+id/delete_Button_PhotoGalleryHolder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="#F3F3F3"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/delete"
            android:textColor="#E61616"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/photo_ImageView_PhotoGalleryHolder"
            app:layout_constraintWidth_percent="0.495" />

        <FrameLayout
            android:id="@+id/progressBar_FrameLayout_PhotoGalleryHolder"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:background="@android:color/darker_gray"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />
        </FrameLayout>

        <TextView
            android:id="@+id/name_TextView_PhotoGalleryHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:maxLength="35"
            android:maxLines="1"
            android:paddingBottom="8dp"
            android:textColor="@android:color/black"
            android:textSize="@dimen/contactUsView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="fwcrewcf" />

        <ImageView
            android:id="@+id/photo_ImageView_PhotoGalleryHolder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"

            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/name_TextView_PhotoGalleryHolder" />

        <View
            android:id="@+id/space_View_PhotoGalleryHolder"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#D4D4D4"
            android:visibility="gone"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/delete_Button_PhotoGalleryHolder"
            app:layout_constraintStart_toEndOf="@id/settings_Button_PhotoGalleryHolder"
            app:layout_constraintTop_toTopOf="@id/delete_Button_PhotoGalleryHolder" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
package com.arapeak.alrbea.deleted;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.ALWAYS_AND_CLOSE_ON_PRAYER_TIME_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.APPEARANCE_ALTERNATELY_KEY;
import static com.arapeak.alrbea.hawk.HawkSettings.isPhotoGalleryManagerEnabledBetweenAzanAndIkama;

import android.content.Context;
import android.widget.ImageView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Model.EventAlrabeeaTimes;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;

import java.util.List;

public class PhotoGalleryManager {
    List<EventAlrabeeaTimes> galleries;
    Context context;
    ImageView imageView;
    private int lastGallary = 0;

    public PhotoGalleryManager(Context context) {
        this.context = context;
        galleries = Utils.getInstanceOfRealm()
                .where(EventAlrabeeaTimes.class)
                .equalTo("type", ConstantsOfApp.EVENT_IMAGE_KEY)
                .findAll();
    }


    public void display() {
        if (!HawkSettings.isPhotoGalleryManagerEnabled() || !isPhotoGalleryManagerEnabledBetweenAzanAndIkama() || galleries.size() <= 0)
            return;
        long endTime = System.currentTimeMillis();
        if (lastGallary >= galleries.size())
            lastGallary = 0;
        EventAlrabeeaTimes gallery = galleries.get(lastGallary);
        int type = getGalleryType(gallery);
        if (type == 0) // alternate show gallery and show prayer times
        {
            endTime += gallery.getAppearanceAlternatelyMilliSecond();
        }
        //todo add the other type of appearance

        while (System.currentTimeMillis() < endTime) {

        }
        lastGallary++;
    }

    public int getGalleryType(EventAlrabeeaTimes eventAlrabeeaTimes) {
        return eventAlrabeeaTimes.getTimesNumberOfAppear().equals(APPEARANCE_ALTERNATELY_KEY) ? 0
                : eventAlrabeeaTimes.getTimesNumberOfAppear().equals(ALWAYS_AND_CLOSE_ON_PRAYER_TIME_KEY) ? 1
                : 2;
    }
}

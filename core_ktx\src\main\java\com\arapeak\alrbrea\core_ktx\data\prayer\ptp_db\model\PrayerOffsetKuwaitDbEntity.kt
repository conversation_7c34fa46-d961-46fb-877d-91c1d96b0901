package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "kuwait_offsets")
data class PrayerOffsetKuwaitDbEntity(
    @PrimaryKey val id: Int,
    @ColumnInfo(name = "city") val city: String?,
    @ColumnInfo(name = "latitude") val lat: Double?,
    @ColumnInfo(name = "longitude") val lon: Double?,
    @ColumnInfo(name = "offset_min") val offsetMinutes: Int?
)
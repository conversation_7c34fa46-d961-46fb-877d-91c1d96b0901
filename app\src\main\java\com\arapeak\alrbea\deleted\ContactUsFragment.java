package com.arapeak.alrbea.deleted;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.NonNull;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Model.ResponseErrors;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.Utils;


public class ContactUsFragment extends AlrabeeaTimesFragment {

    private static final String TAG = "ContactUsFragment";

    private View contactUsView;
    private EditText fullNameEditText, emailEditText, mobileNumberEditText, subjectEditText, bodyOfMessageEditText;
    private Button sendButton;
    private Dialog loadingDialog;

    public ContactUsFragment() {
        // Required empty public constructor
    }

    public static ContactUsFragment newInstance() {
        return new ContactUsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        contactUsView = inflater.inflate(R.layout.fragment_contact_us, container, false);

        initView();
        SetParameter();
        SetAction();

        return contactUsView;
    }

    private void initView() {
        fullNameEditText = contactUsView.findViewById(R.id.fullName_EditText_ContactUsFragment);
        emailEditText = contactUsView.findViewById(R.id.email_EditText_ContactUsFragment);
        mobileNumberEditText = contactUsView.findViewById(R.id.mobileNumber_EditText_ContactUsFragment);
        subjectEditText = contactUsView.findViewById(R.id.subject_EditText_ContactUsFragment);
        bodyOfMessageEditText = contactUsView.findViewById(R.id.bodyOfMessage_EditText_ContactUsFragment);
        sendButton = contactUsView.findViewById(R.id.send_Button_ContactUsFragment);

        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());
    }

    private void SetParameter() {
        /*if (Utils.isLandscape(getAppCompatActivity())){
            SettingsLandscapeActivity.getToolbar().setTitle(R.string.contact_us);
        }else {
            SettingsActivity.getToolbar().setTitle(R.string.contact_us);
        }
        */
    }

    private void SetAction() {
        sendButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isValid()) {
                    loadingDialog.show();
                    AlrabeeaTimesRequests.contactUs(getAppCompatActivity()
                            , ConstantsOfApp.CONTACT_US
                            , fullNameEditText.getText().toString()
                            , emailEditText.getText().toString()
                            , mobileNumberEditText.getText().toString()
                            , subjectEditText.getText().toString()
                            , bodyOfMessageEditText.getText().toString()
                            , new OnCompleteListener<String, Object>() {
                                @Override
                                public void onSuccess(String message) {
                                    loadingDialog.dismiss();
                                    Utils.showSuccessAlert(getAppCompatActivity()
                                            , message);

                                    clearAllView();
                                }

                                @Override
                                public void onFail(Object object) {
                                    loadingDialog.dismiss();
                                    if ((object instanceof ResponseErrors.Errors)) {
                                        ResponseErrors responseErrors = (ResponseErrors) object;
                                        if (responseErrors.getErrors().getName().size() > 0) {
                                            fullNameEditText.setError(responseErrors.getErrors().getName().get(0));
                                        }
                                        if (responseErrors.getErrors().getEmail().size() > 0) {
                                            emailEditText.setError(responseErrors.getErrors().getEmail().get(0));
                                        }
                                        if (responseErrors.getErrors().getPhone().size() > 0) {
                                            mobileNumberEditText.setError(responseErrors.getErrors().getPhone().get(0));
                                        }
                                        if (responseErrors.getErrors().getSubject().size() > 0) {
                                            subjectEditText.setError(responseErrors.getErrors().getSubject().get(0));
                                        }
                                        if (responseErrors.getErrors().getText().size() > 0) {
                                            bodyOfMessageEditText.setError(responseErrors.getErrors().getText().get(0));
                                        }
                                    } else if (object instanceof String) {
                                        Utils.showFailAlert(getAppCompatActivity()
                                                , getString(R.string.there_is_a_problem)
                                                , (String) object);
                                    } else {
                                        Utils.showFailAlert(getAppCompatActivity()
                                                , getString(R.string.there_is_a_problem)
                                                , getString(R.string.try_again));
                                    }
                                }
                            });
                }
            }
        });
    }

    private void clearAllView() {
        fullNameEditText.setText("");
        emailEditText.setText("");
        mobileNumberEditText.setText("");
        subjectEditText.setText("");
        bodyOfMessageEditText.setText("");
    }

    private boolean isValid() {
        boolean isValid = true;
        fullNameEditText.setError(null);
        emailEditText.setError(null);
        mobileNumberEditText.setError(null);
        subjectEditText.setError(null);
        bodyOfMessageEditText.setError(null);

        if (fullNameEditText.getText().toString().isEmpty()) {
            fullNameEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        if (emailEditText.getText().toString().isEmpty()) {
            emailEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        } else if (!Utils.isEmailValid(emailEditText.getText().toString())) {
            emailEditText.setError(getString(R.string.the_email_must_be_a_valid_email_address));
            isValid = false;
        }

        if (mobileNumberEditText.getText().toString().isEmpty()) {
            mobileNumberEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        if (subjectEditText.getText().toString().isEmpty()) {
            subjectEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        if (bodyOfMessageEditText.getText().toString().isEmpty()) {
            bodyOfMessageEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        return isValid;
    }
}

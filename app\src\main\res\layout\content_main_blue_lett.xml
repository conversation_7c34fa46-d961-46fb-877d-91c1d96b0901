<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".UI.Activity.MainActivity">

    <ImageView
        android:id="@+id/background_ImageView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/img_background_blue_lett"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/_180sdp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/movingMessage_TextView_MainActivity"
            style="@style/MovingText"
            android:layout_width="@dimen/_250sdp"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="0dp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:textColor="#19547b"
            app:layout_constrainedWidth="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/upload_photo_message" />

        <ImageView
            android:id="@+id/imageView5"
            style="@style/mosqueIcon"
            android:layout_height="@dimen/_50sdp"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="0dp"
            android:src="@android:color/transparent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/movingMessage_TextView_MainActivity" />

        <LinearLayout
            android:id="@+id/timeNow_LinearLayout_MainActivity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layoutDirection="ltr"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView5">

            <androidx.appcompat.widget.AppCompatTextView
                app:autoSizeTextType="uniform"
                android:id="@+id/timeNow_TextView_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:textColor="#476682"
                android:textSize="@dimen/_50sdp"
                android:textStyle="bold"
                tools:text="11:25" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/time_seconds"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus8sdp"
                    android:layout_marginBottom="@dimen/_minus8sdp"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:includeFontPadding="false"
                    android:textColor="@color/colorBlueMain"
                    android:textSize="@dimen/_20sdp"
                    tools:text="13" />

                <TextView
                    android:id="@+id/timeNowType_TextView_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus8sdp"
                    android:layout_marginBottom="@dimen/_minus8sdp"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:includeFontPadding="false"
                    android:textColor="@color/colorBlueMain"
                    android:textSize="@dimen/_18sdp"
                    tools:text="AM" />

            </LinearLayout>
            <!-- <TextView
                 android:id="@+id/timeNowType_TextView_MainActivity"
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content"
                 android:fontFamily="@font/droid_arabic_kufi_bold"
                 android:layout_gravity="bottom"
                 android:includeFontPadding="false"
                 android:textColor="#738da4"
                 android:textSize="@dimen/_18sdp"
                 tools:text="AM" />-->
        </LinearLayout>

        <LinearLayout
            android:id="@+id/DateNow_TextView_MainActivity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layoutDirection="rtl"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/timeNow_LinearLayout_MainActivity">


            <LinearLayout
                android:layout_width="@dimen/_27sdp"
                android:layout_height="@dimen/_27sdp"
                android:layout_marginEnd="@dimen/_3sdp"
                android:background="@drawable/blue_lett_day_bg"
                android:gravity="center">

                <TextView
                    android:id="@+id/dateNow_TextView_MainActivity"
                    android:layout_width="@dimen/_27sdp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/_minus10sdp"
                    android:layout_marginBottom="@dimen/_minus10sdp"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:textColor="#fff"
                    android:textDirection="ltr"
                    android:textSize="@dimen/_20sdp"
                    android:textStyle="bold"
                    tools:text="50" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:orientation="vertical">
                <!--                android:background="@drawable/background_day_blue_let"-->

                <TextView
                    android:id="@+id/datem"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_3sdp"
                    android:background="@drawable/background_mounth_blue_let"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:paddingLeft="@dimen/_10sdp"
                    android:paddingRight="@dimen/_10sdp"
                    android:shadowColor="@color/white"
                    android:shadowRadius="5"
                    android:textColor="#4b6780"
                    android:textSize="@dimen/_14sdp"
                    android:textStyle="bold"
                    tools:text="جمادي الثاني" />

                <TextView
                    android:id="@+id/datey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_day_name_blue_let"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:paddingLeft="@dimen/_10sdp"
                    android:paddingRight="@dimen/_10sdp"
                    android:textColor="#fff"
                    android:textSize="@dimen/_14sdp"
                    android:textStyle="bold"
                    tools:text="1437 هـ" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/prayerTimeItem_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/alrabeeaTimes_ImageView_MainActivity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/container_ImageView_MainActivity1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/prayerTimeItem_include_MainActivity2"

            app:layout_constraintTop_toTopOf="@+id/prayerTimeItem_include_MainActivity"
            tools:alpha="0.5" />

        <include
            android:id="@+id/prayerTimeItem_include_MainActivity2"
            layout="@layout/layout_prayer_times_blue_lett"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/announcement_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_220sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/alrabeeaTimes_ImageView_MainActivity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_progress_remaining"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                android:id="@+id/progressBar"
                android:layout_width="@dimen/circularProgressBar"
                android:layout_height="@dimen/circularProgressBar"
                android:textColor="@color/white"
                app:cpb_background_progressbar_color="#918F8F"
                app:cpb_background_progressbar_width="3dp"
                app:cpb_progressbar_color="@color/white"
                app:cpb_progressbar_width="@dimen/_3sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@id/progressBar"
                app:layout_constraintEnd_toEndOf="@id/progressBar"
                app:layout_constraintStart_toStartOf="@id/progressBar"
                app:layout_constraintTop_toTopOf="@id/progressBar">

                <TextView
                    android:id="@+id/tv_remaining_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="10:00"
                    android:textColor="@color/white"
                    android:textSize="@dimen/remainingTime" />

                <TextView
                    android:id="@+id/tv_remaining_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/prayerName"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="دقائق" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_remainingOnIkama"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/remaining_on_ikama"
            android:textColor="@color/white"
            android:textSize="@dimen/hadithBetweenAdhaanAndIkama"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/what_is_said_between_the_adhaan_and_ikama"
            android:textColor="@color/white"
            android:textSize="@dimen/prayerTime"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"

            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sdp"

            android:autoSizeTextType="uniform"
            android:autoSizeStepGranularity="1sp"
            android:autoSizeMinTextSize="@dimen/_10sdp"
            android:autoSizeMaxTextSize="@dimen/_22sdp"

            tools:text="@string/what_is_said_between_the_adhaan_and_ikama" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_silent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_50sdp"
            android:src="@drawable/bellsilentline"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="@android:color/black" />
    </LinearLayout>

    <ImageView
        android:id="@+id/alrabeeaTimes_ImageView_MainActivity"
        style="@style/AlrabeaIcon"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/logov2"
        android:scaleY="@fraction/logo_scale_small"
        android:scaleX="@fraction/logo_scale_small"
        android:layout_marginBottom="@dimen/_4sdp"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:tint="@android:color/white" />

    <LinearLayout
        android:id="@+id/layout_athkar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_athkar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scaleType="fitXY"
            android:src="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/layout_time_athkar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_4sdp">

            <TextView
                android:id="@+id/tv_athkar_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="Time"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_icon"
                style="@style/AlrabeaIcon"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_26sdp"
                app:tint="@color/colorblack" />

        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
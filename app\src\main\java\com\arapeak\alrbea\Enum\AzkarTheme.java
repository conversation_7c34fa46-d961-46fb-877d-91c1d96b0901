package com.arapeak.alrbea.Enum;

import static com.arapeak.alrbea.AppController.baseContext;

import androidx.annotation.DrawableRes;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.hawk.HawkSettings;

import java.io.File;

public enum AzkarTheme {
    FIRST(R.drawable.y1, R.drawable.y1l),
    SECOND(R.drawable.y2, R.drawable.y2l),
    THIRD(R.drawable.y3, R.drawable.y3l),
    FOURTH(R.drawable.y4, R.drawable.y4l),
    FIFTH(R.drawable.y5, R.drawable.y5l);
    @DrawableRes
    public final int portGif, landGif;

    AzkarTheme(int portGif, int landGif) {
        this.portGif = portGif;
        this.landGif = landGif;
    }

    public boolean isDownloaded() {
        File file = new File(getDir(), "y.gif");
        return file.isFile();
    }

    public File getDir() {
        return new File(baseContext.getFilesDir() + "/athkar/" + ordinal() + "/");
    }

    public boolean isActive() {
        return this == HawkSettings.getCurrentAzkarTheme();
    }
}

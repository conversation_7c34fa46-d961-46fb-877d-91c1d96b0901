package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_alarm.lib;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: classes.dex */
//public final class d {
//
//    /* renamed from: c, reason: collision with root package name */
//    public static  d f15289c;
//
//    /* renamed from: v, reason: collision with root package name */
//    public static  d f15290v;
//
//    /* renamed from: w, reason: collision with root package name */
//    public static  /* synthetic */ d[] f15291w;
//
//    /* JADX WARN: Multi-variable type inference failed */
//    /* JADX WARN: Type inference failed for: r0v0, types: [t5.d, java.lang.Enum] */
//    /* JADX WARN: Type inference failed for: r1v1, types: [t5.d, java.lang.Enum] */
//    static {
//        d r02 = new Enum("SHAFI", 0);
//        f15289c = r02;
//        d r12 = new Enum("HANAFI", 1);
//        f15290v = r12;
//        f15291w = new d[]{r02, r12};
//    }
//
//    public static d valueOf(String str) {
//        return (d) Enum.valueOf(d.class, str);
//    }
//
//    public static d[] values() {
//        return (d[]) f15291w.clone();
//    }
//}

enum d {
    SHAFI,
    HANAFI

}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="@dimen/_5sdp"
    android:paddingTop="@dimen/_5sdp"
    android:paddingEnd="@dimen/_5sdp"
    android:paddingBottom="@dimen/_5sdp"
    tools:layoutDirection="rtl">

    <TextView
        android:id="@+id/title_TextView_OptionChooseViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="start|center_vertical"
        android:includeFontPadding="false"
        android:padding="4dp"
        android:text="@string/define_location_for_one_time"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_15sdp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/description_TextView_OptionChooseViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="start|center_vertical"
        android:includeFontPadding="false"
        android:padding="@dimen/_5sdp"
        android:text="@string/app_need_your_permission_to_get_location"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_13sdp" />

    <CheckBox
        android:id="@+id/option_CheckBox_OptionChooseViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:button="@drawable/check_box_blue_selector"
        android:checked="true"
        android:fontFamily="@font/droid_arabic_kufi"
        android:paddingStart="@dimen/_5sdp"
        android:paddingEnd="@dimen/_5sdp"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_13sdp"
        android:visibility="gone"
        tools:text="التوقيت الهجري الحالي" />

    <androidx.appcompat.widget.AppCompatRadioButton
        android:id="@+id/option_AppCompatRadioButton_OptionChooseViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/droid_arabic_kufi"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_13sdp"
        android:visibility="gone"
        app:buttonTint="@color/colorPrimary"
        tools:text="عربي" />

</LinearLayout>
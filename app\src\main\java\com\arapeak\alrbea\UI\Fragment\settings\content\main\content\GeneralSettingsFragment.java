package com.arapeak.alrbea.UI.Fragment.settings.content.main.content;

import static android.app.Activity.RESULT_OK;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.FILE_INFO;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.FILE_INFO_PRAY;

import android.Manifest;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.ImageFilePath;
import com.arapeak.alrbea.Model.PrayFileInfo;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimesList;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.google.android.gms.common.util.IOUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.orhanobut.hawk.Hawk;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

import pub.devrel.easypermissions.EasyPermissions;

public class GeneralSettingsFragment extends AlrabeeaTimesFragment implements SettingsAdapterCallback {
    public final static int RESULT_LOAD_FILE_ON_ACTIVITY_RESULT = 210;
    private static final String TAG = "GeneralSettingsFragment";
    private static final int PERMISSION_CODE = 103;

    private View generalSettingsView;
    private RecyclerView settingItemRecyclerView;

    //    private SettingsAdapter settingsAdapter;
    private MainSettingsAdapter mainSettingsAdapter;
    private Dialog loadingDialog;
    private Uri fileUri;

    public GeneralSettingsFragment() {

    }

    public static GeneralSettingsFragment newInstance() {
        return new GeneralSettingsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        generalSettingsView = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return generalSettingsView;
    }

    private void initView() {
        settingItemRecyclerView = generalSettingsView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(getString(R.string.general_settings_title));
//        } else {
//            SettingsActivity.setTextTite(getString(R.string.general_settings_title));
//        }
        SettingsActivity.setTextTite(getString(R.string.general_settings_title));
        settingItemRecyclerView.setAdapter(mainSettingsAdapter);

        setupGeneralSettings();
    }

    private void SetAction() {


    }

    private void setupGeneralSettings() {
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.general_settings);

        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(generalSettingsTitleArray[0]
                , ViewsAlrabeeaTimes.TEXT_VIEW
                , "" /*generalSettingsTitleArray[0]*/
                , true);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(generalSettingsTitleArray[1]
                , ViewsAlrabeeaTimes.TEXT_VIEW
                , "" /*generalSettingsTitleArray[1]*/
                , true);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(generalSettingsTitleArray[2]
                , ViewsAlrabeeaTimes.TEXT_VIEW
                , ""/*generalSettingsTitleArray[2]*/
                , true);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);
//
        // mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.download_excel_template)
        //      , ViewsAlrabeeaTimes.BUTTON, getString(R.string.download)));

        //   mainSettingsAdapter.add(new SubSettingAlrabeeaTimes(getString(R.string.upload_a_custom_file_for_prayer_settings)
        //         , ViewsAlrabeeaTimes.BUTTON, getString(R.string.download)));
    }

    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        switch (position) {
            case 0:
                try {
                    Intent intent = new Intent(Settings.ACTION_SETTINGS);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    if (intent.resolveActivity(requireContext().getPackageManager()) != null) {
                        startActivityForResult(intent, 0);
                    } else {
                        Toast.makeText(requireContext(), "الإعدادات غير مدعومة على هذا الجهاز", Toast.LENGTH_SHORT).show();
                    }
                } catch (ActivityNotFoundException e) {
                    CrashlyticsUtils.INSTANCE.logException(e);
                    Toast.makeText(requireContext(), "لا يمكن فتح الإعدادات. يرجى المحاولة مرة أخرى", Toast.LENGTH_SHORT).show();
                }
                break;
            case 1:
                try {
                    Intent dateIntent = new Intent(android.provider.Settings.ACTION_DATE_SETTINGS);
                    dateIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    if (dateIntent.resolveActivity(requireContext().getPackageManager()) != null) {
                        startActivityForResult(dateIntent, 0);
                    } else {
                        Toast.makeText(requireContext(), "إعدادات التاريخ غير مدعومة على هذا الجهاز", Toast.LENGTH_SHORT).show();
                    }
                } catch (ActivityNotFoundException e) {
                    CrashlyticsUtils.INSTANCE.logException(e);
                    Toast.makeText(requireContext(), "لا يمكن فتح إعدادات التاريخ. يرجى المحاولة مرة أخرى", Toast.LENGTH_SHORT).show();
                }
                break;
            case 2:
                try {
                    Intent wifiIntent = new Intent(android.provider.Settings.ACTION_WIFI_SETTINGS);
                    wifiIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    if (wifiIntent.resolveActivity(requireContext().getPackageManager()) != null) {
                        startActivityForResult(wifiIntent, 0);
                    } else {
                        Toast.makeText(requireContext(), "إعدادات الواي فاي غير مدعومة على هذا الجهاز", Toast.LENGTH_SHORT).show();
                    }
                } catch (ActivityNotFoundException e) {
                    CrashlyticsUtils.INSTANCE.logException(e);
                    Toast.makeText(requireContext(), "لا يمكن فتح إعدادات الواي فاي. يرجى المحاولة مرة أخرى", Toast.LENGTH_SHORT).show();
                }
                break;
            case 3:
                getPrayFileInfoAndGetPermissions();
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && data.getData() != null) {
            if (requestCode == RESULT_LOAD_FILE_ON_ACTIVITY_RESULT) {
                if (resultCode == RESULT_OK) {
                    Log.e(TAG, "data.getData().toString: " + data.getData().toString());
                    if (Utils.getValueWithoutNull(data.getData().toString()).toLowerCase().endsWith("txt")
                            || Utils.getValueWithoutNull(data.getData().toString()).toLowerCase().endsWith("text")) {
                        sendFile(data);
                    } else {
                        String path;
                        if (Utils.isNougatOrHigher()) {
                            path = getRealPathFromURI(getAppCompatActivity(), Uri.parse(fileUri.getPath()));
                        } else {
                            path = getRealPathFromURI(getAppCompatActivity(), fileUri);
                        }
                        Log.e(TAG, "path: " + path);
                        sendFile(path);
                    }
                }
            }
        }
    }

    private void goToFileStorage(boolean isView) {
        String[] perms = {Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
        if (EasyPermissions.hasPermissions(getAppCompatActivity(), perms)) {
            if (isView) {
                openExcel();
            } else {
//                getFile();
                getFile();
            }
        } else {
            EasyPermissions
                    .requestPermissions(getAppCompatActivity()
                            , getString(R.string.get_pic_msg), PERMISSION_CODE, perms);
        }
    }

    private void openExcel() {
        try {
            loadingDialog.show();
            InputStream mInputStream = getAppCompatActivity().getAssets().open("files/preyer_time_template.xlsx");
            File mFile = Utils.getOutputFiles(getAppCompatActivity(), "preyer_time_template.xlsx");
            OutputStream mOutputStream = new FileOutputStream(mFile);
            IOUtils.copyStream(mInputStream, mOutputStream);
            if (!mFile.exists()) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }
            Utils.openExcel(getAppCompatActivity(), mFile);
            loadingDialog.dismiss();
        } catch (IOException e) {
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.try_again));
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            loadingDialog.dismiss();
        }
    }

    private void getFile() {
        Intent filePickerIntent = new Intent(Intent.ACTION_GET_CONTENT);
        if (Utils.isNougatOrHigher()) {
            File imagePath = new File(getAppCompatActivity().getFilesDir(), "files");
            File newFile = new File(imagePath, "file_" + Utils.getDateTimeNowWithFileName() + ".txt");
            fileUri = FileProvider.getUriForFile(getAppCompatActivity(), getContext().getPackageName() + ".fileprovider", newFile);

//            fileUri = FileProvider.getUriForFile(getAppCompatActivity()
//                    , /*getAppCompatActivity().getPackageName()*/BuildConfig.APPLICATION_ID + ".fileprovider"
//                    , Utils.getOutputMediaFile(getAppCompatActivity(), null));
            filePickerIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            filePickerIntent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
        } else {
            fileUri = Uri.fromFile(Utils.getOutputMediaFile(getAppCompatActivity(), null));
        }
        filePickerIntent.putExtra(MediaStore.EXTRA_OUTPUT, fileUri);
//        filePickerIntent.setType("*/*");
        filePickerIntent.setType("text/plain");
        startActivityForResult(filePickerIntent, RESULT_LOAD_FILE_ON_ACTIVITY_RESULT);
    }

//    public void getFile() {
//        Intent filePickerIntent = new Intent(Intent.ACTION_GET_CONTENT);
//        filePickerIntent.setType("*/*");
//        startActivityForResult(filePickerIntent, RESULT_LOAD_FILE_ON_ACTIVITY_RESULT);
//    }

    /**
     * @noinspection t
     */
    private void sendFile(Intent data) {
        try {
            loadingDialog.show();
            if (data == null || data.getData() == null) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }
            File myFile = new File(data.getData().toString());
            if (myFile == null) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }
            String filePath, extension;
            boolean isText;
            if (myFile.getPath().contains("storage")) {
                filePath = myFile.getPath()
                        .substring(myFile.getPath().indexOf("storage")).replace("%2F", "/");
                Log.e(TAG, "filePath: " + filePath);
                extension = filePath.substring(filePath.lastIndexOf(".")).replace(".", "");
                if (Utils.getValueWithoutNull(extension).toLowerCase().endsWith("json")) {
                    isText = false;
                } else if (Utils.getValueWithoutNull(extension).toLowerCase().endsWith("txt")
                        || Utils.getValueWithoutNull(extension).toLowerCase().endsWith("text")) {
                    isText = true;
                } else {
                    Utils.showFailAlert(getAppCompatActivity()
                            , getString(R.string.there_is_a_problem)
                            , getString(R.string.this_file_extension_is_not_supported));
                    loadingDialog.dismiss();
                    return;
                }

            } else {
                if (Utils.getValueWithoutNull(myFile.getName()).toLowerCase().endsWith("json")) {
                    isText = false;
                } else if (Utils.getValueWithoutNull(myFile.getName()).toLowerCase().endsWith("txt")
                        || !Utils.getValueWithoutNull(myFile.getName()).toLowerCase().endsWith("text")) {
                    isText = true;
                } else {
                    Utils.showFailAlert(getAppCompatActivity()
                            , getString(R.string.there_is_a_problem)
                            , getString(R.string.this_file_extension_is_not_supported));
                    loadingDialog.dismiss();
                    return;
                }
                filePath = ImageFilePath.getPath(getAppCompatActivity(), data.getData());
//                extension = filePath.substring(filePath.lastIndexOf(".")).replace(".", "");
            }
            if (Utils.getValueWithoutNull(filePath).isEmpty()) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }

            String json = isText ? loadTextFromStorage(getAppCompatActivity(), filePath) : loadJSONFromStorage(getAppCompatActivity(), filePath);
//            Log.e(TAG, "1- isText: " + isText + "\njson: " + json);

            if (Utils.getValueWithoutNull(json).isEmpty()) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }

//            if (json.contains("\"Month\": \"\"")) {
//                json = json.substring(0, json.lastIndexOf(","));
//            }

            if (json.contains(",\n" +
                    "\t{\n" +
                    "\t\t\"Month\": \"\"\n" +
                    "\t}")) {

                json = json.trim().replaceAll("\n" +
                        "\t\t\"Month\": \"\"\n", "").trim();

                if (json.trim().endsWith("}")) {
                    json = json.trim().substring(0, json.lastIndexOf("}")).trim();
                }
                if (json.trim().endsWith("{")) {
                    json = json.trim().substring(0, json.lastIndexOf("{")).trim();
                }
                if (json.trim().endsWith(",")) {
                    json = json.trim().substring(0, json.lastIndexOf(",")).trim();
                }
            }

            if (!json.trim().toLowerCase().contains("\"timings\":")) {
//                json = "{\n\"timings\": " + json.trim() + "\n}";
                json = "{\n" + "\t\"timings\": " + json.trim() + "\n}";
                json = json.replaceAll(" \uFEFF", " ");
            }

//            Log.e(TAG, "json: " + json);
            GsonBuilder builder = new GsonBuilder();
            Gson mGson = builder.create();
            TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
                    = mGson.fromJson(json
                            .trim()
//                            .toLowerCase()
                            .replaceAll("aa", "am")
                            .replaceAll("pp", "pm")
                    , TimingsAlrabeeaTimesList.class);

//            Log.e(TAG, "timingsAlrabeeaTimesList: " + timingsAlrabeeaTimesList.getTimingsAlrabeeaTimesList().toString());

//            TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
//                    = mGson.fromJson(json.trim()
//                    , TimingsAlrabeeaTimesList.class);

            Hawk.put(ConstantsOfApp.TIMINGS_ALRABEEA_TIMES_LIST_KEY, timingsAlrabeeaTimesList);
            Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, true);

            AlrabeeaTimesRequests.getPrayerTimesThisYear(ConstantsOfApp.BASE_URL_ALADHAN
                    , ConstantsOfApp.LATITUDE_DEFAULT_KEY
                    , ConstantsOfApp.LONGITUDE_DEFAULT_KEY
                    , ConstantsOfApp.PRAYER_METHOD_DEFAULT_KEY
                    , ConstantsOfApp.ADJUST_HIJRI_DATE_DEFAULT_KEY
                    , Utils.getEnglishDateTime(ConstantsOfApp.YEAR)
                    , true
                    , new OnCompleteListener<PrayerApi, String>() {
                        @Override
                        public void onSuccess(PrayerApi prayerApi) {
                            loadingDialog.dismiss();
                            if (prayerApi != null) {
//                                MainActivity.prayerApi = prayerApi;
                                Hawk.put(Utils.getKeyPrayerApi(), prayerApi);
                                if (prayerApi != null && prayerApi.getPrayerList() != null && prayerApi.getPrayerList().getJanuary().size() > 0) {
                                    Hawk.put(ConstantsOfApp.LAST_UPDATE_DATA_PRAYER_TIME_KEY, System.currentTimeMillis());
                                }
                                Intent intentMainActivity = new Intent(getAppCompatActivity(), MainActivity.class);
                                /*if (Utils.isLandscape(getAppCompatActivity())) {
                                    intentMainActivity = new Intent(getAppCompatActivity(), MainLandscapeActivity.class);
//                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                } else {
                                    intentMainActivity = new Intent(getAppCompatActivity(), MainActivity.class);
//                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                }*/

                                intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivity(intentMainActivity);
                                getAppCompatActivity().finish();

                                Utils.showSuccessAlert(getAppCompatActivity()
                                        , getString(R.string.file_was_downloaded_successfully));
                            }
                        }

                        @Override
                        public void onFail(String object) {
                            if (object instanceof String) {
                                Utils.showFailAlert(getAppCompatActivity()
                                        , getString(R.string.there_is_a_problem)
                                        , object);
                            }
                            loadingDialog.dismiss();
                        }
                    });
//            Log.e(TAG, "timingsAlrabeeaTimesList.toString: " + timingsAlrabeeaTimesList.toString());
        } catch (Exception e) {
            loadingDialog.dismiss();
            CrashlyticsUtils.INSTANCE.logException(e);
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.try_again));
        }
    }

    private void sendFile(String pathTextFile) {
        try {
            Log.e(TAG, "pathTextFile: " + pathTextFile);
            loadingDialog.show();
            if (pathTextFile == null || Utils.getValueWithoutNull(pathTextFile).isEmpty()) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }


            String json = loadTextFromStorage(getAppCompatActivity(), pathTextFile);
            Log.e(TAG, "json: " + json);

            if (Utils.getValueWithoutNull(json).isEmpty()) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                loadingDialog.dismiss();
                return;
            }

//            if (json.contains("\"Month\": \"\"")) {
//                json = json.substring(0, json.lastIndexOf(","));
//            }

            if (json.contains(",\n" +
                    "\t{\n" +
                    "\t\t\"Month\": \"\"\n" +
                    "\t}")) {

                json = json.trim().replaceAll("\n" +
                        "\t\t\"Month\": \"\"\n", "").trim();

                if (json.trim().endsWith("}")) {
                    json = json.trim().substring(0, json.lastIndexOf("}")).trim();
                }
                if (json.trim().endsWith("{")) {
                    json = json.trim().substring(0, json.lastIndexOf("{")).trim();
                }
                if (json.trim().endsWith(",")) {
                    json = json.trim().substring(0, json.lastIndexOf(",")).trim();
                }
            }

            if (!json.trim().toLowerCase().contains("\"timings\":")) {
//                json = "{\n\"timings\": " + json.trim() + "\n}";
                json = "{\n" + "\t\"timings\": " + json.trim() + "\n}";
                json = json.replaceAll(" \uFEFF", " ");
            }

//            Log.e(TAG, "json: " + json);
            GsonBuilder builder = new GsonBuilder();
            Gson mGson = builder.create();
            TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
                    = mGson.fromJson(json
                            .trim()
//                            .toLowerCase()
                            .replaceAll("aa", "am")
                            .replaceAll("pp", "pm")
                    , TimingsAlrabeeaTimesList.class);

            Log.e(TAG, "timingsAlrabeeaTimesList: " + timingsAlrabeeaTimesList.getTimingsAlrabeeaTimesList().toString());

//            TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
//                    = mGson.fromJson(json.trim()
//                    , TimingsAlrabeeaTimesList.class);

            Hawk.put(ConstantsOfApp.TIMINGS_ALRABEEA_TIMES_LIST_KEY, timingsAlrabeeaTimesList);
            Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, true);

            AlrabeeaTimesRequests.getPrayerTimesThisYear(ConstantsOfApp.BASE_URL_ALADHAN
                    , ConstantsOfApp.LATITUDE_DEFAULT_KEY
                    , ConstantsOfApp.LONGITUDE_DEFAULT_KEY
                    , ConstantsOfApp.PRAYER_METHOD_DEFAULT_KEY
                    , ConstantsOfApp.ADJUST_HIJRI_DATE_DEFAULT_KEY
                    , Utils.getEnglishDateTime(ConstantsOfApp.YEAR)
                    , true
                    , new OnCompleteListener<PrayerApi, String>() {
                        @Override
                        public void onSuccess(PrayerApi prayerApi) {
                            loadingDialog.dismiss();
                            if (prayerApi != null) {
//                                MainActivity.prayerApi = prayerApi;
                                Hawk.put(Utils.getKeyPrayerApi(), prayerApi);
                                if (prayerApi != null && prayerApi.getPrayerList() != null && prayerApi.getPrayerList().getJanuary().size() > 0) {
                                    Hawk.put(ConstantsOfApp.LAST_UPDATE_DATA_PRAYER_TIME_KEY, System.currentTimeMillis());
                                }
                                Intent intentMainActivity = new Intent(getAppCompatActivity(), MainActivity.class);
                                /*if (Utils.isLandscape(getAppCompatActivity())) {
                                    intentMainActivity = new Intent(getAppCompatActivity(), MainLandscapeActivity.class);
//                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                } else {
                                    intentMainActivity = new Intent(getAppCompatActivity(), MainActivity.class);
//                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                }*/

                                intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivity(intentMainActivity);
                                getAppCompatActivity().finish();

                                Utils.showSuccessAlert(getAppCompatActivity()
                                        , getString(R.string.file_was_downloaded_successfully));
                            }
                        }

                        @Override
                        public void onFail(String object) {
                            if (object instanceof String) {
                                Utils.showFailAlert(getAppCompatActivity()
                                        , getString(R.string.there_is_a_problem)
                                        , object);
                            }
                            loadingDialog.dismiss();
                        }
                    });
//            Log.e(TAG, "timingsAlrabeeaTimesList.toString: " + timingsAlrabeeaTimesList.toString());
        } catch (Exception e) {
            loadingDialog.dismiss();
            CrashlyticsUtils.INSTANCE.logException(e);
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.try_again));
        }
    }

    public String loadJSONFromStorage(Context mContext, String jsonFilePath) {
        if (mContext == null || Utils.getValueWithoutNull(jsonFilePath).isEmpty()) {
            return "";
        }


        String json;
        try {
            InputStream is = new FileInputStream(new File(jsonFilePath));

            int size = is.available();

            byte[] buffer = new byte[size];

            is.read(buffer);

            is.close();

            json = new String(buffer, StandardCharsets.UTF_8);


        } catch (
                IOException ex) {
            CrashlyticsUtils.INSTANCE.logException(ex);
            return null;
        }
        return json;
    }


    public String loadTextFromStorage(Context mContext, String jsonFilePath) {
        if (mContext == null || Utils.getValueWithoutNull(jsonFilePath).isEmpty()) {
            return "";
        }


        StringBuilder json = new StringBuilder();

        try {
            File mFile = new File(jsonFilePath);
            BufferedReader br = new BufferedReader(new FileReader(mFile));
            String line;

            while ((line = br.readLine()) != null) {
                json.append(line);
                json.append('\n');
            }

            br.close();
        } catch (IOException e) {
            CrashlyticsUtils.INSTANCE.logException(e);

            //You'll need to add proper error handling here
        }
        return json.toString();
    }

    public String getRealPathFromURI(Context mContext, Uri contentURI) {
        String result;
        Cursor cursor = mContext.getContentResolver().query(contentURI, null,
                null, null, null);

        if (cursor == null) { // Source is Dropbox or other similar local file
            // path
            result = contentURI.getPath();
        } else {
            cursor.moveToFirst();
            try {
                int idx = cursor
                        .getColumnIndex(MediaStore.Images.ImageColumns.DATA);
                result = cursor.getString(idx);
            } catch (Exception e) {
                Log.e(TAG, "getRealPathFromURI Error: " + e.getMessage());
                CrashlyticsUtils.INSTANCE.logException(e);
                result = "";
            }
            cursor.close();
        }
        return result;
    }

    private void getPrayFileInfoAndGetPermissions() {
        String[] perms = {Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE};
        if (EasyPermissions.hasPermissions(getAppCompatActivity(), perms)) {
            getPrayFileInfo();
        } else {
            EasyPermissions
                    .requestPermissions(getAppCompatActivity()
                            , getString(R.string.permission_to_allow_access_to_memory_to_store_the_application), PERMISSION_CODE, perms);
        }
    }

    private void getPrayFileInfo() {
        loadingDialog.show();
        AlrabeeaTimesRequests.getPrayFileInfo(getAppCompatActivity()
                , FILE_INFO
                , new OnCompleteListener<PrayFileInfo, Object>() {
                    @Override
                    public void onSuccess(final PrayFileInfo prayFileInfo) {
                        if (prayFileInfo == null) {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , getString(R.string.try_again));
                            loadingDialog.dismiss();
                        } else {
                            if (!prayFileInfo.getFile().isEmpty()) {
                                downloadTextFileByAndroidNetworking(prayFileInfo.getFile());
                            } else {
                                loadingDialog.dismiss();
                            }
                        }
                    }

                    @Override
                    public void onFail(Object object) {
                        if (object == null) {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , getString(R.string.try_again));
                        } else {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , "" + object);
                        }
                        loadingDialog.dismiss();

                    }
                });
    }

    private void downloadTextFileByAndroidNetworking(String nameFile) {
        AlrabeeaTimesRequests.downloadTextFileByAndroidNetworking(getAppCompatActivity()
                , FILE_INFO_PRAY
                , nameFile
                , new OnCompleteListener<String, Object>() {
                    @Override
                    public void onSuccess(String filePath) {
                        if (Utils.getValueWithoutNull(filePath).isEmpty()) {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , getString(R.string.try_again));
                        } else {
                            sendFile(filePath);
                        }
                    }

                    @Override
                    public void onFail(Object object) {
                        if (object == null) {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , getString(R.string.try_again));
                        } else {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , "" + object);
                        }
                        loadingDialog.dismiss();
                    }
                });
    }
}

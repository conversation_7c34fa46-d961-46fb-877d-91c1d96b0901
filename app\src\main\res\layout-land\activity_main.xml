<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/home_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <LinearLayout
            android:id="@+id/container_LinearLayout_MainActivity"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitCenter"
            android:visibility="gone"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="0.5" />

        <AbsoluteLayout
            android:id="@+id/abs_text_design"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <include
                android:id="@+id/tv_text_design1"
                layout="@layout/textdesign_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <include
                android:id="@+id/tv_text_design2"
                layout="@layout/textdesign_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />


        </AbsoluteLayout>

        <!--        <TextView-->
        <!--            android:id="@+id/test"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:elevation="24dp"-->
        <!--            android:scaleX="2"-->
        <!--            android:scaleY="2"-->
        <!--            android:text="Land"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/container_LinearLayout_MainActivity"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="@+id/container_LinearLayout_MainActivity"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/container_LinearLayout_MainActivity" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>
    <!--
        <include
            layout="@layout/content_main_brown"
            android:id="@+id/container_include_MainActivity"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    -->

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/activity_main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>

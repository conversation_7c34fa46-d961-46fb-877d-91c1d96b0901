package com.arapeak.alrbea.UI.Fragment.settings.content.screensaver;


import android.app.TimePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapter;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoElementsEnum;
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoSizeEnum;
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverModeEnum;
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverTimingEnum;
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverToggleEnum;
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigDelay;
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigInterval;
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigPeriod;
import com.arapeak.alrbrea.core_ktx.repo.ScreenSaverRepo;
import com.tapadoo.alerter.Alerter;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import kotlin.Pair;


public class ScreensaverFragment extends AlrabeeaTimesFragment {
    private static final String TAG = "ScreensaverFragment";


    private View subSettingsView;
    private LinearLayout llInfoSettings;
    private LinearLayout llMode;
    private LinearLayout llTiming;
    private ScreenSaverRepo screenSaverRepo;


    public ScreensaverFragment() {
    }

    public static ScreensaverFragment newInstance() {
        return new ScreensaverFragment();
    }


    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        subSettingsView = inflater.inflate(R.layout.settings_screensaver, container, false);

        screenSaverRepo = new ScreenSaverRepo();
        llInfoSettings = subSettingsView.findViewById(R.id.ll_ss_info);
        llMode = subSettingsView.findViewById(R.id.ll_ss_mode);
        llTiming = subSettingsView.findViewById(R.id.ll_ss_timing);

        initToggleView();
        initModeView();
        initInfoViews();
        initTimingViews();


        return subSettingsView;
    }

    private void initToggleView() {
        int spinnerId = R.id.options_Spinner_screensaver;
        List<String> valuesList = screenSaverRepo.getTogglesNamesLocalized(requireContext());
        int selected = screenSaverRepo.getCurrentToggle(requireContext()).ordinal();


        Spinner optionsSpinner = subSettingsView.findViewById(spinnerId);
        List<Object> spinnerList = new ArrayList<>(valuesList);
        SpinnerAdapter<Object> spinnerAdapter = new SpinnerAdapter<>(requireContext(), spinnerList);
        optionsSpinner.setAdapter(spinnerAdapter);
        optionsSpinner.setSelection(selected);
        optionsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                screenSaverRepo.updateToggle(requireContext(), ScreensaverToggleEnum.getEntries().get(subPosition));
                updateToggleViews(ScreensaverToggleEnum.getEntries().get(subPosition));
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

    }

    private void initModeView() {
        int spinnerId = R.id.mode_Spinner_screensaver;
        List<String> valuesList = screenSaverRepo.getModesNamesLocalized(requireContext());
        int selected = screenSaverRepo.getCurrentMode(requireContext()).ordinal();


        Spinner optionsSpinner = subSettingsView.findViewById(spinnerId);
        List<Object> spinnerList = new ArrayList<>(valuesList);
        SpinnerAdapter<Object> spinnerAdapter = new SpinnerAdapter<>(requireContext(), spinnerList);
        optionsSpinner.setAdapter(spinnerAdapter);
        optionsSpinner.setSelection(selected);
        optionsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                screenSaverRepo.updateMode(requireContext(), ScreensaverModeEnum.getEntries().get(subPosition));
                updateModeViews(ScreensaverModeEnum.getEntries().get(subPosition));
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

    }


    private void initInfoViews() {
        Spinner elementsSpinner = subSettingsView.findViewById(R.id.options_Spinner_screensaver_elements);
        List<Object> langList = new ArrayList<>(screenSaverRepo.getElementsNamesLocalized(requireContext()));
        SpinnerAdapter<Object> spinnerAdapter = new SpinnerAdapter<>(requireContext(), langList);
        elementsSpinner.setAdapter(spinnerAdapter);
        elementsSpinner.setSelection(screenSaverRepo.getInfoElements(requireContext()).ordinal());
        elementsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                screenSaverRepo.updateElements(requireContext(), ScreenSaverInfoElementsEnum.getEntries().get(subPosition));
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });


        Spinner sizesSpinner = subSettingsView.findViewById(R.id.options_Spinner_screensaver_sizes);
        List<Object> langList2 = new ArrayList<>(screenSaverRepo.getSizesNamesLocalized(requireContext()));
        SpinnerAdapter<Object> spinnerAdapter2 = new SpinnerAdapter<>(requireContext(), langList2);
        sizesSpinner.setAdapter(spinnerAdapter2);
        sizesSpinner.setSelection(screenSaverRepo.getInfoSizes(requireContext()).ordinal());
        sizesSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                screenSaverRepo.updateSizes(requireContext(), ScreenSaverInfoSizeEnum.getEntries().get(subPosition));
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void initTimingViews() {
        Map<ScreensaverTimingEnum, Boolean> timings = screenSaverRepo.getAllTimings(requireContext());

        subSettingsView.findViewById(R.id.tv_show_plus).setOnClickListener(v -> {
            subSettingsView.findViewById(R.id.ss_ll_period).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.ss_ll_interval).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.ss_timing_period_desc).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.ss_timing_interval_desc).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.space_View_ss2hss).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.space_View_ss2hses).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.space_View_ss5).setVisibility(View.VISIBLE);
            subSettingsView.findViewById(R.id.tv_show_plus).setVisibility(View.GONE);
        });

        subSettingsView.findViewById(R.id.ss_ll_period).setVisibility(View.GONE);
        subSettingsView.findViewById(R.id.ss_ll_interval).setVisibility(View.GONE);
        subSettingsView.findViewById(R.id.ss_timing_period_desc).setVisibility(View.GONE);
        subSettingsView.findViewById(R.id.ss_timing_interval_desc).setVisibility(View.GONE);
        subSettingsView.findViewById(R.id.space_View_ss2hss).setVisibility(View.GONE);
        subSettingsView.findViewById(R.id.space_View_ss2hses).setVisibility(View.GONE);
        subSettingsView.findViewById(R.id.space_View_ss5).setVisibility(View.GONE);


        timings.forEach(
                (key, value) -> {
                    switch (key) {
                        case BetweenPrayers: {
                            ImageView ivSettings = subSettingsView.findViewById(R.id.ss_timing_prayers_settings);
                            CheckBox cb = subSettingsView.findViewById(R.id.ss_timing_prayers);
                            cb.setText(screenSaverRepo.getTimingNamesLocalized(getContext(), key));
                            cb.setChecked(value);
                            cb.setAlpha(getAlphaFromValue(value));
                            ivSettings.setAlpha(getAlphaFromValue(value));
                            ivSettings.setEnabled(value);

                            ivSettings.setOnClickListener(v -> {
                                ScreensaverPrayerDialog dialog = ScreensaverPrayerDialog.newInstance(screenSaverRepo);
                                if (getChildFragmentManager().findFragmentByTag(dialog.TAG) == null || !Objects.requireNonNull(getChildFragmentManager().findFragmentByTag(dialog.TAG)).isVisible()) {
                                    dialog.display(getChildFragmentManager());
                                }
                            });

                            cb.setOnCheckedChangeListener((buttonView, isChecked) -> {
                                screenSaverRepo.updateTiming(getContext(), key, isChecked);
                                cb.setAlpha(getAlphaFromValue(isChecked));
                                ivSettings.setAlpha(getAlphaFromValue(isChecked));
                                ivSettings.setEnabled(isChecked);

                                if (isChecked) {
                                    ScreensaverPrayerDialog dialog = ScreensaverPrayerDialog.newInstance(screenSaverRepo);
                                    if (getChildFragmentManager().findFragmentByTag(dialog.TAG) == null || !Objects.requireNonNull(getChildFragmentManager().findFragmentByTag(dialog.TAG)).isVisible()) {
                                        dialog.display(getChildFragmentManager());
                                    }

                                }
                            });


                            break;
                        }

                        case AfterDelay: {
                            LinearLayout llPeriod = subSettingsView.findViewById(R.id.ss_ll_delay);

                            TextView tvDelay = subSettingsView.findViewById(R.id.edit_TextView_SubSettingsHolder);
                            Button btnAdd = subSettingsView.findViewById(R.id.add_Button_SubSettingsHolder);
                            Button btnMinus = subSettingsView.findViewById(R.id.minus_Button_SubSettingsHolder);

                            CheckBox cb3 = subSettingsView.findViewById(R.id.ss_timing_delay);
                            cb3.setText(screenSaverRepo.getTimingNamesLocalized(getContext(), key));
                            cb3.setChecked(value);
                            llPeriod.setAlpha(getAlphaFromValue(value));

                            cb3.setOnCheckedChangeListener((buttonView, isChecked) -> {
                                screenSaverRepo.updateTiming(getContext(), key, isChecked);
                                btnAdd.setEnabled(isChecked);
                                btnMinus.setEnabled(isChecked);
                                llPeriod.setAlpha(getAlphaFromValue(isChecked));

                            });
                            int delay = screenSaverRepo.getTimingConfigDelay(getContext()).getDelayMinutes();

                            btnAdd.setEnabled(value);
                            btnMinus.setEnabled(value);

                            tvDelay.setText(delay + "");

                            btnAdd.setOnClickListener(v -> {
                                int dalue = 0;
                                try {
                                    String current = tvDelay.getText().toString();
                                    dalue = Integer.parseInt(current) + 1;
                                    if (dalue < 0)
                                        dalue = 0;
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                tvDelay.setText(dalue + "");
                                TimingConfigDelay timingDelay = new TimingConfigDelay(dalue);
                                screenSaverRepo.updateTimingConfig(getContext(), timingDelay);
                            });

                            btnMinus.setOnClickListener(v -> {
                                int dalue = 0;
                                try {
                                    String current = tvDelay.getText().toString();
                                    dalue = Integer.parseInt(current) - 1;
                                    if (dalue < 0)
                                        dalue = 0;
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                tvDelay.setText(dalue + "");
                                TimingConfigDelay timingDelay = new TimingConfigDelay(dalue);
                                screenSaverRepo.updateTimingConfig(getContext(), timingDelay);
                            });
                            break;
                        }

                        case Period: {
                            LinearLayout llPeriod = subSettingsView.findViewById(R.id.ss_ll_period);

                            TextView tvTimeFrom = subSettingsView.findViewById(R.id.time_from_TextView_SubSettingsHolder);
                            TextView tvTimeTo = subSettingsView.findViewById(R.id.time_to_TextView_SubSettingsHolder);

                            CheckBox cb2 = subSettingsView.findViewById(R.id.ss_timing_period);
                            cb2.setText(screenSaverRepo.getTimingNamesLocalized(getContext(), key));
                            cb2.setChecked(value);
                            llPeriod.setAlpha(getAlphaFromValue(value));
                            cb2.setOnCheckedChangeListener((buttonView, isChecked) -> {
                                screenSaverRepo.updateTiming(getContext(), key, isChecked);
                                tvTimeFrom.setEnabled(isChecked);
                                tvTimeTo.setEnabled(isChecked);
                                llPeriod.setAlpha(getAlphaFromValue(isChecked));

                            });

                            TimingConfigPeriod period = screenSaverRepo.getTimingConfigPeriod(getContext());
                            tvTimeFrom.setEnabled(value);
                            tvTimeTo.setEnabled(value);

                            tvTimeFrom.setText(period.getFromFormatted());
                            tvTimeTo.setText(period.getToFormatted());


                            tvTimeFrom.setOnClickListener(v -> {
                                LocalTime currentTime = LocalTime.now();
                                TimePickerDialog timePickerDialog = new TimePickerDialog(
                                        getContext(),
                                        (view, hourOfDay, minute) -> {
                                            try {
                                                TimingConfigPeriod timing = new TimingConfigPeriod(
                                                        kotlinx.datetime.LocalTime.Companion.fromSecondOfDay(LocalTime.of(hourOfDay, minute).toSecondOfDay()),
                                                        period.getTo()
                                                );
                                                Pair<Boolean, Integer> validate = timing.validate();
                                                if (validate.getFirst()) {
                                                    screenSaverRepo.updateTimingConfig(
                                                            getContext(),
                                                            timing);

                                                    TimingConfigPeriod nperiod = screenSaverRepo.getTimingConfigPeriod(getContext());

                                                    tvTimeFrom.setText(nperiod.getFromFormatted());
                                                    tvTimeTo.setText(nperiod.getToFormatted());
                                                } else {
                                                    Alerter.create(requireActivity())
                                                            .setText(getText(validate.getSecond()))
                                                            .setBackgroundColorRes(R.color.colorAccent)
                                                            .show();
                                                }

                                            } catch (Exception e) {
                                                CrashlyticsUtils.INSTANCE.logException(e);
                                            }

                                        },
                                        currentTime.getHour(),
                                        currentTime.getMinute(),
                                        true
                                );
                                timePickerDialog.show();
                            });

                            tvTimeTo.setOnClickListener(v -> {
                                LocalTime currentTime = LocalTime.now();
                                TimePickerDialog timePickerDialog = new TimePickerDialog(
                                        getContext(),
                                        (view, hourOfDay, minute) -> {
                                            try {
                                                TimingConfigPeriod timing = new TimingConfigPeriod(
                                                        period.getFrom(),
                                                        kotlinx.datetime.LocalTime.Companion.fromSecondOfDay
                                                                (LocalTime.of(hourOfDay, minute).toSecondOfDay())
                                                );
                                                Pair<Boolean, Integer> validate = timing.validate();
                                                if (validate.getFirst()) {
                                                    screenSaverRepo.updateTimingConfig(
                                                            getContext(),
                                                            timing);

                                                    TimingConfigPeriod nperiod = screenSaverRepo.getTimingConfigPeriod(getContext());

                                                    tvTimeFrom.setText(nperiod.getFromFormatted());
                                                    tvTimeTo.setText(nperiod.getToFormatted());
                                                } else {
                                                    Alerter.create(requireActivity())
                                                            .setText(getText(validate.getSecond()))
                                                            .setBackgroundColorRes(R.color.colorAccent)
                                                            .show();
                                                }

                                            } catch (Exception e) {
                                                CrashlyticsUtils.INSTANCE.logException(e);
                                            }

                                        },
                                        currentTime.getHour(),
                                        currentTime.getMinute(),
                                        true
                                );
                                timePickerDialog.show();
                            });


                            break;
                        }

                        case DuringPrayers: {
                            CheckBox cb = subSettingsView.findViewById(R.id.ss_timing_during_prayers);
                            cb.setText(screenSaverRepo.getTimingNamesLocalized(getContext(), key));
                            cb.setChecked(value);
                            cb.setAlpha(getAlphaFromValue(value));
                            cb.setOnCheckedChangeListener((buttonView, isChecked) -> {
                                screenSaverRepo.updateTiming(getContext(), key, isChecked);
                                cb.setAlpha(getAlphaFromValue(isChecked));
                            });
                            break;

                        }

                        case Intervals: {
                            LinearLayout llInterval = subSettingsView.findViewById(R.id.ss_ll_interval);

                            TextView tvOnFor = subSettingsView.findViewById(R.id.edit_TextView_SubSettingsHolder1);
                            Button btnOnAdd = subSettingsView.findViewById(R.id.add_Button_SubSettingsHolder1);
                            Button btnOnMinus = subSettingsView.findViewById(R.id.minus_Button_SubSettingsHolder1);

                            TextView tvOffFor = subSettingsView.findViewById(R.id.edit_TextView_SubSettingsHolder2);
                            Button btnOffAdd = subSettingsView.findViewById(R.id.add_Button_SubSettingsHolder2);
                            Button btnOffMinus = subSettingsView.findViewById(R.id.minus_Button_SubSettingsHolder2);

                            CheckBox cb3 = subSettingsView.findViewById(R.id.ss_timing_interval);
                            cb3.setText(screenSaverRepo.getTimingNamesLocalized(getContext(), key));
                            cb3.setChecked(value);
                            llInterval.setAlpha(getAlphaFromValue(value));

                            cb3.setOnCheckedChangeListener((buttonView, isChecked) -> {
                                screenSaverRepo.updateTiming(getContext(), key, isChecked);
                                btnOnAdd.setEnabled(isChecked);
                                btnOnMinus.setEnabled(isChecked);
                                btnOffAdd.setEnabled(isChecked);
                                btnOffMinus.setEnabled(isChecked);
                                llInterval.setAlpha(getAlphaFromValue(isChecked));

                            });
                            TimingConfigInterval config = screenSaverRepo.getTimingConfigInterval(getContext());
                            int onFor = config.getOnIntervalMinutes();
                            int offFor = config.getOffIntervalMinutes();

                            btnOffAdd.setEnabled(value);
                            btnOffMinus.setEnabled(value);
                            btnOnAdd.setEnabled(value);
                            btnOnMinus.setEnabled(value);

                            tvOnFor.setText(onFor + "");
                            tvOffFor.setText(offFor + "");

                            btnOnAdd.setOnClickListener(v -> {
                                int dalue = 0;
                                try {
                                    String current = tvOnFor.getText().toString();
                                    dalue = Integer.parseInt(current) + 1;
                                    if (dalue < 0)
                                        dalue = 0;
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                tvOnFor.setText(dalue + "");

                                TimingConfigInterval timing = new TimingConfigInterval(dalue, config.getOffIntervalMinutes());
                                screenSaverRepo.updateTimingConfig(getContext(), timing);
                            });

                            btnOnMinus.setOnClickListener(v -> {
                                int dalue = 0;
                                try {
                                    String current = tvOnFor.getText().toString();
                                    dalue = Integer.parseInt(current) - 1;
                                    if (dalue < 0)
                                        dalue = 0;
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                tvOnFor.setText(dalue + "");
                                TimingConfigInterval timing = new TimingConfigInterval(dalue, config.getOffIntervalMinutes());
                                screenSaverRepo.updateTimingConfig(getContext(), timing);
                            });

                            btnOffAdd.setOnClickListener(v -> {
                                int dalue = 0;
                                try {
                                    String current = tvOffFor.getText().toString();
                                    dalue = Integer.parseInt(current) + 1;
                                    if (dalue < 0)
                                        dalue = 0;
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                tvOnFor.setText(dalue + "");

                                TimingConfigInterval timing = new TimingConfigInterval(config.getOnIntervalMinutes(), dalue);
                                screenSaverRepo.updateTimingConfig(getContext(), timing);
                            });


                            btnOffMinus.setOnClickListener(v -> {
                                int dalue = 0;
                                try {
                                    String current = tvOffFor.getText().toString();
                                    dalue = Integer.parseInt(current) - 1;
                                    if (dalue < 0)
                                        dalue = 0;
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                tvOffFor.setText(dalue + "");
                                TimingConfigInterval timing = new TimingConfigInterval(config.getOnIntervalMinutes(), dalue);
                                screenSaverRepo.updateTimingConfig(getContext(), timing);
                            });
                        }

                    }
                }
        );


    }

    private float getAlphaFromValue(boolean isChecked) {
        if (isChecked)
            return 1;
        else
            return 0.2f;
    }


    private void updateToggleViews(ScreensaverToggleEnum screensaverToggleEnum) {
        ScreensaverModeEnum mode = screenSaverRepo.getCurrentMode(getContext());

        switch (screensaverToggleEnum) {
            case Enabled:
                llMode.setVisibility(View.VISIBLE);
                llTiming.setVisibility(View.VISIBLE);
                updateModeViews(mode);
                break;
            case Disabled:
                llMode.setVisibility(View.INVISIBLE);
                llInfoSettings.setVisibility(View.GONE);
                llTiming.setVisibility(View.GONE);
                break;
        }

    }


    private void updateModeViews(ScreensaverModeEnum screensaverModeEnum) {
        ScreensaverToggleEnum toggle = screenSaverRepo.getCurrentToggle(getContext());

        if (toggle.equals(ScreensaverToggleEnum.Disabled))
            return;

        switch (screensaverModeEnum) {
            case Empty:
                llInfoSettings.setVisibility(View.GONE);
                break;

            case Info:
                llInfoSettings.setVisibility(View.VISIBLE);
                break;
            case ReduceBrightness:
                llInfoSettings.setVisibility(View.GONE);
                break;
        }

    }


}
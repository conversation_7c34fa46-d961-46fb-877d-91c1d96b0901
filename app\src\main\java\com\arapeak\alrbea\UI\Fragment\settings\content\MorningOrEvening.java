package com.arapeak.alrbea.UI.Fragment.settings.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_ATHKAR;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_LOCATION;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_MAIN;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_MESSAGE;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_NEWS;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_PRAYER;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_THEME;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.DateAlrabeeaTimes;
import com.arapeak.alrbea.Model.NewsTicker;
import com.arapeak.alrbea.Model.PrayerSystemsSchools;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkConstants;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.ValueEventListener;
import com.orhanobut.hawk.Hawk;

public class MorningOrEvening extends AlrabeeaTimesFragment implements SettingsAdapterCallback {

    private static final String TAG = "AthkarsAfterPrayerFragment";

    private View athkarsAfterPrayerView;
    private RecyclerView settingItemRecyclerView;

    private MainSettingsAdapter mainSettingsAdapter;
    private DatabaseReference mDatabase;
    private CheckBox citationForMorningCheckBox, citationForMorningCheckBox1, citationForMorningCheckBox2, citationForMorningCheckBox3, citationForMorningCheckBox4, citationForMorningCheckBox5, citationForMorningCheckBox6;
    private TextView citationForMorningTextView, citationForMorningTextView1, citationForMorningTextView2, citationForMorningTextView3, citationForMorningTextView4, citationForMorningTextView5, citationForMorningTextView6;

    private View space1View, space1View1, space1View2, space1View3, space1View4, space1View5, space1View6;
    private Button b, bd;

    public MorningOrEvening() {

    }

    public static MorningOrEvening newInstance() {
        return new MorningOrEvening();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        athkarsAfterPrayerView = inflater.inflate(R.layout.fragment_setting_synce, container, false);

        initView();
        setParameter();
        setAction();

        return athkarsAfterPrayerView;
    }

    private void initView() {

        citationForMorningCheckBox = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment);
        citationForMorningTextView = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment);
        space1View = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment);

        citationForMorningCheckBox1 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment1);
        citationForMorningTextView1 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment1);
        space1View1 = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment1);


        citationForMorningCheckBox2 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment2);
        citationForMorningTextView2 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment2);
        space1View2 = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment2);

        citationForMorningCheckBox3 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment3);
        citationForMorningTextView3 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment3);
        space1View3 = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment3);

        citationForMorningCheckBox4 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment4);
        citationForMorningTextView4 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment4);
        space1View4 = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment4);

        citationForMorningCheckBox5 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment5);
        citationForMorningTextView5 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment5);
        space1View5 = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment5);

        citationForMorningCheckBox6 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_CheckBox_AthkarFragment6);
        citationForMorningTextView6 = athkarsAfterPrayerView.findViewById(R.id.citationForMorning_TextView_AthkarFragment6);
        space1View6 = athkarsAfterPrayerView.findViewById(R.id.space1_View_AthkarFragment6);

        b = athkarsAfterPrayerView.findViewById(R.id.b);
        bd = athkarsAfterPrayerView.findViewById(R.id.bd);
        if (Hawk.get(ConstantsOfApp.SYNCE_USER_KEY, null) == null) {
            bd.setVisibility(View.GONE);
        }

        //   settingItemRecyclerView = athkarsAfterPrayerView.findViewById(R.id.settingItem_RecyclerView_AthkarsAfterPrayerFragment);

        // mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);
    }

    private void setParameter() {
//        if (Utils.isLandscape()){
//            SettingsLandscapeActivity.getToolbar().setTitle("تحديد المزامنة");
//        }else {
//            SettingsActivity.getToolbar().setTitle("تحديد المزامنة");
//        }
        SettingsActivity.getToolbar().setTitle("تحديد المزامنة");

        // settingItemRecyclerView.setAdapter(mainSettingsAdapter);


        citationForMorningTextView.setText("أعدادات الرئسية");
        citationForMorningCheckBox.setChecked(Hawk.get(SYNCE_MAIN, false));
        citationForMorningTextView1.setText("اعدادات أوقات الصلاة");
        citationForMorningCheckBox1.setChecked(Hawk.get(SYNCE_PRAYER, false));
        citationForMorningTextView2.setText(" القالب");
        citationForMorningCheckBox2.setChecked(Hawk.get(SYNCE_THEME, false));
        citationForMorningTextView3.setText(" المكان");
        citationForMorningCheckBox3.setChecked(Hawk.get(SYNCE_LOCATION, false));
        citationForMorningTextView4.setText(" شريط الاخبار");
        citationForMorningCheckBox4.setChecked(Hawk.get(SYNCE_NEWS, false));
        citationForMorningTextView5.setText("رسائل الجنازة ");
        citationForMorningCheckBox5.setChecked(Hawk.get(SYNCE_MESSAGE, false));
        citationForMorningTextView6.setText(" الاذكار");
        citationForMorningCheckBox6.setChecked(Hawk.get(SYNCE_ATHKAR, false));
        // subSettingAlrabeeaTimes.setShowEnableButton(true);
        //subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_DEFAULT));


    }

    private void setAction() {

        citationForMorningCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Hawk.put(SYNCE_MAIN, true);

            }
        });

        citationForMorningCheckBox1.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                Hawk.put(SYNCE_PRAYER, true);

            }
        });

        citationForMorningCheckBox2.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {


                Hawk.put(SYNCE_THEME, true);


            }
        });

        citationForMorningCheckBox3.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {


                Hawk.put(SYNCE_LOCATION, true);
            }
        });

        citationForMorningCheckBox4.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {


                Hawk.put(SYNCE_NEWS, true);


            }
        });

        citationForMorningCheckBox5.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Hawk.put(SYNCE_MESSAGE, true);


            }
        });

        citationForMorningCheckBox6.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {


                Hawk.put(SYNCE_ATHKAR, true);


            }
        });


        b.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                mDatabase = FirebaseDatabase.getInstance().getReference();


                //  InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);


                mDatabase.child("users").child(Hawk.get(ConstantsOfApp.SYNCE_USER_KEY, null)).addListenerForSingleValueEvent(new ValueEventListener() {
                    @Override
                    public void onDataChange(@NonNull DataSnapshot dataSnapshot) {
                        //   Hawk.put(ConstantsOfApp.INFO_OF_CODE_KEY, dataSnapshot.child("INFO_OF_CODE_KEY").getValue())   ;

                        if (Hawk.get(SYNCE_MAIN, false)) {
                            if (dataSnapshot.child("PRAYER_METHOD_KEY").exists()) {
                                PrayerSystemsSchools prayerSystemsSchools = new PrayerSystemsSchools(dataSnapshot.child("PRAYER_METHOD_KEY").child("name").getValue().toString(), Integer.parseInt(dataSnapshot.child("PRAYER_METHOD_KEY").child("value").getValue().toString()));
                                // PrayerSystemsSchools prayerSystemsSchools =  dataSnapshot.child("PRAYER_METHOD_KEY").getValue((PrayerSystemsSchools.class)) ;

                                Hawk.put(ConstantsOfApp.PRAYER_METHOD_KEY, prayerSystemsSchools);
                            }

                            if (dataSnapshot.child("ADJUST_HIJRI_DATE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, dataSnapshot.child("ADJUST_HIJRI_DATE_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_AM_PM_KEY").exists()) {
                                Hawk.put(HawkConstants.IS_AM_PM_KEY, dataSnapshot.child("IS_AM_PM_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_ARABIC_NUMBER").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ARABIC_NUMBER, dataSnapshot.child("IS_ARABIC_NUMBER").getValue());
                            }

                            if (dataSnapshot.child("LAST_UPDATE_DATA_PRAYER_TIME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.LAST_UPDATE_DATA_PRAYER_TIME_KEY, dataSnapshot.child("LAST_UPDATE_DATA_PRAYER_TIME_KEY").getValue());
                            }

                            if (dataSnapshot.child("IS_CUSTOM_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, dataSnapshot.child("IS_CUSTOM_KEY").getValue());
                            }
                            if (dataSnapshot.child("TYPE_OF_SHOW_DATE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TYPE_OF_SHOW_DATE_KEY, dataSnapshot.child("TYPE_OF_SHOW_DATE_KEY").getValue());
                            }
                            if (dataSnapshot.child("DATE_ALRABEEA_TIMES_NOW_KEY").exists()) {
                                DateAlrabeeaTimes dateAlrabeeaTimes = new DateAlrabeeaTimes();
                                dateAlrabeeaTimes = dataSnapshot.child("DATE_ALRABEEA_TIMES_NOW_KEY").getValue(DateAlrabeeaTimes.class);
                                Hawk.put(ConstantsOfApp.DATE_ALRABEEA_TIMES_NOW_KEY, dateAlrabeeaTimes);
                            }
                            if (dataSnapshot.child("TYPE_OF_AL_AHADETH_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TYPE_OF_AL_AHADETH_KEY, dataSnapshot.child("TYPE_OF_AL_AHADETH_KEY").getValue());
                            }
                            if (dataSnapshot.child("TYPE_OF_SCREEN_SLEEP_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TYPE_OF_SCREEN_SLEEP_KEY, dataSnapshot.child("TYPE_OF_SCREEN_SLEEP_KEY").getValue());
                            }

                            if (dataSnapshot.child("IS_SYSTEM_TIME").exists()) {
                                Hawk.put(ConstantsOfApp.IS_SYSTEM_TIME, dataSnapshot.child("IS_SYSTEM_TIME").getValue());
                            }
                            if (dataSnapshot.child("IS_SECONEDS_SHOW").exists()) {
                                Hawk.put(ConstantsOfApp.IS_SECONEDS_SHOW, dataSnapshot.child("IS_SECONEDS_SHOW").getValue());
                            }

                            //  Calendar calendar = Calendar.getInstance();
                            //  calendar.setTimeInMillis(calendar.getTimeInMillis());
                            //  Date date = calendar.getTime();
                            //Process process = Runtime.getRuntime().exec("su");
                            //DataOutputStream os = new DataOutputStream(process.getOutputStream());

                            if (dataSnapshot.child("YEAR").exists()) {
                                Hawk.put(ConstantsOfApp.YEAR, dataSnapshot.child("YEAR").getValue());
                            }
                            if (dataSnapshot.child("MONTH").exists()) {
                                Hawk.put(ConstantsOfApp.MONTH, dataSnapshot.child("MONTH").getValue());
                            }
                            if (dataSnapshot.child("DAY").exists()) {
                                Hawk.put(ConstantsOfApp.DAY, dataSnapshot.child("DAY").getValue());
                            }


                        }
                        if (Hawk.get(SYNCE_LOCATION, false)) {
                            double lat = 0.0;
                            double lng = 0.0;
                            if (dataSnapshot.child("LATITUDE_KEY").exists()) {
                                lat = (double) dataSnapshot.child("LATITUDE_KEY").getValue();
                            }
                            if (dataSnapshot.child("LONGITUDE_KEY").exists()) {
                                lng = (double) dataSnapshot.child("LONGITUDE_KEY").getValue();
                            }
                            if (dataSnapshot.child("UPDATE_LOCATION_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.UPDATE_LOCATION_KEY, dataSnapshot.child("UPDATE_LOCATION_KEY").getValue());
                            }
                            HawkSettings.putLatLong(lat, lng);
                        }
                        if (Hawk.get(SYNCE_THEME, false)) {


                            /*if(dataSnapshot.child("APP_THEME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_KEY,Integer.parseInt(dataSnapshot.child("APP_THEME_KEY").getValue()+""));                            }
                            if(dataSnapshot.child("APP_THEME_BROWN_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_BROWN_KEY, dataSnapshot.child("APP_THEME_BROWN_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_GREEN_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_GREEN_KEY, dataSnapshot.child("APP_THEME_GREEN_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_BLUE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_BLUE_KEY, dataSnapshot.child("APP_THEME_BLUE_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_DARK_GREEN_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_DARK_GREEN_KEY, dataSnapshot.child("APP_THEME_DARK_GREEN_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_RED_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_RED_KEY, dataSnapshot.child("APP_THEME_RED_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_DARK_GRAY_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_DARK_GRAY_KEY, dataSnapshot.child("APP_THEME_DARK_GRAY_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_DARK_Green_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_DARK_Green_KEY, dataSnapshot.child("APP_THEME_DARK_Green_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_RED_NEW_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_RED_NEW_KEY, dataSnapshot.child("APP_THEME_RED_NEW_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_BROWN_NEW_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_BROWN_NEW_KEY, dataSnapshot.child("APP_THEME_BROWN_NEW_KEY").getValue());
                            }
                            if(dataSnapshot.child("APP_THEME_BLUE_NEW_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.APP_THEME_BLUE_NEW_KEY, dataSnapshot.child("APP_THEME_BLUE_NEW_KEY").getValue());
                            }*/
                        }
                        if (Hawk.get(SYNCE_PRAYER, false)) {
                            if (dataSnapshot.child("PRAYER_API_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.PRAYER_API_KEY, dataSnapshot.child("PRAYER_API_KEY").getValue());
                            }
                            if (dataSnapshot.child("LOCK_DURING_PRAYER_KEY").exists()) {

                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("LOCK_DURING_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("FAJR_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.FAJR_KEY, dataSnapshot.child("FAJR_KEY").getValue());
                            }
                            if (dataSnapshot.child("DUHA_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.DUHA_KEY, dataSnapshot.child("DUHA_KEY").getValue());
                            }
                            if (dataSnapshot.child("SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.SUNRISE_KEY, dataSnapshot.child("SUNRISE_KEY").getValue());
                            }
                            if (dataSnapshot.child("DHUHR_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.DHUHR_KEY, dataSnapshot.child("DHUHR_KEY").getValue());
                            }
                            if (dataSnapshot.child("ASR_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ASR_KEY, dataSnapshot.child("ASR_KEY").getValue());
                            }
                            if (dataSnapshot.child("MAGHRIB_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.MAGHRIB_KEY, dataSnapshot.child("MAGHRIB_KEY").getValue());
                            }
                            if (dataSnapshot.child("ISHA_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ISHA_KEY, dataSnapshot.child("ISHA_KEY").getValue());
                            }
                            if (dataSnapshot.child("JOMAA_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.JOMAA_KEY, dataSnapshot.child("JOMAA_KEY").getValue());
                            }
                            if (dataSnapshot.child("TARAWIH_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TARAWIH_KEY, dataSnapshot.child("TARAWIH_KEY").getValue());
                            }
                            if (dataSnapshot.child("TAHAJJUD_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TAHAJJUD_KEY, dataSnapshot.child("TAHAJJUD_KEY").getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY).getValue()));
                            }


                            if (dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY).getValue()));
                            }


                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).getValue()));
                            }


                            if (dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).getValue()));
                            }


                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_FAJR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_FAJR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DHUHR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DHUHR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ASR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ASR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ISHA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ISHA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_JOMAA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_JOMAA_KEY).getValue()));
                            }


                            if (dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_FAJR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_FAJR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_DHUHR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_DHUHR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_ASR_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_ASR_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_MAGHRIB_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_MAGHRIB_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_ISHA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_ISHA_KEY).getValue()));
                            }
                            if (dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_JOMAA_KEY, Integer.parseInt("" + dataSnapshot.child(ConstantsOfApp.POST_OR_PRE_TO_JOMAA_KEY).getValue()));
                            }

                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_KEY).getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ASR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ASR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY).getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY).getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ASR_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ASR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY, dataSnapshot.child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY).getValue());
                            }


                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY).getValue());
                            }
                            if (dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY).exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY, dataSnapshot.child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY).getValue());
                            }


                            if (dataSnapshot.child("POST_OR_PRE_TO_SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_SUNRISE_KEY, Integer.parseInt("" + dataSnapshot.child("POST_OR_PRE_TO_SUNRISE_KEY").getValue()));
                            }

                            if (dataSnapshot.child("POST_OR_PRE_TO_DUHA_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.POST_OR_PRE_TO_DUHA_KEY, Integer.parseInt("" + dataSnapshot.child("POST_OR_PRE_TO_DUHA_KEY").getValue()));
                            }

                            if (dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_SUNRISE_KEY, Integer.parseInt("" + dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_SUNRISE_KEY").getValue()));
                            }

                            if (dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_DUHA_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DUHA_KEY, Integer.parseInt("" + dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_DUHA_KEY").getValue()));
                            }

                            if (dataSnapshot.child("TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, dataSnapshot.child("TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY").getValue());
                            }
                            if (dataSnapshot.child("RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, dataSnapshot.child("RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY").getValue());
                            }


                            if (dataSnapshot.child("TIME_OF_IKAMA_SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TIME_OF_IKAMA_SUNRISE_KEY, dataSnapshot.child("TIME_OF_IKAMA_SUNRISE_KEY").getValue());
                            }
                            if (dataSnapshot.child("RAMADAN_TIME_OF_IKAMA_SUNRISE_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_SUNRISE_KEY, dataSnapshot.child("RAMADAN_TIME_OF_IKAMA_SUNRISE_KEY").getValue());
                            }


                            if (dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY, Integer.parseInt("" + dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY").getValue()));
                            }
                            if (dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY_H").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY_H, Integer.parseInt("" + dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY_H").getValue()));
                            }


                            if (dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY, Integer.parseInt("" + dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY").getValue()));
                            }
                            if (dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME, dataSnapshot.child("RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME").getValue());
                            }


                            if (dataSnapshot.child("RAMADAN_DURATION_OF_TARAWIH_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_KEY, dataSnapshot.child("RAMADAN_DURATION_OF_TARAWIH_KEY").getValue());
                            }
                            if (dataSnapshot.child("RAMADAN_DURATION_OF_TAHAJJUD_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_KEY, dataSnapshot.child("RAMADAN_DURATION_OF_TAHAJJUD_KEY").getValue());
                            }


                        }
                        if (Hawk.get(SYNCE_ATHKAR, false)) {
                            if (dataSnapshot.child("TIME_OF_ATHKAR_AFTER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TIME_OF_ATHKAR_AFTER_KEY, dataSnapshot.child("TIME_OF_ATHKAR_AFTER_KEY").getValue());
                            }
                            if (dataSnapshot.child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TYPE_OF_AZHAR_MORNING_EVENING_KEY, dataSnapshot.child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").getValue());
                            }
                            if (dataSnapshot.child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TYPE_OF_AZHAR_MORNING_EVENING_KEY, dataSnapshot.child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").getValue());

                            }
                            if (dataSnapshot.child("CITATION_FOR_MORNING_TIME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, dataSnapshot.child("CITATION_FOR_MORNING_TIME_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_THERE_CITATION_FOR_MORNING_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_MORNING_KEY, dataSnapshot.child("IS_THERE_CITATION_FOR_MORNING_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_THERE_CITATION_FOR_EVENING_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_EVENING_KEY, dataSnapshot.child("IS_THERE_CITATION_FOR_EVENING_KEY").getValue());
                            }


                            if (dataSnapshot.child("CITATION_FOR_EVENING_TIME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, Integer.parseInt("" + dataSnapshot.child("CITATION_FOR_EVENING_TIME_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_AFTER_FAJER_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_AFTER_FAJER_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_AFTER_FAJER_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_AFTER_DHUHR_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_AFTER_DHUHR_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_AFTER_DHUHR_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_AFTER_ASER_PRAYER_KEY").exists()) {

                                Hawk.put(ConstantsOfApp.ATHKARS_AFTER_ASER_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_AFTER_ASER_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_AFTER_MAGHRIB_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_AFTER_MAGHRIB_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_AFTER_ISHA_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_AFTER_ISHA_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_AFTER_ISHA_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_AFTER_JOMAA_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_AFTER_JOMAA_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_AFTER_JOMAA_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_MORNING_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_MORNING_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_MORNING_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("ATHKARS_EVNING_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.ATHKARS_EVNING_PRAYER_KEY, Integer.parseInt("" + dataSnapshot.child("ATHKARS_EVNING_PRAYER_KEY").getValue()));
                            }
                            if (dataSnapshot.child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TYPE_OF_AZHAR_MORNING_EVENING_KEY, dataSnapshot.child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").getValue());
                            }


                            if (dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY, dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY, dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY, dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY, dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY").getValue());
                            }
                            if (dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY, dataSnapshot.child("IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY").getValue());
                            }

                            if (dataSnapshot.child("EVENING_TIME_NAME").exists()) {
                                Hawk.put(ConstantsOfApp.EVENING_TIME_NAME, dataSnapshot.child("EVENING_TIME_NAME").getValue());
                            }
                            if (dataSnapshot.child("MORNING_TIME_NAME").exists()) {
                                Hawk.put(ConstantsOfApp.MORNING_TIME_NAME, dataSnapshot.child("MORNING_TIME_NAME").getValue());
                            }


                            if (dataSnapshot.child("TARAWIH_TIME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.TARAWIH_TIME_KEY, Integer.parseInt("" + dataSnapshot.child("TARAWIH_TIME_KEY").getValue()));
                            }
                            if (dataSnapshot.child("EVENING_TIME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.EVENING_TIME_KEY, Integer.parseInt("" + dataSnapshot.child("EVENING_TIME_KEY").getValue()));
                            }
                            if (dataSnapshot.child("MORNING_TIME_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.MORNING_TIME_KEY, Integer.parseInt("" + dataSnapshot.child("MORNING_TIME_KEY").getValue()));
                            }


                        }
                        if (Hawk.get(SYNCE_MESSAGE, false)) {
                            if (dataSnapshot.child("FUNERAL_MESSAGES_KEY").exists()) {
                                Hawk.put(ConstantsOfApp.FUNERAL_MESSAGES_KEY, dataSnapshot.child("FUNERAL_MESSAGES_KEY").getValue());
                            }
                        }
                        if (Hawk.get(SYNCE_NEWS, false)) {
                            if (dataSnapshot.child("NEWS_TICKER_KEY").exists()) {
                                boolean active = (boolean) dataSnapshot.child("NEWS_TICKER_KEY").child("active").getValue();
                                NewsTicker newsTicker = new NewsTicker(dataSnapshot.child("NEWS_TICKER_KEY").child("messageEvent").getValue().toString(), dataSnapshot.child("NEWS_TICKER_KEY").child("startDate").getValue().toString(), dataSnapshot.child("NEWS_TICKER_KEY").child("endDate").getValue().toString(), active);
                                // NewsTicker newsTicker=dataSnapshot.child("NEWS_TICKER_KEY").getValue(NewsTicker.class);
                                Hawk.put(ConstantsOfApp.NEWS_TICKER_KEY, newsTicker);
                            }
                        }
                        //   Hawk.put(ConstantsOfApp.EVENT_IMAGE_KEY, dataSnapshot.child("EVENT_IMAGE_KEY").getValue())   ;
                        //  Hawk.put(ConstantsOfApp.APPEARANCE_ALTERNATELY_KEY, dataSnapshot.child("APPEARANCE_ALTERNATELY_KEY").getValue())   ;


                        Utils.showSuccessAlert(getAppCompatActivity()
                                , getString(R.string.application_successfully_activated));
                        Toast.makeText(getAppCompatActivity(), "تم المزامنة بنجاح", Toast.LENGTH_LONG).show();

                        //clear old prayer times
                        HawkSettings.resetPrayerTimes();
                        goToMainActivity();

                    }

                    @Override
                    public void onCancelled(@NonNull DatabaseError databaseError) {
                        Utils.showFailAlert(getAppCompatActivity()
                                , getString(R.string.there_is_a_problem)
                                , getString(R.string.there_is_a_problem));
                    }
                });


            }
        });


        bd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {


                Hawk.delete(ConstantsOfApp.SYNCE_USER_KEY);
                goToMainActivity();


            }
        });


    }

    private void goToMainActivity() {
        Intent intent;
        intent = new Intent(getContext(), MainActivity.class);
        /*if (Utils.isLandscape(getContext()))
            intent = new Intent(getContext(), MainLandscapeActivity.class);
        else
            intent = new Intent(getContext(), MainActivity.class);*/
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
    }


    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = mainSettingsAdapter.getItem(position);
        if (settingItemRecyclerView == null) {
            return;
        }

        boolean isEnable = subPosition == 1;

        switch (viewsAlrabeeaTimes) {
            case CHECK_BOX:
                if (position == 0) {
                    Hawk.put(SYNCE_MAIN, true);

                } else if (position == 1) {
                    Hawk.put(SYNCE_PRAYER, true);
                } else if (position == 2) {
                    Hawk.put(SYNCE_THEME, true);
                } else if (position == 3) {
                    Hawk.put(SYNCE_LOCATION, true);
                } else if (position == 4) {
                    Hawk.put(SYNCE_NEWS, true);
                } else if (position == 5) {
                    Hawk.put(SYNCE_MESSAGE, true);
                } else if (position == 6) {
                    Hawk.put(SYNCE_ATHKAR, true);

                }


                break;
            case BUTTON:
                if (position == 7) {


                }


                break;

        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="top|center_horizontal"
    android:orientation="vertical"
    tools:context=".UI.Fragment.settings.content.main.content.designs.content.themes.CustomTheme">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top|center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_80sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:src="@drawable/custom_theme_icon" />

            <TextView
                android:id="@+id/tv_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/custom_theme"
                android:textColor="@color/colorblack"
                android:textSize="@dimen/_18sdp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/design_your_own_theme"
                android:textSize="@dimen/_14sdp"
                android:textStyle="normal" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="top|center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/layout_backgroundsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:gravity="top|center_horizontal"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/_10sdp"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:text="@string/horizontal_mode"
                        android:textSize="@dimen/_14sdp"
                        android:textStyle="normal" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/_200sdp"
                        android:src="@drawable/custom_theme_example_portirait" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="vertical">

                            <Button
                                android:id="@+id/btn_change_background_portrait"
                                android:layout_width="@dimen/_120sdp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_10sdp"
                                android:background="@drawable/button_green_without_corners_shape"
                                android:fontFamily="@font/droid_arabic_kufi"
                                android:text="@string/change_background"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_14sdp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/iv_background_portrait"
                            android:layout_width="@dimen/_120sdp"
                            android:layout_height="@dimen/_200sdp"
                            android:src="@drawable/theme_custom_1_background_portrait" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:layout_marginBottom="@dimen/_10sdp"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:text="@string/vertical_mode"
                        android:textSize="@dimen/_14sdp"
                        android:textStyle="normal" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/_100sdp"
                        android:src="@drawable/custom_theme_example_land" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <Button
                                android:id="@+id/btn_change_background_landscape"
                                android:layout_width="@dimen/_120sdp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/_10sdp"
                                android:background="@drawable/button_green_without_corners_shape"
                                android:fontFamily="@font/droid_arabic_kufi"
                                android:text="@string/change_background"
                                android:textColor="@color/white"
                                android:textSize="@dimen/_14sdp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/iv_background_landscape"
                            android:layout_width="@dimen/_140sdp"
                            android:layout_height="@dimen/_100sdp"
                            android:src="@drawable/theme_custom_1_background_landscape" />
                    </LinearLayout>

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/show_ikama_times_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:fontFamily="@font/droid_arabic_kufi_bold"
                        android:padding="@dimen/_10sdp"
                        android:text="@string/show_ikama_times"
                        android:textSize="@dimen/_14sdp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_logoContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:gravity="top|center_horizontal"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/btn_add_or_remove_logo"
                            android:layout_width="@dimen/_120sdp"
                            android:layout_height="wrap_content"
                            android:background="@drawable/button_green_without_corners_shape"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:padding="@dimen/_5sdp"
                            android:text="@string/logoch"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_14sdp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/btn_logo_color"
                            android:layout_width="@dimen/_120sdp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_10sdp"
                            android:background="@drawable/button_green_without_corners_shape"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:padding="@dimen/_5sdp"
                            android:text="@string/logo_color"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_14sdp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/btn_logo_color_remove"
                            android:layout_width="@dimen/_120sdp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_10sdp"
                            android:background="@drawable/button_green_without_corners_shape"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:padding="@dimen/_5sdp"
                            android:text="@string/logo_color_remove"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_14sdp"
                            android:textStyle="bold" />

                        <!--                        <Button-->
                        <!--                            android:id="@+id/btn_alrabea_logo_remove"-->
                        <!--                            android:layout_width="@dimen/_120sdp"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:textSize="@dimen/_14sdp"-->
                        <!--                            android:layout_marginTop="@dimen/_10sdp"-->
                        <!--                            android:padding="@dimen/_5sdp"-->
                        <!--                            android:fontFamily="@font/droid_arabic_kufi"-->
                        <!--                            android:textStyle="bold"-->
                        <!--                            android:textColor="@color/white"-->
                        <!--                            android:background="@drawable/button_green_without_corners_shape"-->
                        <!--                            android:text="@string/alrbea_logo_remove"/>-->
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/iv_logo"
                        android:layout_width="@dimen/_150sdp"
                        android:layout_height="@dimen/_100sdp"
                        android:layout_gravity="center"
                        android:src="@drawable/img_logo_alrabeea" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_colorsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="@dimen/_10sdp"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:includeFontPadding="false"
                            android:text="@string/main_color"
                            android:textColor="#343434"
                            android:textSize="@dimen/_15sdp" />

                        <ImageView
                            android:id="@+id/btn_color_1"
                            android:layout_width="@dimen/_80sdp"
                            android:layout_height="40dp"
                            android:layout_gravity="center" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:includeFontPadding="false"
                            android:text="@string/secondary_color"
                            android:textColor="#343434"
                            android:textSize="@dimen/_15sdp" />

                        <ImageView
                            android:id="@+id/btn_color_2"
                            android:layout_width="@dimen/_80sdp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:background="@drawable/button_green_without_corners_shape" />
                    </LinearLayout>
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="top|center_vertical"
        android:orientation="horizontal"
        android:paddingTop="@dimen/_5sdp"
        android:paddingBottom="@dimen/_5sdp">

        <LinearLayout
            android:id="@+id/layout_addBackground"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/button_gray_without_corners_shape"
            android:gravity="top|center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                android:layout_margin="@dimen/_10sdp"
                android:src="@drawable/custom_theme_icon_background" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="@string/add_background"
                android:textColor="@color/colorblack"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_addLogo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="top|center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                android:layout_margin="@dimen/_10sdp"
                android:src="@drawable/custom_theme_icon_change_logo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="@string/add_logo"
                android:textColor="@color/colorblack"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_addColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="top|center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/_40sdp"
                android:layout_height="@dimen/_40sdp"
                android:layout_margin="@dimen/_10sdp"
                android:src="@drawable/custom_theme_icon_change_color" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="@string/select_colors"
                android:textColor="@color/colorblack"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_5sdp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_save"
            android:layout_width="@dimen/_100sdp"
            android:layout_height="wrap_content"
            android:background="@drawable/button_green_without_corners_shape"
            android:fontFamily="@font/droid_arabic_kufi"
            android:text="@string/save"
            android:textColor="@color/white"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_reset"
            android:layout_width="@dimen/_100sdp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:background="@drawable/button_red_without_corners_shape"
            android:fontFamily="@font/droid_arabic_kufi"
            android:text="@string/reset_setting"
            android:textColor="@color/white"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>
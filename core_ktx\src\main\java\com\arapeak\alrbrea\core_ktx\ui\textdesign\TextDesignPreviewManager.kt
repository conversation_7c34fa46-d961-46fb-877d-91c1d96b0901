package com.arapeak.alrbrea.core_ktx.ui.textdesign

import android.content.Context
import android.util.Log
import android.view.View
import androidx.core.view.drawToBitmap
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.ui.utils.saveImageToCache
import java.io.File

class TextDesignPreviewManager {
    val path = "image/"
    val screenshotName = "screenshot"

    fun getScreenshotName(isLand: Boolean) = if (isLand) "${screenshotName}_land" else screenshotName

    fun getPreviewImagePath(
        activity: Context,
        isLand: Boolean
    ): String? {
        try {
            val cacheFile = File(activity.cacheDir, "$path${getScreenshotName(isLand)}")

            Log.d("textdesign", "saveImageToCache: ${cacheFile.absoluteFile}")

            return cacheFile.absolutePath
        } catch (e: Exception) {
            logException(e)
        }
        return null
    }
    fun saveLayoutPreview(context: Context, view: View, isLand: Boolean) {
        view.post {
            try {
                if (view.width > 0 && view.height > 0) {
                    val bitmap = view.drawToBitmap()
                    context.saveImageToCache(bitmap, path, getScreenshotName(isLand))
                } else {
                    logException(IllegalStateException("View not laid out: width or height is 0"))
                }
            } catch (e: Exception) {
                logException(e)
            }
        }
    }


}
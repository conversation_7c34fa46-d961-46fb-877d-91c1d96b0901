package com.arapeak.alrbrea.core_ktx.ui.textdesign

import android.content.Context
import android.util.Log
import android.view.View
import androidx.core.view.drawToBitmap
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.ui.utils.saveImageToCache
import java.io.File

class TextDesignPreviewManager {
    val path = "image/"
    val screenshotName = "screenshot"

    fun getScreenshotName(isLand: Boolean) = if (isLand) "${screenshotName}_land" else screenshotName

    fun getPreviewImagePath(
        activity: Context,
        isLand: Boolean
    ): String? {
        try {
            val cacheFile = File(activity.cacheDir, "$path${getScreenshotName(isLand)}")

            Log.d("textdesign", "saveImageToCache: ${cacheFile.absoluteFile}")

            return cacheFile.absolutePath
        } catch (e: Exception) {
            logException(e)
        }
        return null
    }
    fun saveLayoutPreview(context: Context, view: View, isLand: Boolean) {
        try {
            // Check if view is valid and attached
            if (view.parent == null) {
                Log.w("TextDesignPreview", "View is not attached to parent, skipping preview save")
                return
            }

            // Use ViewTreeObserver to wait for layout completion
            if (view.width > 0 && view.height > 0) {
                // View is already laid out, save immediately
                savePreviewInternal(context, view, isLand)
            } else {
                // Wait for layout to complete
                view.viewTreeObserver.addOnGlobalLayoutListener(object : android.view.ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        try {
                            view.viewTreeObserver.removeOnGlobalLayoutListener(this)

                            if (view.width > 0 && view.height > 0) {
                                savePreviewInternal(context, view, isLand)
                            } else {
                                Log.w("TextDesignPreview", "View still not laid out after global layout, width=${view.width}, height=${view.height}")
                            }
                        } catch (e: Exception) {
                            Log.e("TextDesignPreview", "Error in global layout listener", e)
                            logException(e)
                        }
                    }
                })
            }
        } catch (e: Exception) {
            Log.e("TextDesignPreview", "Error in saveLayoutPreview", e)
            logException(e)
        }
    }

    private fun savePreviewInternal(context: Context, view: View, isLand: Boolean) {
        try {
            Log.d("TextDesignPreview", "Saving preview for view with dimensions: ${view.width}x${view.height}")
            val bitmap = view.drawToBitmap()
            context.saveImageToCache(bitmap, path, getScreenshotName(isLand))
            Log.d("TextDesignPreview", "Preview saved successfully")
        } catch (e: Exception) {
            Log.e("TextDesignPreview", "Error saving preview bitmap", e)
            logException(e)
        }
    }


}
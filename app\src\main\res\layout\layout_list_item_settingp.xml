<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content_LinearLayout_SettingViewHolder"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="@dimen/_15sdp"
    tools:layoutDirection="rtl">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:id="@+id/icon_CardView_SettingViewHolder"
            android:layout_width="@dimen/_45sdp"
            android:layout_height="@dimen/_45sdp"
            android:layout_marginStart="@dimen/_15sdp"
            android:layout_marginTop="@dimen/_5sdp"
            app:cardBackgroundColor="#414141"
            app:cardCornerRadius="@dimen/_15sdp"
            app:cardElevation="0dp"
            app:contentPadding="@dimen/_14sdp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/icon_ImageView_SettingViewHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_gravity="center"
                app:tint="@android:color/white"
                tools:src="@drawable/ic_background" />

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/titleDescription_LinearLayout_SettingViewHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_15sdp"
            android:layout_marginEnd="@dimen/_15sdp"
            android:orientation="vertical"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/icon_CardView_SettingViewHolder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/icon_CardView_SettingViewHolder"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title_TextView_SettingViewHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold"
                tools:text="الإعدادات الرئيسية" />

            <TextView
                android:id="@+id/description_TextView_SettingViewHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_9sdp"
                tools:text="إعدادات التطبيق الرئيسية" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/space_View_SettingViewHolder"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_15sdp"
        android:background="#D1D1D1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_CardView_SettingViewHolder" />

</LinearLayout>
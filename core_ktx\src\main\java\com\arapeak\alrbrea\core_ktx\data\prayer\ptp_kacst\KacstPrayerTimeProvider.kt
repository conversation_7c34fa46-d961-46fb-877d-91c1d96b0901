package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst

import android.location.Location
import com.arapeak.alrbrea.core_ktx.data.prayer.PrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper.MadehabMapper
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper.PrayerTimeMapper
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper.toKacstMethod
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.Prayers
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.arapeak.alrbrea.core_ktx.model.prayer.Prayer
import com.arapeak.alrbrea.core_ktx.model.prayer.PrayerEnum
import com.batoulapps.adhan2.Madhab
import timber.log.Timber
import java.util.Calendar

class KacstPrayerTimeProvider : PrayerTimeProvider() {
    override fun getPrayerTime(location: Location, calculationMethod: CalculationMethod, madhab: Madhab, date: Calendar): DayPrayers {
        val year = date.get(Calendar.YEAR)
        val month = date.get(Calendar.MONTH) + 1
        val day = date.get(Calendar.DAY_OF_MONTH)


        val lat = location.latitude
        val long = location.longitude

//        runTest(year, month, day, lat, long)


        val prayerTimes = Prayers.getPrayerTimes(
            year,
            month,
            day,
            lat * 0.017453292519943295,
            - (long * 0.017453292519943295),
            PrayerTimeType.getPrayerTimeType(
                calculationMethod.toKacstMethod(),
                MadehabMapper().map(madhab)
            )
        )


        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, PrayerTimeMapper().map(prayerTimes.Fajer)),
            dhuhr = Prayer(PrayerEnum.dhuhr, PrayerTimeMapper().map(prayerTimes.Zohar)),
            asr = Prayer(PrayerEnum.asr, PrayerTimeMapper().map(prayerTimes.Aser)),
            maghrib = Prayer(PrayerEnum.maghrib, PrayerTimeMapper().map(prayerTimes.Magreb)),
            isha = Prayer(PrayerEnum.isha, PrayerTimeMapper().map(prayerTimes.Isha)),
            sunrise = Prayer(PrayerEnum.sunrise, PrayerTimeMapper().map(prayerTimes.Sunrise)),
            eid = Prayer(PrayerEnum.eid, PrayerTimeMapper().map(prayerTimes.Eid)),
        )





        return dayPrayers

    }

    private fun runTest(year: Int, month: Int, day: Int, lat: Double, long: Double) {

        val methods = listOf(
            PrayerTimeType.METHOD_ID_UMMALQURA,
            PrayerTimeType.METHOD_ID_EGYPTIAN_GENERAL,
            PrayerTimeType.METHOD_ID_MUSLIM_WORLD_LEAGUE,
            PrayerTimeType.METHOD_ID_ISLAMIC_SOCITY_OF_NORTH_AMERICA,
            PrayerTimeType.METHOD_ID_MINISTRY_OF_AWQAF_AND_ISLAMIC_AFFAIRS,
            PrayerTimeType.METHOD_ID_UNIVERSITY_OF_ISLAMIC_SCIENCES,
            PrayerTimeType.METHOD_ID_UNION_OF_ISLAMIC_ORGANAIZATIONS,
        )
        val madhabs = listOf(
            PrayerTimeType.ASER_ID_SHAFI,
//            PrayerTimeType.ASER_ID_HANAFI
        )


        val fajrAngles = listOf(
            18.0
        )
        val ishaAngles = listOf(
            18.0
        )

        methods.forEach { m ->
            madhabs.forEach { d ->
//                fajrAngles.forEach { f ->
//                    ishaAngles.forEach { i ->

                extracted(
                    year,
                    month,
                    day,
                    lat,
                    long,
                    m,
                    d,
//                    f,
//                    i
                )

//            }
//            }
            }
        }
    }

    private fun extracted(year: Int, month: Int, day: Int, lat: Double, long: Double, method: Int, madhab: Int) {
        val prayerTimes = Prayers.getPrayerTimes(
            year,
            month,
            day,
            lat * 0.017453292519943295,
            - (long * 0.017453292519943295),
            PrayerTimeType.getPrayerTimeType(
                method,
                madhab
            ).apply {
                this.IshaAngle = 18.0
                this.FajarAngle = 18.0
            },

            )
        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, PrayerTimeMapper().map(prayerTimes.Fajer)),
            dhuhr = Prayer(PrayerEnum.dhuhr, PrayerTimeMapper().map(prayerTimes.Zohar)),
            asr = Prayer(PrayerEnum.asr, PrayerTimeMapper().map(prayerTimes.Aser)),
            maghrib = Prayer(PrayerEnum.maghrib, PrayerTimeMapper().map(prayerTimes.Magreb)),
            isha = Prayer(PrayerEnum.isha, PrayerTimeMapper().map(prayerTimes.Isha)),
            sunrise = Prayer(PrayerEnum.sunrise, PrayerTimeMapper().map(prayerTimes.Sunrise)),
            eid = Prayer(PrayerEnum.eid, PrayerTimeMapper().map(prayerTimes.Eid)),
        )

        Timber.tag("TestPray").i(" Kacst   Madhab : " + madhab + " ||  method : " + method)
        Timber.tag("TestPray").i(" Kacst " + dayPrayers.toString())
        Timber.tag("TestPray").i(" Kacst " + "\n")
    }
}
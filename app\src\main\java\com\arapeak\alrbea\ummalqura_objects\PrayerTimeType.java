package com.arapeak.alrbea.ummalqura_objects;

import java.util.Calendar;

public class PrayerTimeType {
    //    public static final int ASER_ID_HANAFI = 0;
//    public static final int ASER_ID_SHAFI = 1;
//    public static final int METHOD_ID_EGYPTIAN_GENERAL = 2;
//    public static final int METHOD_ID_ISLAMIC_SOCITY_OF_NORTH_AMERICA = 4;
//    public static final int METHOD_ID_MINISTRY_OF_AWQAF_AND_ISLAMIC_AFFAIRS = 6;
//    public static final int METHOD_ID_MUSLIM_WORLD_LEAGUE = 1;
//    public static final int METHOD_ID_UMMALQURA = 0;
//    public static final int METHOD_ID_UNION_OF_ISLAMIC_ORGANAIZATIONS = 5;
//    public static final int METHOD_ID_UNIVERSITY_OF_ISLAMIC_SCIENCES = 3;
    public double Aser;
    public double EidAngle;
    public double FajarAngle;
    public double HeightDiffEast;
    public double HeightDiffWest;
    public double IshaAngle;
    public double IshaFixedSunset;
    public double RabitaSuggestedAngle;
    public double SafetyTime;
    public double TimeZone;

    public static PrayerTimeType DefaultUmmulQuraTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.32288591161895097d;
        prayerTimeType.IshaAngle = CoreVars.TJ2000;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 1.5d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultMuslimWorldLeagueTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.3141592653589793d;
        prayerTimeType.IshaAngle = 0.29670597283903605d;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = CoreVars.TJ2000;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultEgyptianGeneralTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.34033920413889424d;
        prayerTimeType.IshaAngle = 0.30543261909900765d;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = CoreVars.TJ2000;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultUniversityOfIslamicSciencesTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.3141592653589793d;
        prayerTimeType.IshaAngle = 0.3141592653589793d;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = CoreVars.TJ2000;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultIslamicSocietyOfNorthAmericaTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.2617993877991494d;
        prayerTimeType.IshaAngle = 0.2617993877991494d;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = CoreVars.TJ2000;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultUnionOfIslamicOrganaizationsTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.20943951023931956d;
        prayerTimeType.IshaAngle = 0.20943951023931956d;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = CoreVars.TJ2000;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultMinistryOfAwqafAndIslamicAffairsTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = CoreVars.TJ2000;
        prayerTimeType.HeightDiffWest = CoreVars.TJ2000;
        prayerTimeType.FajarAngle = 0.3141592653589793d;
        prayerTimeType.IshaAngle = 0.30543261909900765d;
        prayerTimeType.TimeZone = (((double) Calendar.getInstance().getTimeZone().getRawOffset()) / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = CoreVars.TJ2000;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType getPrayerTimeType(int i) {
        switch (i) {
            case 0:
                return DefaultUmmulQuraTimeType();
            case 1:
                return DefaultMuslimWorldLeagueTimeType();
            case 2:
                return DefaultEgyptianGeneralTimeType();
            case 3:
                return DefaultUniversityOfIslamicSciencesTimeType();
            case 4:
                return DefaultIslamicSocietyOfNorthAmericaTimeType();
            case 5:
                return DefaultUnionOfIslamicOrganaizationsTimeType();
            case 6:
                return DefaultMinistryOfAwqafAndIslamicAffairsTimeType();
            default:
                return DefaultUmmulQuraTimeType();
        }
    }

    /*
    public static PrayerTimeType getPrayerTimeType() {
        PrayerTimeType result = null;
        switch (HawkSettings.getCurrentPrayerMethod()) {
            case muslim_world_league:
                result = DefaultMuslimWorldLeagueTimeType();
                break;
            case egyptian_general_authority_of_survey:
                result = DefaultEgyptianGeneralTimeType();
                break;
            case university_of_islamic_sciences_karachi:
                result = DefaultUniversityOfIslamicSciencesTimeType();
                break;
            case islamic_society_of_north_america:
                result = DefaultIslamicSocietyOfNorthAmericaTimeType();
                break;
            case union_organization_islamic_de_france:
                result = DefaultUnionOfIslamicOrganaizationsTimeType();
                break;
            case kuwait:
                result = DefaultMinistryOfAwqafAndIslamicAffairsTimeType();
                break;
            default:
                result = DefaultUmmulQuraTimeType();
                break;
        }
//        result.Aser = 1;
        return result;
        /*switch (Utils.getCurrentPrayerMethod()) {
            case PrayerMethod.custom_calendar:
                return DefaultUmmulQuraTimeType();
            case 1:
                return DefaultMuslimWorldLeagueTimeType();
            case 2:
                return DefaultEgyptianGeneralTimeType();
            case 3:
                return DefaultUniversityOfIslamicSciencesTimeType();
            case 4:
                return DefaultIslamicSocietyOfNorthAmericaTimeType();
            case 5:
                return DefaultUnionOfIslamicOrganaizationsTimeType();
            case 6:
                return DefaultMinistryOfAwqafAndIslamicAffairsTimeType();
            default:
                return DefaultUmmulQuraTimeType();
        }


    }

     */


    public static PrayerTimeType getPrayerTimeType(int i, int i2) {
        PrayerTimeType prayerTimeType = getPrayerTimeType(i);
        prayerTimeType.Aser = i2;
        return prayerTimeType;
    }
}

package com.arapeak.alrbrea.core_ktx.ui.utils

import android.content.SharedPreferences
import android.content.SharedPreferences.Editor


fun Editor.putDouble(key: String?, value: Double): Editor {
    return putLong(key, java.lang.Double.doubleToRawLongBits(value))
}

fun SharedPreferences.getDouble(key: String?, defaultValue: Double): Double {
    return java.lang.Double.longBitsToDouble(getLong(key, java.lang.Double.doubleToLongBits(defaultValue)))
}
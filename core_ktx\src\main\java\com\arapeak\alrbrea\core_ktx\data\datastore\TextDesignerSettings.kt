package com.arapeak.alrbrea.core_ktx.data.datastore

import android.content.Context
import android.graphics.Color
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignFontEnum
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignPosition
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignToggleEnum
import com.arapeak.alrbrea.core_ktx.ui.utils.getDouble
import com.arapeak.alrbrea.core_ktx.ui.utils.putDouble

class TextDesignerSettings() {
    private val prefsFileName = "settings_ktx"

    private val prefix = "text_design_"

    //Keys
    private val toggleKey = "${prefix}toggle"
    private val fontKey = "${prefix}font"
    private val colorKey = "${prefix}color"
    private val textKey = "${prefix}text"
    private val sizeKey = "${prefix}size"
    private val showDuringPrayersKey = "${prefix}show_during_prayers"
    private val positionXKey = "${prefix}position_x"
    private val positionYKey = "${prefix}position_y"
    private val twoLinesKey = "${prefix}two_lines"

    //Defaults
    private val toggleDefault = TextDesignToggleEnum.Disabled
    private val fontDefault = TextDesignFontEnum.Kufi
    private val textDefault = ""
    private val sizeDefault = 6
    private val showDuringPrayersDefault = false
    private val colorDefault = Color.DKGRAY
    private val positionXDefault = 9999999.0
    private val positionYDefault = 9999999.0
    private val twoLinesDefault = false

    fun resetAll(context: Context) {
        setText(context, textDefault)
        setText(context, textDefault, true)

        setToggle(context, toggleDefault)
        setIsTwoLines(context, twoLinesDefault)

        setFont(context, fontDefault)
        setFont(context, fontDefault, true)

        setShowDuringPrayers(context, showDuringPrayersDefault)

        setColor(context, colorDefault)
        setColor(context, colorDefault, true)

        setPosition(context, TextDesignPosition(positionXDefault, positionYDefault))
        setPosition(context, TextDesignPosition(positionXDefault, positionYDefault), true)

        setSize(context, sizeDefault)
        setSize(context, sizeDefault, true)
    }

    fun getToggle(context: Context): TextDesignToggleEnum {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(toggleKey, toggleDefault.ordinal)

        return TextDesignToggleEnum.entries[res]

    }

    fun setToggle(context: Context, toggle: TextDesignToggleEnum) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(toggleKey, toggle.ordinal)
            apply()
        }
    }

    fun isTwoLines(context: Context): Boolean {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getBoolean(twoLinesKey, twoLinesDefault)

        return res

    }

    fun setIsTwoLines(context: Context, toggle: Boolean) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putBoolean(twoLinesKey, toggle)
            apply()
        }
    }


    fun getFont(context: Context, secondLine: Boolean = false): TextDesignFontEnum {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(if (secondLine) "${fontKey}_2" else fontKey, fontDefault.ordinal)

        return TextDesignFontEnum.entries[modeInt]
    }

    fun setFont(context: Context, font: TextDesignFontEnum, secondLine: Boolean = false) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(if (secondLine) "${fontKey}_2" else fontKey, font.ordinal)
            apply()
        }
    }


    fun getColor(context: Context, secondLine: Boolean = false): Int {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(if (secondLine) "${colorKey}_2" else colorKey, colorDefault)

        return modeInt
    }

    fun setColor(context: Context, color: Int, secondLine: Boolean = false) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(if (secondLine) "${colorKey}_2" else colorKey, color)
            apply()
        }
    }


    fun getShowDuringPrayers(context: Context): Boolean {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getBoolean(showDuringPrayersKey, showDuringPrayersDefault)

        return modeInt
    }

    fun setShowDuringPrayers(context: Context, p: Boolean) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putBoolean(showDuringPrayersKey, p)
            apply()
        }
    }


    fun getText(context: Context, secondLine: Boolean = false): String {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getString(if (secondLine) "${textKey}_2" else textKey, textDefault) ?: textDefault

        return modeInt
    }

    fun setText(context: Context, txt: String, secondLine: Boolean = false) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putString(if (secondLine) "${textKey}_2" else textKey, txt)
            apply()
        }
    }


    fun getSize(context: Context, secondLine: Boolean = false): Int {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(if (secondLine) "${sizeKey}_2" else sizeKey, sizeDefault)
        return modeInt
    }

    fun setSize(context: Context, size: Int, secondLine: Boolean = false) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(if (secondLine) "${sizeKey}_2" else sizeKey, size)
            apply()
        }
    }

    fun getPosition(context: Context, secondLine: Boolean = false): TextDesignPosition {
        val x = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getDouble(if (secondLine) "${positionXKey}_2" else positionXKey, positionXDefault)

        val y = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getDouble(if (secondLine) "${positionYKey}_2" else positionYKey, positionYDefault)

        return TextDesignPosition(x, y)
    }

    fun setPosition(context: Context, p: TextDesignPosition, secondLine: Boolean = false) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putDouble(if (secondLine) "${positionXKey}_2" else positionXKey, p.x)
            putDouble(if (secondLine) "${positionYKey}_2" else positionYKey, p.y)
            apply()
        }
    }


}
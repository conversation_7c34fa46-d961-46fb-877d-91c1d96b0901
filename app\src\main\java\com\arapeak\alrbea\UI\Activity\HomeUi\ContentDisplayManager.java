//package com.arapeak.alrbea.UI.Activity.HomeUi;
//import android.app.Activity;
//import android.graphics.Bitmap;
//import android.graphics.BitmapFactory;
//import android.graphics.Color;
//import android.graphics.PorterDuff;
//import android.net.Uri;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import androidx.palette.graphics.Palette;
//import com.arapeak.alrbea.AnnouncementMessage;
//import com.arapeak.alrbea.Enum.AnnouncementType;
//import com.arapeak.alrbea.Enum.AthkarType;
//import com.arapeak.alrbea.Enum.PrayerType;
//import com.arapeak.alrbea.Model.PhotoGallery;
//import com.arapeak.alrbea.R;
//import com.arapeak.alrbea.UI.Activity.HomeUi.MainLayoutManager;
//import com.arapeak.alrbea.UI.AnnouncementManager; // Keep this manager for announcement logic
//import com.arapeak.alrbea.UI.Margin;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbea.hawk.HawkSettings;
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//import com.arapeak.alrbrea.core_ktx.ui.textdesign.TextDesignUiManager;
//
//import org.sufficientlysecure.rootcommands.util.Log;
//
//import java.io.File;
//
//// This class manages displaying specific content types (Announcements, Athkar, Photo Gallery)
//public class ContentDisplayManager {
//
//    private Activity activity;
//    private MainLayoutManager mainLayoutManager;
//    private AnnouncementManager announcementManager;
//    private TextDesignUiManager textDesignUiManager; // Assuming a dedicated text design manager
//
//    // UI elements directly managed by this class
//    private ViewGroup prayerTimeContainer, announcementContainer, athkarContainer;
//    private ImageView athkarImageView, athkarIcon;
//    private TextView athkarTimeTextView, athkarTextView; // athkarTextView was remainingPrayer_TextView_MainActivity_
//    private LinearLayout athkarTime; // layout_time_athkar
//
//    private boolean isShowingAthkar = false;
//    private boolean isShowingAnouncement = false;
//    private boolean isShowingPhotoGallery = false;
//    private int lastDisplayedGalleryPhoto = -1;
//
//    public ContentDisplayManager(Activity activity, MainLayoutManager mainLayoutManager, TextDesignUiManager textDesignUiManager) {
//        this.activity = activity;
//        this.mainLayoutManager = mainLayoutManager;
//        this.textDesignUiManager = textDesignUiManager;
//        initViews();
//        this.announcementManager = new AnnouncementManager(activity); // Initialize here
//    }
//
//    private void initViews() {
//        ViewGroup container = mainLayoutManager.getContainerLinearLayout();
//        prayerTimeContainer = container.findViewById(R.id.prayerTimeItem_include_MainActivity);
//        announcementContainer = container.findViewById(R.id.announcement_include_MainActivity);
//        athkarContainer = container.findViewById(R.id.layout_athkar);
//
//        athkarImageView = athkarContainer.findViewById(R.id.iv_athkar);
//        athkarTimeTextView = athkarContainer.findViewById(R.id.tv_athkar_time);
//        athkarIcon = athkarContainer.findViewById(R.id.iv_icon);
//        athkarTime = athkarContainer.findViewById(R.id.layout_time_athkar);
//        athkarTextView = athkarContainer.findViewById(R.id.remainingPrayer_TextView_MainActivity_);
//    }
//
//    public void displayPrayerTimes() {
//        // Revert to main prayer times view
//        Utils.setVisibility(prayerTimeContainer, View.VISIBLE);
//        Utils.setVisibility(announcementContainer, View.GONE);
//        Utils.setVisibility(athkarContainer, View.GONE);
//        isShowingAthkar = false;
//        isShowingAnouncement = false;
//        isShowingPhotoGallery = false;
//        if (announcementManager != null) announcementManager.onStop(); // Stop announcement specific logic
//        showTextDesign();
//    }
//
//    public void displayAnnouncement(AnnouncementMessage message) {
//        Utils.setVisibility(prayerTimeContainer, View.GONE);
//        Utils.setVisibility(athkarContainer, View.GONE);
//        Utils.setVisibility(announcementContainer, View.VISIBLE);
//
//        isShowingAnouncement = true;
//        isShowingAthkar = false;
//        isShowingPhotoGallery = false;
//
//        // Determine if text design should be hidden during prayers/azan
//        if (!textDesignUiManager.shouldShowDuringPrayers(activity)) {
//            hideTextDesign();
//        }
//
//        if (announcementManager != null) {
//            announcementManager.viewMessage(message);
//        }
//    }
//
//    public void displayAthkar(AthkarType athkarType, PrayerType prayerType) {
//        Utils.setVisibility(prayerTimeContainer, View.GONE);
//        Utils.setVisibility(announcementContainer, View.GONE);
//        Utils.setVisibility(athkarContainer, View.VISIBLE);
//
//        isShowingAthkar = true;
//        isShowingAnouncement = false;
//        isShowingPhotoGallery = false;
//
//        hideTextDesign(); // Hide text design for athkar
//
//        Utils.setScaleType(athkarImageView, ImageView.ScaleType.FIT_XY);
//        Utils.setVisibility(athkarTimeTextView, HawkSettings.getEnableTimeWithAzkar() ? View.VISIBLE : View.GONE);
//
//        loadAthkarsColors(athkarType);
//        Utils.setMargins(athkarTime, new Margin()); // Reset margins for athkar time layout
//
//        switch (athkarType) {
//            case MorningAthkar:
//                Utils.setImage(athkarImageView, getMorningAthkarImageFile());
//                break;
//            case EveningAthkar:
//                Utils.setImage(athkarImageView, getEveningAthkarImageFile());
//                break;
//            case AfterPrayer:
//                Utils.setImage(athkarImageView, getPrayerAthkarImageFile(prayerType));
//                break;
//        }
//        // Need to update athkarTextView with the specific athkar text.
//        // This requires the athkar list and current index to be passed here or fetched.
//        // For simplicity, assuming athkarTextView is set by a background loop or a specific call.
//        // E.g., `updateAthkarText(String text)`
//    }
//
//    // Call this from the athkarUpdaterRunnable in BackgroundTaskManager
//    public void updateAthkarText(String athkarText) {
//        Utils.setText(athkarTextView, athkarText);
//    }
//
//
//    public void displayPhotoGallery(PhotoGallery gallery) {
//        if (gallery == null || gallery.images == null || gallery.images.isEmpty()) {
//            return;
//        }
//
//        Utils.setVisibility(prayerTimeContainer, View.GONE);
//        Utils.setVisibility(announcementContainer, View.GONE);
//        Utils.setVisibility(athkarContainer, View.VISIBLE); // Using same container
//
//        isShowingPhotoGallery = true;
//        isShowingAthkar = false;
//        isShowingAnouncement = false;
//
//        hideTextDesign(); // Hide text design for photo gallery
//
//        if (lastDisplayedGalleryPhoto < 0 || lastDisplayedGalleryPhoto >= gallery.images.size())
//            lastDisplayedGalleryPhoto = 0;
//
//        File file = new File(gallery.images.get(lastDisplayedGalleryPhoto).imageUri);
//        while (lastDisplayedGalleryPhoto < gallery.images.size() && !file.exists()) {
//            Log.w("ContentDisplayManager", "Gallery image file not found: " + gallery.images.get(lastDisplayedGalleryPhoto).imageUri);
//            lastDisplayedGalleryPhoto++;
//            if (lastDisplayedGalleryPhoto < gallery.images.size()) {
//                file = new File(gallery.images.get(lastDisplayedGalleryPhoto).imageUri);
//            }
//        }
//
//        if (lastDisplayedGalleryPhoto >= gallery.images.size() || !file.exists()) {
//            Log.e("ContentDisplayManager", "No valid photo gallery images found to display.");
//            // Potentially revert to prayer times or show an error
//            displayPrayerTimes();
//            return;
//        }
//
//        Utils.setImage(athkarImageView, file.getAbsolutePath());
//        setBackgroundColorFromImageResource(athkarImageView, file.getAbsolutePath()); // Apply dominant color as background
//
//        // Adjust athkar time layout for gallery
//        Margin m = new Margin();
//        m.t = activity.getResources().getDimensionPixelOffset(com.intuit.sdp.R.dimen._minus26sdp);
//        Utils.setMargins(athkarTime, m);
//        athkarTime.setBackgroundColor(Color.TRANSPARENT); // No background for gallery time
//        Utils.setVisibility(athkarTimeTextView, View.GONE); // Hide time text for gallery, or make it configurable
//
//        Utils.setScaleType(athkarImageView, ImageView.ScaleType.FIT_CENTER);
//        lastDisplayedGalleryPhoto++; // Prepare for next photo
//    }
//
//    private void loadAthkarsColors(AthkarType type) {
//        Utils.setVisibility(athkarIcon, View.VISIBLE); // Ensure icon is visible for athkar
//        athkarIcon.getLayoutParams().width = activity.getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._60sdp);
//        athkarIcon.setScaleType(ImageView.ScaleType.FIT_XY);
//        athkarIcon.requestLayout();
//
//        switch (HawkSettings.getCurrentAzkarTheme()) {
//            case FIRST:
//                switch (type) {
//                    case MorningAthkar:
//                        athkarTime.setBackgroundResource(R.color.theme1_time_layout_morning);
//                        athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.white));
//                        break;
//                    case EveningAthkar:
//                        athkarTime.setBackgroundResource(R.color.theme1_time_layout_evening);
//                        athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.white));
//                        break;
//                    case AfterPrayer:
//                        athkarTime.setBackgroundResource(R.color.theme1_time_layout_afterPrayer);
//                        athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.theme1_time_text_afterPrayer));
//                        break;
//                }
//                break;
//            case SECOND:
//                athkarTime.setBackgroundResource(R.color.theme2_time_layout);
//                athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.theme2_time_text));
//                break;
//            case THIRD:
//                if (type == AthkarType.EveningAthkar) {
//                    athkarTime.setBackgroundResource(R.color.theme3_time_layout_evening);
//                    athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.theme3_time_text_evening));
//                } else {
//                    athkarTime.setBackgroundResource(R.color.theme3_time_layout);
//                    athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.theme3_time_text));
//                }
//                break;
//            case FOURTH:
//                athkarTime.setBackgroundResource(R.color.athkar_fourth_background);
//                athkarTimeTextView.setTextColor(activity.getResources().getColor(R.color.athkar_fourth_text));
//                break;
//        }
//    }
//
//    private void setBackgroundColorFromImageResource(ImageView view, String imagePath) {
//        try {
//            File imgFile = new File(imagePath);
//            if (!imgFile.exists()) return;
//
//            Bitmap bitmap = BitmapFactory.decodeFile(imgFile.getPath());
//            if (bitmap != null) {
//                Palette p = Palette.from(bitmap).generate();
//                Palette.Swatch s = p.getDominantSwatch();
//                if (s != null) {
//                    view.setBackgroundColor(s.getRgb());
//                } else {
//                    view.setBackgroundColor(Color.WHITE);
//                }
//                bitmap.recycle(); // Important to recycle
//            }
//        } catch (Exception e) {
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    private File getMorningAthkarImageFile() {
//        boolean isLandscape = mainLayoutManager.isLandscape();
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "athkarm" + (isLandscape ? "l" : "") + ".png");
//    }
//
//    private File getEveningAthkarImageFile() {
//        boolean isLandscape = mainLayoutManager.isLandscape();
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "athkare" + (isLandscape ? "l" : "") + ".png");
//    }
//
//    private File getAfterPrayer1AthkarImageFile() {
//        boolean isLandscape = mainLayoutManager.isLandscape();
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "allwhite" + (isLandscape ? "" : "h") + ".png");
//    }
//
//    private File getAfterPrayer2AthkarImageFile() {
//        boolean isLandscape = mainLayoutManager.isLandscape();
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "fmwhite" + (isLandscape ? "" : "h") + ".png");
//    }
//
//    private File getPrayerAthkarImageFile(PrayerType prayerType) {
//        switch (prayerType) {
//            case Fajr:
//            case Maghrib:
//                return getAfterPrayer2AthkarImageFile();
//            default:
//                return getAfterPrayer1AthkarImageFile();
//        }
//    }
//
//    public void showTextDesign() {
//        if (textDesignUiManager != null) {
//            textDesignUiManager.showTextDesign(activity);
//        }
//    }
//
//    public void hideTextDesign() {
//        if (textDesignUiManager != null) {
//            textDesignUiManager.hideTextDesign(activity);
//        }
//    }
//}
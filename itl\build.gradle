apply plugin: 'java'

version = '0.7.0'
sourceCompatibility = "1.8"
targetCompatibility = "1.8"

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
}

task copyToApp(type: Copy) {
    from jar
    into project(':app').file('libs')
}

task dist(type: Zip) {
    from jar
    from file('../COPYING')
    from file('../AUTHORS')
    from file('../README.md')
    from file('../README.orig.md')
}

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.dao

import androidx.room.Dao
import androidx.room.Query
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model.PrayerTimeAdenDbEntity

@Dao
interface PrayerTimeAdenDAO {
    @Query("SELECT * FROM Aden_prayers")
    fun getAll(): List<PrayerTimeAdenDbEntity>

    @Query("SELECT * FROM Aden_prayers WHERE date LIKE :arg LIMIT 1")
    fun findByDate(arg: String): PrayerTimeAdenDbEntity

}
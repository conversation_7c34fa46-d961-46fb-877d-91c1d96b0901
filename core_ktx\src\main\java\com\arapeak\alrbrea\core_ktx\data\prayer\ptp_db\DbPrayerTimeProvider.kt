package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db

import android.content.Context
import android.location.Location
import androidx.room.Room
import com.arapeak.alrbrea.core_ktx.data.prayer.PrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.db.PrayerTimesDB
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.mapper.toCalendar
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model.PrayerTimeKuwaitDbEntity
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.arapeak.alrbrea.core_ktx.model.prayer.Prayer
import com.arapeak.alrbrea.core_ktx.model.prayer.PrayerEnum
import com.arapeak.alrbrea.core_ktx.ui.utils.threePlaces
import com.batoulapps.adhan2.Madhab
import kotlinx.datetime.toJavaLocalTime
import timber.log.Timber
import java.util.Calendar

class DbPrayerTimeProvider(context: Context) : PrayerTimeProvider() {

    val db: PrayerTimesDB = Room.databaseBuilder(
        context.applicationContext,
        PrayerTimesDB::class.java, "prayerTimes"
    )
        .createFromAsset("prayerTimes.db")
        .fallbackToDestructiveMigration()
        .build()

    override fun getPrayerTime(location: Location, calculationMethod: CalculationMethod, madhab: Madhab, date: Calendar): DayPrayers {
        return DayPrayers()
    }

    fun getPrayerTimeKuwait(location: Location, date: Calendar): DayPrayers {

        val day = date.get(Calendar.DAY_OF_MONTH)
        val month = date.get(Calendar.MONTH) + 1

        val pt = db.prayerTimeKuwaitDao().findByDate("$month/$day/24")
        val offset = getOffset(location)


//        test(pt, getOffset(Location("test").apply {
//            latitude = 29.076944
//            longitude = 48.083889
//        }))

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, pt.getFajrTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            dhuhr = Prayer(PrayerEnum.dhuhr, pt.getDhurTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            asr = Prayer(PrayerEnum.asr, pt.getAsrTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            maghrib = Prayer(PrayerEnum.maghrib, pt.getMaghrebTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            isha = Prayer(PrayerEnum.isha, pt.getIshaTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            sunrise = Prayer(PrayerEnum.sunrise, pt.getSunriseTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            eid = Prayer(PrayerEnum.eid, Calendar.getInstance()),
        )


        return dayPrayers

    }

    fun getPrayerTimeAden(location: Location, date: Calendar): DayPrayers {

        val day = date.get(Calendar.DAY_OF_MONTH)
        val month = date.get(Calendar.MONTH) + 1

        val dayZ = if(day < 10) "0$day" else day
        val monthZ = if(month < 10) "0$month" else month
        val pt = db.prayerTimeAdenDao().findByDate("$monthZ/$dayZ/2025")




//        test(pt, getOffset(Location("test").apply {
//            latitude = 29.076944
//            longitude = 48.083889
//        }))

        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, pt.getFajrTime().toJavaLocalTime().toCalendar()),
            dhuhr = Prayer(PrayerEnum.dhuhr, pt.getDhurTime().toJavaLocalTime().toCalendar()),
            asr = Prayer(PrayerEnum.asr, pt.getAsrTime().toJavaLocalTime().toCalendar()),
            maghrib = Prayer(PrayerEnum.maghrib, pt.getMaghrebTime().toJavaLocalTime().toCalendar()),
            isha = Prayer(PrayerEnum.isha, pt.getIshaTime().toJavaLocalTime().toCalendar()),
            sunrise = Prayer(PrayerEnum.sunrise, pt.getSunriseTime().toJavaLocalTime().toCalendar()),
            eid = Prayer(PrayerEnum.eid, Calendar.getInstance()),
        )


        return dayPrayers

    }



    private fun test(pt: PrayerTimeKuwaitDbEntity, offset: Int) {


        val dayPrayers = DayPrayers(
            fajr = Prayer(PrayerEnum.fajr, pt.getFajrTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            dhuhr = Prayer(PrayerEnum.dhuhr, pt.getDhurTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            asr = Prayer(PrayerEnum.asr, pt.getAsrTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            maghrib = Prayer(PrayerEnum.maghrib, pt.getMaghrebTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            isha = Prayer(PrayerEnum.isha, pt.getIshaTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            sunrise = Prayer(PrayerEnum.sunrise, pt.getSunriseTime().toJavaLocalTime().plusMinutes(offset.toLong()).toCalendar()),
            eid = Prayer(PrayerEnum.eid, Calendar.getInstance()),
        )

        Timber.tag("TestK").e(offset.toString())
        Timber.tag("TestK").e(dayPrayers.toString())

    }

    private fun getOffset(location: Location): Int {
        val offsets = db.prayerOffsetKuwaitDAO().getAll()
        val res = offsets.filter {
            location.latitude.threePlaces() == it.lat?.threePlaces() && location.longitude.threePlaces() == it.lon?.threePlaces()
        }
        return res.firstOrNull()?.offsetMinutes ?: 0
    }


}
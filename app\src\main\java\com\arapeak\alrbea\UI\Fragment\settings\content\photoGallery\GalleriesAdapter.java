package com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery;

import static com.arapeak.alrbea.hawk.HawkConstants.SELECTED_GALLERY_ID;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class GalleriesAdapter extends RecyclerView.Adapter<GalleriesAdapter.ViewHolder> {

    public Listener listener;
    ArrayList<PhotoGallery> galleries = new ArrayList<>();

    public GalleriesAdapter(Listener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.galleries_layout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setGallery(galleries.get(position));
    }

    public void setGalleries(ArrayList<PhotoGallery> galleries) {
        this.galleries = galleries;
        notifyDataSetChanged();
    }

    public void removeGallery(int index) {
        this.galleries.remove(index);
        notifyItemRemoved(index);
    }

    public void addGallery(PhotoGallery gallery) {
        galleries.add(gallery);
        notifyItemInserted(galleries.size());
    }

    public void clearGalleries() {
        galleries.clear();
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return galleries.size();
    }

    public interface Listener {
        void loadGallery(PhotoGallery gallery);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final LinearLayout layout;
        private final TextView galleryName;
        private final TextView imagesCount;
        private final TextView galleryType;
        private final RadioButton enabledSwitch;

        private PhotoGallery gallery = null;

        public ViewHolder(View view) {
            super(view);
            layout = view.findViewById(R.id.linear_layout_gallery_container);
            galleryName = view.findViewById(R.id.gallery_name);
            imagesCount = view.findViewById(R.id.gallery_image_count);
            galleryType = view.findViewById(R.id.gallery_image_type);
            enabledSwitch = view.findViewById(R.id.gallery_enabled_switch);
            enabledSwitch.setOnClickListener(l -> {
                if (isEnabled())
                    Hawk.delete(SELECTED_GALLERY_ID);
                else
                    Hawk.put(SELECTED_GALLERY_ID, gallery.id);
                itemView.post(() -> {
                    GalleriesAdapter adapter = getAdapter();
                    if (adapter != null) adapter.notifyDataSetChanged();
                });
            });
            layout.setOnClickListener(l -> {
                GalleriesAdapter adapter = (GalleriesAdapter) getBindingAdapter();
                if (adapter != null)
                    adapter.listener.loadGallery(gallery);
            });
        }

        private GalleriesAdapter getAdapter() {
            return (GalleriesAdapter) getBindingAdapter();
        }

        public PhotoGallery getGallery() {
            return gallery;
        }

        public void setGallery(@NonNull PhotoGallery gallery) {
            this.gallery = gallery;
            loadData();
        }

        public void loadData() {
            galleryName.setText(gallery.name);
            imagesCount.setText("" + gallery.images.size());
            enabledSwitch.setChecked(isEnabled());
            enabledSwitch.setText(isEnabled() ? Utils.getString(R.string.enabled) : Utils.getString(R.string.not_enabled));
        }

        public boolean isEnabled() {
            return gallery.id == HawkSettings.getSelectedPhotoGalleryId();
        }
    }

}

package com.arapeak.alrbea.UI.Fragment.settings.content.athkar.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_ASER_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_ASER_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_DHUHR_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_FAJER_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_FAJER_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_ISHA_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_ISHA_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_JOMAA_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ATHKARS_AFTER_MAGHRIB_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class AthkarsAfterPrayerFragment extends AlrabeeaTimesFragment implements SettingsAdapterCallback {

    private static final String TAG = "AthkarsAfterPrayerFragment";

    private View athkarsAfterPrayerView;
    private RecyclerView settingItemRecyclerView;

    private MainSettingsAdapter mainSettingsAdapter;

    public AthkarsAfterPrayerFragment() {

    }

    public static AthkarsAfterPrayerFragment newInstance() {
        return new AthkarsAfterPrayerFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        athkarsAfterPrayerView = inflater.inflate(R.layout.fragment_athkars_after_prayer, container, false);

        initView();
        setParameter();
        setAction();

        return athkarsAfterPrayerView;
    }

    private void initView() {
        settingItemRecyclerView = athkarsAfterPrayerView.findViewById(R.id.settingItem_RecyclerView_AthkarsAfterPrayerFragment);

        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);
    }

    private void setParameter() {
//        if (Utils.isLandscape()){
//            SettingsLandscapeActivity.setTextTite(getString(R.string.athkars_after_prayer));
//        }else {
//            SettingsActivity.setTextTite(getString(R.string.athkars_after_prayer));
//        }
        SettingsActivity.setTextTite(getString(R.string.athkars_after_prayer));

        settingItemRecyclerView.setAdapter(mainSettingsAdapter);

        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.fajr_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , /*getString(R.string.show_athkar_after_prayer)+ "\n" +*/ getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_AFTER_FAJER_PRAYER_KEY, ATHKARS_AFTER_FAJER_PRAYER_DEFAULT)
                , false);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_DEFAULT));

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.dhuhr_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , /*getString(R.string.show_athkar_after_prayer)+ "\n" +*/ getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_AFTER_DHUHR_PRAYER_KEY, ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT)
                , false);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT));

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.asr_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , /*getString(R.string.show_athkar_after_prayer) + "\n" +*/ getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_AFTER_ASER_PRAYER_KEY, ATHKARS_AFTER_ASER_PRAYER_DEFAULT)
                , false);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_DEFAULT));

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.maghrib_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , /*getString(R.string.show_athkar_after_prayer)+ "\n" +*/ getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT)
                , false);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT));

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.isha_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , /*getString(R.string.show_athkar_after_prayer)+ "\n" + */getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_AFTER_ISHA_PRAYER_KEY, ATHKARS_AFTER_ISHA_PRAYER_DEFAULT)
                , false);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_DEFAULT));

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.jomaa_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , /*getString(R.string.show_athkar_after_prayer)+ "\n" +*/ getString(R.string.duration_of_after_prayer)
                , Hawk.get(ATHKARS_AFTER_JOMAA_PRAYER_KEY, ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT)
                , false);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY, IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT));

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);


    }

    private void setAction() {

    }

    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = mainSettingsAdapter.getItem(position);
        if (settingItemRecyclerView == null) {
            return;
        }

        boolean isEnable = subPosition == 1;

        switch (position) {
            case 0:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.delete(ATHKARS_AFTER_FAJER_PRAYER_KEY);
                    } else {
                        Hawk.put(ATHKARS_AFTER_FAJER_PRAYER_KEY, ATHKARS_AFTER_FAJER_PRAYER_DEFAULT);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }

                    return;
                }
                Hawk.put(ATHKARS_AFTER_FAJER_PRAYER_KEY, subPosition);

                break;
            case 1:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.delete(ATHKARS_AFTER_DHUHR_PRAYER_KEY);
                    } else {
                        Hawk.put(ATHKARS_AFTER_DHUHR_PRAYER_KEY, ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }

                    return;
                }
                Hawk.put(ATHKARS_AFTER_DHUHR_PRAYER_KEY, subPosition);

                break;
            case 2:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.delete(ATHKARS_AFTER_ASER_PRAYER_KEY);
                    } else {
                        Hawk.put(ATHKARS_AFTER_ASER_PRAYER_KEY, ATHKARS_AFTER_ASER_PRAYER_DEFAULT);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }

                    return;
                }
                Hawk.put(ATHKARS_AFTER_ASER_PRAYER_KEY, subPosition);

                break;
            case 3:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.delete(ATHKARS_AFTER_MAGHRIB_PRAYER_KEY);
                    } else {
                        Hawk.put(ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }

                    return;
                }
                Hawk.put(ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, subPosition);

                break;
            case 4:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.delete(ATHKARS_AFTER_ISHA_PRAYER_KEY);
                    } else {
                        Hawk.put(ATHKARS_AFTER_ISHA_PRAYER_KEY, ATHKARS_AFTER_ISHA_PRAYER_DEFAULT);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }

                    return;
                }
                Hawk.put(ATHKARS_AFTER_ISHA_PRAYER_KEY, subPosition);

                break;

            case 5:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.delete(ATHKARS_AFTER_JOMAA_PRAYER_KEY);
                    } else {
                        Hawk.put(ATHKARS_AFTER_JOMAA_PRAYER_KEY, ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }

                    return;
                }
                Hawk.put(ATHKARS_AFTER_JOMAA_PRAYER_KEY, subPosition);

                break;
        }
    }
}

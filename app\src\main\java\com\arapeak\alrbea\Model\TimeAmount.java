package com.arapeak.alrbea.Model;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.HOURS_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SECONDS_MILLI_SECOND;

import android.util.Log;


public class TimeAmount {
    public int seconds = 0;
    public int minutes = 0;
    public int hours = 0;

    public TimeAmount(int hours, int minutes, int seconds) {
        this.seconds = seconds;
        this.minutes = minutes;
        this.hours = hours;
    }

    public TimeAmount() {
    }

    public long getTime() {
        Log.i("time amount :", "hours: " + hours + " minutes: " + minutes + " seconds: " + seconds);
        return hours * HOURS_MILLI_SECOND + minutes * MINUTES_MILLI_SECOND + seconds * SECONDS_MILLI_SECOND;
    }
}

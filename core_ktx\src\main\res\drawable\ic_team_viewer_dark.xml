<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="36"
    android:viewportHeight="36">
  <path
      android:pathData="m29,36h-22c-3.9,0 -7,-3.1 -7,-7v-22c0,-3.9 3.1,-7 7,-7h22c3.9,0 7,3.1 7,7v22c0,3.9 -3.1,7 -7,7z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18"
          android:startY="0.459"
          android:endX="18"
          android:endY="40.991"
          android:type="linear">
        <item android:offset="0" android:color="#2D3438"/>
        <item android:offset="1" android:color="#0B0F12"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="m18,34c-8.8,0 -16,-7.2 -16,-16 0,-8.8 7.2,-16 16,-16 8.8,0 16,7.2 16,16 0,8.8 -7.2,16 -16,16z"
      android:strokeAlpha="0.1"
      android:fillAlpha="0.1">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="18"
          android:centerY="18"
          android:gradientRadius="16"
          android:type="radial">
        <item android:offset="0.671" android:color="#FF000000"/>
        <item android:offset="1" android:color="#00000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="m18,32c-7.7,0 -14,-6.3 -14,-14 0,-7.7 6.3,-14 14,-14 7.7,0 14,6.3 14,14 0,7.7 -6.3,14 -14,14z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="m31,18l-9,-5 1,3h-10l1,-3 -9,5 9,5 -1,-3h10l-1,3z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="31"
          android:startY="18"
          android:endX="5"
          android:endY="18"
          android:type="linear">
        <item android:offset="0" android:color="#131516"/>
        <item android:offset="1" android:color="#03131F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>

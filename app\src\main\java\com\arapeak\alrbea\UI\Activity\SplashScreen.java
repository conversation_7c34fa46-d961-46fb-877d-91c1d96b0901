package com.arapeak.alrbea.UI.Activity;


import static com.arapeak.alrbea.APIs.ConstantsOfApp.APP_FIREBASE_STORAGE_CUSTOM_ICONS;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.PACKAGE_INSTALLING_APK_REQUEST_CODE;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.PACKAGE_INSTALL_PERMISSION_REQUEST_CODE;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.Enum.AzkarTheme;
import com.arapeak.alrbea.Model.InfoOfCode;
import com.arapeak.alrbea.Model.PremiumUserModel;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.data.network.model.LatestVersionResponse;
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppCheckState;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.AppUpdater;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.DownloadRequest;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.DownloadResult;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.Downloader;
import com.google.android.gms.tasks.Task;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.storage.FileDownloadTask;
import com.google.firebase.storage.FirebaseStorage;
import com.google.firebase.storage.ListResult;
import com.google.firebase.storage.StorageReference;
import com.orhanobut.hawk.Hawk;

import org.sufficientlysecure.rootcommands.Shell;
import org.sufficientlysecure.rootcommands.command.SimpleCommand;

import java.io.File;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import kotlin.Unit;
import pub.devrel.easypermissions.EasyPermissions;

/**
 * @noinspection t
 */
public class SplashScreen extends BaseAppCompatActivity implements EasyPermissions.PermissionCallbacks {
    public static AtomicBoolean overlayPermissionResultReturned = new AtomicBoolean(true);
    public static AtomicBoolean packagePermissionResultReturned = new AtomicBoolean(false);
    public static AtomicBoolean packageInstallerResultReturned = new AtomicBoolean(false);
    public static AtomicBoolean downloadCompleted = new AtomicBoolean(false);
    public static AtomicBoolean screenActive = new AtomicBoolean(false);
    FirebaseStorage storage;
    int systemCounter = 0;
    int canDrawOverlaysCounter = 0;
    Dialog packagePermissionDialog = null;
    boolean mainStarted = false;

    volatile int downloadCount = 0;
    volatile int failedCount = 0;
    List<DownloadRequest> downloads = new ArrayList<>();
    private TextView textView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.splashscreen);

        textView = findViewById(R.id.dateNow_TextView_MainActivity12);
        textView.setText(Utils.getString(this, R.string.initializing));
        storage = FirebaseStorage.getInstance();

        Log.e("Splash", "onCreate");

        checkPermissions();

        Resources resources = getResources();
        Configuration config = resources.getConfiguration();

        if (config.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Log.e("Orientatiob", "SplashScreen Portrait");
        } else if (config.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.e("Orientatiob", "SplashScreen LANDSCAPE");
        }

        trustAll();

    }

    private void trustAll() {
        // Create a trust manager that accepts all certificates
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new java.security.cert.X509Certificate[]{};
                    }

                    public void checkClientTrusted(
                            java.security.cert.X509Certificate[] chain, String authType) {
                    }


                    public void checkServerTrusted(
                            java.security.cert.X509Certificate[] chain, String authType) {
                    }

                }
        };

        // Install the trust manager
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public void restartActivity() {
        Intent intent = new Intent(this, SplashScreen.class);
        startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        screenActive.set(true);
        Log.e("updater", "on resume" + screenActive.toString());

        Log.e("Splash", "onResume");
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.e("updater", "on onPause" + screenActive.toString());

    }

    void alert() {
        Toast.makeText(this, "الرجاء منح الصلاحيات الضرورية ليعمل التطبيق بصورة سليمة", Toast.LENGTH_LONG).show();
    }

    /**
     * @noinspection t
     */
    private void checkPermissions() {
        ActivityResultLauncher<Intent> permissionActivityResultLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    Log.e("Splash", "ActivityResultLauncher " + result.getResultCode());
                    if (result.getResultCode() == Activity.RESULT_OK)
                        checkPermissions();
                    else {
                        Log.e("Splash", "checkPermissions  finish");
                        finish();
                        restartActivity();
                    }
                });
        Log.e("Splash", "checkPermissions");

        //Rockchip Devices wont use this permissions
        if (!AppController.isRockchipDevice1 && !Settings.System.canWrite(this) && systemCounter++ < 3) {
            alert();
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS, Uri.parse("package:" + getPackageName()));
                permissionActivityResultLauncher.launch(intent);
//                startActivity(intent);
                return;
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
        if (!AppController.isRockchipDevice1 && !Settings.canDrawOverlays(this) && canDrawOverlaysCounter++ < 3) {
            alert();
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + getPackageName()));
                permissionActivityResultLauncher.launch(intent);
//                startActivity(intent);
                return;
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && !getPackageManager().canRequestPackageInstalls()) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
            intent.setData(Uri.parse(String.format("package:%s", getPackageName())));
            permissionActivityResultLauncher.launch(intent);
            return;
//            startActivityForResult(intent, 1234);
        }
        Log.i("Splash Settings.System.canWrite: ", "" + Settings.System.canWrite(this));
        Log.i("Splash Settings.canDrawOverlays: ", " " + Settings.canDrawOverlays(this));

        String[] perms;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            perms = new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.READ_MEDIA_AUDIO,
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.QUERY_ALL_PACKAGES,
                    Manifest.permission.CAMERA};
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            perms = new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.QUERY_ALL_PACKAGES,
                    Manifest.permission.CAMERA};
        } else {
            perms = new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.CAMERA};
        }
        if (EasyPermissions.hasPermissions(this, perms)) {
//            main.start();
            onPermissionsGranted(0, new ArrayList<>());
        } else
            EasyPermissions.requestPermissions(this, "الرجاء منح الصلاحيات الضرورية ليعمل التطبيق بصورة سليمة", 1, perms);


    }

    public void runOnUiSafe(Runnable run) {
        runOnUiThread(() -> {
            try {
                run.run();
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
//        airLocation.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PACKAGE_INSTALL_PERMISSION_REQUEST_CODE) {
            packagePermissionDialog.dismiss();
            packagePermissionResultReturned.set(true);
        } else if (requestCode == PACKAGE_INSTALLING_APK_REQUEST_CODE) {
            packageInstallerResultReturned.set(true);
        } else if (requestCode == 11)
            overlayPermissionResultReturned.set(true);
    }

    /**
     * @noinspection t
     */
    void mainNew() {
        if (mainStarted) return;
        mainStarted = true;
        Thread main = new Thread(() -> {
            try {
                InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);
                if (infoOfCode == null || infoOfCode.getCode().isEmpty()) {
                    //check if internet connected
                    if (!isConnected()) {
                        setText(textView, Utils.getString(this, R.string.no_internet));
                        startActivityForResult(new Intent(Settings.ACTION_WIFI_SETTINGS), 0);
                        while (!isConnected())
                            Thread.sleep(1000);
                    }
                } else {
                    updatePremiumSettings();
                }

                checkAzkarDownloaded();

                AppUpdater updater = new AppUpdater();
                LatestVersionResponse update = updater.checkForUpdate(this, state -> {
                    if (state instanceof AppCheckState.Checking) {
                        runOnUiSafe(() -> textView.setText(Utils.getString(this, R.string.checking_app_version)));
                    }

                    if (state instanceof AppCheckState.Failed) {
                        Log.e("d", "Failed");
                    }

                    if (state instanceof AppCheckState.UpToDate) {
                        Log.e("d", "UpToDate");

                    }

                    if (state instanceof AppCheckState.Outdated) {
                        Log.e("d", "Outdated");

                    }
                });

                if (update != null) {
                    downloads.add(
                            new DownloadRequest(
                                    0,
                                    Objects.requireNonNull(update.getLink()),
                                    "update.apk",
                                    () -> {
                                        Log.e("d", "22");
                                        return Unit.INSTANCE;
                                    }
                            )
                    );
                }

//                checkSupportApksNew();

                Downloader downloader = new Downloader(this);
                Set<DownloadResult> files = downloader.downloadBulk(downloads, loading -> {
                    if (loading >= 0) {
                        setText(textView, Utils.getStringwithArg(this, R.string.downloading_update, loading));
                    } else {
                        setText(textView, Utils.getStringwithArg(this, R.string.downloading_update, 0));
                    }
                    return Unit.INSTANCE;
                });

                final int[] i = {0};
                files.forEach(file -> {
                    i[0]++;
                    setText(textView, Utils.getStringwithArg(this, R.string.installing_update, i[0], files.size()));
                    installApk(file.getFilePath());
                    file.component2().invoke();
                });


                safeRunSeparate(() -> {

                    runOnUiSafe(() -> {
                        Intent mainClass = new Intent(SplashScreen.this, InitialSetupActivity.class);
                        startActivity(mainClass);
                        finish();
                    });

                });

            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
                runOnUiSafe(() -> textView.setText(Utils.getString(this, R.string.exception)));
            }
        });
        main.start();
    }

    /**
     * @noinspection t
     */


    private void installApk(String apkPath) {
        if (Utils.hasRootPermission())
            installApkWithRoot(apkPath);
        else
            installApkWithoutRoot(apkPath);
    }

    private void installApkWithRoot(String apkPath) {
        try {
            Shell shell = Shell.startRootShell();
            String runCmd = "am start -n com.alrbea.prayer/com.arapeak.alrbea.ui.Activity.SplashScreen";
            SimpleCommand command = new SimpleCommand("pm install -r " + apkPath + " && " + runCmd);
            shell.add(command).waitForFinish();
            shell.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void installApkWithoutRoot(String apkPath) {
        Intent intent = getIntentForApkInstall(apkPath);
        try {
            Log.i("installAPk", "install apk : " + apkPath);

            startActivity(intent);
            Log.i("installAPk", "install " + apkPath);

            screenActive.set(false);
            while (!screenActive.get()) {
                Log.i("installAPk", "waiting to come back after install " + apkPath);

                Thread.sleep(1000);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private Intent getIntentForApkInstall(String apkPath) {
        File apkFile = new File(apkPath);
        Uri apkUri = FileProvider.getUriForFile(this, getApplicationContext().getPackageName() + ".fileprovider", apkFile);
        Intent intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
        intent.setData(apkUri);
        intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        return intent;
    }

    private void updatePremiumSettings() {
        runOnUiSafe(() -> textView.setText(Utils.getString(getBaseContext(), R.string.downloading_file)));
        try {
            if (!isConnected())
                return;
            InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);
            DatabaseReference databaseRef = FirebaseDatabase.getInstance().getReference().child(ConstantsOfApp.APP_FIREBASE_DATABASE_PREMIUM_DEVICES);
            StorageReference storageRef = FirebaseStorage.getInstance().getReference().child(APP_FIREBASE_STORAGE_CUSTOM_ICONS);
            PremiumUserModel oldUser = HawkSettings.getPremiumUser();
            File oldFile = HawkSettings.getPremiumUserCustomIcon();

            Task<DataSnapshot> valTask = databaseRef.child(infoOfCode.getCode()).get();
            if (!waitForTask(valTask, MINUTES_MILLI_SECOND)) return;

            PremiumUserModel user = valTask.getResult().getValue(PremiumUserModel.class);
            HawkSettings.setPremiumUser(user);

            if (user == null || oldUser == null || !user.customIconUrl.contentEquals(oldUser.customIconUrl)) {
                HawkSettings.deletePremiumUserCustomIcon();
            }

            if (user == null || user.iconRemoved || user.customIconUrl.isEmpty()) return;

            Task<ListResult> imagesTask = storageRef.child(user.userCode).listAll();
            if (!waitForTask(imagesTask, MINUTES_MILLI_SECOND)) return;
            ListResult images = imagesTask.getResult();
            if (images.getItems().size() == 0) return;

            StorageReference imageRef = images.getItems().get(0);
            File file = new File(getApplicationInfo().dataDir, imageRef.getName());
            FileDownloadTask downloadTask = imageRef.getFile(file);
            if (!waitForTask(downloadTask, MINUTES_MILLI_SECOND)) return;
            HawkSettings.setPremiumUserCustomIcon(imageRef.getName());

//            downloadFileSync(TEAM_VIEWER_QUICK_SUPPORT_URL, Environment.getDataDirectory() + "", TEAM_VIEWER_QUICK_SUPPORT_FILENAME);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        } finally {
            runOnUiSafe(() -> textView.setText(Utils.getString(getBaseContext(), R.string.download_completed)));
        }
    }

    private boolean waitForTask(Task task, long timeout) {
        try {
            long timePass = 0;
            while (!task.isComplete() && timePass < timeout) {
                Thread.sleep(100);
                timePass += 100;
            }
            return task.isSuccessful();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return false;
    }

    private void checkAzkarDownloaded() {

        AtomicBoolean isCompleted = new AtomicBoolean(false);
        AzkarTheme athkarTheme = HawkSettings.getCurrentAzkarTheme();
        if (!athkarTheme.isDownloaded()) {

            StorageReference listRef = FirebaseStorage.getInstance().getReference().child(ConstantsOfApp.AzkarRef + athkarTheme.ordinal());
            while (!isCompleted.get() || failedCount != 0) {
                failedCount = 0;
                isCompleted.set(false);
                listRef.listAll().addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        List<StorageReference> items = task.getResult().getItems();
                        downloadCount = items.size();
                        for (StorageReference item : items) {
                            File file = new File(athkarTheme.getDir(), item.getName());
                            Utils.createFile(file);
                            item.getFile(file).addOnCompleteListener(task1 -> {
                                downloadCount--;
                                if (!task1.isSuccessful()) failedCount++;
                                if (downloadCount == 0) isCompleted.set(true);
                            });
                        }
                    } else {
                        isCompleted.set(true);
                        failedCount = 1;
                    }
                });

                while (!isCompleted.get() ) {
                    try {
                        Thread.sleep(500);
                    } catch (Exception e) {
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                }
            }

        }
    }

    public boolean isConnected() {
        try {
            ConnectivityManager cm = (ConnectivityManager) AppController.baseContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) return false;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network network = cm.getActiveNetwork();
                if (network == null) return false;

                NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
                return capabilities != null &&
                        (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET));
            } else {
                NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
                return activeNetwork != null && activeNetwork.isConnected();
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        airLocation.onRequestPermissionsResult(requestCode, permissions, grantResults);
        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }

    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {
        mainNew();
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
//        finish();
    }

}

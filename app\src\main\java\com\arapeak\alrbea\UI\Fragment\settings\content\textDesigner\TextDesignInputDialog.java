package com.arapeak.alrbea.UI.Fragment.settings.content.textDesigner;

import android.graphics.Point;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.arapeak.alrbea.R;
import com.arapeak.alrbrea.core_ktx.ui.textdesign.TextDesignSettingsManager;

public class TextDesignInputDialog extends DialogFragment {
    String TAG = "inputDialog";
    EditText et;
    Button btnSave, btnDismiss;
    String value = "";
    private TextDesignSettingsManager manager;
    private boolean lineOne;


    private TextDesignInputDialog(TextDesignSettingsManager repo, boolean lineONe) {
        super();
        this.manager = repo;
        this.lineOne = lineONe;
    }

    static TextDesignInputDialog newInstance(TextDesignSettingsManager repo, boolean lineONe) {
        return new TextDesignInputDialog(repo, lineONe);
    }

    void display(FragmentManager fragmentManager) {
        show(fragmentManager, "inputDialog");
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.textdesign_input_dialog, container, false);

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        et = view.findViewById(R.id.et_input);
        btnSave = view.findViewById(R.id.btn_save);
        btnDismiss = view.findViewById(R.id.btn_dismmis);

        btnDismiss.setOnClickListener(v -> {
            this.dismiss();
        });

        value = manager.getRepo().getText(requireContext(), !lineOne);

        disableSave(value);

        btnSave.setOnClickListener(v -> {
            manager.getRepo().updateText(requireContext(), value, !lineOne);
            manager.updateText();
            dismiss();

        });

        et.setText(value, TextView.BufferType.NORMAL);
        et.addTextChangedListener(new TextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                value = s.toString();
                disableSave(value);
            }
        });


    }

    private void disableSave(String value) {
        boolean enabled = !value.trim().isEmpty();
        btnSave.setEnabled(enabled);
        if (!enabled)
            et.setError(getResources().getString(R.string.error_empty_field));
        else
            et.setError(null);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.prayerTimingStyle);
    }


    @Override
    public void onResume() {
        super.onResume();

        Window window = getDialog().getWindow();
        Point size = new Point();

        Display display = window.getWindowManager().getDefaultDisplay();
        display.getSize(size);

        int width = size.x;
        int height = size.y;

        window.setLayout((int) (width * 0.6), (int) (height * 0.6));

        window.setGravity(Gravity.CENTER);

    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/enableDisablePhoto_SwitchCompat_AddPhotoFragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        android:checked="true"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:includeFontPadding="false"
        android:padding="@dimen/_5sdp"
        android:text="@string/enable_disable_events"
        android:textColor="#474747"
        android:textSize="@dimen/dateNowMain"
        app:switchMinWidth="@dimen/_30sdp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/themes_RecyclerView_ThemesFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/_4sdp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:overScrollMode="never"

        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constrainedHeight="true"
        app:reverseLayout="false"
        app:spanCount="1"
        tools:listitem="@layout/list_item_event_view_holder" />

    <Button
        android:id="@+id/btn_add"
        android:layout_width="@dimen/_100sdp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/_10sdp"
        android:background="@drawable/without_corners_50_background_blue"
        android:fontFamily="@font/droid_arabic_kufi"
        android:padding="@dimen/_5sdp"
        android:text="@string/add"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_14sdp" />
</LinearLayout>
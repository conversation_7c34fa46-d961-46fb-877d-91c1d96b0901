package com.arapeak.alrbea.UI.Activity;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.text.SpannableString;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.appcompat.app.AppCompatActivity;

import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.UI.Margin;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.squareup.picasso.Picasso;

import org.sufficientlysecure.rootcommands.Shell;
import org.sufficientlysecure.rootcommands.command.SimpleCommand;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class BaseAppCompatActivity extends AppCompatActivity {

    private static final String TAG = "BaseAppCompatActivity";
    private static final String PREFS_NAME = "base_activity_prefs";
    private static final String SCALE_ADJUSTED_KEY = "scale_adjusted";
    private static final String ROTATION_ADJUSTED_KEY = "rotation_adjusted";

    // Instance variables instead of static to prevent memory leaks
    private boolean isScaleAdjusted = false;
    private boolean isRotationAdjusted = false;

    // Handler management for background operations
    private Handler backgroundHandler;
    private HandlerThread backgroundThread;
    private Handler mainHandler;
    private ExecutorService executorService;

    // Lifecycle management
    private boolean isActivityDestroyed = false;

    @Override
    protected void onCreate(android.os.Bundle savedInstanceState) {
        try {
            Log.d(TAG, "BaseAppCompatActivity onCreate started");
            super.onCreate(savedInstanceState);

            initializeHandlers();
            loadPreferences();

            Log.d(TAG, "BaseAppCompatActivity onCreate completed");
        } catch (Exception e) {
            Log.e(TAG, "Error in BaseAppCompatActivity onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Initialize handlers for background operations
     */
    private void initializeHandlers() {
        try {
            Log.d(TAG, "Initializing handlers");

            // Main thread handler
            mainHandler = new Handler(Looper.getMainLooper());

            // Background thread for heavy operations
            backgroundThread = new HandlerThread("BaseActivity-Background");
            backgroundThread.start();
            backgroundHandler = new Handler(backgroundThread.getLooper());

            // Executor service for concurrent operations
            executorService = Executors.newFixedThreadPool(2);

            Log.d(TAG, "Handlers initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing handlers", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Load preferences for scale and rotation adjustments
     */
    private void loadPreferences() {
        try {
            SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            isScaleAdjusted = prefs.getBoolean(SCALE_ADJUSTED_KEY, false);
            isRotationAdjusted = prefs.getBoolean(ROTATION_ADJUSTED_KEY, false);

            Log.d(TAG, "Preferences loaded - Scale adjusted: " + isScaleAdjusted +
                    ", Rotation adjusted: " + isRotationAdjusted);
        } catch (Exception e) {
            Log.e(TAG, "Error loading preferences", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Save preferences for scale and rotation adjustments
     */
    private void savePreferences() {
        try {
            SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            prefs.edit()
                    .putBoolean(SCALE_ADJUSTED_KEY, isScaleAdjusted)
                    .putBoolean(ROTATION_ADJUSTED_KEY, isRotationAdjusted)
                    .apply();

            Log.d(TAG, "Preferences saved");
        } catch (Exception e) {
            Log.e(TAG, "Error saving preferences", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Execute task in background thread
     * Replaces the problematic startThread method
     */
    public void executeInBackground(Runnable task) {
        if (isActivityDestroyed || backgroundHandler == null || task == null) {
            Log.w(TAG, "Cannot execute background task - activity destroyed or invalid parameters");
            return;
        }

        try {
            backgroundHandler.post(() -> {
                try {
                    task.run();
                } catch (Exception e) {
                    Log.e(TAG, "Error in background task", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error posting background task", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Execute task in background with callback on main thread
     */
    public void executeInBackgroundWithCallback(Runnable backgroundTask, Runnable mainThreadCallback) {
        if (isActivityDestroyed || backgroundHandler == null || mainHandler == null) {
            Log.w(TAG, "Cannot execute background task with callback - activity destroyed or invalid handlers");
            return;
        }

        try {
            backgroundHandler.post(() -> {
                try {
                    if (backgroundTask != null) {
                        backgroundTask.run();
                    }

                    // Execute callback on main thread
                    if (mainThreadCallback != null && !isActivityDestroyed) {
                        mainHandler.post(() -> {
                            try {
                                mainThreadCallback.run();
                            } catch (Exception e) {
                                Log.e(TAG, "Error in main thread callback", e);
                                CrashlyticsUtils.INSTANCE.logException(e);
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in background task with callback", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error posting background task with callback", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Execute task using executor service for concurrent operations
     */
    public void executeInExecutor(Runnable task) {
        if (isActivityDestroyed || executorService == null || task == null) {
            Log.w(TAG, "Cannot execute executor task - activity destroyed or invalid parameters");
            return;
        }

        try {
            executorService.submit(() -> {
                try {
                    task.run();
                } catch (Exception e) {
                    Log.e(TAG, "Error in executor task", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error submitting executor task", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Improved safe UI thread execution with better error handling
     */
    public void safeRunOnUi(Runnable toRun) {
        if (isActivityDestroyed || toRun == null) {
            Log.w(TAG, "Cannot run on UI thread - activity destroyed or null runnable");
            return;
        }

        try {
            runOnUiThread(() -> {
                try {
                    if (!isActivityDestroyed) {
                        toRun.run();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in UI thread runnable", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error posting to UI thread", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Improved safe execution with better error handling
     */
    public void safeRun(Runnable toRun) {
        if (toRun == null) {
            Log.w(TAG, "Cannot run null runnable");
            return;
        }

        try {
            toRun.run();
        } catch (Exception e) {
            Log.e(TAG, "Error in safe run", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Replaced problematic thread creation with background handler
     * 
     * @deprecated Use executeInBackground() instead
     */
    @Deprecated
    public void safeRunSeparate(Runnable toRun) {
        Log.w(TAG, "safeRunSeparate is deprecated, use executeInBackground instead");
        executeInBackground(toRun);
    }

    public void setText(TextView view, String text) {
        if (view != null) {
            safeRunOnUi(() -> view.setText(Utils.replaceNumberWithSettings(text)));
        }
    }

    public void setText(TextView view, SpannableString text) {
        if (view != null) {
            safeRunOnUi(() -> view.setText(text));
        }
    }

    public void setVisibility(View view, int vis) {
        if (view == null)
            return;
        safeRunOnUi(() -> view.setVisibility(vis));
    }

    public void setImage(ImageView iv, String path) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setImageURI(Uri.parse(path)));
    }

    public void setImage(ImageView iv, File path) {
        if (iv != null)
            safeRunOnUi(() -> Picasso.get().load(path).into(iv));
    }

    public void setScaleType(ImageView iv, ImageView.ScaleType type) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setScaleType(type));

    }

    public void setBackgroundColor(View iv, int color) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setBackgroundColor(color));
    }

    public void setBackgroundColor(View iv, String path) {
        if (iv == null || !new File(path).exists())
            return;
        safeRunOnUi(() -> iv.setBackground(Drawable.createFromPath(path)));
    }

    public void setTextColor(TextView tv, int color) {
        if (tv != null) {
            safeRunOnUi(() -> {
                tv.setTextColor(color);
            });
        }
    }

    public void setColorFilter(ImageView iv, int color) {
        if (iv != null) {
            safeRunOnUi(() -> {
                iv.setColorFilter(color, PorterDuff.Mode.SRC_IN);
            });
        }
    }

    public void setSelected(TextView tv) {
        if (tv == null)
            return;
        tv.setSelected(true);
    }

    public void setColorFilterView(View iv, int color) {
        if (iv != null) {
            safeRunOnUi(() -> {
                // iv.setColorFilter(color);
                Drawable bg = iv.getBackground();
                if (bg != null)
                    bg.setColorFilter(color, PorterDuff.Mode.SRC_IN);
            });
        }
    }

    public void setImage(ImageView iv, Bitmap image) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setImageBitmap(image));
    }

    public void setImageResource(ImageView iv, @DrawableRes int res) {
        if (iv != null) {
            safeRunOnUi(() -> iv.setImageResource(res));
        }
    }

    public void scaleTextViewSize(float percent, TextView tv) {
        if (tv == null)
            return;
        tv.setTextSize(tv.getTextSize() * percent);
    }

    public boolean isLandscape() {
        return Utils.isLandscape();
    }

    /**
     * Improved display scale adjustment with comprehensive error handling
     */
    public void adjustDisplayScale() {
        try {
            Log.d(TAG, "Starting display scale adjustment");

            if (isScaleAdjusted) {
                Log.d(TAG, "Scale already adjusted, skipping");
                return;
            }

            WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
            if (wm == null) {
                Log.e(TAG, "WindowManager is null, cannot adjust display scale");
                return;
            }

            Display display = wm.getDefaultDisplay();
            if (display == null) {
                Log.e(TAG, "Display is null, cannot adjust display scale");
                return;
            }

            DisplayMetrics metrics = new DisplayMetrics();
            DisplayMetrics realMetrics = new DisplayMetrics();

            display.getRealMetrics(realMetrics);
            display.getMetrics(metrics);

            int width = Math.min(metrics.widthPixels, metrics.heightPixels);
            int height = Math.max(metrics.widthPixels, metrics.heightPixels);

            Display.Mode mode = display.getMode();
            int physicalWidth = mode.getPhysicalWidth();
            int physicalHeight = mode.getPhysicalHeight();
            int dpi = metrics.densityDpi;

            Log.d(TAG, String.format("Current display: width=%d, height=%d, dpi=%d", width, height, dpi));
            Log.d(TAG, String.format("Physical display: width=%d, height=%d", physicalWidth, physicalHeight));

            if (width == 720 && height == 1280 && dpi == 160) {
                Log.d(TAG, "Display already at target size (720x1280, 160dpi)");
                isScaleAdjusted = true;
                savePreferences();
                return;
            }

            if (AppController.isRooted) {
                adjustScaleWithRoot(physicalWidth, physicalHeight);
            } else {
                Log.d(TAG, "Device not rooted, using local adjustment");
                adjustLocally();
            }

            isScaleAdjusted = true;
            savePreferences();
            Log.d(TAG, "Display scale adjustment completed");

        } catch (Exception e) {
            Log.e(TAG, "Error adjusting display scale", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Fallback to local adjustment
            try {
                Log.d(TAG, "Attempting fallback to local adjustment");
                adjustLocally();
            } catch (Exception fallbackError) {
                Log.e(TAG, "Fallback adjustment also failed", fallbackError);
                CrashlyticsUtils.INSTANCE.logException(fallbackError);
            }
        }
    }

    /**
     * Adjust display scale using root commands with improved error handling
     */
    private void adjustScaleWithRoot(int physicalWidth, int physicalHeight) {
        Shell shell = null;
        try {
            Log.d(TAG, "Adjusting scale with root commands");

            shell = Shell.startRootShell();
            if (shell == null) {
                Log.e(TAG, "Failed to start root shell");
                throw new RuntimeException("Failed to start root shell");
            }

            // Execute su command
            SimpleCommand suCommand = new SimpleCommand("su");
            shell.add(suCommand).waitForFinish();

            if (suCommand.getExitCode() != 0) {
                Log.e(TAG, "Su command failed with exit code: " + suCommand.getExitCode());
                throw new RuntimeException("Su command failed");
            }

            // Determine size command based on orientation
            String sizeCommand;
            if (physicalWidth > physicalHeight) {
                sizeCommand = "wm size 1280x720"; // Landscape
                Log.d(TAG, "Setting landscape size: 1280x720");
            } else {
                sizeCommand = "wm size 720x1280"; // Portrait
                Log.d(TAG, "Setting portrait size: 720x1280");
            }

            // Execute size command
            SimpleCommand wmSizeCommand = new SimpleCommand(sizeCommand);
            shell.add(wmSizeCommand).waitForFinish();

            if (wmSizeCommand.getExitCode() != 0) {
                Log.e(TAG, "WM size command failed with exit code: " + wmSizeCommand.getExitCode());
                throw new RuntimeException("WM size command failed");
            }

            // Execute density command
            SimpleCommand densityCommand = new SimpleCommand("wm density 160");
            shell.add(densityCommand).waitForFinish();

            if (densityCommand.getExitCode() != 0) {
                Log.e(TAG, "WM density command failed with exit code: " + densityCommand.getExitCode());
                throw new RuntimeException("WM density command failed");
            }

            Log.d(TAG, "Root scale adjustment commands executed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error in root scale adjustment", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            throw e; // Re-throw to trigger fallback
        } finally {
            if (shell != null) {
                try {
                    shell.close();
                    Log.d(TAG, "Root shell closed");
                } catch (Exception e) {
                    Log.e(TAG, "Error closing root shell", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            }
        }
    }

    /**
     * Improved rotation adjustment with comprehensive error handling
     */
    private void adjustRotation() {
        try {
            Log.d(TAG, "Starting rotation adjustment");

            if (isRotationAdjusted) {
                Log.d(TAG, "Rotation already adjusted, skipping");
                return;
            }

            WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
            if (wm == null) {
                Log.e(TAG, "WindowManager is null, cannot adjust rotation");
                return;
            }

            Display display = wm.getDefaultDisplay();
            if (display == null) {
                Log.e(TAG, "Display is null, cannot adjust rotation");
                return;
            }

            int screenOrientation = display.getRotation();
            int settingsOrientation = HawkSettings.getAppOrientation();

            Log.d(TAG, "Current screen orientation: " + screenOrientation);
            Log.d(TAG, "Target settings orientation: " + settingsOrientation);

            if (screenOrientation == settingsOrientation) {
                Log.d(TAG, "Orientation already matches settings");
                isRotationAdjusted = true;
                savePreferences();
                return;
            }

            if (AppController.isRooted) {
                adjustRotationWithRoot(settingsOrientation);
            } else {
                Log.d(TAG, "Device not rooted, using orientation enforcer");
                if (AppController.screenOrientationEnforcer != null) {
                    AppController.screenOrientationEnforcer.start();
                } else {
                    Log.w(TAG, "Screen orientation enforcer is null");
                }
            }

            isRotationAdjusted = true;
            savePreferences();
            Log.d(TAG, "Rotation adjustment completed");

        } catch (Exception e) {
            Log.e(TAG, "Error adjusting rotation", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Adjust rotation using root commands with improved error handling
     */
    private void adjustRotationWithRoot(int settingsOrientation) {
        Shell shell = null;
        try {
            Log.d(TAG, "Adjusting rotation with root commands");

            shell = Shell.startRootShell();
            if (shell == null) {
                Log.e(TAG, "Failed to start root shell for rotation adjustment");
                throw new RuntimeException("Failed to start root shell");
            }

            // Execute su command
            SimpleCommand suCommand = new SimpleCommand("su");
            shell.add(suCommand).waitForFinish();

            if (suCommand.getExitCode() != 0) {
                Log.e(TAG, "Su command failed for rotation adjustment");
                throw new RuntimeException("Su command failed");
            }

            // Disable accelerometer rotation
            SimpleCommand accelCommand = new SimpleCommand("settings put system accelerometer_rotation 0");
            shell.add(accelCommand).waitForFinish();

            if (accelCommand.getExitCode() != 0) {
                Log.e(TAG, "Accelerometer rotation command failed");
                throw new RuntimeException("Accelerometer rotation command failed");
            }

            // Set user rotation
            SimpleCommand rotationCommand = new SimpleCommand(
                    "settings put system user_rotation " + settingsOrientation);
            shell.add(rotationCommand).waitForFinish();

            if (rotationCommand.getExitCode() != 0) {
                Log.e(TAG, "User rotation command failed");
                throw new RuntimeException("User rotation command failed");
            }

            Log.d(TAG, "Root rotation adjustment commands executed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error in root rotation adjustment", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            throw e;
        } finally {
            if (shell != null) {
                try {
                    shell.close();
                    Log.d(TAG, "Root shell closed for rotation adjustment");
                } catch (Exception e) {
                    Log.e(TAG, "Error closing root shell for rotation", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            }
        }
    }

    public void adjustLocally() {

        Configuration configuration = getResources().getConfiguration();
        if (configuration == null) {
            return;
        }

        Log.d("TAG", "adjustDisplayScale: " + configuration.densityDpi);
        // configuration.densityDpi = 160; //decrease "display size" by ~30
        if (isLandscape()) {
            configuration.orientation = Configuration.ORIENTATION_LANDSCAPE;
        } else {
            configuration.orientation = Configuration.ORIENTATION_PORTRAIT;

        }
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        WindowManager wm = (WindowManager) getSystemService(WINDOW_SERVICE);
        wm.getDefaultDisplay().getMetrics(metrics);
        configuration.smallestScreenWidthDp = (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE)
                ? metrics.heightPixels
                : metrics.widthPixels;
        configuration.densityDpi = 160;
        configuration.fontScale = 1.15f;

        // if(true)
        // return;
        // metrics.density = 1;
        // metrics.densityDpi = 160;
        // metrics.heightPixels = 1280;
        // metrics.widthPixels= 720;
        // metrics.scaledDensity= 1.15f;
        // metrics.xdpi= 160;
        // metrics.ydpi= 160;

        // configuration.densityDpi = 160;
        // configuration.screenHeightDp = 1256;
        // configuration.screenWidthDp = 720;
        // configuration.smallestScreenWidthDp = 720;

        getResources().getDisplayMetrics().setTo(metrics);
        // this.getResources().updateConfiguration(configuration, metrics);

    }

    public void setMargins(View v, Margin m) {
        if (v != null && v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(m.l, m.t, m.r, m.b);
            v.requestLayout();
        }
    }

    /**
     * Safe spannable text creation with error handling
     */
    public SpannableString getSpannableText(String text) {
        try {
            if (text == null) {
                Log.w(TAG, "Creating spannable text with null input");
                text = "";
            }

            String processedText = Utils.replaceNumberWithSettings(text);
            SpannableString textMono = new SpannableString(processedText);
            // textMono.setSpan(new MonospaceSpan(), 0, textMono.length(), 0);
            return textMono;

        } catch (Exception e) {
            Log.e(TAG, "Error creating spannable text", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // Return simple spannable string as fallback
            return new SpannableString(text != null ? text : "");
        }
    }

    @Override
    protected void onDestroy() {
        try {
            Log.d(TAG, "BaseAppCompatActivity onDestroy started");

            // Mark activity as destroyed to prevent further operations
            isActivityDestroyed = true;

            // Save preferences before cleanup
            savePreferences();

            // Clean up all resources
            cleanupResources();

            Log.d(TAG, "BaseAppCompatActivity onDestroy completed");

        } catch (Exception e) {
            Log.e(TAG, "Error in BaseAppCompatActivity onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        } finally {
            super.onDestroy();
        }
    }

    /**
     * Comprehensive resource cleanup to prevent memory leaks
     */
    private void cleanupResources() {
        try {
            Log.d(TAG, "Starting resource cleanup");

            // Clean up main handler
            if (mainHandler != null) {
                mainHandler.removeCallbacksAndMessages(null);
                mainHandler = null;
                Log.d(TAG, "Main handler cleaned up");
            }

            // Clean up background handler
            if (backgroundHandler != null) {
                backgroundHandler.removeCallbacksAndMessages(null);
                backgroundHandler = null;
                Log.d(TAG, "Background handler cleaned up");
            }

            // Clean up background thread
            if (backgroundThread != null) {
                backgroundThread.quitSafely();
                try {
                    backgroundThread.join(1000); // Wait max 1 second
                    Log.d(TAG, "Background thread joined successfully");
                } catch (InterruptedException e) {
                    Log.w(TAG, "Interrupted while waiting for background thread to finish");
                    Thread.currentThread().interrupt();
                }
                backgroundThread = null;
            }

            // Clean up executor service
            if (executorService != null) {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(2, TimeUnit.SECONDS)) {
                        Log.w(TAG, "Executor service did not terminate gracefully, forcing shutdown");
                        executorService.shutdownNow();
                    }
                    Log.d(TAG, "Executor service cleaned up");
                } catch (InterruptedException e) {
                    Log.w(TAG, "Interrupted while waiting for executor service to terminate");
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
                executorService = null;
            }

            Log.d(TAG, "Resource cleanup completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error during resource cleanup", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Check if activity is destroyed to prevent operations on destroyed activity
     */
    protected boolean isActivityDestroyed() {
        return isActivityDestroyed;
    }

    /**
     * Get background handler for background operations
     */
    protected Handler getBackgroundHandler() {
        return backgroundHandler;
    }

    /**
     * Get main handler for UI operations
     */
    protected Handler getMainHandler() {
        return mainHandler;
    }

}

package com.arapeak.alrbea.UI.Activity;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.SpannableString;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.appcompat.app.AppCompatActivity;

import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.UI.Margin;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.squareup.picasso.Picasso;

import org.sufficientlysecure.rootcommands.Shell;
import org.sufficientlysecure.rootcommands.command.SimpleCommand;

import java.io.File;

public class BaseAppCompatActivity extends AppCompatActivity {

    public static boolean isScaleAdjusted = false;
    public static boolean isRotationAdjusted = false;

    public static void stopThread(Thread thread) {
        try {
            if (thread != null && thread.isAlive()) {
                thread.interrupt();
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public static void startThread(Thread thread) {
        try {
            if (thread != null && thread.getState() == Thread.State.NEW) {
                thread.setPriority(Thread.MAX_PRIORITY);
                thread.start();
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }


    public void safeRunOnUi(Runnable toRun) {
        runOnUiThread(() -> {
            try {
                toRun.run();
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    public void safeRun(Runnable toRun) {
        try {
            toRun.run();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public void safeRunSeparate(Runnable toRun) {
        new Thread(() -> {
            try {
                toRun.run();
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }

        }).start();

    }

    public void setText(TextView view, String text) {
        if (view != null) {
            safeRunOnUi(() -> view.setText(Utils.replaceNumberWithSettings(text)));
        }
    }

    public void setText(TextView view, SpannableString text) {
        if (view != null) {
            safeRunOnUi(() -> view.setText(text));
        }
    }

    public void setVisibility(View view, int vis) {
        if (view == null)
            return;
        safeRunOnUi(() -> view.setVisibility(vis));
    }


    public void setImage(ImageView iv, String path) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setImageURI(Uri.parse(path)));
    }

    public void setImage(ImageView iv, File path) {
        if (iv != null) safeRunOnUi(() -> Picasso.get().load(path).into(iv));
    }

    public void setScaleType(ImageView iv, ImageView.ScaleType type) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setScaleType(type));

    }

    public void setBackgroundColor(View iv, int color) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setBackgroundColor(color));
    }

    public void setBackgroundColor(View iv, String path) {
        if (iv == null || !new File(path).exists())
            return;
        safeRunOnUi(() -> iv.setBackground(Drawable.createFromPath(path)));
    }

    public void setTextColor(TextView tv, int color) {
        if (tv != null) {
            safeRunOnUi(() -> {
                tv.setTextColor(color);
            });
        }
    }

    public void setColorFilter(ImageView iv, int color) {
        if (iv != null) {
            safeRunOnUi(() -> {
                iv.setColorFilter(color, PorterDuff.Mode.SRC_IN);
            });
        }
    }

    public void setSelected(TextView tv) {
        if (tv == null)
            return;
        tv.setSelected(true);
    }

    public void setColorFilterView(View iv, int color) {
        if (iv != null) {
            safeRunOnUi(() -> {
//                iv.setColorFilter(color);
                Drawable bg = iv.getBackground();
                if (bg != null)
                    bg.setColorFilter(color, PorterDuff.Mode.SRC_IN);
            });
        }
    }

    public void setImage(ImageView iv, Bitmap image) {
        if (iv == null)
            return;
        safeRunOnUi(() -> iv.setImageBitmap(image));
    }

    public void setImageResource(ImageView iv, @DrawableRes int res) {
        if (iv != null) {
            safeRunOnUi(() -> iv.setImageResource(res));
        }
    }

    public void scaleTextViewSize(float percent, TextView tv) {
        if (tv == null)
            return;
        tv.setTextSize(tv.getTextSize() * percent);
    }

    public boolean isLandscape() {
        return Utils.isLandscape();
    }

    public void adjustDisplayScale() {
//        adjustRotation();
        WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        DisplayMetrics metrics = new DisplayMetrics();
        DisplayMetrics realMetrics = new DisplayMetrics();
        display.getRealMetrics(realMetrics);
        display.getMetrics(metrics);

        int width = Math.min(metrics.widthPixels, metrics.heightPixels);//safety for orientation
        int height = Math.max(metrics.widthPixels, metrics.heightPixels);

//        int width = metrics.widthPixels;//safety for orientation
//        int height = metrics.heightPixels;

        Display.Mode mode = display.getMode();
        int physicalWidth = mode.getPhysicalWidth();
        int physicalHeight = mode.getPhysicalHeight();
        int dpi = metrics.densityDpi;


        if (width == 720 && height == 1280 && dpi == 160) {
            Log.e("Orientation", "adjustDisplayScale already at the current sizes w720,h1280,dpi160");

            return;//already at the current sizes
        }
//        settings put system user_rotation 3

        if (AppController.isRooted) {
            if (isScaleAdjusted)
                return;
            isScaleAdjusted = true;
            try {
                Shell shell = Shell.startRootShell();
                shell.add(new SimpleCommand("su")).waitForFinish();
                if (physicalWidth > physicalHeight)//landscape device
                    shell.add(new SimpleCommand("wm size 1280x720")).waitForFinish();//wm size WxH
                else
                    shell.add(new SimpleCommand("wm size 720x1280")).waitForFinish();
                shell.add(new SimpleCommand("wm density 160")).waitForFinish();

                //shell.add(new SimpleCommand("am start -a android.intent.action.REBOOT")).waitForFinish();
                shell.close();
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        } else {
            adjustLocally();
        }
//        String path = Environment.getExternalStorageDirectory() + "/out.txt";
//        Uri file = Uri.fromFile(new File(path));
//
//        FirebaseStorage storage = FirebaseStorage.getInstance();
//        StorageReference ourRef = storage.getReference().child("out/"+file.getLastPathSegment());
//        ourRef.putFile(file);


    }

    private void adjustRotation() {

        WindowManager wm = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        int screenOrientation = wm.getDefaultDisplay().getRotation();
        int settingsOrientation = HawkSettings.getAppOrientation();
        Log.i("BaseAppCompactActivity", "WM Orientation: " + screenOrientation);
        Log.i("BaseAppCompactActivity", "App Orientation: " + settingsOrientation);
        if (screenOrientation != settingsOrientation) {
            if (AppController.isRooted) {
                isRotationAdjusted = true;
                try {
                    Shell shell = Shell.startRootShell();
                    shell.add(new SimpleCommand("su")).waitForFinish();
                    shell.add(new SimpleCommand("settings put system accelerometer_rotation 0")).waitForFinish();
                    shell.add(new SimpleCommand("settings put system user_rotation " + settingsOrientation)).waitForFinish();
                    shell.close();
                } catch (Exception e) {
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            } else {
                AppController.screenOrientationEnforcer.start();
            }
        }
    }

    public void adjustLocally() {

        Configuration configuration = getResources().getConfiguration();
        if (configuration == null) {
            return;
        }

        Log.d("TAG", "adjustDisplayScale: " + configuration.densityDpi);
//            configuration.densityDpi = 160; //decrease "display size" by ~30
        if (isLandscape()) {
            configuration.orientation = Configuration.ORIENTATION_LANDSCAPE;
        } else {
            configuration.orientation = Configuration.ORIENTATION_PORTRAIT;

        }
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        WindowManager wm = (WindowManager) getSystemService(WINDOW_SERVICE);
        wm.getDefaultDisplay().getMetrics(metrics);
        configuration.smallestScreenWidthDp = (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) ? metrics.heightPixels : metrics.widthPixels;
        configuration.densityDpi = 160;
        configuration.fontScale = 1.15f;

//            if(true)
//                return;
//            metrics.density = 1;
//            metrics.densityDpi = 160;
//            metrics.heightPixels = 1280;
//            metrics.widthPixels= 720;
//            metrics.scaledDensity= 1.15f;
//            metrics.xdpi= 160;
//            metrics.ydpi= 160;


//            configuration.densityDpi = 160;
//            configuration.screenHeightDp = 1256;
//            configuration.screenWidthDp = 720;
//            configuration.smallestScreenWidthDp = 720;

        getResources().getDisplayMetrics().setTo(metrics);
//            this.getResources().updateConfiguration(configuration, metrics);

    }

    public void setMargins(View v, Margin m) {
        if (v != null && v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(m.l, m.t, m.r, m.b);
            v.requestLayout();
        }
    }

    public SpannableString getSpannableText(String text) {
        SpannableString textMono = new SpannableString(Utils.replaceNumberWithSettings(text));
//        textMono.setSpan(new MonospaceSpan(), 0, textMono.length(), 0);
        return textMono;
    }

}

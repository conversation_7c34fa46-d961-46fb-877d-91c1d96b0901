package com.arapeak.alrbea.Enum;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.CUSTOM_AZAN;

import android.net.Uri;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;

public enum AzanAudio implements IAudio {
    ALERT,
    HACHEM_ELSAKAAF,
    ISLAM_SUBHI,
    AHMED_DOQIRY,
    AHMED_DOQIRY_NORMAL,
    SAID_NUGSHIBANDI,
    ABDALLAH_EL_ZILAY,
    ABDALMAGEED_EL_SERIEHI,
    MOHAMMED_FAROG,
    MUSHARI_EL_AFFASY,
    WADIA_EL_YAMANY,
    CUSTOM;

    AzanAudio() {
    }

    public Uri getUri() {
        return Uri.parse(getPath());
    }

    public String getPath() {
        if (this == CUSTOM)
            return Hawk.get(CUSTOM_AZAN, "");
        return Utils.getDownloadPath() + "/azan/" + this.ordinal();
    }

    public String getURL() {
        switch (this) {
            case ISLAM_SUBHI:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Feslam.mp3?alt=media&token=a9c71a2b-0af0-4a2f-ae3b-c06fddbf6ca9";
            case AHMED_DOQIRY:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fhamedfajer.mp3?alt=media&token=1e31fc00-d76a-427e-8c07-90b1504569b1";
            case AHMED_DOQIRY_NORMAL:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fhameddoghriri.mp3?alt=media&token=ab197491-efeb-41ae-a61e-fd73f86290f9";
            case SAID_NUGSHIBANDI:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fsaid.mp3?alt=media&token=b7e95e14-1035-40f3-b28f-f31c4dad60f2";
            case ABDALLAH_EL_ZILAY:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fabduall.mp3?alt=media&token=143459ef-3fad-4e67-93d6-887b43483eb9";
            case ABDALMAGEED_EL_SERIEHI:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fabdalmjed.mp3?alt=media&token=93041372-9d85-492e-8baa-a6136e3304f9";
            case MOHAMMED_FAROG:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fmohammed.mp3?alt=media&token=83e3a868-cc9f-4347-ac89-2cba5abe610c";
            case MUSHARI_EL_AFFASY:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fmshary.mp3?alt=media&token=dad69d94-9915-46c1-9cce-5ae41204192b";
            case HACHEM_ELSAKAAF:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Fhachemelsaaf.mp3?alt=media&token=abb10324-fc9f-4ece-bcd3-bd84034f0d30";
            case WADIA_EL_YAMANY:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/Azanmp3%2Foadia.mp3?alt=media&token=135e1dce-dabc-46e7-8dbf-f3334937df9c";
            default:
                return "";
        }
    }

    public String getName() {
        if (this == ALERT)
            return Utils.getString(R.string.audio_alert);
        else
            return Utils.getStringArray(R.array.prayer_azan_sound)[ordinal() - 1];
    }

    @Override

    public Boolean isOffline() {
        return this == HACHEM_ELSAKAAF || this == ALERT;
    }

    @Override

    public int getOfflineResource() {
        if (!this.isOffline())
            return 0;

        switch (this) {
            case ALERT:
                return R.raw.a;

            case HACHEM_ELSAKAAF:
                return R.raw.hachemelsaaf;
        }
        return 0;
    }

    @Override
    public int getOrdinal() {
        return ordinal();
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content_LinearLayout_SettingViewHolder"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_200sdp"
    android:orientation="vertical"
    android:paddingBottom="16dp"
    tools:layoutDirection="rtl">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:id="@+id/icon_CardView_SettingViewHolder"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"

            android:layout_margin="10dp"
            android:layout_weight="1"
            android:elevation="0dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/isNew_TextView_SettingViewHolder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/title_TextView_SettingViewHolder"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="2dp"
                    android:layout_marginBottom="2dp"
                    android:foregroundGravity="center"
                    android:gravity="center"
                    android:rotation="6"
                    android:rotationY="19"
                    android:text="@string/new_feature"
                    android:textAlignment="center"
                    android:textColor="#F44336"
                    android:textSize="@dimen/_12sdp"
                    app:drawableEndCompat="@drawable/round_auto_awesome_24"
                    app:drawableTint="#F44336"
                    app:fontFamily="@font/droid_arabic_kufi_bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/title_TextView_SettingViewHolder" />

                <TextView
                    android:id="@+id/title_TextView_SettingViewHolder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/train"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:text="Train"
                    android:textAlignment="center"
                    android:textSize="@dimen/_14sdp"
                    app:fontFamily="@font/droid_arabic_kufi_bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/train" />

                <ImageView
                    android:id="@+id/train"
                    android:layout_width="@dimen/_80sdp"
                    android:layout_height="@dimen/_80sdp"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/_25sdp"
                    app:layout_constraintBottom_toTopOf="@+id/title_TextView_SettingViewHolder"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_background" />
            </RelativeLayout>
        </androidx.cardview.widget.CardView>


    </LinearLayout>
    <!--View
        android:id="@+id/space_View_SettingViewHolder"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="20dp"
        android:background="#D1D1D1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon_CardView_SettingViewHolder" /-->

</LinearLayout>
#!/bin/bash

echo "========================================"
echo "Building Signed APK with Size Optimization"
echo "========================================"

# Check if required environment variables are set
if [ -z "$JAVA_HOME" ]; then
    echo "ERROR: JAVA_HOME environment variable is not set"
    echo "Please set JAVA_HOME to your JDK installation directory"
    exit 1
fi

if [ -z "$ANDROID_HOME" ]; then
    echo "ERROR: ANDROID_HOME environment variable is not set"
    echo "Please set ANDROID_HOME to your Android SDK installation directory"
    exit 1
fi

echo "Using JAVA_HOME: $JAVA_HOME"
echo "Using ANDROID_HOME: $ANDROID_HOME"
echo

# Make gradlew executable
chmod +x gradlew

# Clean previous builds
echo "Cleaning previous builds..."
./gradlew clean
if [ $? -ne 0 ]; then
    echo "ERROR: Clean failed"
    exit 1
fi

echo
echo "========================================"
echo "Building Release APK with Optimizations"
echo "========================================"

# Build the signed release APK
echo "Building signed release APK..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo
echo "========================================"
echo "Build Completed Successfully!"
echo "========================================"

# Find and display APK information
echo
echo "Generated APK files:"
find app/build/outputs/apk/release -name "*.apk" -exec ls -lh {} \;

echo
echo "========================================"
echo "APK Size Analysis"
echo "========================================"

# Create APK analysis report
echo "Generating APK analysis..."
./gradlew :app:analyzeReleaseBundle
if [ $? -ne 0 ]; then
    echo "Warning: APK analysis failed, but APK was built successfully"
fi

echo
echo "========================================"
echo "Additional Size Optimization Tips"
echo "========================================"
echo
echo "1. The APK has been optimized with:"
echo "   - ProGuard code shrinking and obfuscation"
echo "   - Resource shrinking"
echo "   - PNG crunching"
echo "   - Unused resource removal"
echo "   - Native library filtering (ARM only)"
echo "   - Debug log removal"
echo
echo "2. For even smaller size, consider:"
echo "   - Using App Bundle instead of APK"
echo "   - Removing unused dependencies"
echo "   - Converting large images to WebP"
echo "   - Using vector drawables instead of PNGs"
echo
echo "3. APK location: app/build/outputs/apk/release/"
echo

echo "Build completed successfully!"

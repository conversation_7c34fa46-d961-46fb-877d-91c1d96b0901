//package com.arapeak.alrbea.UI.Activity;
//
//import android.util.Log;
//
//import com.arapeak.alrbea.APIs.ConstantsOfApp;
//import com.arapeak.alrbea.Enum.PrayerType;
//import com.arapeak.alrbea.Interface.PrayerTime;
//import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
//import com.arapeak.alrbea.PrayerUtils;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//
///**
// * Helper class for MainActivity Prayer Logic
// * Handles prayer time calculations, next prayer selection, and prayer state
// * management
// */
//public class MainActivityPrayerLogic {
//    private static final String TAG = "MainActivityPrayerLogic";
//    private final MainActivity activity;
//    private final MainActivityPrayerManager prayerManager;
//    private final MainActivityDateUtils dateUtils;
//
//    public MainActivityPrayerLogic(MainActivity activity, MainActivityPrayerManager prayerManager,
//            MainActivityDateUtils dateUtils) {
//        this.activity = activity;
//        this.prayerManager = prayerManager;
//        this.dateUtils = dateUtils;
//    }
//
//    /**
//     * Select next prayer and display with comprehensive error handling
//     */
//    public long selectNextPrayerAndDisplay() {
//        try {
//            Log.d(TAG, "Starting selectNextPrayerAndDisplay...");
//
//            // Ensure prayer times are available
//            if (!prayerManager.arePrayerTimesAvailable()) {
//                Log.w(TAG, "Prayer times not available, attempting to refresh");
//                if (!prayerManager.refreshPrayerTimes()) {
//                    Log.e(TAG, "Failed to refresh prayer times");
//                    return System.currentTimeMillis() + 10000; // Retry in 10 seconds
//                }
//            }
//
//            TimingsAlrabeeaTimes timings = prayerManager.getCurrentPrayerTimes();
//            if (timings == null) {
//                Log.e(TAG, "Prayer timings are null after refresh attempt");
//                return System.currentTimeMillis() + 10000;
//            }
//
//            // Update PrayerTime class with current timings
//            PrayerTime.setCurrentTimings(timings);
//
//            long currentTime = System.currentTimeMillis();
//            long nextEventTime = currentTime + (24 * 60 * 60 * 1000); // Default to next day
//
//            // Check each prayer type for active events
//            for (PrayerType prayerType : PrayerType.values()) {
//                try {
//                    if (!prayerManager.isPrayerTimeValid(prayerType)) {
//                        Log.w(TAG, "Prayer time not valid for " + prayerType);
//                        continue;
//                    }
//
//                    PrayerTime prayerTime = prayerType.prayerTime;
//
//                    // Check for active prayer events
//                    if (prayerTime.isDuringPrayer()) {
//                        Log.d(TAG, "Currently during " + prayerType + " prayer");
//                        activity.displayPrayerState(prayerType, "PRAYER");
//                        long prayerEndTime = prayerTime.getTimeUntilUnlockPrayer();
//                        return Math.min(nextEventTime, prayerEndTime + 1000);
//                    }
//
//                    // Check for athkar time
//                    if (prayerTime.isNowLockingDuringAthkar()) {
//                        Log.d(TAG, "Currently during " + prayerType + " athkar time");
//                        activity.displayPrayerState(prayerType, "ATHKAR");
//                        long athkarEndTime = prayerTime.getTimeUntilUnlockAthkar();
//                        return Math.min(nextEventTime, athkarEndTime + 1000);
//                    }
//
//                    // Check for azan-ikama period
//                    if (prayerTime.isBetweenAzanAndIkama()) {
//                        Log.d(TAG, "Currently between azan and ikama for " + prayerType);
//                        activity.displayPrayerState(prayerType, "BETWEEN_AZAN_IKAMA");
//                        long ikamaTime = prayerTime.getIkamaTime();
//                        return Math.min(nextEventTime, ikamaTime + 1000);
//                    }
//
//                    // Calculate next event time for this prayer
//                    long azanTime = prayerTime.getAzanTime();
//                    if (azanTime > currentTime) {
//                        nextEventTime = Math.min(nextEventTime, azanTime);
//                    }
//
//                } catch (Exception e) {
//                    Log.e(TAG, "Error processing prayer type " + prayerType.toString() + ": " + e.getMessage());
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                    // Continue to next prayer type instead of crashing
//                }
//            }
//
//            // If no specific prayer event is active, display default prayer times
//            activity.displayPrayerTimes();
//
//            Log.d(TAG, "Next event scheduled for: " + Utils.getTimeFormatted(nextEventTime, "HH:mm:ss dd/MM/yyyy"));
//            return nextEventTime;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Critical error in selectNextPrayerAndDisplay: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//            activity.displayPrayerTimes(); // Show default prayer times view
//            return System.currentTimeMillis() + 10000; // Retry in 10 seconds
//        }
//    }
//
//    /**
//     * Get next prayer type
//     */
//    public PrayerType getNextPrayer() {
//        try {
//            long currentTime = System.currentTimeMillis();
//            PrayerType nextPrayer = null;
//            long nextPrayerTime = Long.MAX_VALUE;
//
//            for (PrayerType prayerType : PrayerType.values()) {
//                try {
//                    if (!prayerManager.isPrayerTimeValid(prayerType)) {
//                        continue;
//                    }
//
//                    long azanTime = prayerType.prayerTime.getAzanTime();
//                    if (azanTime > currentTime && azanTime < nextPrayerTime) {
//                        nextPrayerTime = azanTime;
//                        nextPrayer = prayerType;
//                    }
//                } catch (Exception e) {
//                    Log.e(TAG, "Error checking next prayer for " + prayerType + ": " + e.getMessage());
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                }
//            }
//
//            if (nextPrayer == null) {
//                // No prayer found for today, get first prayer of next day
//                nextPrayer = getFirstPrayerOfNextDay();
//            }
//
//            Log.d(TAG, "Next prayer: " + (nextPrayer != null ? nextPrayer.toString() : "null"));
//            return nextPrayer;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error getting next prayer: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return PrayerType.Fajr; // Default fallback
//        }
//    }
//
//    /**
//     * Get first prayer of next day
//     */
//    private PrayerType getFirstPrayerOfNextDay() {
//        try {
//            return PrayerType.Fajr; // First prayer of the day
//        } catch (Exception e) {
//            Log.e(TAG, "Error getting first prayer of next day: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return PrayerType.Fajr;
//        }
//    }
//
//    /**
//     * Check if currently in prayer time
//     */
//    public boolean isCurrentlyInPrayer() {
//        try {
//            for (PrayerType prayerType : PrayerType.values()) {
//                if (prayerManager.isPrayerTimeValid(prayerType) &&
//                        prayerType.prayerTime.isDuringPrayer()) {
//                    return true;
//                }
//            }
//            return false;
//        } catch (Exception e) {
//            Log.e(TAG, "Error checking if currently in prayer: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return false;
//        }
//    }
//
//    /**
//     * Check if currently in athkar time
//     */
//    public boolean isCurrentlyInAthkar() {
//        try {
//            for (PrayerType prayerType : PrayerType.values()) {
//                if (prayerManager.isPrayerTimeValid(prayerType) &&
//                        prayerType.prayerTime.isNowLockingDuringAthkar()) {
//                    return true;
//                }
//            }
//            return false;
//        } catch (Exception e) {
//            Log.e(TAG, "Error checking if currently in athkar: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return false;
//        }
//    }
//
//    /**
//     * Check if currently between azan and ikama
//     */
//    public boolean isCurrentlyBetweenAzanAndIkama() {
//        try {
//            for (PrayerType prayerType : PrayerType.values()) {
//                if (prayerManager.isPrayerTimeValid(prayerType) &&
//                        prayerType.prayerTime.isBetweenAzanAndIkama()) {
//                    return true;
//                }
//            }
//            return false;
//        } catch (Exception e) {
//            Log.e(TAG, "Error checking if between azan and ikama: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return false;
//        }
//    }
//}
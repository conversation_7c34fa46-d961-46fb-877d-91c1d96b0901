<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginStart="30dp"
    android:layout_marginEnd="30dp"
    android:animateLayoutChanges="true"
    tools:context=".UI.Fragment.settings.content.main.content.mainSettings.MainSettingsFragment">

    <ProgressBar
        android:id="@+id/pb_settings_main"
        android:layout_width="match_parent"
        android:indeterminate="true"
        android:padding="@dimen/_80sdp"
        android:layout_height="match_parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/settingItem_RecyclerView_SubSettingsFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"

        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:reverseLayout="false"
        tools:listitem="@layout/layout_list_item_sub_setting" />

</androidx.constraintlayout.widget.ConstraintLayout>
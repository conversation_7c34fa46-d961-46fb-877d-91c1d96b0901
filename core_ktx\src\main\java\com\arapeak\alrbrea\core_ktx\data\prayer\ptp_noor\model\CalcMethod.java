package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class CalcMethod {
    public static final String CityType_1 = "1";
    public static final String CityType_2 = "2";

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: a */
    @Nullable
    private Type f3753a;

    /* renamed from: b, reason: from kotlin metadata */
    private int spinnerIndex;


    public CalcMethod() {
        this.f3753a = Type.UmmAlQuraUniv;
    }


    public CalcMethod(@NotNull Type T) {
        Intrinsics.checkNotNullParameter(T, "T");
        this.f3753a = T;
    }

    @Nullable
    /* renamed from: calenderInt, reason: from getter */
    public final Type getF3753a() {
        return this.f3753a;
    }

    @NotNull
    public final String calenderString() {
        Type type = this.f3753a;

        return type.name();
    }

    public final int getSpinnerIndex() {
        return this.spinnerIndex;
    }

    public final void setSpinnerIndex(int i) {
        this.spinnerIndex = i;
    }

    public final void setCalender(@NotNull Type r2) {
        Intrinsics.checkNotNullParameter(r2, "T");
        this.f3753a = r2;
    }

    public final int toInt_C_SHARB() {
        Type type = this.f3753a;
        if (type == Type.UmmAlQuraUniv) {
            return 3;
        }
        if (type == Type.EgytionGeneralAuthorityofSurvey) {
            return 1;
        }
        if (type == Type.UnivOfIslamicScincesKarachi) {
            return 2;
        }
        if (type == Type.IslamicSocietyOfNorthAmerica) {
            return 4;
        }
        if (type == Type.Turkey) {
            return 5;
        }
        if (type == Type.Custom) {
            return 6;
        }
        return 0;
    }

    @Nullable
    public final Type type() {
        return getF3753a();
    }

    public final void setCalender(int type) {
        this.f3753a = Type.values()[type];
    }

    public final void setCalender(@NotNull String r4) {
        Intrinsics.checkNotNullParameter(r4, "string");
        MyTool.MyLog("aaa1", " m_season-- textViewResultCityExp_Action 00 " + r4);
        try {
            Type type = Type.valueOf(r4);
            this.f3753a = type;
            this.spinnerIndex = toInt_C_SHARB();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        MyTool.MyLog("aaa1", " m_season-- textViewResultCityExp_Action 00 " + r4 + ' ' + this.spinnerIndex + ' ' + this.f3753a);
    }

    public enum Type {
        UmmAlQuraUniv,
        EgytionGeneralAuthorityofSurvey,
        UnivOfIslamicScincesKarachi,
        IslamicSocietyOfNorthAmerica,
        MuslimWorldLeague,
        Turkey,
        Custom
    }

    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        @Nullable
        public final Type getCalcMethod(@NotNull String calcMethod) {
            Intrinsics.checkNotNullParameter(calcMethod, "calcMethod");
            CalcMethod calcMethod2 = new CalcMethod();
            calcMethod2.setCalender(calcMethod);
            return calcMethod2.type();
        }

        public final int getCalcMethodBeforeSave(int calcMethod) {
            switch (calcMethod) {
                case 0:
                    return 0;
                case 1:
                    return 4;
                case 2:
                    return 2;
                case 3:
                default:
                    return 1;
                case 4:
                    return 3;
                case 5:
                    return 5;
                case 6:
                    return 6;
            }
        }

        @NotNull
        public final String getStr4Spinner(int val) {
            if (val != 0) {
                if (val != 1) {
                    if (val != 2) {
                        if (val != 4) {
                            if (val != 5) {
                                if (val != 6) {
                                    return "4";
                                }
                                return "6";
                            }
                            return "5";
                        }
                        return CityType_1;
                    }
                    return "2";
                }
                return "3";
            }
            return "0";
        }
    }
}

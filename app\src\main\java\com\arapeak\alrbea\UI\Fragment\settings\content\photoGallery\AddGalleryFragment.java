package com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery;

import android.app.Activity;
import android.content.ClipData;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbea.Model.PhotoGalleryImage;
import com.arapeak.alrbea.Model.TimeAmount;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.RealPathUtil;
import com.arapeak.alrbea.UI.Activity.BaseAppCompatActivity;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.PhotoGalleryRepository;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.obsez.android.lib.filechooser.ChooserDialog;


public class AddGalleryFragment extends Fragment {
    EditText nameEditText;
    TextView imageResolution;
    LinearLayout uploadButton;
    Button save;
    Button delete;
    PhotoGallery gallery;
    RecyclerView photoRecycler;
    Button expandSetting;
    Button prayerTimesDuration;
    Button singleImageDuration;
    SwitchCompat enabledBetweenAzanAndIkama;
    SwitchCompat enabledBetweenAzanAndIkamaJomaa;
    ViewGroup advancedSettingContainer;
    PhotoGalleryImageAdapter adapter;
    ActivityResultLauncher<String> mGetContent;
    ActivityResultLauncher<Intent> launchSomeActivity;
    private boolean isNew = true;
    private boolean isExpanded = true;

    public AddGalleryFragment(PhotoGallery gallery) {
        if (gallery == null) {
            isNew = true;
            this.gallery = new PhotoGallery();
        } else {
            isNew = false;
            this.gallery = gallery;
        }
    }

    public AddGalleryFragment() {
        isNew = true;
        gallery = new PhotoGallery();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_add_gallery, container, false);


        launchSomeActivity = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() != Activity.RESULT_OK)
                        return;
                    Intent data = result.getData();
                    if (data == null)
                        return;
                    Uri selectedImage = data.getData();
                    if (selectedImage != null) {
                        String path = RealPathUtil.getImagePathFromUri(requireContext(), selectedImage);
                        if (path != null && !path.isEmpty())
                            adapter.addImage(new PhotoGalleryImage(path));
                        return;
                    }
                    ClipData mClipData = data.getClipData();
                    if (mClipData == null)
                        return;
                    for (int i = 0; i < mClipData.getItemCount(); i++) {
                        ClipData.Item item = mClipData.getItemAt(i);
                        Uri uri = item.getUri();
                        String path = RealPathUtil.getRealPath(requireContext(), uri);
                        if (path != null && !path.isEmpty()) {
                            adapter.addImage(new PhotoGalleryImage(path));
                        }
                    }
                });
        initUI(view);
        return view;
    }

    public void pickImage() {
        try {
            Intent intent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            launchSomeActivity.launch(intent);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            new ChooserDialog(requireContext())
                    .withFilter(false, false, "png")
                    .withChosenListener((path, pathFile) -> {
                        adapter.addImage(new PhotoGalleryImage(path));
                    }).build().show();
        }
    }

    public void expandSetting() {
        if (isExpanded) {
            expandSetting.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_baseline_expand_more_24, 0, 0, 0);
            advancedSettingContainer.setVisibility(View.GONE);
        } else {
            expandSetting.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_baseline_expand_less_24, 0, 0, 0);
            advancedSettingContainer.setVisibility(View.VISIBLE);
        }
        isExpanded = !isExpanded;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    private void initUI(View view) {
        nameEditText = view.findViewById(R.id.galleryName_EditText_AddPhotoFragment);
        imageResolution = view.findViewById(R.id.tv_gallery_image_resolution);
        uploadButton = view.findViewById(R.id.uploadPhoto_LinearLayout_AddPhotoFragment);
        save = view.findViewById(R.id.save_Button_AddPhotoFragment);
        delete = view.findViewById(R.id.delete_Button_AddPhotoFragment);
        expandSetting = view.findViewById(R.id.btn_expand_setting);
        expandSetting.setOnClickListener(l -> expandSetting());
        prayerTimesDuration = view.findViewById(R.id.btn_duration_of_prayer_times);
        singleImageDuration = view.findViewById(R.id.btn_duration_of_single_image);
        advancedSettingContainer = view.findViewById(R.id.advanced_setting_container);
        enabledBetweenAzanAndIkama = view.findViewById(R.id.enable_gallery_button_between_azan);
        enabledBetweenAzanAndIkamaJomaa = view.findViewById(R.id.enable_gallery_button_between_azan_jomaa);
        enabledBetweenAzanAndIkama.setChecked(gallery.isEnabledBetweenAzanAndIkama());
        enabledBetweenAzanAndIkamaJomaa.setChecked(gallery.isEnabledBetweenAzanAndIkamaJomaa());
        enabledBetweenAzanAndIkama.setOnClickListener(l -> {
            boolean isEnabled = gallery.isEnabledBetweenAzanAndIkama();
            gallery.setEnabledBetweenAzanAndIkama(!isEnabled);
        });
        enabledBetweenAzanAndIkamaJomaa.setOnClickListener(l -> {
            boolean isEnabled = gallery.isEnabledBetweenAzanAndIkamaJomaa();
            gallery.setEnabledBetweenAzanAndIkamaJomaa(!isEnabled);
        });
        expandSetting();
        photoRecycler = view.findViewById(R.id.photo_RecyclerView_AddPhotoFragment);
//        photoRecycler.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new PhotoGalleryImageAdapter();
        photoRecycler.setAdapter(adapter);
        save.setOnClickListener(i -> save());
        if (!isNew)
            delete.setOnClickListener(i -> delete());
        else
            delete.setVisibility(View.GONE);
//        if (Utils.isLandscape()) {
        if (((BaseAppCompatActivity) getActivity()).isLandscape()) {
            imageResolution.setText(getString(R.string.screen_aspects_land));
        } else {
            imageResolution.setText(getString(R.string.screen_aspects_port));
        }
        uploadButton.setOnClickListener(i -> pickImage());
//        uploadButton.setOnClickListener(i -> mGetContent.launch("image/*"));

        prayerTimesDuration.setOnClickListener(i -> {
            Utils.initGetTimeAmountDialog(getActivity()
                    , 0
                    , getString(R.string.duration_of_prayer_times_minutes)
                    , "تعديل"
                    , gallery.getPrayerTimesDuration()
                    , true
                    , true
                    , new OnSuccessful() {
                        @Override
                        public void onSuccessful(boolean isSuccessful, int a, int b, int d) {
                            if (isSuccessful) {
                                gallery.setPrayerTimesDuration(new TimeAmount(a, b, d));
                            }
                        }
                    });
        });
        singleImageDuration.setOnClickListener(i -> {
            Utils.initGetTimeAmountDialog(getActivity()
                    , 0
                    , getString(R.string.duration_of_photo_gallery_minutes)
                    , "تعديل"
                    , gallery.getSingleImageDuration()
                    , true
                    , true
                    , new OnSuccessful() {
                        @Override
                        public void onSuccessful(boolean isSuccessful, int a, int b, int d) {
                            if (isSuccessful) {
                                gallery.setSingleImageDuration(new TimeAmount(a, b, d));
                            }
                        }
                    });
        });
        loadGallery();
    }

    public void save() {
        gallery.name = nameEditText.getText().toString();
        gallery.images = adapter.getImages();
        if (isNew)
            gallery = PhotoGalleryRepository.insert(gallery);
        else
            gallery = PhotoGalleryRepository.update(gallery);
//        FragmentManager fm = getFragmentManager();
//        if(fm != null)
//            fm.popBackStack();
        requireActivity().onBackPressed();
        //loadGallery();
    }

    public void delete() {
        Utils.initConfirmDialog(
                requireContext(),
                0, Utils.getString(R.string.delete_gallery),
                Utils.getString(R.string.do_you_sure_want_to_delete_the_gallery),
                true,
                true,
                new OnSuccessful() {
                    @Override
                    public void onSuccessful(boolean isSuccessful) {
                        if (isSuccessful) {
                            PhotoGalleryRepository.delete(gallery);
                            requireActivity().onBackPressed();
                        }
                        super.onSuccessful(isSuccessful);
                    }
                }
        );
    }

    public void loadGallery() {
        nameEditText.getText().clear();
        nameEditText.setText(gallery.name);
        adapter.setImages(gallery.images);
    }
}
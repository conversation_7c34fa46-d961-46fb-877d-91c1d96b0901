<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/_20sdp"
    tools:context=".UI.Fragment.settings.content.main.content.designs.content.BackgroundFragment"
    tools:layoutDirection="locale">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/isEnable_CheckBox_BackgroundFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_15sdp"
                android:layout_marginTop="@dimen/_15sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:paddingStart="@dimen/_10sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:text="@string/display_a_logo_behind_the_prayer_table"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_14sdp"
                app:buttonTint="@color/colorPrimary"
                app:layout_constrainedWidth="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/selectBackground_Button_BackgroundFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:paddingStart="@dimen/_15sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_15sdp"
                android:paddingBottom="@dimen/_5sdp"
                android:text="@string/choose_image"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/isEnable_CheckBox_BackgroundFragment"
                app:layout_constraintWidth_percent="0.5" />

            <ImageView
                android:id="@+id/background_ImageView_BackgroundFragment"
                android:layout_width="@dimen/theme_item_width_land"
                android:layout_height="@dimen/theme_item_width_land"
                android:layout_marginTop="@dimen/_5sdp"
                android:scaleType="fitCenter"
                android:visibility="gone" />

            <TextView
                android:id="@+id/transparencyBackground_TextView_BackgroundFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:paddingStart="@dimen/_10sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:text="@string/transparency_percentage"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_14sdp"
                android:visibility="gone" />

            <SeekBar
                android:id="@+id/transparencyBackground_SeekBar_BackgroundFragment"
                android:layout_width="@dimen/_150sdp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_15sdp"
                android:visibility="gone"
                tools:progress="80" />

            <Button
                android:id="@+id/color_Button_BackgroundFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:paddingStart="@dimen/_15sdp"
                android:paddingTop="@dimen/_4sdp"
                android:paddingEnd="@dimen/_10sdp"
                android:paddingBottom="@dimen/_4sdp"
                android:text="@string/pick_color"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/save_Button_BackgroundFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:paddingStart="@dimen/_20sdp"
                android:paddingTop="@dimen/_4sdp"
                android:paddingEnd="@dimen/_20sdp"
                android:paddingBottom="@dimen/_4sdp"
                android:text="@string/save"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />
        </LinearLayout>
    </ScrollView>

</LinearLayout>
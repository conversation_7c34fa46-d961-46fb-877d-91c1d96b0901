package com.arapeak.alrbea.UI;

import static com.arapeak.alrbea.AppController.baseContext;
import static com.arapeak.alrbea.AppController.isRockchipDevice1;
import static com.arapeak.alrbea.AppController.isRockchipDevice2;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;

import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import org.sufficientlysecure.rootcommands.Shell;
import org.sufficientlysecure.rootcommands.command.SimpleCommand;

public class ScreenOrientationEnforcer {

    private final View view;
    private final WindowManager windows;
    boolean isStarted = false;

    public ScreenOrientationEnforcer() {
        windows = (WindowManager) baseContext.getSystemService(Context.WINDOW_SERVICE);
        view = new View(baseContext);
    }

    @SuppressLint("InlinedApi")
    public static boolean requestOverlayDrawPermission(Activity act) {
        try {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + act.getPackageName()));
            act.startActivityForResult(intent, 11);
            return true;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return false;
    }

    @SuppressLint("NewApi")
    public static boolean canDrawOverlayViews() {
        try {
            return Settings.canDrawOverlays(baseContext);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            return true;
        }
    }

    int getSettingOrientation() {
        return HawkSettings.getAppOrientation();
    }

    int getWindowOrientation() {
        WindowManager wm = (WindowManager) baseContext.getSystemService(Context.WINDOW_SERVICE);
        return wm.getDefaultDisplay().getRotation();
    }

    public boolean isOrientationApplied() {
        boolean res = getSettingOrientation() == getWindowOrientation();
        Log.i("ScreenOrientationEnforcer", "isOrientationApplied: " + res);
        Log.i("ScreenOrientationEnforcer", "isRooted: " + AppController.isRooted);
        return res;
//        WindowManager wm = (WindowManager) baseContext.getSystemService(Context.WINDOW_SERVICE);
//        int windowOrientation = wm.getDefaultDisplay().getRotation();
//        int settingsOrientation = Utils.getAppOrientation();
//        return windowOrientation == settingsOrientation;
    }

    //    boolean
    public void start() {

        int r = HawkSettings.getAppOrientation();

        if (AppController.isLandscapeDevice()) {
            if (r == 0)//portrait
                r = 1;
            else if (r == 1)//land
                r = 0;
            else if (r == 2)//reverse portrait
                r = 3;
        }


        if (isRockchipDevice1) {
            Log.i("persist.sys.rotation value : ", Utils.getSystemProperties("persist.sys.rotation"));
            Utils.setSystemProperties("persist.sys.rotation", "" + (r * 90));
            Log.i("persist.sys.rotation value after Edit : ", Utils.getSystemProperties("persist.sys.rotation"));
            return;
        }
        if (isRockchipDevice2) {
            try {
                startLocaly();
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            return;
        }

        try {
            if (Settings.System.getInt(baseContext.getContentResolver(), Settings.System.ACCELEROMETER_ROTATION, 0) != 0)
                Settings.System.putInt(baseContext.getContentResolver(), Settings.System.ACCELEROMETER_ROTATION, 0);

            if (r >= 0 && r <= 3) {
                if (Settings.System.getInt(baseContext.getContentResolver(), Settings.System.USER_ROTATION, 0) != r) {
                    Settings.System.putInt(baseContext.getContentResolver(), Settings.System.USER_ROTATION, r);
                }
            } else {
                CrashlyticsUtils.INSTANCE.logException(new IllegalArgumentException("Invalid user_rotation value: " + r));
            }

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            Log.e(e.getMessage(), "Failed to set rotation");
        }

//        Surface.ROTATION_

        if (true) return;//not needed for the current time (caused issues with non rooted devices)

        try {
            startLocaly();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public boolean setOrientationRoot() {

        try {
            Shell shell = Shell.startRootShell();
            shell.add(new SimpleCommand("su")).waitForFinish();
            shell.add(new SimpleCommand("settings put system accelerometer_rotation 0")).waitForFinish();
            shell.add(new SimpleCommand("settings put system user_rotation " + getSettingOrientation())).waitForFinish();
//            shell.add(new SimpleCommand("settings get system user_rotation "+getSettingOrientation())).waitForFinish();
            shell.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return isOrientationApplied();
    }

    public void startLocaly() {
        try {
            WindowManager.LayoutParams layout = generateLayout();
            if (isStarted)
                stop();
            windows.addView(view, layout);
            view.setVisibility(View.VISIBLE);
            isStarted = true;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            isStarted = false;
        }
    }

    public void update() {
        start();
        /*else{
            try{
                WindowManager.LayoutParams layout = generateLayout();
                windows.updateViewLayout(view, layout);
            }
            catch (Exception e){
                e.printStackTrace();
            }
        }*/
    }

    public void stop() {
        if (!isStarted) return;
        try {
            windows.removeView(view);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        isStarted = false;
    }

    private WindowManager.LayoutParams generateLayout() {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }


        //Just in case the window type somehow doesn't enforce this
        layoutParams.flags =
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                        | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE;

        //Prevents breaking apps that detect overlying windows enabling
        //(eg UBank app, or enabling an accessibility service)
        layoutParams.width = 0;
        layoutParams.height = 0;

        //Try to make it completely invisible
        layoutParams.format = PixelFormat.TRANSPARENT;
        layoutParams.alpha = 0f;
        int orient = HawkSettings.getAppOrientation();
        switch (orient) {
            case 0:
                layoutParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
                break;
            case 1:
                layoutParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;
                break;
            case 2:
                layoutParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
                break;
            case 3:
                layoutParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE;
                break;
        }

        return layoutParams;
    }


}
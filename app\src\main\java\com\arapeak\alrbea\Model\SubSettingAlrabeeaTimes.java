package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.List;

public class SubSettingAlrabeeaTimes {

    private List<Object> objectList;
    private Object tag;
    private String title;
    private String description;
    private List<String> descriptionList;
    private List<Integer> valueList, descriptionIntegerList;
    private ViewsAlrabeeaTimes viewsAlrabeeaTimes;
    private int positionDefaultValue;
    private String positionDefaultValueString;
    private String defaultValueString;
    private Integer defaultValueInteger;
    private PrayerSystemsSchools defaultValuePrayerSystemsSchools;
    private List<PrayerSystemsSchools> typeSystemPray;
    private int positionCheck, from;
    private boolean isMinus, isShowEnableButton, isEnable, isChecked, isSpace, isEnabled, isPray;
    private boolean isEnableCallback;

    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes, String description) {
        this.title = title;
        this.description = description;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = -1;
        this.positionCheck = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;

    }


    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes, String description, boolean isEnableCallback) {
        this.title = title;
        this.description = description;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = -1;
        this.positionCheck = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
        this.isEnableCallback = isEnableCallback;

    }

    public SubSettingAlrabeeaTimes(String title
            , ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , String description
            , int defaultValue) {
        this.title = title;
        this.description = description;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = defaultValue;
        this.positionCheck = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
        this.isMinus = true;
    }


    public SubSettingAlrabeeaTimes(String title
            , ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , String description
            , String defaultValue) {
        this.title = title;
        this.description = description;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValueString = defaultValue;
        this.positionCheck = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
        this.isMinus = true;
    }

    public SubSettingAlrabeeaTimes(String title
            , ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , String description
            , int defaultValue
            , boolean isMinus) {
        this.title = title;
        this.description = description;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = defaultValue;
        this.positionCheck = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
        this.isMinus = isMinus;
    }

   /* public SubSettingAlrabeeaTimes(String title
            , ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<Integer> descriptionIntegerList
            , int defaultValue) {
        this.title = title;
        this.descriptionIntegerList = descriptionIntegerList;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = defaultValue;
        this.positionCheck = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
    }*/

    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<String> descriptionList
            , int positionCheck
            , List<Integer> valueList) {
        this.title = title;
        this.descriptionList = descriptionList;
        this.valueList = valueList;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionCheck = positionCheck;
        this.positionDefaultValue = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
    }

    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<String> descriptionList
            , List<Integer> valueList
            , int positionDefaultValue) {
        this.title = title;
        this.descriptionList = descriptionList;
        this.valueList = valueList;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = positionDefaultValue;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = null;
        this.positionCheck = -1;
    }

    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<String> descriptionList
            , List<Integer> valueList
            , String defaultValueString
            , Integer defaultValueInteger) {
        this.title = title;
        this.descriptionList = descriptionList;
        this.valueList = valueList;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = -1;
        this.defaultValueString = defaultValueString;
        this.defaultValueInteger = defaultValueInteger;
        this.defaultValuePrayerSystemsSchools = null;
        this.positionCheck = -1;
    }

    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<Object> objectList
            , int defaultIndex) {
        this.title = title;
        this.objectList = objectList;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.positionDefaultValue = defaultIndex;
    }


    public SubSettingAlrabeeaTimes(String title, ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<PrayerSystemsSchools> typeSystemPray
            , PrayerSystemsSchools defaultValuePrayerSystemsSchools) {
        this.title = title;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.typeSystemPray = typeSystemPray;
        this.positionDefaultValue = -1;
        this.defaultValueString = null;
        this.defaultValueInteger = null;
        this.defaultValuePrayerSystemsSchools = defaultValuePrayerSystemsSchools;
        this.positionCheck = -1;
    }

    public String getTitle() {
        return Utils.getValueWithoutNull(title);
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getTag() {
        return tag;
    }

    public void setTag(Object tag) {
        this.tag = tag;
    }


    public String getDescription() {
        return Utils.getValueWithoutNull(description);
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getDescriptionList() {
        return descriptionList == null ? new ArrayList<String>() : descriptionList;
    }

    public void setDescriptionList(List<String> descriptionList) {
        this.descriptionList = descriptionList;
    }

    public List<Integer> getValueList() {
        return valueList == null ? new ArrayList<Integer>() : valueList;
    }

    public void setValueList(List<Integer> valueList) {
        this.valueList = valueList;
    }

    public ViewsAlrabeeaTimes getViewsAlrabeeaTimes() {
        return viewsAlrabeeaTimes;
    }

    public void setViewsAlrabeeaTimes(ViewsAlrabeeaTimes viewsAlrabeeaTimes) {
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
    }

    public int getPositionDefaultValue() {
        return positionDefaultValue;
    }

    public void setPositionDefaultValue(int positionDefaultValue) {
        this.positionDefaultValue = positionDefaultValue;
    }

    public String getDefaultValueString() {
        return Utils.getValueWithoutNull(defaultValueString);
    }

    public void setDefaultValueString(String defaultValueString) {
        this.defaultValueString = defaultValueString;
    }

    public Integer getDefaultValueInteger() {
        return defaultValueInteger;
    }

    public void setDefaultValueInteger(Integer defaultValueInteger) {
        this.defaultValueInteger = defaultValueInteger;
    }

    public PrayerSystemsSchools getDefaultValuePrayerSystemsSchools() {
        return defaultValuePrayerSystemsSchools;
    }

    public void setDefaultValuePrayerSystemsSchools(PrayerSystemsSchools defaultValuePrayerSystemsSchools) {
        this.defaultValuePrayerSystemsSchools = defaultValuePrayerSystemsSchools;
    }

    public List<PrayerSystemsSchools> getTypeSystemPray() {
        return typeSystemPray == null ? new ArrayList<PrayerSystemsSchools>() : typeSystemPray;
    }

    public void setTypeSystemPray(List<PrayerSystemsSchools> typeSystemPray) {
        this.typeSystemPray = typeSystemPray;
    }

    public int getPositionCheck() {
        return positionCheck;
    }

    public void setPositionCheck(int positionCheck) {
        this.positionCheck = positionCheck;
    }

    public boolean isMinus() {
        return isMinus;
    }

    public void setMinus(boolean minus) {
        isMinus = minus;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public void setEnable(boolean enable) {
        isEnable = enable;
    }


    public boolean isEnabled() {
        return isEnabled;
    }

    public void setEnabled(boolean enable) {
        isEnabled = enable;// i don't know why the previous programmer make it .. (@_@)
    }

    public boolean isPray() {
        return isPray;
    }

    public void setisPray(boolean enable) {
        isPray = enable;
    }

    public boolean isShowEnableButton() {
        return isShowEnableButton;
    }

    public void setShowEnableButton(boolean showEnableButton) {
        isShowEnableButton = showEnableButton;
    }

    public boolean isShowSpace() {
        return isSpace;
    }

    public void setisShowSpace(boolean showEnableButton) {
        isSpace = showEnableButton;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public boolean isEnableCallback() {
        return isEnableCallback;
    }

    public void setEnableCallback(boolean enableCallback) {
        isEnableCallback = enableCallback;
    }

    public String getPositionDefaultValueString() {
        return Utils.getValueWithoutNull(positionDefaultValueString);
    }

    public void setPositionDefaultValueString(String positionDefaultValueString) {
        this.positionDefaultValueString = positionDefaultValueString;
    }

    public List<Integer> getDescriptionIntegerList() {
        return descriptionIntegerList == null ? new ArrayList<Integer>() : descriptionIntegerList;
    }

    public void setDescriptionIntegerList(List<Integer> descriptionIntegerList) {
        this.descriptionIntegerList = descriptionIntegerList;
    }

    public List<Object> getObjectList() {
        return objectList;
    }
}

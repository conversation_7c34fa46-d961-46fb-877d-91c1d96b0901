package com.arapeak.alrbea.Enum;

import static com.arapeak.alrbea.Utils.getString;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.R;

public enum PrayerType {
    Midnight(ConstantsOfApp.FAJR_KEY, R.string.midnight, R.string.midnight),
    Tahajjud(ConstantsOfApp.TAHAJJUD_KEY, R.string.tahajjud, R.string.tahajjud_prayer),
    LastThird(ConstantsOfApp.FAJR_KEY, R.string.last_third, R.string.last_third),
    Fajr(ConstantsOfApp.FAJR_KEY, R.string.fajr, R.string.fajr_prayer),
    Sunrise(ConstantsOfApp.SUNRISE_KEY, R.string.sunrise, R.string.sunrise),
    <PERSON><PERSON>(ConstantsOfApp.DUHA_KEY, R.string.duha, R.string.duha_prayer),
    <PERSON><PERSON><PERSON>(ConstantsOfApp.DHUHR_KEY, R.string.dhuhr, R.string.dhuhr_prayer),
    Asr(ConstantsOfApp.ASR_KEY, R.string.asr, R.string.asr_prayer),
    Maghrib(ConstantsOfApp.MAGHRIB_KEY, R.string.maghrib, R.string.maghrib_prayer),
    Isha(ConstantsOfApp.ISHA_KEY, R.string.isha, R.string.isha_prayer),
    Tarawih(ConstantsOfApp.TARAWIH_KEY, R.string.tarawih, R.string.tarawih_prayer);

    private final String KEY;
    public int String_R_ID;
    public int FullString_R_ID;
    public PrayerTime prayerTime;

    PrayerType(String key, int stringId, int fullStringId) {
        KEY = key;
        String_R_ID = stringId;
        FullString_R_ID = fullStringId;
        prayerTime = new PrayerTime(this);
    }

    public String getKEY() {
        if (prayerTime.isJomaa() && prayerTime.prayerType == PrayerType.Dhuhr)
            return ConstantsOfApp.JOMAA_KEY;
        return KEY;
    }

    public String getName() {
        return (this == Dhuhr && prayerTime.isJomaa()) ? getString(R.string.jomaa) : getString(String_R_ID);
    }

    public String getFullName() {
        return (this == Dhuhr && prayerTime.isJomaa()) ? getString(R.string.jomaa_prayer) : getString(FullString_R_ID);
    }

    public boolean isFard() {
        switch (this) {
            case Fajr:
            case Dhuhr:
            case Asr:
            case Maghrib:
            case Isha:
                return true;
            default:
                return false;
        }
    }
}

package com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.events.content;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.Calendar;

public class EventViewHolder extends RecyclerView.ViewHolder {
    public Event event;
    CardView cardView;
    TextView tv_enabled, tv_text, tv_date;

    public EventViewHolder(@NonNull View itemView) {
        super(itemView);
        cardView = itemView.findViewById(R.id.card);
        tv_enabled = itemView.findViewById(R.id.tv_enabled);
        tv_text = itemView.findViewById(R.id.tv_text);
        tv_date = itemView.findViewById(R.id.tv_date);
    }

    @SuppressLint("SetTextI18n")
    public void Bind(Event event) {
        this.event = event;
        tv_enabled.setText(event.enabled ? Utils.getString(R.string.enabled) : Utils.getString(R.string.not_enabled));
        cardView.setCardBackgroundColor(Color.parseColor(event.enabled ? "#CFECEA" : "#EDD1D3"));
        tv_text.setText(event.text);
//        SimpleDateFormat dateFormat = new SimpleDateFormat("", Utils.getLocale());
//        dateFormat.setCalendar(Utils.getUmmalquraCalendar());
////        dateFormat.setCalendar(event.sCal);
//        dateFormat.applyPattern("dd hh:mm a");
        String sMonth = event.sCal.getDisplayName(UmmalquraCalendar.MONTH, Calendar.LONG, HawkSettings.getLocale());//idk why , but "MMMM" doesn't work in pattern
        String eMonth = event.eCal.getDisplayName(UmmalquraCalendar.MONTH, Calendar.LONG, HawkSettings.getLocale());

        tv_date.setText(Utils.getString(R.string.from) + " " + sMonth + " " + Utils.formatUmmalquraCalendar("dd hh:mm a", event.sCal.getTime())
                + "\n" + Utils.getString(R.string.to) + " " + eMonth + " " + Utils.formatUmmalquraCalendar("dd hh:mm a", event.eCal.getTime()));//
//        tv_date.setText(Utils.getString(R.string.from)+" "+sMonth+" "+dateFormat.format(event.sCal.getTime())
//                + "\n"+Utils.getString(R.string.to)+" "+eMonth+" "+dateFormat.format(event.eCal.getTime()));//
    }
}

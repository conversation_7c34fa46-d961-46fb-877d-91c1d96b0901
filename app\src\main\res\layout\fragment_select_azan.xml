<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".UI.Fragment.settings.content.main.content.alerts.content.SelectAzan">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/enableDisablePhoto_SwitchCompat_AddPhotoFragment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        android:checked="true"
        android:fontFamily="@font/droid_arabic_kufi_bold"

        android:includeFontPadding="false"
        android:padding="@dimen/_5sdp"
        android:text="@string/enable_disable_azan_sound"
        android:textColor="#474747"
        android:textSize="@dimen/dateNowMain"

        app:buttonTint="@color/colorPrimary"
        app:switchMinWidth="@dimen/_30sdp"
        app:thumbTint="@android:color/white"
        app:trackTint="@color/colorPrimary" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_audio"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:overScrollMode="never"

            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:spanCount="2"
            tools:itemCount="3"
            tools:listitem="@layout/layout_list_item_audio_select"

            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/_10sdp">


            <TextView
                android:id="@+id/citationForMorningTime_TextView_AthkarFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:includeFontPadding="false"
                android:text="@string/audio_level"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/citationForMorningTime_SeekBar_AthkarFragment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="10"
                android:maxHeight="@dimen/_6sdp"
                android:progress="7"
                android:progressDrawable="@drawable/seek_bar_shape"
                android:thumb="@drawable/sb_thumb" />

            <ImageButton
                android:layout_width="@dimen/_30sdp"
                android:layout_height="@dimen/_30sdp"
                android:background="@color/white"
                android:src="@drawable/ic_baseline_play_arrow_24"
                android:visibility="gone" />


        </LinearLayout>
    </LinearLayout>


</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    android:layout_marginTop="16dp"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context=".UI.Fragment.settings.SettingsFragment">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/settingItem_RecyclerView_SettingsActivity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"

        android:overScrollMode="never"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:reverseLayout="false"
        tools:listitem="@layout/layout_list_item_setting" />

</androidx.constraintlayout.widget.ConstraintLayout>
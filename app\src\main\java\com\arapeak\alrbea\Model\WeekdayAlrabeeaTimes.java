package com.arapeak.alrbea.Model;


import static com.arapeak.alrbea.hawk.HawkConstants.AR_LANGUAGE;

import com.arapeak.alrbea.hawk.HawkSettings;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class WeekdayAlrabeeaTimes {
    @Expose
    @SerializedName("ar")
    private String ar;
    @Expose
    @SerializedName("en")
    private String en;

    public String getAr() {
        return ar;
    }

    public void setAr(String ar) {
        this.ar = ar;
    }

    public String getEn() {
        return en;
    }

    public void setEn(String en) {
        this.en = en;
    }

    public String getDefault() {
        if (HawkSettings.getLocaleLanguage().equalsIgnoreCase(AR_LANGUAGE))
            return getAr();
        else
            return getEn();
    }

    public int getNumber() {
        switch (getEn()) {
            case "Saturday":
                return 1;
            case "Sunday":
                return 2;
            case "Monday":
                return 3;
            case "Tuesday":
                return 4;
            case "Wednesday":
                return 5;
            case "Thursday":
                return 6;
        }
        return 7;
    }
}

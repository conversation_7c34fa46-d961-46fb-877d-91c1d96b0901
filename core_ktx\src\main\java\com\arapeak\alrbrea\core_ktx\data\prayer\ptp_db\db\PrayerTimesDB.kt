package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.db

import androidx.room.Database
import androidx.room.RoomDatabase
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.dao.PrayerOffsetKuwaitDAO
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.dao.PrayerTimeAdenDAO
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.dao.PrayerTimeKuwaitDAO
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model.PrayerOffsetKuwaitDbEntity
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model.PrayerTimeAdenDbEntity
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model.PrayerTimeKuwaitDbEntity

@Database(entities = [PrayerTimeKuwaitDbEntity::class, PrayerOffsetKuwaitDbEntity::class, PrayerTimeAdenDbEntity::class], version = 3)
abstract class PrayerTimesDB : RoomDatabase() {
    abstract fun prayerTimeAdenDao(): PrayerTimeAdenDAO
    abstract fun prayerTimeKuwaitDao(): PrayerTimeKuwaitDAO
    abstract fun prayerOffsetKuwaitDAO(): PrayerOffsetKuwaitDAO
}

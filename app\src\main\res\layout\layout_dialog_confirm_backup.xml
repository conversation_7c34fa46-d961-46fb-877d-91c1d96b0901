<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal|top"
    android:layout_margin="@dimen/_20sdp"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:minWidth="@dimen/_250sdp"
    android:orientation="vertical"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="@dimen/_15sdp"
    app:cardElevation="0dp"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:minWidth="@dimen/_250sdp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/layout_gray_without_top_corners"
            android:gravity="center"
            android:paddingStart="@dimen/_15sdp"
            android:paddingTop="12dp"
            android:paddingEnd="20dp"
            android:paddingBottom="12dp">

            <ImageView
                android:id="@+id/icon_ImageView_ConfirmationDialog"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                tools:srcCompat="@drawable/ic_menu_gallery" />


            <TextView
                android:id="@+id/title_TextView_ConfirmationDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:includeFontPadding="false"
                android:textColor="#343434"
                android:textSize="18sp"
                tools:hint="+966"
                tools:text="سبب الغياب" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_8sdp"
            android:layout_marginTop="@dimen/_15sdp"
            android:layout_marginEnd="@dimen/_8sdp"
            android:layout_marginBottom="@dimen/_15sdp"
            android:gravity="center"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:id="@+id/submit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:background="#fff"
                    android:clipToPadding="false"
                    app:cardBackgroundColor="#fff"
                    app:cardCornerRadius="@dimen/_20sdp"
                    app:cardElevation="@dimen/_1sdp"
                    app:cardPreventCornerOverlap="false"
                    app:cardUseCompatPadding="true"
                    app:contentPaddingBottom="@dimen/_8sdp"
                    app:contentPaddingTop="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi_bold"
                        android:text="@string/backup_setting"
                        android:textSize="@dimen/_12sdp" />
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/submitbackup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:background="#fff"
                    android:clipToPadding="false"
                    app:cardBackgroundColor="#fff"
                    app:cardCornerRadius="@dimen/_20sdp"
                    app:cardElevation="@dimen/_1sdp"
                    app:cardPreventCornerOverlap="false"
                    app:cardUseCompatPadding="true"
                    app:contentPaddingBottom="@dimen/_8sdp"
                    app:contentPaddingTop="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi_bold"
                        android:text="@string/restore"
                        android:textSize="@dimen/_12sdp" />
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/premium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:background="#fff"
                    android:clipToPadding="false"
                    app:cardBackgroundColor="#fff"
                    app:cardCornerRadius="@dimen/_20sdp"
                    app:cardElevation="@dimen/_1sdp"
                    app:cardPreventCornerOverlap="false"
                    app:cardUseCompatPadding="true"
                    app:contentPaddingBottom="@dimen/_8sdp"
                    app:contentPaddingTop="@dimen/_8sdp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi_bold"
                        android:text="@string/alrbea_logo_remove"
                        android:textSize="@dimen/_12sdp" />
                </androidx.cardview.widget.CardView>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center">

                <Button
                    android:id="@+id/confirm_Button_ConfirmationDialog"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_green_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingStart="20dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="8dp"
                    android:text="@string/yes"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/cancel_Button_ConfirmationDialog"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="center"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_red_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingStart="20dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="8dp"
                    android:text="@string/no"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_13sdp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</androidx.cardview.widget.CardView>

package com.arapeak.alrbea;


import android.content.Context;

import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbea.ummalqura_objects.PrayerTimeType;
import com.arapeak.alrbea.ummalqura_objects.PrayerTimes;
import com.arapeak.alrbea.ummalqura_objects.Prayers;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;


public class PrayerTimesHelper {
    private final List<PrayerTimeItem> ITEMS = new ArrayList();
    //    private Calendar date;
    int year = 0;
    int month = 0;
    int day = 0;
    private double latitude = 0;
    private double longitude = 0;

    public PrayerTimesHelper(int year, int month, int day, double latitude, double longitude) {
        this.year = year;
        this.month = month;
        this.day = day;
        this.latitude = latitude;
        this.longitude = longitude;
        calculatePrayerTimes();
    }

    private void calculatePrayerTimes() {
/*
        int year = this.date.get(Calendar.YEAR);
        int month = 1 + this.date.get(Calendar.MONTH);
        int day = this.date.get(Calendar.DAY_OF_MONTH);*/

//        System.out.println(this.date.get(1));
//        System.out.println(this.date.get(2));
//        System.out.println(this.date.get(5));
        PrayerTimes prayerTimes = Prayers.getPrayerTimes(year, month, day,
                latitude * 0.017453292519943295d,
                -(longitude * 0.017453292519943295d),
                PrayerTimeType.DefaultUmmulQuraTimeType());
//                PrayerTimeType.getPrayerTimeType());

//                PrayerTimeType.getPrayerTimeType(PreferencesHelper.getPrayerTimeMethod(context),
//                        PreferencesHelper.getAserPrayerTimeMethod(context)));
//        if (UDate.G2HA(i, i2, i3).Month == 9 && PreferencesHelper.getPrayerTimeMethod(context) == 0) {
//            prayerTimes.Isha += 0.5d;
//        }
        this.ITEMS.add(new PrayerTimeItem(0, prayerTimes.Fajer));
        this.ITEMS.add(new PrayerTimeItem(1, prayerTimes.Sunrise));
        this.ITEMS.add(new PrayerTimeItem(2, prayerTimes.Eid));
        this.ITEMS.add(new PrayerTimeItem(3, prayerTimes.Zohar));
        this.ITEMS.add(new PrayerTimeItem(4, prayerTimes.Aser));
        this.ITEMS.add(new PrayerTimeItem(5, prayerTimes.Magreb));
        this.ITEMS.add(new PrayerTimeItem(6, prayerTimes.Isha));


    }

    public List<PrayerTimeItem> getPrayerTimes() {
        return this.ITEMS;
    }

    public class PrayerTimeItem {
        public static final int ASR_ID = 4;
        public static final int DHR_ID = 3;
        public static final int EID_ID = 2;
        public static final int FAJR_ID = 0;
        public static final int ISHA_ID = 6;
        public static final int MGRB_ID = 5;
        public static final int SHROK_ID = 1;
        public final int id;
        //        public int iqamaTime;
        public final double prayerTime;

        public PrayerTimeItem(int i, double d) {


            this.id = i;
            this.prayerTime = d;
//            SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(context);
         /*
            if (i != 0) {
                switch (i) {
                    case 3:
                        this.iqamaTime = defaultSharedPreferences.getInt("dhr_iqama_time", 20);
                        return;
                    case 4:
                        this.iqamaTime = defaultSharedPreferences.getInt("asr_iqama_time", 20);
                        return;
                    case 5:
                        this.iqamaTime = defaultSharedPreferences.getInt("mgrb_iqama_time", 10);
                        return;
                    case 6:
                        this.iqamaTime = defaultSharedPreferences.getInt("isha_iqama_time", 20);
                        return;
                    default:
                        this.iqamaTime = 0;
                        return;
                }
            }
            this.iqamaTime = defaultSharedPreferences.getInt("fajr_iqama_time", 25);*/
        }

        public String getPrayerName(Context context) {
            switch (this.id) {
                case 0:
                    return context.getResources().getString(R.string.fajr);
                case 1:
                    return context.getResources().getString(R.string.sunrise);
                case 2:
                    return "عيد";
                case 3:
                    return context.getResources().getString(R.string.dhuhr);
                case 4:
                    return context.getResources().getString(R.string.asr);
                case 5:
                    return context.getResources().getString(R.string.maghrib);
                case 6:
                    return context.getResources().getString(R.string.isha);
                default:
                    return "";
            }
        }

        public String getPrayerTime() {
            return new SimpleDateFormat("hh:mm a", new Locale(HawkSettings.getTypeNumber())).format(getPrayerTimeCal().getTime());
        }

        public String getPrayerTimeWidget() {
            return new SimpleDateFormat("h:mm a").format(getPrayerTimeCal().getTime());
        }

        public Calendar getPrayerTimeCal() {


            Calendar instance = Calendar.getInstance();
            instance.set(year, month, day);
//            instance.setTime(PrayerTimesHelper.this.date.getTime());
            int i = (int) this.prayerTime;
            int i2 = (int) ((this.prayerTime - ((double) i)) * 60.0d);
            if (i2 == 60) {
                i++;
                i2 = 0;
            }
            if (i2 < 0) {
                i2 = -i2;
            }
            instance.set(Calendar.HOUR_OF_DAY, i);
            instance.set(Calendar.MINUTE, i2);
            instance.set(Calendar.SECOND, 0);
            instance.set(Calendar.MILLISECOND, 0);
            return instance;
        }
/*
        public Calendar getIqamaTimeCal() {
            Calendar prayerTimeCal = getPrayerTimeCal();
            prayerTimeCal.add(12, getIqamaTime());
            return prayerTimeCal;
        }*/
/*
        public int getIqamaTime() {
            return this.iqamaTime;
        }*/

        public String toString() {
            return getPrayerTime();
        }
    }

    /*@Nullable
    public static PrayerTimeItem getNextPrayer(Calendar calendar, List<PrayerTimeItem> list) {
        PrayerTimeItem prayerTimeItem = null;
        for (PrayerTimeItem prayerTimeItem2 : list) {
            if (prayerTimeItem2.getPrayerTimeCal().after(calendar) && (prayerTimeItem == null || prayerTimeItem2.getPrayerTimeCal().before(prayerTimeItem.getPrayerTimeCal()))) {
                prayerTimeItem = prayerTimeItem2;
            }
        }
        return prayerTimeItem;
    }

    @Nullable
    public static PrayerTimeItem getNextPrayerConsideringIqama(Calendar calendar, List<PrayerTimeItem> list) {
        PrayerTimeItem prayerTimeItem = null;
        for (PrayerTimeItem prayerTimeItem2 : list) {
            Calendar instance = Calendar.getInstance();
            //instance.setTimeInMillis(prayerTimeItem2.getIqamaTimeCal().getTimeInMillis() + NotificationsBroadcastReceiver.CANCELLATION_TIME_MILLISECONDS);
            if ((prayerTimeItem2.getPrayerTimeCal().after(calendar) || instance.after(calendar)) && (prayerTimeItem == null || prayerTimeItem2.getPrayerTimeCal().before(prayerTimeItem.getPrayerTimeCal()))) {
                prayerTimeItem = prayerTimeItem2;
            }
        }
        return prayerTimeItem;
    }*/
}

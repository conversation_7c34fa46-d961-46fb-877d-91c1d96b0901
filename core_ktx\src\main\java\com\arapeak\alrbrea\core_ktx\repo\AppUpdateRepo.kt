package com.arapeak.alrbrea.core_ktx.repo

import android.util.Log
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.data.network.NetworkApi
import com.arapeak.alrbrea.core_ktx.data.network.model.LatestVersionResponse
import com.arapeak.alrbrea.core_ktx.repo.utils.CallResult
import com.arapeak.alrbrea.core_ktx.repo.utils.NetworkClient
import retrofit2.Retrofit

class AppUpdateRepo {
    private var retrofitClient: Retrofit? = null

    init {
        try {
            retrofitClient = NetworkClient.getRetrofitClient(true)
        } catch (e: Exception) {
            logException(e)
        }
    }

    suspend fun getLatestVersion(): CallResult<LatestVersionResponse?, Exception> {
        try {
            val api = retrofitClient?.create(NetworkApi::class.java)
            val response = api?.latestVersion()

            Log.d(
                "getLatestVersion",
                response?.message() + " is successful ? : " + response?.isSuccessful.toString()
            )

            return if (response?.isSuccessful == true)
                CallResult.success(response.body())
            else
                CallResult.error(IllegalStateException(response?.errorBody()?.string() ?: ""))

        } catch (e: Exception) {
            return CallResult.error(e)
        }

    }
}
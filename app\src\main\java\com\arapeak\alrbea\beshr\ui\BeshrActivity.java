//package com.arapeak.alrbea.beshr.ui;
//
//
//import androidx.appcompat.app.AppCompatActivity;
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.lifecycle.ViewModelProvider;
//
//import android.content.Intent;
//import android.os.Bundle;
//import android.util.Log;
//import android.view.View;
//import android.widget.Button;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//import android.widget.Toast;
//
//import com.arapeak.alrbea.Enum.AthkarType;
//import com.arapeak.alrbea.R;
//import com.arapeak.alrbea.UI.Activity.SettingsActivity;
//import com.arapeak.alrbea.UI.Fragment.settings.content.ads.SettingAds;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbea.beshr.view_model.PrayerViewModel;
//import com.arapeak.alrbea.beshr.enums.Mode;
//import com.arapeak.alrbea.hawk.HawkSettings;
//
//import java.io.File;
//
///**
// * Main Activity serving as the View layer in MVVM.
// * It observes LiveData from the PrayerViewModel and updates the UI using findViewById.
// * It delegates user actions to the ViewModel.
// */
//public class BeshrActivity extends AppCompatActivity {
//
//
//
//    private static final String TAG = "BeshrActivity";
//
//    // --- UI Elements ---
//
//    private LinearLayout ikamaAnnouncementContainer;
//    private ConstraintLayout prayerTimeContainer;
//    private LinearLayout athkarContainer;
//    private boolean isLandscape = false;
//    private ImageView imageViewCurrentDay;
//    private TextView textViewCurrentTime;
//    private TextView tv_athkar_time;
//    private TextView tvDayName;
//    private TextView tv_arabicYear;
//    private TextView tv_englishMothNumber;
//    private TextView tv_arabicMothNumber;
//    private TextView tv_arabicMonth;
//    private TextView tv_englishYear;
//    private TextView tv_englishMonth;
//    private TextView textViewCurrentDateGregorian;
//    private TextView textViewCurrentDateHijri;
//    private ImageView imageViewAthkar;
//
//    // TextViews for Prayer Times
//    private TextView textViewFajrTime;
//    private TextView textViewSunriseTime;
//    private TextView textViewDhuhrTime;
//    private TextView textViewAsrTime;
//    private TextView textViewMaghribTime;
//    private TextView textViewIshaTime;
//    // Add corresponding labels if they are separate TextViews
//
//    // Add TextViews/Layouts for the different mode contents
//    private View prayerTimesSection; // A LinearLayout or ConstraintLayout holding all prayer time views
//    private TextView textViewModeTitle;
//    private TextView textViewModeContent; // Used for Athkar and Ikama list
//    private LinearLayout homeContainer;
//    // Mode buttons
//    private Button buttonModePrayer;
//    private Button buttonModeIkama; // Renamed from Athkar as per request
//    private Button buttonModeAthkar; // New button for Athkar mode
//
//    // --- ViewModel ---
//    private PrayerViewModel prayerViewModel;
//
//
//    //Binding
//
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        Utils.initActivity(this);
//
//        setContentView(R.layout.activity_main);
//
//        Log.d(TAG, "onCreate");
//
//
//        // --- Find UI Elements ---
//        homeContainer = findViewById(R.id.container_LinearLayout_MainActivity);
////        textViewCurrentDateGregorian = findViewById(R.id.textViewCurrentDateGregorian);
////        textViewCurrentDateHijri = findViewById(R.id.textViewCurrentDateHijri);
////
////        // Prayer Times Views (assuming they are grouped in a layout)
////        prayerTimesSection = findViewById(R.id.prayerTimesSection); // Assuming this ID exists
////
////        textViewFajrTime = findViewById(R.id.textViewFajrTime);
////        textViewDhuhrTime = findViewById(R.id.textViewDhuhrTime);
////        textViewAsrTime = findViewById(R.id.textViewAsrTime);
////        textViewMaghribTime = findViewById(R.id.textViewMaghribTime);
////        textViewIshaTime = findViewById(R.id.textViewIshaTime);
//        // Find Prayer label TextViews if separate:
//        // TextView labelFajr = findViewById(R.id.labelFajr); ...
//
//
////        // Mode Content Views
////        textViewModeTitle = findViewById(R.id.textViewModeTitle);
////        textViewModeContent = findViewById(R.id.textViewModeContent); // Used for Athkar/Ikama list
////
////
////        // Mode buttons
////        buttonModePrayer = findViewById(R.id.buttonModePrayer);
////        buttonModeIkama = findViewById(R.id.buttonModeIkama); // Assumed renamed ID
////        buttonModeAthkar = findViewById(R.id.buttonModeAthkar); // Assumed new ID
////
////
////        // --- Get ViewModel ---
//        prayerViewModel = new ViewModelProvider(this).get(PrayerViewModel.class);
////
//
//
//        prayerViewModel.currentTheme.observe(this, theme -> {
//            if (theme != null) {
//                runOnUiThread(() -> {
//                    textViewCurrentTime = theme.getView().findViewById(R.id.timeNow_TextView_MainActivity);
//                    textViewFajrTime= theme.getView().findViewById(R.id.fajrTime_TextView_MainActivity);
//                    textViewSunriseTime= theme.getView().findViewById(R.id.sunriseATime_TextView_MainActivity);
//                    imageViewAthkar= theme.getView().findViewById(R.id.iv_athkar);
//                    textViewDhuhrTime = theme.getView().findViewById(R.id.dhuhrTime_TextView_MainActivity);
//                    textViewAsrTime = theme.getView().findViewById(R.id.asrTime_TextView_MainActivity);
//                    textViewMaghribTime = theme.getView().findViewById(R.id.maghribTime_TextView_MainActivity);
//                    textViewIshaTime = theme.getView().findViewById(R.id.ishaTime_TextView_MainActivity);
//                    tv_athkar_time = theme.getView().findViewById(R.id.tv_athkar_time);
//                    textViewDhuhrTime = theme.getView().findViewById(R.id.dhuhrTime_TextView_MainActivity);
//                    ikamaAnnouncementContainer = theme.getView().findViewById(R.id.announcement_include_MainActivity);
//                    prayerTimeContainer = theme.getView().findViewById(R.id.prayerTimeItem_include_MainActivity);
//                    athkarContainer = theme.getView().findViewById(R.id.layout_athkar);
//                    imageViewCurrentDay = theme.getView().findViewById(R.id.dayimage);
//                    tvDayName = theme.getView().findViewById(R.id.day_text);
//                    tv_arabicYear = theme.getView().findViewById(R.id.datey);
//                    tv_englishMothNumber = theme.getView().findViewById(R.id.dateNow1_TextView_MainActivity);
//                    tv_arabicMothNumber = theme.getView().findViewById(R.id.dateNow_TextView_MainActivity);
//                    tv_arabicMonth = theme.getView().findViewById(R.id.datehm);
//                    tv_englishYear = theme.getView().findViewById(R.id.datehy);
//                    tv_englishMonth = theme.getView().findViewById(R.id.datem);
//
//                    setContentView(theme.getView());
//
////                    startActivity(new Intent(this, SettingsActivity.class));
//
//                    prayerViewModel.setMode(Mode.PRAYER);
//                    prayerViewModel.currentMode.observe(this, mode -> {
//                        Toast.makeText(this, ""+mode, Toast.LENGTH_SHORT).show();
//
//                        if(mode == Mode.PRAYER){
//                            runOnUiThread(()->{
//                                prayerTimeContainer.setVisibility(View.VISIBLE);
//                                ikamaAnnouncementContainer.setVisibility(View.GONE);
//                                athkarContainer.setVisibility(View.GONE);
//                            });
//                        }else if(mode == Mode.IKAMA){
//                            runOnUiThread(()->{
//                                prayerTimeContainer.setVisibility(View.GONE);
//                                ikamaAnnouncementContainer.setVisibility(View.VISIBLE);
//                                athkarContainer.setVisibility(View.GONE);
//                            });
//                        } else if(mode == Mode.ATHKAR){
//                            prayerTimeContainer.setVisibility(View.GONE);
//                            ikamaAnnouncementContainer.setVisibility(View.GONE);
//                            athkarContainer.setVisibility(View.VISIBLE);
//                            prayerViewModel.currentAthkarType.observe(this, athkarType -> {
//                                runOnUiThread(()->{
//                                  if(athkarType!=null){
//                                        if (athkarType == AthkarType.EveningAthkar) {
//                                            getEveningAthkarImageFile();
//                                            imageViewAthkar.setImageResource(R.drawable.theme_brown_3_athkar_evening);
//                                        imageViewAthkar.setImage
//                                        }else if(athkarType== AthkarType.MorningAthkar){
//
//                                      }else if(athkarType== AthkarType.AfterPrayer){
//
//                                      }
//                                  }
//                                });
//                            });
//                        }
//                    });
//                });
//
//                prayerViewModel.fetchedPrayerTimes.observe(this, prayerTimes -> {
//                    if(prayerTimes!=null){
//                        textViewFajrTime.setText(prayerTimes.formatTime(prayerTimes.getFajr()));
//                        textViewSunriseTime.setText(prayerTimes.formatTime(prayerTimes.getSunrise()));
//                        textViewDhuhrTime.setText(prayerTimes.formatTime(prayerTimes.getDhuhr()));
//                        textViewAsrTime.setText(prayerTimes.formatTime(prayerTimes.getAsr()));
//                        textViewMaghribTime.setText(prayerTimes.formatTime(prayerTimes.getMaghrib()));
//                        textViewIshaTime.setText(prayerTimes.formatTime(prayerTimes.getIsha()));
//                    }
//                });
//            }
//        });
////        // --- Observe LiveData from ViewModel ---
//
//
//        prayerViewModel.currentTime.observe(this, dateTime -> {
//            runOnUiThread(() -> {
//                if(imageViewCurrentDay!=null && dateTime!=null ){ {
//                    textViewCurrentTime.setText(dateTime.getTime());
//                    tv_athkar_time.setText(dateTime.getTime());
//                    tvDayName.setText(dateTime.getDayOfWeekName());
//                    tv_arabicYear.setText(dateTime.getArabicYear());
//                    tv_englishYear.setText(dateTime.getEnglishYear());
//                    tv_arabicMonth.setText(dateTime.getArabicMonthName());
//                    tv_englishMonth.setText(dateTime.getEnglishMonthName());
//                    tv_arabicMothNumber.setText(dateTime.getArabicMonthNumber()+"");
//                    tv_englishMothNumber.setText(dateTime.getEnglishMonthNumber()+"");
//
//
//                    if(imageViewCurrentDay!=null){
//                        switch (dateTime.getDayOfWeek()) {
//                            case 0:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_1);
//                                break;
//                            case 1:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_2);
//                                break;
//                            case 2:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_3);
//                                break;
//                            case 3:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_4);
//                                break;
//                            case 4:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_5);
//                                break;
//                            case 5:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_6);
//                                break;
//                            case 6:
//                                imageViewCurrentDay.setImageResource(R.drawable.theme_brown_3_day_7);
//                                break;
//                        }
//                    }
////                    imageViewCurrentDay.setImageDrawable(dateTime.getDayImage());
//                }
////                    imageViewCurrentDay.setImageResource(day);
//                }
//            });
//        });
//
////
////        prayerViewModel.currentDisplayDates.observe(this, dates -> {
////            if (dates != null) {
////                if (textViewCurrentDateGregorian != null) textViewCurrentDateGregorian.setText(dates.getGregorianDate());
////                if (textViewCurrentDateHijri != null) textViewCurrentDateHijri.setText(dates.getHijriDate());
////            } else {
////                if (textViewCurrentDateGregorian != null) textViewCurrentDateGregorian.setText("--/--/----");
////                if (textViewCurrentDateHijri != null) textViewCurrentDateHijri.setText("--/--/----");
////            }
////        });
////
////        // Observe Fetched Prayer Times - Only used to update the PrayerTimesSection views
////        prayerViewModel.fetchedPrayerTimes.observe(this, times -> {
////            if (times != null) {
////                if (textViewFajrTime != null) textViewFajrTime.setText(times.formatTime(times.getFajr()));
////                if (textViewDhuhrTime != null) textViewDhuhrTime.setText(times.formatTime(times.getDhuhr()));
////                if (textViewAsrTime != null) textViewAsrTime.setText(times.formatTime(times.getAsr()));
////                if (textViewMaghribTime != null) textViewMaghribTime.setText(times.formatTime(times.getMaghrib()));
////                if (textViewIshaTime != null) textViewIshaTime.setText(times.formatTime(times.getIsha()));
////                // Update labels too if they are separate TextViews
////                Log.d(TAG, "UI updated with fetched prayer times.");
////            } else {
////                Log.w(TAG, "Fetched prayer times data is null.");
////                // Clear UI or show error message
////                if (textViewFajrTime != null) textViewFajrTime.setText("--:--");
////                if (textViewDhuhrTime != null) textViewDhuhrTime.setText("--:--");
////                if (textViewAsrTime != null) textViewAsrTime.setText("--:--");
////                if (textViewMaghribTime != null) textViewMaghribTime.setText("--:--");
////                if (textViewIshaTime != null) textViewIshaTime.setText("--:--");
////            }
////        });
////
////        // Observe Current Mode - This observer is key to switching views
//
////
////        // Observe Mode Content - Update the shared content TextView
////        prayerViewModel.modeContent.observe(this, content -> {
////            if (textViewModeContent != null) textViewModeContent.setText(content);
////            Log.d(TAG, "Mode content updated.");
////        });
////
////
////        // --- Set up Button Click Listeners ---
////        if (buttonModePrayer != null) buttonModePrayer.setOnClickListener(v -> prayerViewModel.setMode(Mode.PRAYER));
////        if (buttonModeIkama != null) buttonModeIkama.setOnClickListener(v -> prayerViewModel.setMode(Mode.IKAMA));
////        if (buttonModeAthkar != null) buttonModeAthkar.setOnClickListener(v -> prayerViewModel.setMode(Mode.ATHKAR));
//
//
//        // --- Start the Service (Optional, if Service handles background fetching) ---
//        // Decide if you need the Service to run. If fetching/persistence is only needed while the app is open,
//        // you might not need the Service or can stop it on app close.
//        // If you need background fetching/persistence across app restarts, keep the Service.
//        // PrayerTimeService.start(this); // Uncomment if you need the Service
//
//        // Optional: Request Permissions (Location if needed for fetch)
//        // requestPermissions(); // Implement this method if needed
//
//    }
//
//    /**
//     * Helper to get a user-friendly title for the current mode.
//     * @param mode The current mode.
//     * @return The display title for the mode.
//     */
//    private File getMorningAthkarImageFile() {
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "athkarm" + (isLandscape ? "l" : "") + ".png");
//    }
//
//    private File getEveningAthkarImageFile() {
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "athkare" + (isLandscape ? "l" : "") + ".png");
//    }
//
//    private File getAfterPrayer1AthkarImageFile() {
//        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "allwhite" + (isLandscape ? "" : "h") + ".png");
//    }
//
//
//    /**
//     * Updates UI visibility based on the current mode.
//     * Hides/shows the appropriate content sections.
//     * @param mode The current mode.
//     */
//    private void updateUIForMode(Mode mode) {
//
//        // Manage visibility of main mode content area (textViewModeContent)
//        // And the prayer times specific section
//        if (prayerTimesSection != null) prayerTimesSection.setVisibility(View.GONE);
//        if (textViewModeContent != null) textViewModeContent.setVisibility(View.GONE);
//
//        switch (mode) {
//            case PRAYER:
//                // Show the prayer times list section
//                if (prayerTimesSection != null) prayerTimesSection.setVisibility(View.VISIBLE);
//                // The textViewModeContent might be hidden or show a minimal message in this mode
//                break;
//            case IKAMA:
//                // Show the general mode content TextView for the Ikama list
//                if (ikamaAnnouncementContainer != null) ikamaAnnouncementContainer.setVisibility(View.VISIBLE);
//                // You would also show UI for editing offsets here, if implemented
//                break;
//            case ATHKAR:
//                // Show the general mode content TextView for the Athkar text
//                if (textViewModeContent != null) textViewModeContent.setVisibility(View.VISIBLE);
//                break;
//        }
//
//        // Ensure Date, Time, Mode Title, and Mode Buttons are always visible
//        if (textViewCurrentTime != null) textViewCurrentTime.setVisibility(View.VISIBLE);
//        if (textViewCurrentDateGregorian != null) textViewCurrentDateGregorian.setVisibility(View.VISIBLE);
//        if (textViewCurrentDateHijri != null) textViewCurrentDateHijri.setVisibility(View.VISIBLE);
//        if (textViewModeTitle != null) textViewModeTitle.setVisibility(View.VISIBLE);
//        if (buttonModePrayer != null) buttonModePrayer.setVisibility(View.VISIBLE);
//        if (buttonModeIkama != null) buttonModeIkama.setVisibility(View.VISIBLE);
//        if (buttonModeAthkar != null) buttonModeAthkar.setVisibility(View.VISIBLE);
//    }
//
//
//    // Optional: Implement requestPermissions if using location for fetching
//
//    @Override
//    protected void onDestroy() {
//        super.onDestroy();
//        Log.d(TAG, "onDestroy");
//        // ViewModel's onCleared is called automatically
//        // Service lifecycle is managed separately.
//    }
//
//}
package com.arapeak.alrbea.UI.Activity;

import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsFragment;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;

public class SettingsActivity extends BaseAppCompatActivity {

    public static final String IS_INITIAL = "isInitial";
    private static final String TAG = "SettingsActivity";
    private static Toolbar toolbar;
    private static TextView textView;
    private static TextView textView2;
    private CoordinatorLayout contentLayout;
    private String localLanguage;
    private boolean isExit;
    private boolean isInitial;

    public static void showHideToolbar(boolean isShow) {
        if (isShow) {
            toolbar.setVisibility(View.VISIBLE);
        } else {
            toolbar.setVisibility(View.GONE);
        }
    }

    public static void setVisibilityItemMenu(boolean isShow) {
        if (getDefaultToolbar() == null) {
            return;
        }
        getDefaultToolbar().setNavigationIcon(isShow ? R.drawable.ic_back_arrow : null);
    }

    public static Toolbar getDefaultToolbar() {
        return toolbar;
    }

    public static Toolbar getToolbar() {
        return toolbar;
    }

    public static void setTextTite(String exit) {

        if (textView != null)
            textView.setText(exit);
    }

    public static void setTextTite2(String exit) {

        if (textView2 != null)
            textView2.setText(exit);
    }

    public static void setViewTite() {

        if (textView != null)
            textView.setVisibility(View.GONE);
    }

    public static void setViewTites() {

        if (textView != null)
            textView.setVisibility(View.VISIBLE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        adjustDisplayScale();
        Utils.initActivity(SettingsActivity.this);
        setContentView(R.layout.app_bar_settings);

        initView();
        SetParameter();
        SetAction();

        Resources resources = getResources();
        Configuration config = resources.getConfiguration();

        if (config.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Log.e("Orientatiob", "SettingsActivity Portrait");
        } else if (config.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.e("Orientatiob", " SettingsActivity LANDSCAPE");
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Utils.initActivity(SettingsActivity.this);
        Utils.setScreenBrightnessMax(SettingsActivity.this);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
//        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);

//        switch (HawkSettings.getAppOrientation()) {
//            case 1:
//                Log.e("Orientation", "switch to SCREEN_ORIENTATION_LANDSCAPE");
//                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
//                break;
//            case 2:
//                Log.e("Orientation", "switch to SCREEN_ORIENTATION_REVERSE_PORTRAIT");
//                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
//                break;
//            default:
//                Log.e("Orientation", "switch to SCREEN_ORIENTATION_PORTRAIT");
//                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
//        }

    }

    @Override
    protected void onSaveInstanceState(Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            savedInstanceState.putAll(getIntent().getExtras());
        }
        super.onSaveInstanceState(savedInstanceState);
    }

    @Override
    public void onBackPressed() {
        if (getSupportFragmentManager().getBackStackEntryCount() == 1) {
            startActivity(new Intent(this, MainActivity.class));
            return;
        }
        super.onBackPressed();
    }

    public void restartActivity() {
        Intent intent = new Intent(SettingsActivity.this, MainActivity.class);
        startActivity(intent);
        finish();

    }

    private void initView() {
        toolbar = findViewById(R.id.toolbar);
        contentLayout = findViewById(R.id.content_CoordinatorLayout_SettingsActivity);
        textView = findViewById(R.id.textView);
        textView2 = findViewById(R.id.textView2);

        localLanguage = HawkSettings.getLocaleLanguage();
        if (getIntent() != null && getIntent().getExtras() != null) {
            isInitial = getIntent().getExtras().getBoolean(IS_INITIAL, false);
        }
    }

    private void SetParameter() {

        setSupportActionBar(toolbar);


        if (HawkSettings.isArabic()) {
            ViewCompat.setLayoutDirection(contentLayout, ViewCompat.LAYOUT_DIRECTION_RTL);
        } else {
            ViewCompat.setLayoutDirection(contentLayout, ViewCompat.LAYOUT_DIRECTION_LTR);
        }

        setVisibilityItemMenu(true);

        if (isInitial) {
            // Utils.loadFragment(ContactUsFragment.newInstance()
            //       , SettingsActivity.this
            //     , -1);
        } else {
            Utils.loadFragment(SettingsFragment.newInstance()
                    , SettingsActivity.this
                    , -1);
        }
    }

    private void SetAction() {
        getDefaultToolbar().setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
    }

    public void setExit(boolean exit) {
        isExit = exit;
    }

    public String getLocalLanguage() {
        return localLanguage;
    }
}

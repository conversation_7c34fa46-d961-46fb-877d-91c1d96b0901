package com.arapeak.alrbea.Service;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.IBinder;
import android.os.SystemClock;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.arapeak.alrbea.BroadcastReceiver.AlarmReceiver;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

public class AppMonitorService extends Service {
    private static final String TAG = "AppMonitorService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "AppMonitorChannel";
    private static final long CHECK_INTERVAL = 5 * 60 * 1000; // 10 minutes for 24/7 monitoring
    private static final long HEARTBEAT_INTERVAL = 2 * 60 * 1000; // 2 minutes heartbeat
    private static final String PREFS_NAME = "AppMonitorPrefs";
    private static final String LAST_CHECK_KEY = "last_check_time";
    private static final String RESTART_COUNT_KEY = "restart_count";

    private AlarmManager alarmManager;
    private PendingIntent alarmPendingIntent;
    private NotificationManager notificationManager;
    private long lastHeartbeat = 0;
    private int restartCount = 0;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "AppMonitorService created for 24/7 monitoring");

        try {
            // Initialize notification manager
            notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

            // Load previous state
            loadServiceState();

            createNotificationChannel();

            // For Android 14 (API 34) and above, we need to specify the foreground service
            // type
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // API 34
                startForeground(NOTIFICATION_ID, createNotification(),
                        ServiceInfo.FOREGROUND_SERVICE_TYPE_SYSTEM_EXEMPTED);
            } else {
                startForeground(NOTIFICATION_ID, createNotification());
            }

            // Set up the alarm to check app status every 10 minutes
            setupAlarm();

            // Initialize heartbeat
            lastHeartbeat = System.currentTimeMillis();

            Log.i(TAG, "24/7 App Monitor Service initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Load service state from preferences
     */
    private void loadServiceState() {
        try {
            android.content.SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            restartCount = prefs.getInt(RESTART_COUNT_KEY, 0);
            long lastCheck = prefs.getLong(LAST_CHECK_KEY, 0);

            Log.d(TAG, "Loaded service state - Restart count: " + restartCount +
                    ", Last check: " + (lastCheck > 0 ? new java.util.Date(lastCheck) : "Never"));

        } catch (Exception e) {
            Log.e(TAG, "Error loading service state", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Save service state to preferences
     */
    private void saveServiceState() {
        try {
            android.content.SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            prefs.edit()
                    .putInt(RESTART_COUNT_KEY, restartCount)
                    .putLong(LAST_CHECK_KEY, System.currentTimeMillis())
                    .apply();

        } catch (Exception e) {
            Log.e(TAG, "Error saving service state", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "AppMonitorService started");

        // If service is killed, restart it
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "AppMonitorService destroyed");

        try {
            // Restart the service if it's destroyed
            Intent restartServiceIntent = new Intent(getApplicationContext(), AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(restartServiceIntent);
            } else {
                startService(restartServiceIntent);
            }

            // Cancel the alarm
            if (alarmManager != null && alarmPendingIntent != null) {
                alarmManager.cancel(alarmPendingIntent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "App Monitor Service",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("Keeps the app running in the background");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    private Notification createNotification() {
        return createNotificationWithStatus("24/7 Monitoring Active");
    }

    /**
     * Create notification with custom status message
     */
    private Notification createNotificationWithStatus(String status) {
        try {
            Intent notificationIntent = new Intent(this, MainActivity.class);
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }
            PendingIntent pendingIntent = PendingIntent.getActivity(
                    this, 0, notificationIntent, flags);

            // Create detailed status text
            String detailedStatus = status + " • Restarts: " + restartCount;
            String timeInfo = "Last check: " + new java.text.SimpleDateFormat("HH:mm",
                    java.util.Locale.getDefault()).format(new java.util.Date());

            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("مواقيت الربيع 24/7")
                    .setContentText(detailedStatus)
                    .setSubText(timeInfo)
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setContentIntent(pendingIntent)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setOngoing(true) // Make it persistent
                    .setAutoCancel(false) // Prevent accidental dismissal
                    .build();

        } catch (Exception e) {
            Log.e(TAG, "Error creating notification", e);
            // Fallback to simple notification
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("مواقيت الربيع")
                    .setContentText("")
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .build();
        }
    }

    /**
     * Update notification with current status
     */
    public void updateNotificationStatus(String status) {
        try {
            if (notificationManager != null) {
                Notification updatedNotification = createNotificationWithStatus(status);
                notificationManager.notify(NOTIFICATION_ID, updatedNotification);
                Log.v(TAG, "Notification updated: " + status);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating notification", e);
        }
    }

    private void setupAlarm() {
        alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        Intent alarmIntent = new Intent(this, AlarmReceiver.class);
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        alarmPendingIntent = PendingIntent.getBroadcast(this, 0, alarmIntent, flags);

        // Set repeating alarm every 10 minutes for 24/7 monitoring
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Use setExactAndAllowWhileIdle for better reliability on newer Android
            // versions
            alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                    alarmPendingIntent);
        } else {
            alarmManager.setRepeating(
                    AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                    CHECK_INTERVAL,
                    alarmPendingIntent);
        }

        Log.i(TAG, "Alarm set to check app status every 10 minutes for 24/7 monitoring");
    }
}

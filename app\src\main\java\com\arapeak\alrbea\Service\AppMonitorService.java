package com.arapeak.alrbea.Service;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.IBinder;
import android.os.SystemClock;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.arapeak.alrbea.BroadcastReceiver.AlarmReceiver;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

public class AppMonitorService extends Service {
    private static final String TAG = "AppMonitorService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "AppMonitorChannel";
    private static final long CHECK_INTERVAL = 2 * 60 * 1000; // 10 minutes for 24/7 monitoring
    private static final long QUICK_CHECK_INTERVAL = 10*60* 1000; // 30 seconds for quick checks
    private static final long HEARTBEAT_INTERVAL = 2 * 60 * 1000; // 2 minutes heartbeat
    private static final String PREFS_NAME = "AppMonitorPrefs";
    private static final String LAST_CHECK_KEY = "last_check_time";
    private static final String RESTART_COUNT_KEY = "restart_count";
    private static final String LAST_FOREGROUND_CHECK_KEY = "last_foreground_check";

    private AlarmManager alarmManager;
    private PendingIntent alarmPendingIntent;
    private PendingIntent quickCheckPendingIntent;
    private NotificationManager notificationManager;
    private long lastHeartbeat = 0;
    private int restartCount = 0;
    private long lastForegroundCheck = 0;
    private boolean isAggressiveMode = true; // More aggressive monitoring

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "AppMonitorService created for 24/7 monitoring");

        try {
            // Initialize notification manager
            notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

            // Load previous state
            loadServiceState();

            createNotificationChannel();

            // For Android 14 (API 34) and above, we need to specify the foreground service
            // type
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // API 34
                startForeground(NOTIFICATION_ID, createNotification(),
                        ServiceInfo.FOREGROUND_SERVICE_TYPE_SYSTEM_EXEMPTED);
            } else {
                startForeground(NOTIFICATION_ID, createNotification());
            }

            // Set up the alarm to check app status every 10 minutes
            setupAlarm();

            // Initialize heartbeat
            lastHeartbeat = System.currentTimeMillis();

            Log.i(TAG, "24/7 App Monitor Service initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Load service state from preferences
     */
    private void loadServiceState() {
        try {
            android.content.SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            restartCount = prefs.getInt(RESTART_COUNT_KEY, 0);
            long lastCheck = prefs.getLong(LAST_CHECK_KEY, 0);

            Log.d(TAG, "Loaded service state - Restart count: " + restartCount +
                    ", Last check: " + (lastCheck > 0 ? new java.util.Date(lastCheck) : "Never"));

        } catch (Exception e) {
            Log.e(TAG, "Error loading service state", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Save service state to preferences
     */
    private void saveServiceState() {
        try {
            android.content.SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            prefs.edit()
                    .putInt(RESTART_COUNT_KEY, restartCount)
                    .putLong(LAST_CHECK_KEY, System.currentTimeMillis())
                    .apply();

        } catch (Exception e) {
            Log.e(TAG, "Error saving service state", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "AppMonitorService started");

        // If service is killed, restart it
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "AppMonitorService destroyed");

        try {
            // Restart the service if it's destroyed
            Intent restartServiceIntent = new Intent(getApplicationContext(), AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(restartServiceIntent);
            } else {
                startService(restartServiceIntent);
            }

            // Cancel the alarm
            if (alarmManager != null && alarmPendingIntent != null) {
                alarmManager.cancel(alarmPendingIntent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "App Monitor Service",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("Keeps the app running in the background");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    private Notification createNotification() {
        return createNotificationWithStatus("24/7 Monitoring Active");
    }

    /**
     * Create notification with custom status message
     */
    private Notification createNotificationWithStatus(String status) {
        try {
            Intent notificationIntent = new Intent(this, MainActivity.class);
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }
            PendingIntent pendingIntent = PendingIntent.getActivity(
                    this, 0, notificationIntent, flags);

            // Create detailed status text
            String detailedStatus = status + " • اعادة تشغيل: " + restartCount;
            String timeInfo = "اخر تحديث: " + new java.text.SimpleDateFormat("HH:mm",
                    java.util.Locale.getDefault()).format(new java.util.Date());

            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("مواقيت الربيع 24/7")
                    .setContentText(detailedStatus)
                    .setSubText(timeInfo)
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setContentIntent(pendingIntent)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setOngoing(true) // Make it persistent
                    .setAutoCancel(false) // Prevent accidental dismissal
                    .build();

        } catch (Exception e) {
            Log.e(TAG, "Error creating notification", e);
            // Fallback to simple notification
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("مواقيت الربيع")
                    .setContentText("")
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .build();
        }
    }

    /**
     * Update notification with current status
     */
    public void updateNotificationStatus(String status) {
        try {
            if (notificationManager != null) {
                Notification updatedNotification = createNotificationWithStatus(status);
                notificationManager.notify(NOTIFICATION_ID, updatedNotification);
                Log.v(TAG, "Notification updated: " + status);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating notification", e);
        }
    }

    private void setupAlarm() {
        try {
            alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
            if (alarmManager == null) {
                Log.e(TAG, "AlarmManager is null, cannot setup alarms");
                return;
            }

            // Setup main 10-minute check alarm
            setupMainAlarm();

            // Setup aggressive quick check alarm for immediate app recovery
            if (isAggressiveMode) {
                setupQuickCheckAlarm();
            }

            Log.i(TAG, "Alarms set up for aggressive 24/7 monitoring - Main: 10min, Quick: 30sec");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up alarms", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Setup main 10-minute monitoring alarm
     */
    private void setupMainAlarm() {
        try {
            Intent alarmIntent = new Intent(this, AlarmReceiver.class);
            alarmIntent.putExtra("alarm_type", "main_check");

            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }
            alarmPendingIntent = PendingIntent.getBroadcast(this, 1001, alarmIntent, flags);

            // Set repeating alarm every 10 minutes for 24/7 monitoring
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Use setExactAndAllowWhileIdle for better reliability on newer Android
                // versions
                alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                        alarmPendingIntent);
            } else {
                alarmManager.setRepeating(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                        CHECK_INTERVAL,
                        alarmPendingIntent);
            }

            Log.d(TAG, "Main alarm set for 10-minute checks");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up main alarm", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Setup aggressive quick check alarm for immediate app recovery
     */
    private void setupQuickCheckAlarm() {
        try {
            Intent quickAlarmIntent = new Intent(this, AlarmReceiver.class);
            quickAlarmIntent.putExtra("alarm_type", "quick_check");

            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }
            quickCheckPendingIntent = PendingIntent.getBroadcast(this, 1002, quickAlarmIntent, flags);

            // Set quick check alarm every 30 seconds for aggressive monitoring
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + QUICK_CHECK_INTERVAL,
                        quickCheckPendingIntent);
            } else {
                alarmManager.setRepeating(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + QUICK_CHECK_INTERVAL,
                        QUICK_CHECK_INTERVAL,
                        quickCheckPendingIntent);
            }

            Log.d(TAG, "Quick check alarm set for 30-second aggressive monitoring");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up quick check alarm", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}

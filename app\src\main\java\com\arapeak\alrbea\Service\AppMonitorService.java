package com.arapeak.alrbea.Service;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.IBinder;
import android.os.SystemClock;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.arapeak.alrbea.BroadcastReceiver.AlarmReceiver;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

public class AppMonitorService extends Service {
    private static final String TAG = "AppMonitorService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "AppMonitorChannel";
    private static final long CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

    private AlarmManager alarmManager;
    private PendingIntent alarmPendingIntent;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "AppMonitorService created");

        try {
            createNotificationChannel();

            // For Android 14 (API 34) and above, we need to specify the foreground service type
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // API 34
                startForeground(NOTIFICATION_ID, createNotification(), ServiceInfo.FOREGROUND_SERVICE_TYPE_SYSTEM_EXEMPTED);
            } else {
                startForeground(NOTIFICATION_ID, createNotification());
            }

            // Set up the alarm to check app status every 5 minutes
            setupAlarm();
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "AppMonitorService started");

        // If service is killed, restart it
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "AppMonitorService destroyed");

        try {
            // Restart the service if it's destroyed
            Intent restartServiceIntent = new Intent(getApplicationContext(), AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(restartServiceIntent);
            } else {
                startService(restartServiceIntent);
            }

            // Cancel the alarm
            if (alarmManager != null && alarmPendingIntent != null) {
                alarmManager.cancel(alarmPendingIntent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "App Monitor Service",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Keeps the app running in the background");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    private Notification createNotification() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, notificationIntent, flags);

        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("App Monitor")
                .setContentText("Keeping your app running")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .build();
    }

    private void setupAlarm() {
        alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        Intent alarmIntent = new Intent(this, AlarmReceiver.class);
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        alarmPendingIntent = PendingIntent.getBroadcast(this, 0, alarmIntent, flags);

        // Set repeating alarm every 5 minutes
        alarmManager.setRepeating(
                AlarmManager.ELAPSED_REALTIME_WAKEUP,
                SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                CHECK_INTERVAL,
                alarmPendingIntent
        );

        Log.d(TAG, "Alarm set to check app status every 5 minutes");
    }
}

package com.arapeak.alrbrea.core_ktx.ui.appupdater

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException

class UpdaterDialogs {


    fun initConfirmDialog(
        context: Context,
        imageResourcesId: Int,
        title: String,
        bodyMessage: String,
        isCancelable: Boolean,
        isShowConfirmDialog: Boolean,
        onSuccessful: (<PERSON>olean) -> Unit
    ): Dialog {

        val view: View = LayoutInflater.from(context).inflate(R.layout.layout_dialog_confirm, null, false)

        val iconImageView = view.findViewById<ImageView>(R.id.icon_ImageView_ConfirmationDialog)
        val titleTextView = view.findViewById<TextView>(R.id.title_TextView_ConfirmationDialog)
        val bodyMessageTextView = view.findViewById<TextView>(R.id.bodyMessage_TextView_ConfirmationDialog)
        val confirmButton = view.findViewById<Button>(R.id.confirm_Button_ConfirmationDialog)
        val cancelButton = view.findViewById<Button>(R.id.cancel_Button_ConfirmationDialog)
        val username = view.findViewById<EditText>(R.id.username)
        username.visibility = View.GONE
        titleTextView.text = title
        bodyMessageTextView.text = bodyMessage

        try {
            iconImageView.setImageResource(imageResourcesId)
        } catch (e: Exception) {
            logException(e)
        }

        val confirmDialog = Dialog(context, R.style.ConfirmDialogStyle)
        confirmDialog.setContentView(view)
        confirmDialog.setCancelable(isCancelable)

        if (! isCancelable) {
            cancelButton.visibility = View.GONE
        }

        confirmButton.setOnClickListener {
            onSuccessful(true)
            confirmDialog.dismiss()
        }

        cancelButton.setOnClickListener {
            onSuccessful(false)

            confirmDialog.dismiss()
        }

        if (isShowConfirmDialog) {
            confirmDialog.show()
        }
        return confirmDialog
    }
}
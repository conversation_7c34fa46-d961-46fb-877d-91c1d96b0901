<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!--<item>
        <shape>
            <solid android:color="#11000000" />
            <corners android:radius="4dp" />
        </shape>

    </item>-->
    <item
        android:bottom="2dp"
        android:gravity="center"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape>


            <solid android:color="#CACACA" />
            <corners android:radius="0.5dp" />
            <!--<stroke
                android:width="@dimen/CheckBoxWidthStroke"
                android:color="@color/checkBoxStroke" />-->
            <size
                android:width="52dp"
                android:height="52dp" />
        </shape>
    </item>
</layer-list>
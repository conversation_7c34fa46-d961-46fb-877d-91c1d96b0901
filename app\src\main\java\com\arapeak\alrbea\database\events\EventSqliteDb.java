package com.arapeak.alrbea.database.events;

import static com.arapeak.alrbea.AppController.db;

import android.database.sqlite.SQLiteDatabase;

import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.ArrayList;
import java.util.List;

public class EventSqliteDb implements IEventDb {
    private static IEventDb instance = null;

    public static IEventDb instance() {
        if (instance == null) instance = new EventSqliteDb();
        return instance;
    }

    @Override
    public List<Event> getAll() {
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            return EventSqlExecutioner.getAll(database);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<Event> getAllEnabled() {
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            return EventSqlExecutioner.getAllEnabled(database);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return new ArrayList<>();
    }

    @Override
    public Event get(int id) {
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            return EventSqlExecutioner.get(database, id);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return null;
    }

    @Override
    public boolean add(Event event) {
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            return EventSqlExecutioner.add(database, event);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return false;
    }

    @Override
    public boolean update(Event event) {
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            return EventSqlExecutioner.update(database, event);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return false;
    }

    @Override
    public boolean delete(Event event) {
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            return EventSqlExecutioner.delete(database, event);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return false;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/theme_ConstraintLayout_ThemeViewHolder"
    android:layout_width="@dimen/theme_item_width"
    android:layout_height="@dimen/theme_item_height"
    android:layout_marginBottom="@dimen/_5sdp"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/imageTheme_ImageView_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/loadingann"
        android:layout_width="match_parent"
        android:layout_height="match_parent"


        android:visibility="gone"

        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/selectedTheme_View_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#4D1DAFEC"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/selectedTheme_TextView_ThemeViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/enable"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_24sdp"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RadioButton
        android:id="@+id/checkTheme_RadioButton_ThemeViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:checked="true"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imageTheme_ImageView_ThemeViewHolder" />

    <Button
        android:id="@+id/logo_ThemeViewHolderdownload"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/without_corners_50_background_blue"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="center_vertical|center_horizontal"
        android:includeFontPadding="false"
        android:text="@string/download"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_14sdp"
        android:visibility="gone"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/customize_ThemeViewHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/customize"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp"
            android:visibility="gone" />

        <Button
            android:id="@+id/logo_ThemeViewHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/logoch"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp"
            android:visibility="gone" />

        <Button
            android:id="@+id/color_ThemeViewHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/logo_color"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp"
            android:visibility="gone" />

        <Button
            android:id="@+id/unlogo_ThemeViewHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/unlogoch"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp"
            android:visibility="gone" />
    </LinearLayout>
    <!--

    <Button
        android:id="@+id/logo_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/without_corners_50_background_blue"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="center_vertical|center_horizontal"
        android:includeFontPadding="false"
        android:text="@string/logoch"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_14sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <Button
        android:id="@+id/color_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/without_corners_50_background_blue"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="center_vertical|center_horizontal"
        android:includeFontPadding="false"
        android:text="@string/logo_color"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_14sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/unlogo_ThemeViewHolder"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <Button
        android:id="@+id/unlogo_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/without_corners_50_background_blue"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="center_vertical|center_horizontal"
        android:includeFontPadding="false"
        android:text="@string/unlogoch"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_14sdp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />-->


</androidx.constraintlayout.widget.ConstraintLayout>
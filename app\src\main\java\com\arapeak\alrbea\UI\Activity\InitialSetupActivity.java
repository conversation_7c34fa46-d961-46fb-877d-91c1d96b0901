package com.arapeak.alrbea.UI.Activity;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_START;

import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.location.Location;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.Enum.PrayerMethod;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Model.InfoOfCode;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Service.GPSTracker;
import com.arapeak.alrbea.UI.Activity.Country.CountryActivity;
import com.arapeak.alrbea.UI.CustomView.OptionChooseAdapter;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbea.ummalqura_objects.City;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationRequest;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/*, GoogleApiClient.ConnectionCallbacks
        , GoogleApiClient.OnConnectionFailedListener
        , com.google.android.gms.location.LocationListener*/
public class InitialSetupActivity extends BaseAppCompatActivity
        implements View.OnClickListener, AdapterCallback {

    private static final String TAG = "UI.Act.InitSetupAct";
    public static boolean isAddLocation;
    private final int ACCESS_COARSE_FINE_LOCATION_REQUEST_CODE = 102;
    private final int UPDATE_INTERVAL = 1000;
    private final int FASTEST_INTERVAL = 1000;
    GPSTracker gpsTracker;
    boolean isOverlayDrawPermissionRequested = false;
    private EditText activeCodeEditText;
    private Button enableButton, contactUsButton;
    //    private TextView doNotHaveCodeTextView;
    private Dialog loadingDialog, loadingDialogmap;
    private ScrollView rootScrollView;
    private ConstraintLayout contentLayout;
    private LinearLayout activeCodeLinearLayout, optionChooseLayout;
    private TextView titleTextView;
    private RecyclerView optionChooseRecyclerView;
    private Button saveSelectButton;
    private TextView debug1TextView, debug2TextView;
    private OptionChooseAdapter optionChooseAdapter;
    private GoogleApiClient mGoogleApiClient;
    private Location mLocation;
    private LocationRequest mLocationRequest;
    private City currentCity;
    private BroadcastReceiver receiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        adjustDisplayScale();
        Utils.initActivity(InitialSetupActivity.this);
        setContentView(R.layout.activity_initial_setup);
        initView();
        SetParameter();
        SetAction();
        gpsTracker = new GPSTracker(this);
        AppController.screenOrientationEnforcer.start();


        Resources resources = getResources();
        Configuration config = resources.getConfiguration();

        if (config.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Log.e("Orientatiob", "InitialSetupActivity Portrait");
        } else if (config.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.e("Orientatiob", "InitialSetupActivity LANDSCAPE");
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Utils.initActivity(InitialSetupActivity.this);
        Utils.setScreenBrightnessMax(InitialSetupActivity.this);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        boolean isInitialSetup = Hawk.get(ConstantsOfApp.IS_INITIAL_SETUP_KEY, true);
        InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);

        PrayerMethod prayerMethod = HawkSettings.getCurrentPrayerMethod(true);
        if (infoOfCode != null && !infoOfCode.getCode().isEmpty()) {
            FirebaseCrashlytics.getInstance().setCustomKey("user", infoOfCode.getCode());
            FirebaseCrashlytics.getInstance().setUserId(infoOfCode.getCode());

            if (isInitialSetup || HawkSettings.isUpdateLocation()) {
                HawkSettings.setUpdateLocation(false);
                locationManual();
            } else if (prayerMethod == null) {
                activeCodeLinearLayout.setVisibility(View.GONE);
                optionChooseLayout.setVisibility(View.VISIBLE);
                List<Object> objectList = new ArrayList<>();
                Collections.addAll(objectList, PrayerMethod.values());
                optionChooseAdapter = new OptionChooseAdapter(InitialSetupActivity.this
                        , objectList
                        , ViewsAlrabeeaTimes.RADIO_BUTTON
                        , getString(R.string.setting_app_for_first_time)
                        , getString(R.string.choose_pray_time_system)
                        , this);
                optionChooseAdapter.setId(1);
                optionChooseRecyclerView.setAdapter(optionChooseAdapter);
            } else if (HawkSettings.getAppOrientation() == -1) {
                activeCodeLinearLayout.setVisibility(View.GONE);
                optionChooseLayout.setVisibility(View.VISIBLE);

                List<Object> objectList = new ArrayList<>();
                objectList.addAll(Arrays.asList(getResources().getStringArray(R.array.orientation_detection_Methods)));
                optionChooseAdapter = new OptionChooseAdapter(InitialSetupActivity.this
                        , objectList
                        , ViewsAlrabeeaTimes.RADIO_BUTTON
                        , getString(R.string.define_orientation_for_one_time)
                        , ""
                        , this);
                optionChooseAdapter.setId(2);

                optionChooseRecyclerView.setAdapter(optionChooseAdapter);

            } else {

                // Bundle bundle = new Bundle();
                // bundle.putString(FirebaseAnalytics.Param.ITEM_ID, infoOfCode.getCode());
                // bundle.putString(FirebaseAnalytics.Param.ITEM_NAME, infoOfCode.getName());
                //bundle.putString(FirebaseAnalytics.Param.CONTENT_TYPE, infoOfCode.getDevice_id());
                //mFirebaseAnalytics.logEvent(FirebaseAnalytics.Event.SELECT_CONTENT, bundle);
                goToMainActivity();
            }
        } else {
            activeCodeLinearLayout.setVisibility(View.VISIBLE);
            optionChooseLayout.setVisibility(View.GONE);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mGoogleApiClient != null && mGoogleApiClient.isConnected()) {
            mGoogleApiClient.disconnect();
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            savedInstanceState.putAll(getIntent().getExtras());
        }
        super.onSaveInstanceState(savedInstanceState);
    }

    private void initView() {
        rootScrollView = findViewById(R.id.root_ScrollView_InitialSetupActivity);
        contentLayout = findViewById(R.id.content_ConstraintLayout_InitialSetupActivity);
        activeCodeLinearLayout = findViewById(R.id.activeCode_LinearLayout_InitialSetupActivity);
        titleTextView = findViewById(R.id.title_TextView_InitialSetupActivity);
        activeCodeEditText = findViewById(R.id.activeCode_EditText_InitialSetupActivity);
        enableButton = findViewById(R.id.enable_Button_InitialSetupActivity);
        contactUsButton = findViewById(R.id.contactUs_Button_InitialSetupActivity);
        optionChooseRecyclerView = findViewById(R.id.optionChoose_RecyclerView_InitialSetupActivity);
        optionChooseLayout = findViewById(R.id.optionChoose_LinearLayout_InitialSetupActivity);
        saveSelectButton = findViewById(R.id.saveSelect_Button_InitialSetupActivity);
        debug1TextView = findViewById(R.id.debug1_TextView_InitialSetupActivity);
        debug2TextView = findViewById(R.id.debug2_TextView_InitialSetupActivity);
        loadingDialog = Utils.initLoadingDialog(InitialSetupActivity.this);
//        loadingDialogmap = Utils.initLoadingDialogmap(InitialSetupActivity.this);
//        locationManager = (LocationManager)
//                getSystemService(Context.LOCATION_SERVICE);
//        locationListener = new AlrabeeaTimesLocationListener(this, R.string.your_location_has_been_successfully_updated);


    }

    private void SetParameter() {
        Hawk.put(IS_START, true);
        if (HawkSettings.isArabic()) {
            ViewCompat.setLayoutDirection(rootScrollView, ViewCompat.LAYOUT_DIRECTION_RTL);
        } else {
            ViewCompat.setLayoutDirection(rootScrollView, ViewCompat.LAYOUT_DIRECTION_LTR);
        }

//        Utils.copyToCountryAndCityRealm(InitialSetupActivity.this);

    }

    private void SetAction() {
        enableButton.setOnClickListener(this);
        contactUsButton.setOnClickListener(this);
        saveSelectButton.setOnClickListener(this);

    }

    /**
     * @noinspection t
     */
    @Override
    public void onClick(View v) {
        if (v.getId() == enableButton.getId()) {
            loadingDialog.show();
            if (isValid()) {
                WifiManager manager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
//                String macAddress = Utils.recupAdresseMAC(manager);

                AlrabeeaTimesRequests.checkAndUseActivationCode(getApplicationContext()
                        , ConstantsOfApp.CHECK_OR_ADD_LICENSES
                        , activeCodeEditText.getText().toString()
                        , Utils.getAndroidDeviceId(InitialSetupActivity.this)
                        , new OnCompleteListener<InfoOfCode, String>() {
                            @Override
                            public void onSuccess(InfoOfCode infoOfCode) {
                                loadingDialog.dismiss();
                                Hawk.put(ConstantsOfApp.INFO_OF_CODE_KEY, infoOfCode);
                                Utils.showSuccessAlert(InitialSetupActivity.this
                                        , getString(R.string.application_successfully_activated));

                                FirebaseCrashlytics.getInstance().setUserId(infoOfCode.getCode());
                                FirebaseCrashlytics.getInstance().setCustomKey("user", infoOfCode.getCode());

                                restartActivity();
                            }

                            @Override
                            public void onFail(String object) {
                                loadingDialog.dismiss();
                                if (Utils.getValueWithoutNull(object).isEmpty()) {
                                    Utils.showFailAlert(InitialSetupActivity.this
                                            , getString(R.string.there_is_a_problem)
                                            , getString(R.string.incorrect_activate_code));
                                } else {
                                    Utils.showFailAlert(InitialSetupActivity.this
                                            , getString(R.string.there_is_a_problem)
                                            , object);
                                }
                                FirebaseCrashlytics.getInstance().log("Error trying to Authenticate " + (Utils.getValueWithoutNull(object)));

                            }
                        });
            } else {
                loadingDialog.dismiss();
            }
        } else if (v.getId() == saveSelectButton.getId()) {


            int position = optionChooseAdapter.getPositionRadioButtonChecked();
            Object object = optionChooseAdapter.getItem(position);

            if (object == null) {
                Utils.showFailAlert(InitialSetupActivity.this
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.you_must_choose_a_item));
                return;
            }

            if (object instanceof String) {
                if (optionChooseAdapter.getId() == 0) {

                    switch (position) {
                        case 0:
                            locationAutomatic();
                            break;
                        case 1:
                            locationManual();
                            break;
                    }

                } else if (optionChooseAdapter.getId() == 2) {
                    HawkSettings.setAppOrientation(position);
                    goToMainActivity();
                }
            } else if (object instanceof PrayerMethod) {
                PrayerMethod prayerMethod = (PrayerMethod) object;
                HawkSettings.setCurrentPrayerMethod(prayerMethod);
//                Hawk.put(ConstantsOfApp.PRAYER_METHOD_KEY, prayerSystemsSchools);

                Intent intentSettingsActivity = new Intent(InitialSetupActivity.this, InitialSetupActivity.class);
                intentSettingsActivity.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                intentSettingsActivity.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intentSettingsActivity.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intentSettingsActivity);
            }
            /*else if (object instanceof PrayerSystemsSchools) {
                PrayerSystemsSchools prayerSystemsSchools = (PrayerSystemsSchools) object;
                Hawk.put(ConstantsOfApp.PRAYER_METHOD_KEY, prayerSystemsSchools);

                Intent intentSettingsActivity = new Intent(InitialSetupActivity.this, InitialSetupActivity.class);
                intentSettingsActivity.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                intentSettingsActivity.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intentSettingsActivity.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intentSettingsActivity);
            }*/
        } else if (v.getId() == contactUsButton.getId()) {
            Intent intentSettingsActivity = new Intent(InitialSetupActivity.this, SettingsActivity.class);
            intentSettingsActivity.putExtra(SettingsActivity.IS_INITIAL, true);
            startActivity(intentSettingsActivity);
        }
    }

    private void locationManual() {
        Intent intentCountryActivity = new Intent(InitialSetupActivity.this, CountryActivity.class);
        intentCountryActivity.putExtra(CountryActivity.MESSAGE_AFTER_FINISH_RESOURCES_ID_KEY
                , R.string.your_location_has_been_successfully_updated);
        startActivity(intentCountryActivity);
    }

    private void restartActivity() {
        boolean isInitialSetup = Hawk.get(ConstantsOfApp.IS_INITIAL_SETUP_KEY, true);
        InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);
//        PrayerSystemsSchools prayerSystemsSchools = Hawk.get(ConstantsOfApp.PRAYER_METHOD_KEY, null);
        PrayerMethod method = HawkSettings.getCurrentPrayerMethod(true);
        if ((infoOfCode == null || !infoOfCode.getCode().isEmpty())
                || (isInitialSetup || HawkSettings.isUpdateLocation())
                || method == null) {
            startActivity(new Intent(InitialSetupActivity.this, InitialSetupActivity.class));
            finish();
        }
    }

    private void goToMainActivity() {
        Intent intent;

        intent = new Intent(InitialSetupActivity.this, MainActivity.class);

        String packageName = getPackageName();
        PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
        if (pm.isIgnoringBatteryOptimizations(packageName))
            intent.setAction(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
        else {
            intent.setAction(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + packageName));
        }
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private boolean isValid() {
        boolean isValid = true;
        if (activeCodeEditText.getText().toString().isEmpty()) {
            activeCodeEditText.setError(getString(R.string.this_field_is_required));
            isValid = false;
        }

        return isValid;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == ACCESS_COARSE_FINE_LOCATION_REQUEST_CODE) {
            boolean allGranted = false;
            for (int i = 0; i < permissions.length; i++) {
                int grantResult = grantResults[i];
                if (grantResult == PackageManager.PERMISSION_GRANTED) {
                    allGranted = true;
                } else {
                    allGranted = false;
                    return;
                }
            }
            if (allGranted) {
            } else {
                Utils.showFailAlert(InitialSetupActivity.this
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.the_application_will_not_work_without_permission));
            }

        }
    }

    private void locationAutomatic() {
//        if(gpsTracker == null)
//            gpsTracker = new GPSTracker(this);
        if (!gpsTracker.isLocationEnabled()) {
            /*
                just for prevent going to main activity onResume :)
                this just one of the previous programmer shit , not me (^_^)
                ... jkd ,, this is my shit so what you gonna do about it haa?!
             */
            HawkSettings.setUpdateLocation(true);
            startActivity(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS));
        } else
            gpsTracker.getLocation(() -> runOnUiThread(() -> {
                HawkSettings.setUpdateLocation(false);
                restartActivity();
            }), null);

    }

    @Override
    public void onItemClick(int position, String tag) {
        Object object = optionChooseAdapter.getItem(position);
        if (object == null) {
            return;
        }
        if (object instanceof String) {
            switch (position) {
                case 0:
                    locationAutomatic();
                    break;
                case 1:
                    Intent intentCountryActivity = new Intent(InitialSetupActivity.this, CountryActivity.class);
                    intentCountryActivity.putExtra(CountryActivity.MESSAGE_AFTER_FINISH_RESOURCES_ID_KEY
                            , R.string.your_location_has_been_successfully_updated);
                    startActivity(intentCountryActivity);
                    break;
            }
        } else if (object instanceof PrayerMethod) {
            HawkSettings.setCurrentPrayerMethod((PrayerMethod) object);

            goToMainActivity();
        }
    }

}

package com.arapeak.alrbrea.core_ktx.model.screensaver

import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.ui.utils.formattedHH_SS
import kotlinx.datetime.LocalTime
import kotlinx.datetime.toJavaLocalTime

abstract class ScreensaverTimingConfig {
    abstract fun validate(): Pair<Boolean, Int>

    abstract fun convertToSave(): String

    abstract fun parseFromSave(value: String)
}


class TimingConfigDelay(private var delayMinutes: Int) : ScreensaverTimingConfig() {
    private val MaxDelayMinutes: Int = 23 * 60

    override fun validate(): Pair<Boolean, Int> {
        if (delayMinutes >= MaxDelayMinutes || delayMinutes <= 0)
            return false to R.string.delay_too_long
        return true to 0
    }

    override fun convertToSave(): String {
        return delayMinutes.toString()
    }

    override fun parseFromSave(value: String) {
        try {
            this.delayMinutes = Integer.parseInt(value)
        } catch (e: Exception) {
            logException(e)
        }
    }

    fun getDelayMinutes(): Int = delayMinutes

}

class TimingConfigPeriod(private var from: LocalTime, private var to: LocalTime) : ScreensaverTimingConfig() {

    override fun validate(): Pair<Boolean, Int> {
        if (to.toJavaLocalTime().isAfter(from.toJavaLocalTime()).not())
            return false to R.string.period_invalid

        if (to.hour == from.hour && to.minute == from.minute)
            return false to R.string.period_invalid_2

        return true to 0
    }

    override fun convertToSave(): String {
        return "${from.hour.toString().padStart(2, '0')}:" +
                "${from.minute.toString().padStart(2, '0')}-" +
                "${to.hour.toString().padStart(2, '0')}:" +
                "${to.minute.toString().padStart(2, '0')}"
    }

    override fun parseFromSave(value: String) {
        try {
            this.from = LocalTime.parse(value.substringBefore('-'))
            this.to = LocalTime.parse(value.substringAfter('-'))
        } catch (e: Exception) {
            logException(e)
        }
    }

    fun getFrom(): LocalTime = from
    fun getTo(): LocalTime = to

    fun getFromFormatted() = from.formattedHH_SS()
    fun getToFormatted() = to.formattedHH_SS()
}

class TimingConfigInterval(private var onIntervalMinutes: Int, private var offIntervalMinutes: Int) : ScreensaverTimingConfig() {

    private val MaxIntervalMinutes: Int = 23 * 60

    override fun validate(): Pair<Boolean, Int> {
        if (onIntervalMinutes >= MaxIntervalMinutes || onIntervalMinutes <= 0)
            return false to R.string.delay_too_long
        if (offIntervalMinutes >= MaxIntervalMinutes || offIntervalMinutes <= 0)
            return false to R.string.delay_too_long

        return true to 0
    }

    override fun convertToSave(): String {
        return "${this.onIntervalMinutes}-${this.offIntervalMinutes}"
    }

    override fun parseFromSave(value: String) {
        try {
            this.onIntervalMinutes = Integer.parseInt(value.substringBefore('-'))
            this.offIntervalMinutes = Integer.parseInt(value.substringAfter('-'))
        } catch (e: Exception) {
            logException(e)
        }
    }

    fun getOnIntervalMinutes(): Int = onIntervalMinutes
    fun getOffIntervalMinutes(): Int = offIntervalMinutes

}
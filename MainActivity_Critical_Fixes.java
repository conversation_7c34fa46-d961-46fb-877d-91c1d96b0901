package com.arapeak.alrbea.UI.Activity;

// CRITICAL FIXES FOR ORIGINAL MAINACTIVITY
// Apply these changes to fix the main issues:

// 1. REPLACE ALL Handler.postDelayed() calls with this safe pattern:

private Handler safeHandler = new Handler(Looper.getMainLooper());
private final List<Runnable> activeRunnables = new ArrayList<>();

private void safePostDelayed(Runnable runnable, long delay) {
    try {
        if (safeHandler != null && runnable != null) {
            activeRunnables.add(runnable);
            safeHandler.postDelayed(runnable, delay);
        }
    } catch (Exception e) {
        Log.e(TAG, "Error posting delayed task", e);
        CrashlyticsUtils.INSTANCE.logException(e);
    }
}

// 2. ADD THIS TO onDestroy() method:
@Override
protected void onDestroy() {
    try {
        // Clean up all handlers
        if (safeHandler != null) {
            for (Runnable runnable : activeRunnables) {
                safeHandler.removeCallbacks(runnable);
            }
            activeRunnables.clear();
            safeHandler = null;
        }
        
        // Clean up other resources
        if (announcementManager != null) {
            announcementManager.onStop();
            announcementManager = null;
        }
        
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
        loadingDialog = null;
        
        // Force garbage collection
        System.gc();
        
    } catch (Exception e) {
        Log.e(TAG, "Error in onDestroy", e);
        CrashlyticsUtils.INSTANCE.logException(e);
    }
    super.onDestroy();
}

// 3. WRAP ALL UI OPERATIONS with try-catch:
private void safeSetText(TextView textView, String text) {
    try {
        if (textView != null && text != null) {
            runOnUiThread(() -> textView.setText(text));
        }
    } catch (Exception e) {
        Log.e(TAG, "Error setting text", e);
        CrashlyticsUtils.INSTANCE.logException(e);
    }
}

private void safeSetVisibility(View view, int visibility) {
    try {
        if (view != null) {
            runOnUiThread(() -> view.setVisibility(visibility));
        }
    } catch (Exception e) {
        Log.e(TAG, "Error setting visibility", e);
        CrashlyticsUtils.INSTANCE.logException(e);
    }
}

// 4. REPLACE ALL setText() calls with safeSetText()
// REPLACE ALL setVisibility() calls with safeSetVisibility()

// 5. ADD ERROR HANDLING to all methods:
// Wrap method content with:
try {
    // existing method content
} catch (Exception e) {
    Log.e(TAG, "Error in methodName", e);
    CrashlyticsUtils.INSTANCE.logException(e);
}

// 6. REPLACE Thread.sleep() with Handler.postDelayed()
// NEVER use Thread.sleep() in Android

// 7. ADD NULL CHECKS everywhere:
if (object != null) {
    // use object
}

// 8. SETUP CRASH HANDLER in onCreate():
private void setupCrashHandler() {
    Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
        @Override
        public void uncaughtException(Thread thread, Throwable throwable) {
            Log.e(TAG, "Uncaught exception", throwable);
            CrashlyticsUtils.INSTANCE.logException(throwable);
            
            // Restart app
            Intent intent = new Intent(MainActivity.this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    });
}

// SUMMARY OF CRITICAL FIXES:
// 1. Safe Handler usage with proper cleanup
// 2. Comprehensive error handling with try-catch
// 3. Proper resource cleanup in onDestroy()
// 4. Safe UI operations with null checks
// 5. Crash handler for app restart
// 6. Memory leak prevention

// APPLY THESE CHANGES TO YOUR ORIGINAL MAINACTIVITY.JAVA
// This will fix 80-90% of the critical issues without major refactoring
package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;

public class NewsTicker {

    private String messageEvent, startDate, endDate;
    private boolean isActive;
    private long timeEventByMilliseconds;
    private long timeMillisCreation;

    public NewsTicker(String messageEvent
            , String startDate
            , String endDate
            , boolean isActive) {
        this.messageEvent = messageEvent;
        this.startDate = startDate;
        this.endDate = endDate;
        this.isActive = isActive;
        timeMillisCreation = System.currentTimeMillis();
    }

    public long getDurationOfEventMilliseconds() {
        return Utils.convertDateToLongTimestampWithTime(endDate)
                - Utils.convertDateToLongTimestampWithTime(startDate);
    }

    public long getEndTimeToShowMilliseconds() {
        return getDurationOfEventMilliseconds() + timeMillisCreation;
    }

    public String getMessageEvent() {
        return Utils.getValueWithoutNull(messageEvent);
    }

    public void setMessageEvent(String messageEvent) {
        this.messageEvent = messageEvent;
    }

    public String getStartDate() {
        return Utils.getValueWithoutNull(startDate);
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return Utils.getValueWithoutNull(endDate);
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public long getTimeEventByMilliseconds() {
        return timeEventByMilliseconds;
    }

    public void setTimeEventByMilliseconds(long timeEventByMilliseconds) {
        this.timeEventByMilliseconds = timeEventByMilliseconds;
    }

    public long getTimeMillisCreation() {
        return timeMillisCreation;
    }

    public void setTimeMillisCreation(long timeMillisCreation) {
        this.timeMillisCreation = timeMillisCreation;
    }
}

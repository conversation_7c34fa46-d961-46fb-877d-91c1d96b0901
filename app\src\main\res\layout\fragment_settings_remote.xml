<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="@dimen/_15sdp"
    android:layout_marginBottom="@dimen/_15sdp"
    android:fillViewport="true"
    tools:layoutDirection="rtl">


    <ProgressBar
        android:id="@+id/pb_remote_settings"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:indeterminate="true"
        android:padding="@dimen/_80sdp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/title_TextView_SubSettingsHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="14dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            android:textStyle="bold"
            android:text="@string/remote_control_tools" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_remote_tools"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</FrameLayout>
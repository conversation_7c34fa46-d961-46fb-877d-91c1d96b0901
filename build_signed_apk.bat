@echo off
echo ========================================
echo Building Signed APK with Size Optimization
echo ========================================

REM Set environment variables
set JAVA_HOME=%JAVA_HOME%
set ANDROID_HOME=%ANDROID_HOME%

REM Check if required environment variables are set
if "%JAVA_HOME%"=="" (
    echo ERROR: JAVA_HOME environment variable is not set
    echo Please set JAVA_HOME to your JDK installation directory
    pause
    exit /b 1
)

if "%ANDROID_HOME%"=="" (
    echo ERROR: ANDROID_HOME environment variable is not set
    echo Please set ANDROID_HOME to your Android SDK installation directory
    pause
    exit /b 1
)

echo Using JAVA_HOME: %JAVA_HOME%
echo Using ANDROID_HOME: %ANDROID_HOME%
echo.

REM Clean previous builds
echo Cleaning previous builds...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Building Release APK with Optimizations
echo ========================================

REM Build the signed release APK
echo Building signed release APK...
call gradlew assembleRelease
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build Completed Successfully!
echo ========================================

REM Find and display APK information
echo.
echo Generated APK files:
for /r "app\build\outputs\apk\release" %%f in (*.apk) do (
    echo %%f
    echo Size: 
    for %%A in ("%%f") do echo %%~zA bytes
    echo.
)

echo.
echo ========================================
echo APK Size Analysis
echo ========================================

REM Create APK analysis report
echo Generating APK analysis...
call gradlew :app:analyzeReleaseBundle
if %ERRORLEVEL% neq 0 (
    echo Warning: APK analysis failed, but APK was built successfully
)

echo.
echo ========================================
echo Additional Size Optimization Tips
echo ========================================
echo.
echo 1. The APK has been optimized with:
echo    - ProGuard code shrinking and obfuscation
echo    - Resource shrinking
echo    - PNG crunching
echo    - Unused resource removal
echo    - Native library filtering (ARM only)
echo    - Debug log removal
echo.
echo 2. For even smaller size, consider:
echo    - Using App Bundle instead of APK
echo    - Removing unused dependencies
echo    - Converting large images to WebP
echo    - Using vector drawables instead of PNGs
echo.
echo 3. APK location: app\build\outputs\apk\release\
echo.

pause

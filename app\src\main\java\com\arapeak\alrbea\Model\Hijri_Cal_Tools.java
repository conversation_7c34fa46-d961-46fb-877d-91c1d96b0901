package com.arapeak.alrbea.Model;

public class Hijri_Cal_Tools {
    public static double e3;
    public static double e4;
    public static int hijriDay;
    public static int hijriMonth;
    public static int hijriYear;
    public static double n3;
    public static double n4;
    public static double v1;
    public static double v10;
    public static double v11;
    public static double dhuhr;
    public static double v14;
    public static double v15;
    public static double v16;
    public static double v17;
    public static double v18;
    public static double v2;
    public static double v20;
    public static double v22;
    public static double sunRise;
    public static double maghrib;
    public static double v27;
    public static double ishaa;
    public static double v3;
    public static double v30;
    public static double fajr;
    public static double v32;
    public static double midNight;
    public static double thirdNight;
    public static double v4;
    public static double v5;
    public static double v6;
    public static double v7;
    public static double v8;
    public static double v9;
    public static double valpha;
    private static double lat1;
    private static double lat2;
    private static double lon1;
    private static double lon2;
    private static double mday;
    private static double mmonth;
    private static double myear;

    public static void setDay(Double d) {
        mday = mday + d.doubleValue();
        calculation(lat1, lat2, lon1, lon2, myear, mmonth, mday);
    }

    public static void print(String str) {
        System.out.println(str);
    }

    public static void calculation(double lat1, double lat2, double lon1, double lon2, double year, double month, double day) {
        double d8;
        double d9;
        Hijri_Cal_Tools.lat1 = lat1;
        Hijri_Cal_Tools.lat2 = lat2;
        Hijri_Cal_Tools.lon1 = lon1;
        Hijri_Cal_Tools.lon2 = lon2;
        mday = day;
        mmonth = month;
        myear = year;
        double d10 = lon2 / 60.0d;
        e3 = d10;
        e4 = lon1 + d10;
        double d11 = lat2 / 60.0d;
        n3 = d11;
        n4 = lat1 + d11;
        double d12 = ((((367.0d * year) - ((double) ((int) ((year + ((double) ((int) ((month + 9.0d) / 12.0d)))) * 1.75d)))) + ((double) ((int) ((month / 9.0d) * 275.0d)))) + day) - 730531.5d;
        v1 = d12;
        double d13 = ((0.9856474d * d12) + 280.461d) % 360.0d;
        v2 = d13;
        double d14 = ((d12 * 0.9856003d) + 357.528d) % 360.0d;
        v3 = d14;
        v4 = d13 + (Math.sin((d14 * 3.141592653589793d) / 180.0d) * 1.915d) + (Math.sin(((v3 * 2.0d) * 3.141592653589793d) / 180.0d) * 0.02d);
        double d15 = 23.439d - (v1 * 4.0E-7d);
        v5 = d15;
        double atan = (Math.atan(Math.cos((d15 * 3.141592653589793d) / 180.0d) * Math.tan((v4 * 3.141592653589793d) / 180.0d)) * 180.0d) / 3.141592653589793d;
        v6 = atan;
        double floor = atan - (Math.floor(atan / 360.0d) * 360.0d);
        v7 = floor;
        valpha = floor + ((Math.floor(v4 / 90.0d) - Math.floor(v7 / 90.0d)) * 90.0d);
        double d16 = v1;
        v8 = ((d16 * 0.985647352d) + 100.46d) - (Math.floor(((d16 * 0.985647352d) + 100.46d) / 360.0d) * 360.0d);
        v9 = (Math.asin(Math.sin((v5 * 3.141592653589793d) / 180.0d) * Math.sin((v4 * 3.141592653589793d) / 180.0d)) * 180.0d) / 3.141592653589793d;
        double d17 = valpha;
        double d18 = v8;
        double floor2 = (d17 - d18) - (Math.floor((d17 - d18) / 360.0d) * 360.0d);
        v10 = floor2;
        double d19 = floor2 - e4;
        v11 = d19;
        dhuhr = (d19 / 15.0d) + 4.0d;
        double asin = (Math.asin((Math.sin((n4 * 3.141592653589793d) / 180.0d) * Math.sin((v9 * 3.141592653589793d) / 180.0d)) + (Math.cos((n4 * 3.141592653589793d) / 180.0d) * Math.cos((v9 * 3.141592653589793d) / 180.0d))) * 180.0d) / 3.141592653589793d;
        v14 = asin;
        double atan2 = 90.0d - ((Math.atan(1.0d / ((1.0d / Math.tan((asin * 3.141592653589793d) / 180.0d)) + 1.0d)) * 180.0d) / 3.141592653589793d);
        v15 = atan2;
        double acos = ((Math.acos((Math.sin(((90.0d - atan2) * 3.141592653589793d) / 180.0d) - (Math.sin((v9 * 3.141592653589793d) / 180.0d) * Math.sin((n4 * 3.141592653589793d) / 180.0d))) / (Math.cos((v9 * 3.141592653589793d) / 180.0d) * Math.cos((n4 * 3.141592653589793d) / 180.0d))) * 180.0d) / 3.141592653589793d) / 15.0d;
        v16 = acos;
        double d20 = dhuhr;
        v18 = acos + d20;
        setV20(d20 + v17);
        double acos2 = (Math.acos((Math.sin(-0.01454441043328608d) - (Math.sin((v9 * 3.141592653589793d) / 180.0d) * Math.sin((n4 * 3.141592653589793d) / 180.0d))) / (Math.cos((v9 * 3.141592653589793d) / 180.0d) * Math.cos((n4 * 3.141592653589793d) / 180.0d))) * 180.0d) / 3.141592653589793d;
        v22 = acos2;
        double d21 = dhuhr;
        sunRise = d21 - (acos2 / 15.0d);
        maghrib = d21 + (acos2 / 15.0d);
        double acos3 = (Math.acos((Math.sin(-0.3141592653589793d) - (Math.sin((v9 * 3.141592653589793d) / 180.0d) * Math.sin((n4 * 3.141592653589793d) / 180.0d))) / (Math.cos((v9 * 3.141592653589793d) / 180.0d) * Math.cos((n4 * 3.141592653589793d) / 180.0d))) * 180.0d) / 3.141592653589793d;
        v27 = acos3;
        ishaa = dhuhr + (acos3 / 15.0d);
        double acos4 = (Math.acos((Math.sin(-0.3141592653589793d) - (Math.sin((v9 * 3.141592653589793d) / 180.0d) * Math.sin((n4 * 3.141592653589793d) / 180.0d))) / (Math.cos((v9 * 3.141592653589793d) / 180.0d) * Math.cos((n4 * 3.141592653589793d) / 180.0d))) * 180.0d) / 3.141592653589793d;
        v30 = acos4;
        double d22 = dhuhr;
        double d23 = d22 - (acos4 / 15.0d);
        fajr = d23;
        dhuhr = d22 + 0.08333333333333333d;
        v18 += 0.08333333333333333d;
        double d24 = maghrib + 0.08333333333333333d;
        maghrib = d24;
        if (month <= 2.0d) {
            d8 = year - 1.0d;
            d9 = month + 12.0d;
        } else {
            d8 = year;
            d9 = month;
        }
        double d25 = (int) (d8 / 100.0d);
        double d26 = (((double) ((int) (((((double) (((int) ((d8 + 4716.0d) * 365.25d)) + ((int) ((d9 + 1.0d) * 30.6001d)))) + day) + ((2.0d - d25) + ((double) ((int) (d25 / 4.0d))))) - 1524.5d))) - 1948440.0d) + 10632.0d;
        double d27 = (int) ((d26 - 1.0d) / 10631.0d);
        double d28 = (d26 - (10631.0d * d27)) + 354.0d;
        double d29 = (((int) ((10985.0d - d28) / 5316.0d)) * ((int) ((d28 * 50.0d) / 17719.0d))) + (((int) (d28 / 5670.0d)) * ((int) ((d28 * 43.0d) / 15238.0d)));
        double d30 = ((d28 - ((double) (((int) ((30.0d - d29) / 15.0d)) * ((int) ((17719.0d * d29) / 50.0d))))) - ((double) (((int) (d29 / 16.0d)) * ((int) ((15238.0d * d29) / 43.0d))))) + 29.0d;
        int i = (int) ((d30 * 24.0d) / 709.0d);
        hijriMonth = i;
        hijriDay = (int) (d30 - ((double) ((int) ((((double) i) * 709.0d) / 24.0d))));
        hijriYear = (int) (((d27 * 30.0d) + d29) - 30.0d);
        double d31 = d24 - 0.08333333333333333d;
        v32 = d31;
        double d32 = 24.0d - (d31 - d23);
        midNight = d31 + (d32 / 2.0d);
        thirdNight = d23 - (d32 / 3.0d);
    }

    public static String getTimeHHMM(double d) {

        int hour = (int) d;
        double d2 = (d - ((double) hour)) * 60.0d;
        int minute = (int) d2;
        if (((int) ((d2 - ((double) minute)) * 60.0d)) >= 30) {
            minute++;
        }
        if (minute == 60) {
            minute = 0;
            hour++;
        }
        return String.format("%02d:%02d", hour, minute);
    }

    public static int getHijriDay() {
        return hijriDay;
    }

    public static int getHijriMonth() {
        return hijriMonth;
    }

    public static int getHijriYear() {
        return hijriYear;
    }

    public static String getDhuhur() {
        return getTimeHHMM(dhuhr);
    }

    public static String getAsar() {
        return getTimeHHMM(dhuhr + v16);
    }

    public static String getSunRise() {
        return getTimeHHMM(sunRise);
    }

    public static String getMagrib() {
        return getTimeHHMM(maghrib);
    }

    public static String getFajer() {
        return getTimeHHMM(fajr);
    }

    public static String getIshaa() {
        return getTimeHHMM(ishaa);
    }

    public static String getMidNight() {
        return getTimeHHMM(midNight);
    }

    public static String getThirdNight() {
        return getTimeHHMM(thirdNight);
    }

    public static Double getDhuhurDouble() {
        return dhuhr;
    }

    public static Double getAsarDouble() {
        return dhuhr + v16;
    }

    public static Double getSunRiseDouble() {
        return sunRise;
    }

    public static Double getMagribDouble() {
        return maghrib;
    }

    public static Double getFajerDouble() {
        return fajr;
    }

    public static Double getIshaaDouble() {
        return ishaa;
    }

    public static String getDuha() {
        return getTimeHHMM(sunRise + 0.25d);
    }

    public static double getV20() {
        return v20;
    }

    public static void setV20(double d) {
        v20 = d;
    }

    public static int[] getTimeHHMMCal(double d) {
        int i = (int) d;
        double d2 = (d - ((double) i)) * 60.0d;
        int i2 = (int) d2;
        if (((int) ((d2 - ((double) i2)) * 60.0d)) >= 30) {
            i2++;
        }
        if (i2 == 60) {
            i++;
            i2 = 0;
        }
        int[] iArr = {i, i2};
        return iArr;
    }

    public static int[] getDhuhurCal() {
        return getTimeHHMMCal(dhuhr);
    }

    public static int[] getAsarCal() {
        return getTimeHHMMCal(dhuhr + v16);
    }

    public static int[] getSunRiseCal() {
        return getTimeHHMMCal(sunRise);
    }

    public static int[] getMagribCal() {
        return getTimeHHMMCal(maghrib);
    }

    public static int[] getFajerCal() {
        return getTimeHHMMCal(fajr);
    }

    public static int[] getIshaaCal() {
        return getTimeHHMMCal(ishaa);
    }

    public static int[] getGhurobCal() {
        return getTimeHHMMCal(v32);
    }

    public static int[] getMidNightCal() {
        return getTimeHHMMCal(midNight);
    }

    public static int[] getThirdNightCal() {
        return getTimeHHMMCal(thirdNight);
    }
}
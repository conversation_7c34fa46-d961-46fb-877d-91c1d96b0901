package com.arapeak.alrbea.Enum;

import android.util.Log;

import com.arapeak.alrbea.hawk.HawkSettings;


public enum UITheme {
    BROWN,
    DARK_GREEN,
    DARK_GRAY,
    BLUE,
    RED,
    <PERSON><PERSON><PERSON>,
    <PERSON>W_GREEN,
    BROWN_NEW,
    BLUE_NEW,
    WHIT<PERSON>,
    <PERSON>LUE_LET,
    WHITE_NEW,
    BROWN_NEW_3,
    CUSTOM_1,
    CUSTOM_FIREBASE;

    public boolean isActive() {
        UITheme current = HawkSettings.getCurrentTheme();
        Log.i("UITheme", "isActive : theme : " + name() + " | current : " + current.name());
        return this == HawkSettings.getCurrentTheme();
    }
}

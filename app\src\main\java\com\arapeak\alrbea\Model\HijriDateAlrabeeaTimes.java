package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;
import java.util.Locale;

public class HijriDateAlrabeeaTimes {
    @Expose
    @SerializedName("holidays")
    private List<String> holidays;
    @Expose
    @SerializedName("designation")
    private DesignationAlrabeeaTimes designation;
    @Expose
    @SerializedName("year")
    private String year;
    @Expose
    @SerializedName("month")
    private MonthAlrabeeaTimes month;
    @Expose
    @SerializedName("weekday")
    private WeekdayAlrabeeaTimes weekday;
    @Expose
    @SerializedName("day")
    private String day;
    @Expose
    @SerializedName("format")
    private String format;
    @Expose
    @SerializedName("date")
    private String date;

    public List<String> getHolidays() {
        return holidays;
    }

    public void setHolidays(List<String> holidays) {
        this.holidays = holidays;
    }

    public DesignationAlrabeeaTimes getDesignation() {
        return designation;
    }

    public void setDesignation(DesignationAlrabeeaTimes designation) {
        this.designation = designation;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public MonthAlrabeeaTimes getMonth() {
        return month;
    }

    public void setMonth(MonthAlrabeeaTimes month) {
        this.month = month;
    }

    public WeekdayAlrabeeaTimes getWeekday() {
        return weekday;
    }

    public void setWeekday(WeekdayAlrabeeaTimes weekday) {
        this.weekday = weekday;
    }

    public String getDay() {
        return String.format(Locale.ENGLISH, "%02d", Integer.parseInt(day));
//        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}

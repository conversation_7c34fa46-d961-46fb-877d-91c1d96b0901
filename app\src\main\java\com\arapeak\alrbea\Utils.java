package com.arapeak.alrbea;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.PACKAGE_INSTALL_PERMISSION_REQUEST_CODE;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SYNCE_USER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.realm;
import static com.arapeak.alrbea.AppController.baseContext;
import static com.arapeak.alrbea.hawk.HawkConstants.IS_AM_PM_KEY;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Point;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.NumberPicker;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.Model.InfoOfCode;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.TimeAmount;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimesList;
import com.arapeak.alrbea.UI.CustomView.OptionChooseAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.MorningOrEvening;
import com.arapeak.alrbea.database.OmanCitiesDb;
import com.arapeak.alrbea.hawk.HawkConstants;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.Picasso;
import com.tapadoo.alerter.Alert;
import com.tapadoo.alerter.Alerter;

import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.json.JSONObject;
import org.sufficientlysecure.rootcommands.RootCommands;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.realm.Realm;

public class Utils {

    public static final String TIME_SERVER = "time.nist.gov";
    private final static String TAG = "Utils";
    private static final String TODO = "";
    public static PrayerApi PRAYER_API;
    public static int YEAR = 2020;
    public static int MONTH = 1;
    public static int DAY = 1;
    private static DatabaseReference mDatabase;


    public static String getTimeNow() {
        SimpleDateFormat sdf = new SimpleDateFormat("hh:mm"
                , new Locale(HawkSettings.getTypeNumber()));
        Date currDate = new Date();
        if (Hawk.get(ConstantsOfApp.SYSTEM_TIME_KEY, 0).equals(0)) {

            return sdf.format(currDate);
        } else {
            SimpleDateFormat sdf12 = new SimpleDateFormat("hh:mm aa"
                    , new Locale(HawkSettings.getTypeNumber()));
            SimpleDateFormat sdf24 = new SimpleDateFormat("HH:mm"
                    , new Locale(HawkSettings.getTypeNumber()));
            try {
                currDate = sdf.parse(sdf12.format(currDate));
            } catch (ParseException e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            return sdf24.format(currDate);
        }


        // return sdf.format(currDate);
    }

    public static String getTimeWith24(String timeIn12) {
        if (getValueWithoutNull(timeIn12).isEmpty()) {
            return "";
        }
        SimpleDateFormat sdf12 = new SimpleDateFormat("hh:mm aa"
                , new Locale(HawkSettings.getTypeNumber()));

        SimpleDateFormat sdf24 = new SimpleDateFormat("HH:mm"
                , new Locale(HawkSettings.getTypeNumber()));

        Date currDate = new Date();
        try {
            currDate = sdf12.parse(timeIn12);
        } catch (ParseException e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        return sdf24.format(currDate);
    }

    public static String getTimeWith12(String timeIn24) {
        if (getValueWithoutNull(timeIn24).isEmpty()) {
            return "";
        }
        SimpleDateFormat sdf12 = new SimpleDateFormat("hh:mm aa"
                , new Locale(HawkSettings.getTypeNumber()));

        SimpleDateFormat sdf24 = new SimpleDateFormat("HH:mm"
                , new Locale(HawkSettings.getTypeNumber()));

        Date currDate = new Date();
        try {
            currDate = sdf24.parse(timeIn24);
        } catch (ParseException e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        return sdf12.format(currDate);
    }

    public static String getEnglishDateTime(String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, new Locale(ConstantsOfApp.EN_LANGUAGE));
        Date currDate = new Date();

        return sdf.format(currDate);
    }

    public static String getEnglishDateTime(String pattern, long time) {
        long currentTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, new Locale(ConstantsOfApp.EN_LANGUAGE));
        Date currDate = new Date(currentTime > time ? currentTime : time);
        return sdf.format(currDate);
    }

    public static String getTypeTimeNow() {
        // SimpleDateFormat sdf;

        SimpleDateFormat sdf = new SimpleDateFormat("a"
                , new Locale(HawkSettings.getTypeAM()));

        Date currDate = new Date();
        return sdf.format(currDate);
    }

    public static String ConvertLongTimestampToDate(long timestampLong) {
        if (timestampLong < 1) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd  hh:mm a"
                , new Locale(HawkSettings.getTypeNumber())).format(new Date(timestampLong));
    }

    public static Long convertDateToLongTimestamp(String dateString, String timeString) {
        if (getValueWithoutNull(dateString).isEmpty() || getValueWithoutNull(timeString).isEmpty()) {
            return 0L;
        }
        try {
            String dateTime = dateString + " " + timeString;

            SimpleDateFormat df = new SimpleDateFormat("dd-MM-yyyy HH:mm", Locale.ENGLISH);
            Date date = df.parse(dateTime);
            return date.getTime();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            return 0L;
        }
    }

    public static Long convert24TimeToLongTimestamp(String timeString) {
        if (getValueWithoutNull(timeString).isEmpty()) {
            return 0L;
        }
        try {
            SimpleDateFormat df = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
            Date date = df.parse(timeString);
            return date.getTime();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            return 0L;
        }
    }

    public static String getCurrentDate(String pattern) {
        SimpleDateFormat curFormater = new SimpleDateFormat(pattern);
        return curFormater.format(new Date());
    }

    public static Long convertDateToLongTimestampWithTime(String dateString) {
        if (getValueWithoutNull(dateString).isEmpty()) {
            return 0L;
        }
        try {
            dateString += " 00:00";

            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.ENGLISH);
            Date date = df.parse(dateString);
            return date.getTime();
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            return 0L;
        }
    }

    public static Long GetDateFromTimestampMillis(String dateTimestamp) {

        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);

            Date date = dateFormat.parse(dateTimestamp);
            //            date.setTime(date.getTime() + Calendar.getInstance().getTimeZone().getRawOffset());

            return date.getTime();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        return 0L;
    }

    public static String getDifferenceTime(long time) {
        long different = time - System.currentTimeMillis();

        long elapsedHours = different / ConstantsOfApp.HOURS_MILLI_SECOND;
        different = different % ConstantsOfApp.HOURS_MILLI_SECOND;

        long elapsedMinutes = different / ConstantsOfApp.MINUTES_MILLI_SECOND;
        different = different % ConstantsOfApp.MINUTES_MILLI_SECOND;

        long elapsedSeconds = different / ConstantsOfApp.SECONDS_MILLI_SECOND;

        String differenceTime = "";

        if (elapsedHours > 0) {
            differenceTime = getValueStringOfNumber(elapsedHours, getString(R.string.hour)
                    , getString(R.string.two_hours)
                    , getString(R.string.hours));
        }

        if (elapsedMinutes > 0) {

            if (elapsedSeconds > 0) {
                ++elapsedMinutes;
            }
            if (!differenceTime.isEmpty()) {
                differenceTime += " " + getString(R.string.and) + " ";
            }

            differenceTime += getValueStringOfNumber(elapsedMinutes, getString(R.string.minute)
                    , getString(R.string.two_minutes)
                    , getString(R.string.minutes));
        }

        if (elapsedHours == 0 && elapsedMinutes == 0 && elapsedSeconds > 0) {
            differenceTime = getString(R.string.less_than_a_minute);
            //      differenceTime = getValueStringOfNumber(elapsedSeconds, getString(R.string.second)
            //              , getString(R.string.two_seconds)
            //              , getString(R.string.seconds));
        }

        return differenceTime.trim();

    }

    public static String getDifferenceTime(Context context, long startTime, long endTime) {
        //milliseconds
        long different = endTime - startTime;

        if (context == null) {
            return "";
        }

          /*long elapsedDays = different / daysInMilli;
          different = different % daysInMilli;*/

        long elapsedHours = different / ConstantsOfApp.HOURS_MILLI_SECOND;
        different = different % ConstantsOfApp.HOURS_MILLI_SECOND;

        long elapsedMinutes = different / ConstantsOfApp.MINUTES_MILLI_SECOND;
        different = different % ConstantsOfApp.MINUTES_MILLI_SECOND;

        long elapsedSeconds = different / ConstantsOfApp.SECONDS_MILLI_SECOND;

        //        String differenceTime = elapsedHours + " hours: ," + elapsedMinutes + " minutes";

        String differenceTime = "";

        if (elapsedHours > 0) {
            differenceTime = getValueStringOfNumber(elapsedHours, getString(R.string.hour)
                    , getString(R.string.two_hours)
                    , getString(R.string.hours));
        }

        if (elapsedMinutes > 0) {

            if (elapsedSeconds > 0) {
                ++elapsedMinutes;
            }
            if (!differenceTime.isEmpty()) {
                differenceTime += " " + getString(R.string.and) + " ";
            }

            differenceTime += getValueStringOfNumber(elapsedMinutes, getString(R.string.minute)
                    , getString(R.string.two_minutes)
                    , getString(R.string.minutes));
        }

        if (elapsedHours == 0 && elapsedMinutes == 0 && elapsedSeconds > 0) {
            differenceTime = getValueStringOfNumber(elapsedSeconds, getString(R.string.second)
                    , getString(R.string.two_seconds)
                    , getString(R.string.seconds));
        }

        return differenceTime.trim();
    }

    public static String[] getDifferenceTimeForIkama(PrayerTime prayerTime) {
        long different = prayerTime.getIkamaTime() - System.currentTimeMillis();

        if (different <= 0)
            return new String[]{"0", getString(R.string.minute)};

        int number = 0;
        long elapsedHours = different / ConstantsOfApp.HOURS_MILLI_SECOND;
        different %= ConstantsOfApp.HOURS_MILLI_SECOND;

        long elapsedMinutes = different / ConstantsOfApp.MINUTES_MILLI_SECOND;
        different %= ConstantsOfApp.MINUTES_MILLI_SECOND;

        long elapsedSeconds = different / ConstantsOfApp.SECONDS_MILLI_SECOND;

        String differenceTime;

        if (elapsedHours > 0) {
            number = (int) elapsedHours;
            differenceTime = getValueStringOfNumber(elapsedHours, getString(R.string.hour), getString(R.string.hours));
        } else if (elapsedMinutes > 0) {
            if (elapsedSeconds > 0)
                ++elapsedMinutes;
            number = (int) elapsedMinutes;
            differenceTime = getValueStringOfNumber(elapsedMinutes, getString(R.string.minute), getString(R.string.minutes));
        } else if (elapsedSeconds > 0) {
            number = (int) elapsedSeconds;
            differenceTime = getValueStringOfNumber(elapsedSeconds, getString(R.string.second), getString(R.string.seconds));
        } else {
            number = 0;
            differenceTime = getString(R.string.second);
        }

        return new String[]{String.valueOf(number), differenceTime.trim()};
    }

    public static ResourcesLocale getResources(String localeTarget, Context context) {
        return new ResourcesLocale(context, new Locale("en"), new Locale(localeTarget));
    }

    public static String getValueStringOfNumber(long number, String one, String two, String many) {


        String differenceTime;
        if (HawkSettings.isArabic()) {
            if (number == 1) {
                differenceTime = one;
            } else if (number == 2) {
                differenceTime = two;
            } else if (number < 11) {
                differenceTime = number + " " + many;
            } else {
                differenceTime = number + " " + one;
            }
        } else {
            if (number == 1) {
                differenceTime = number + " " + one;
            } else {
                differenceTime = number + " " + many;
            }
        }


        return differenceTime;
    }

    public static String getValueStringOfNumber(long number, String one, String many) {

        String differenceTime;
        if (HawkSettings.isArabic()) {
            if (number == 1) {
                differenceTime = one;
            } else if (number == 2) {
                //                differenceTime = number + " " + one;
                differenceTime = one;
            } else if (number < 11) {
                //                differenceTime = number + " " + many;
                differenceTime = many;
            } else {
                //                differenceTime = number + " " + one;
                differenceTime = one;
            }
        } else {
            if (number == 1) {
                //                differenceTime = number + " " + one;
                differenceTime = one;
            } else {
                //                differenceTime = number + " " + many;
                differenceTime = many;
            }
        }

        return differenceTime;
    }

    public static String convertDateTimeToOldPatternToNewPattern(String time, String oldPattern, String newPattern, String lang) {
        if (getValueWithoutNull(time).isEmpty()) {
            return "";
        }
        return convertDateTimeToOldPatternToNewPattern(time, oldPattern, newPattern, 0, lang);
          /*try {
              SimpleDateFormat dateFormat = new SimpleDateFormat(oldPattern, new Locale(lang));
              Date sourceDate = dateFormat.parse(time);
              SimpleDateFormat targetFormat = new SimpleDateFormat(newPattern, new Locale(lang));

              return targetFormat.format(sourceDate);
          } catch (Exception e) {
              Log.e(TAG, "Error: " + e.getMessage());
              e.printStackTrace();

              return time;
          }*/
    }

    public static String convertDateTimeToOldPatternToNewPattern(String time
            , String oldPattern
            , String newPattern
            , long addTimeMilliseconds
            , String lang) {
        if (getValueWithoutNull(time).isEmpty()) {
            return "";
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(oldPattern, new Locale(lang));
            Date sourceDate = dateFormat.parse(time);
            SimpleDateFormat targetFormat = new SimpleDateFormat(newPattern, new Locale(lang));
            sourceDate.setTime(sourceDate.getTime() + addTimeMilliseconds);
            return targetFormat.format(sourceDate);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);

            return time;
        }
    }

    public static String[] convertDateTimeToTimeAndTypeTime(String time) {
        if (getValueWithoutNull(time).isEmpty()) {
            return new String[]{"", ""};
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("hh:mm a", new Locale(HawkSettings.getTypeAM()));
            Date sourceDate = dateFormat.parse(time);

            SimpleDateFormat targetFormat = new SimpleDateFormat("hh:mm", new Locale(HawkSettings.getTypeNumber()));
            //      sourceDate.setTime(sourceDate.getTime());

            SimpleDateFormat targetFormat2 = new SimpleDateFormat("a", new Locale(HawkSettings.getTypeAM()));
            //      sourceDate.setTime(sourceDate.getTime());

            String timeResult = replaceNumberWithSettings(targetFormat.format(sourceDate));
            String amResult = targetFormat2.format(sourceDate);
            return new String[]{timeResult, amResult};
        } catch (Exception e) {
            Log.e(TAG, "convertDateTimeToTimeAndTypeTime Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);

            return new String[]{"", ""};
        }
    }

    public static String getTimeFormatted(long time, String pattern) {
        String result = "";
        try {
            Date date = new Date(time);
            result = new SimpleDateFormat(pattern, new Locale(HawkSettings.getTypeAM())).format(date);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return replaceNumberWithSettings(result);
    }

    public static String convertTimePrayer(String time, long addTimeMilliseconds) {

        //        String amPm = convertDateTimeToOldPatternToNewPattern(time, "HH:mm", "a", getTypeAM());

        return convertDateTimeToOldPatternToNewPattern(time, "HH:mm", "HH:mm", addTimeMilliseconds, HawkSettings.getTypeNumber()) /*+ " " + amPm*/;
    }

    public static String convertTimePrayer12(String time, long addTimeMilliseconds) {
        return convertTimePrayerCustom(time, addTimeMilliseconds);
//        return convertDateTimeToOldPatternToNewPattern(time, "hh:mm a", "HH:mm", addTimeMilliseconds, getTypeNumber()) /*+ " " + amPm*/;
    }

    public static String convertTimePrayerCustom(String time, long addTimeMilliseconds) {

        //        String amPm = convertDateTimeToOldPatternToNewPattern(time, "HH:mm", "a", getTypeAM());

        return convertDateTimeToOldPatternToNewPattern(time, "K:mm", "HH:mm", addTimeMilliseconds, HawkSettings.getTypeNumber()) /*+ " " + amPm*/;
    }


    public static void setLocaleLanguage(Context mContext, String localeLanguage) {

        if (getValueWithoutNull(localeLanguage).isEmpty()) {
            return;
        }
        //        Hawk.put(ConstantsOfApp.LOCALE_LANGUAGE_KEY, localeLanguage);
        Locale locale = new Locale(localeLanguage);
        Locale.setDefault(locale);
        mContext.getResources().getConfiguration().setLayoutDirection(locale);
        mContext.getResources().getConfiguration().locale = locale;
        mContext.getResources().getConfiguration().setLocale(locale);
        mContext.getResources().updateConfiguration
                (mContext.getResources().getConfiguration(), mContext.getResources().getDisplayMetrics());
    }


    public static String getValueWithoutNull(String string) {
        return string == null ? "" : string.trim();
    }

    public static void loadFragment(Fragment fragmentHalaPro
            , AppCompatActivity appCompatActivity
            , int levelOfPopBackStack) {
        try {
            if (fragmentHalaPro != null && !fragmentHalaPro.isVisible()) {

                if (appCompatActivity == null) {
                    return;
                }
                FragmentManager fragmentManager = appCompatActivity.getSupportFragmentManager();

                if (levelOfPopBackStack != 0) {
                    if (levelOfPopBackStack < 0) {
                        for (int i = 0; i < fragmentManager.getBackStackEntryCount(); ++i) {
                            fragmentManager.popBackStack();
                        }
                    } else {
                        levelOfPopBackStack = levelOfPopBackStack > fragmentManager.getBackStackEntryCount() ? fragmentManager.getBackStackEntryCount() : levelOfPopBackStack;
                        for (int i = levelOfPopBackStack; i > 0; --i) {
                            fragmentManager.popBackStack();
                        }
                    }
                }
                if (Utils.isAppIsInBackground(appCompatActivity)) {
                    return;
                }

                FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
                fragmentTransaction.replace(R.id.container_include_SettingsActivity, fragmentHalaPro);
                fragmentTransaction.addToBackStack(null);

                fragmentTransaction.addToBackStack(fragmentHalaPro.toString() + System.currentTimeMillis());
                if (!Utils.isAppIsInBackground(appCompatActivity)) {
                    fragmentTransaction.commit();
                }
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public static boolean isAppIsInBackground(Context context) {
        boolean isInBackground = true;
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT_WATCH) {
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = am.getRunningAppProcesses();
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                if (processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                    for (String activeProcess : processInfo.pkgList) {
                        if (activeProcess.equals(context.getPackageName())) {
                            isInBackground = false;
                        }
                    }
                }
            }
        } else {
            List<ActivityManager.RunningTaskInfo> taskInfo = am.getRunningTasks(1);
            ComponentName componentInfo = taskInfo.get(0).topActivity;
            if (componentInfo.getPackageName().equals(context.getPackageName())) {
                isInBackground = false;
            }
        }

        return isInBackground;
    }

    public static boolean isPackageInstallGranted(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return context.getPackageManager().canRequestPackageInstalls();
        }
        return true;
    }

    public static void sendPackageInstallPermissionRequest(Activity context) {
        context.startActivityForResult(new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                .setData(Uri.parse(String.format("package:%s", context.getPackageName()))), PACKAGE_INSTALL_PERMISSION_REQUEST_CODE);
    }

    public static boolean hasRootPermission() {
//        RootCommands.DEBUG = true;
        return RootCommands.rootAccessGiven();
    }

    public static Dialog initAskForPackageInstallPermissionDialog(Context mContext, DialogInterface.OnClickListener onClick) {

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_ladoing, null, false);
        Dialog loadingDialog = new Dialog(mContext, R.style.LoadingDialogStyle);
        loadingDialog.setContentView(view);
        loadingDialog.setCancelable(false);

        AlertDialog alertDialog = new AlertDialog.Builder(mContext).create();
        alertDialog.setTitle("Team Viewer");
        alertDialog.setCancelable(false);
        alertDialog.setMessage(mContext.getResources().getString(R.string.packagePermission));

        alertDialog.setButton(AlertDialog.BUTTON_POSITIVE, mContext.getResources().getString(R.string.ok), onClick);
      /*alertDialog.setButton(AlertDialog.BUTTON_POSITIVE, getResources().getString(R.string.ok), new DialogInterface.OnClickListener() {
        public void onClick(DialogInterface dialog, int which) {

        }
      });*/
        return loadingDialog;
    }

    public static String getString(int id) {
        return getResources(HawkSettings.getLocaleLanguage(), baseContext).getString(id);
    }

    public static String[] getStringArray(int id) {
        return getResources(HawkSettings.getLocaleLanguage(), baseContext).getStringArray(id);
    }

    public static String getString(Context context, int id) {
        return getResources(HawkSettings.getLocaleLanguage(), context).getString(id);
    }

    public static String getStringwithArg(Context context, int id, int arg) {
        return getResources(HawkSettings.getLocaleLanguage(), context).getString(id, arg);
    }

    public static String getStringwithArg(Context context, int id, int arg, int arg2) {
        return getResources(HawkSettings.getLocaleLanguage(), context).getString(id, arg, arg2);
    }

    public static Dialog initLoadingDialogWithString(Context mContext, String info) {

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_ladoing, null, false);
        TextView text = view.findViewById(R.id.loading_textview);
        text.setVisibility(View.VISIBLE);
        text.setText(info);
        Dialog loadingDialog = new Dialog(mContext, R.style.LoadingDialogStyle);
        loadingDialog.setContentView(view);
        loadingDialog.setCancelable(true);

        return loadingDialog;
    }

    public static Dialog initLoadingDialog(Context mContext) {

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_ladoing, null, false);
        Dialog loadingDialog = new Dialog(mContext, R.style.LoadingDialogStyle);
        loadingDialog.setContentView(view);
        loadingDialog.setCancelable(false);

        return loadingDialog;
    }

    public static Dialog initLoadingDialogmap(Context mContext) {

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_map_loading, null, false);
        Dialog loadingDialog = new Dialog(mContext, R.style.LoadingDialogStyle);
        loadingDialog.setContentView(view);
        loadingDialog.setCancelable(false);

        return loadingDialog;
    }

//    private Dialog loadingDialog;

    public static Dialog initConfirmDialog(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText username = view.findViewById(R.id.username);
        username.setVisibility(View.GONE);
        titleTextView.setText(title);
        bodyMessageTextView.setText(bodyMessage);

        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }

        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {

                    onSuccessful.onSuccessful(true);

                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initConfirmDialogsynce(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText username = view.findViewById(R.id.username);
        username.setVisibility(View.GONE);
        EditText synceuser = view.findViewById(R.id.synce);
        synceuser.setVisibility(View.VISIBLE);
        titleTextView.setText(title);
        bodyMessageTextView.setText(bodyMessage);

        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }

        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {

                    // loadingDialog.dismiss();
                    Hawk.put(SYNCE_USER_KEY, synceuser.getText().toString().trim());
                    onSuccessful.onSuccessful(true);

                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initConfirmDialogbackup(Activity mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm_backup, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        CardView cardView = view.findViewById(R.id.submit);
        CardView cardView2 = view.findViewById(R.id.submitbackup);
        //  TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        // Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        // EditText username = view.findViewById(R.id.username);
        // username.setVisibility(View.GONE);
        // EditText synceuser = view.findViewById(R.id.synce);
        // synceuser.setVisibility(View.VISIBLE);
        //titleTextView.setText(title);
        //bodyMessageTextView.setText(bodyMessage);

        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }
        Dialog loadingDialog;
        loadingDialog = Utils.initLoadingDialog(mContext);
        InfoOfCode infoOfCode = Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null);
        cardView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadingDialog.show();
                //region mDatabase

                mDatabase = FirebaseDatabase.getInstance().getReference();


                mDatabase.child("users").child(infoOfCode.getCode()).child("INFO_OF_CODE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.INFO_OF_CODE_KEY, null)


                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("PRAYER_METHOD_KEY").setValue(

                        Hawk.get(ConstantsOfApp.PRAYER_METHOD_KEY, null)


                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("LATITUDE_KEY").setValue(
                        HawkSettings.getLatitude()
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("LONGITUDE_KEY").setValue(
                        HawkSettings.getLongitude()
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("TIME_OF_ATHKAR_AFTER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TIME_OF_ATHKAR_AFTER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("PRAYER_API_KEY").setValue(
                        Hawk.get(ConstantsOfApp.PRAYER_API_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ADJUST_HIJRI_DATE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_AM_PM_KEY").setValue(
                        Hawk.get(IS_AM_PM_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ARABIC_NUMBER").setValue(
                        Hawk.get(ConstantsOfApp.IS_ARABIC_NUMBER, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("UPDATE_LOCATION_KEY").setValue(
                        Hawk.get(ConstantsOfApp.UPDATE_LOCATION_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("LAST_UPDATE_DATA_PRAYER_TIME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.LAST_UPDATE_DATA_PRAYER_TIME_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("FUNERAL_MESSAGES_KEY").setValue(
                        Hawk.get(ConstantsOfApp.FUNERAL_MESSAGES_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("EVENT_IMAGE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.EVENT_IMAGE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APPEARANCE_ALTERNATELY_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APPEARANCE_ALTERNATELY_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_CUSTOM_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_CUSTOM_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_AFTER_FAJER_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_AFTER_FAJER_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_AFTER_DHUHR_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_AFTER_DHUHR_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_AFTER_ASER_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_AFTER_ASER_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_AFTER_MAGHRIB_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_AFTER_ISHA_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_AFTER_ISHA_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_AFTER_JOMAA_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_AFTER_JOMAA_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_FUNERAL_MESSAGES_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_FUNERAL_MESSAGES_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("TYPE_OF_AZHAR_MORNING_EVENING_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TYPE_OF_AZHAR_MORNING_EVENING_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("TYPE_OF_SHOW_DATE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TYPE_OF_SHOW_DATE_KEY, null)
                );
//                mDatabase.child("users").child(infoOfCode.getCode()).child("DATE_ALRABEEA_TIMES_NOW_KEY").setValue(
//                        MainActivity.dateAlrabeeaTimes
//                );
                //        mDatabase.child("users").child(infoOfCode.getCode()).child("DATE_ALRABEEA_TIMES_NOW_KEY").setValue(
                ////                Hawk.get(ConstantsOfApp.DATE_ALRABEEA_TIMES_NOW_KEY, null)
                //                MainActivity.dateAlrabeeaTimes;
                //        );

                mDatabase.child("users").child(infoOfCode.getCode()).child("LOCK_DURING_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("NEWS_TICKER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.NEWS_TICKER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_EVNING_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_EVNING_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ATHKARS_MORNING_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ATHKARS_MORNING_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("CITATION_FOR_EVENING_TIME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("CITATION_FOR_MORNING_TIME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_THERE_CITATION_FOR_MORNING_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_THERE_CITATION_FOR_MORNING_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_THERE_CITATION_FOR_EVENING_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_THERE_CITATION_FOR_EVENING_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_SHOW_CITATION_FOR_MORNING_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_MORNING_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_SHOW_CITATION_FOR_EVENING_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_SHOW_CITATION_FOR_EVENING_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("FAJR_KEY").setValue(
                        Hawk.get(ConstantsOfApp.FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("DUHA_KEY").setValue(
                        Hawk.get(ConstantsOfApp.DUHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("DHUHR_KEY").setValue(
                        Hawk.get(ConstantsOfApp.DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ASR_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("MAGHRIB_KEY").setValue(
                        Hawk.get(ConstantsOfApp.MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("ISHA_KEY").setValue(
                        Hawk.get(ConstantsOfApp.ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("JOMAA_KEY").setValue(
                        Hawk.get(ConstantsOfApp.JOMAA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("TARAWIH_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TARAWIH_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("TAHAJJUD_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TAHAJJUD_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("TYPE_OF_AL_AHADETH_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TYPE_OF_AL_AHADETH_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY + ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.JOMAA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.LOCK_DURING_PRAYER_KEY + ConstantsOfApp.FAJR_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.POST_OR_PRE_TO_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.POST_OR_PRE_TO_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.POST_OR_PRE_TO_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.POST_OR_PRE_TO_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.POST_OR_PRE_TO_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.POST_OR_PRE_TO_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY).setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY").setValue(
                        Hawk.get(ConstantsOfApp.IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY, null)
                );


                /*mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_BROWN_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_BROWN_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_GREEN_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_GREEN_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_BLUE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_BLUE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_DARK_GREEN_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_DARK_GREEN_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_RED_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_RED_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_DARK_GRAY_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_DARK_GRAY_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_DARK_Green_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_DARK_Green_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_RED_NEW_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_RED_NEW_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_BROWN_NEW_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_BROWN_NEW_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("APP_THEME_BLUE_NEW_KEY").setValue(
                        Hawk.get(ConstantsOfApp.APP_THEME_BLUE_NEW_KEY, null)
                );*/


                mDatabase.child("users").child(infoOfCode.getCode()).child("YEAR").setValue(
                        Hawk.get(ConstantsOfApp.YEAR, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("MONTH").setValue(
                        Hawk.get(ConstantsOfApp.MONTH, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("DAY").setValue(
                        Hawk.get(ConstantsOfApp.DAY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("MORNING_TIME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.MORNING_TIME_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("EVENING_TIME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.EVENING_TIME_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("TARAWIH_TIME_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TARAWIH_TIME_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("POST_OR_PRE_TO_SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("POST_OR_PRE_TO_DUHA_KEY").setValue(
                        Hawk.get(ConstantsOfApp.POST_OR_PRE_TO_DUHA_KEY, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_POST_OR_PRE_TO_SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_POST_OR_PRE_TO_DUHA_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DUHA_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("TIME_OF_IKAMA_SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.TIME_OF_IKAMA_SUNRISE_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_TIME_OF_IKAMA_SUNRISE_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_SUNRISE_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY_H").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY_H, null)
                );

                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_DURATION_OF_TARAWIH_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_KEY, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("RAMADAN_DURATION_OF_TAHAJJUD_KEY").setValue(
                        Hawk.get(ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_KEY, null)
                );


                mDatabase.child("users").child(infoOfCode.getCode()).child("MORNING_TIME_NAME").setValue(
                        Hawk.get(ConstantsOfApp.MORNING_TIME_NAME, null)
                );
                mDatabase.child("users").child(infoOfCode.getCode()).child("EVENING_TIME_NAME").setValue(
                        Hawk.get(ConstantsOfApp.EVENING_TIME_NAME, null)
                );
                loadingDialog.dismiss();
                Utils.showSuccessAlert(mContext
                        , "تم النسخ بنجاح");
                Toast.makeText(mContext, "تم النسخ بنجاح", Toast.LENGTH_LONG).show();
//endregion

            }
        });
        cardView2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                confirmDialog.dismiss();
                if (Hawk.get(ConstantsOfApp.SYNCE_USER_KEY, null) == null) {

                    Utils.initConfirmDialogsynce(mContext
                            , 0
                            , "أستعادة"
                            , "أستعادة"
                                    + " \"" + "\""
                                    + " "
                            , true
                            , true
                            , new OnSuccessful() {
                                @Override
                                public void onSuccessful(boolean isSuccessful) {
                                    if (isSuccessful) {
                                        // writeObjectFile.readObject(Hawk.get(ConstantsOfApp.SYNCE_USER_KEY, null) +".json");


                                    }


                                }

                            });


                } else {

                    Utils.loadFragment(MorningOrEvening.newInstance()
                            , (AppCompatActivity) mContext
                            , 0);


                }


            }
        });
        cancelButton.setText(getString(R.string.close));
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        CardView cvPremium = view.findViewById(R.id.premium);
        if (Utils.getIsPremium()) {
            cvPremium.setVisibility(View.GONE);
        } else {
            cvPremium.setOnClickListener(i -> {
                Utils.showFailAlert(mContext, "خطأ", "الرجاء التواصل مع المسؤول لإضافة الميزة");
            });
        }
        /*cvPremium.setOnClickListener(i->{

            if(infoOfCode == null)
                return;
            loadingDialog.show();

//        DatabaseReference scoresRefTheme = FirebaseDatabase.getInstance().getReference("premium_devices");
//        Query sChatQueryAthkar = scoresRefTheme.orderByValue() ;
            String deviceKey = infoOfCode.getCode();

            FirebaseDatabase.getInstance().getReference("premium_devices").get().addOnCompleteListener(task -> {
                boolean isPremium = false;
                try{
                    if(task.isSuccessful()){
                        for(DataSnapshot data : task.getResult().getChildren()){
                            String key =data.getKey();
                            if(key.equals(deviceKey)){
                                isPremium = true;
                                break;
                            }
                        }
                        if(isPremium){
                            Utils.setIsPremium(true);
                            Toast.makeText(mContext, "تم إزالة الشعار", Toast.LENGTH_LONG).show();
                            Utils.showSuccessAlert(mContext,"تم إزالة الشعار");
                        }
                        else{
                            Utils.setIsPremium(false);
                            Toast.makeText(mContext, "الرجاء التواصل مع المسؤول لإضافة الميزة", Toast.LENGTH_LONG).show();
                            Utils.showFailAlert(mContext,"خطأ","الرجاء التواصل مع المسؤول لإضافة الميزة");
                        }
                    }
                    else
                        Toast.makeText(mContext, "فشل إزالة الشعار", Toast.LENGTH_LONG).show();
                    Utils.showFailAlert(mContext,"خطأ","فشل إزالة الشعار");
                }
                catch (Exception e){
                    Toast.makeText(mContext, "فشل إزالة الشعار", Toast.LENGTH_LONG).show();
                    Utils.showFailAlert(mContext,"خطأ","فشل إزالة الشعار");
                }
                loadingDialog.dismiss();
            });

        });*/


        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }


    public static Dialog initConfirmDialogs(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText username = view.findViewById(R.id.username);


        titleTextView.setText(title);
        bodyMessageTextView.setText(bodyMessage);

        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    if (username.getText().toString().trim().equals("admin") || username.getText().toString().trim().equals("669988")) {
                        System.out.println("jjjj");
                        onSuccessful.onSuccessful(true);

                    }
                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initGetTimeAmountDialog(Context context
            , int imageResourcesId
            , String title
            , String bodyMessage
            , TimeAmount initialTimeAmount
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {
        return initConfirmDialogNumber(context,
                imageResourcesId,
                title, bodyMessage,
                initialTimeAmount.hours, initialTimeAmount.minutes, initialTimeAmount.seconds,
                isCancelable, isShowConfirmDialog, onSuccessful);
    }

    public static Dialog initConfirmDialogNumber(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , int h, int m, int s
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final int[] second = {s};
        final int[] minuts = {m};
        final int[] hour = {h};

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_number, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);

        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);

        NumberPicker pickhour = view.findViewById(R.id.pickhour);

        pickhour.setMinValue(0);
        pickhour.setMaxValue(23);
        pickhour.setValue(h);
        pickhour.setWrapSelectorWheel(true);

        pickhour.setOnValueChangedListener((picker, oldVal, newVal) -> {
            if (second[0] == 0 && minuts[0] < 1 && newVal == 0)
                minuts[0] = 1;
            else
                hour[0] = newVal;
            hour[0] = newVal;

        });
        NumberPicker pickminuts = view.findViewById(R.id.pickminuts);

        pickminuts.setMinValue(0);
        pickminuts.setMaxValue(59);
        pickminuts.setValue(m);
        pickminuts.setWrapSelectorWheel(true);


        pickminuts.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                if (second[0] == 0 && hour[0] == 0 && newVal < 1)
                    minuts[0] = 1;
                else
                    minuts[0] = newVal;

                // minuts[0] =newVal;
            }
        });
        NumberPicker picksecond = view.findViewById(R.id.picksecond);


        picksecond.setMinValue(0);
        picksecond.setMaxValue(59);
        picksecond.setValue(s);
        picksecond.setWrapSelectorWheel(true);


        picksecond.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {

                if (minuts[0] < 1 && hour[0] == 0 && newVal == 0)
                    minuts[0] = 1;
                else
                    second[0] = newVal;
                second[0] = newVal;
            }
        });

        titleTextView.setText(title);


        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {

                    System.out.println("h" + hour[0] + "-m" + minuts[0] + "-s" + second[0]);
                    onSuccessful.onSuccessful(true, hour[0], minuts[0], second[0]);


                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static void clearCalendarDate(Calendar cal) {
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
    }

    public static void initConfirmDialogDate(Context mContext
            , UmmalquraCalendar calendar, OnUmmalquraCalendarSelected callback) {

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_date_picker, null, false);


        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);

        NumberPicker pickMonth = view.findViewById(R.id.pickmonth);
        NumberPicker pickDay = view.findViewById(R.id.pickday);

        pickMonth.setMinValue(0);
        pickDay.setMinValue(1);

        pickMonth.setWrapSelectorWheel(true);
        pickDay.setWrapSelectorWheel(true);

        UmmalquraCalendar cal = new UmmalquraCalendar();
        cal.setTimeInMillis(calendar.getTimeInMillis());

        String[] months = new String[12];

        Map<String, Integer> names = cal.getDisplayNames(UmmalquraCalendar.MONTH, Calendar.LONG, HawkSettings.getLocale());
        Integer[] keys = names.values().toArray(new Integer[0]);
        String[] temp = names.keySet().toArray(new String[0]);

        for (int i = 0; i < 12; i++) {
            months[keys[i]] = temp[i];
        }
//        String[] months = cal.getDisplayNames(UmmalquraCalendar.MONTH,Calendar.LONG,getLocale()).keySet().toArray(new String[0]);
        pickMonth.setMaxValue(months.length - 1);
        pickMonth.setDisplayedValues(months);
        pickDay.setMaxValue(cal.lengthOfMonth());
        pickDay.setValue(cal.get(UmmalquraCalendar.DAY_OF_MONTH));
        pickMonth.setValue(cal.get(UmmalquraCalendar.MONTH));

        pickMonth.setOnValueChangedListener((picker, oldVal, newVal) -> {
            cal.set(UmmalquraCalendar.MONTH, newVal);
            pickDay.setMaxValue(cal.lengthOfMonth());
            pickDay.setValue(cal.get(UmmalquraCalendar.DAY_OF_MONTH));
        });
        pickDay.setOnValueChangedListener((picker, oldVal, newVal) -> cal.set(UmmalquraCalendar.DAY_OF_MONTH, newVal));


        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(true);

        confirmButton.setOnClickListener(v -> {
            callback.result(cal);
            confirmDialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> confirmDialog.dismiss());
        confirmDialog.show();
    }

    public static Dialog initConfirmDialogNumberVideo(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , int h
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {


        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }


        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_number, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView textminuts = view.findViewById(R.id.textminuts);
        TextView textseconds = view.findViewById(R.id.textseconds);
        TextView texthour = view.findViewById(R.id.texthour);
        textminuts.setVisibility(View.INVISIBLE);
        textseconds.setVisibility(View.INVISIBLE);
        texthour.setVisibility(View.INVISIBLE);


        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);

        NumberPicker pickhour = view.findViewById(R.id.pickhour);

        pickhour.setVisibility(View.INVISIBLE);

        pickhour.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {


            }
        });
        NumberPicker pickminuts = view.findViewById(R.id.pickminuts);

        final int[] hn = {0};


        pickminuts.setMinValue(1);
        pickminuts.setMaxValue(50);

        pickminuts.setValue(h);


        pickminuts.setWrapSelectorWheel(true);


        pickminuts.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {

                hn[0] = newVal;

            }
        });
        NumberPicker picksecond = view.findViewById(R.id.picksecond);
        picksecond.setVisibility(View.INVISIBLE);


        picksecond.setWrapSelectorWheel(true);


        picksecond.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {


            }
        });

        titleTextView.setText(title);


        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {


                    onSuccessful.onSuccessful(true, hn[0]);


                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initConfirmDialogNumberphoto(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , int h, int m, int s
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }


        System.out.println("h" + h + "-m" + m + "-s" + s);
        final int[] second = {s};
        final int[] minuts = {m};
        final int[] hour = {h};

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_number, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);

        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);

        NumberPicker pickhour = view.findViewById(R.id.pickhour);

        pickhour.setMinValue(0);
        pickhour.setMaxValue(24);
        pickhour.setValue(h);
        pickhour.setWrapSelectorWheel(true);


        pickhour.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                if (second[0] < 15 && minuts[0] == 0 && newVal == 0)
                    second[0] = 15;
                else
                    hour[0] = newVal;
                hour[0] = newVal;

            }
        });
        NumberPicker pickminuts = view.findViewById(R.id.pickminuts);

        pickminuts.setMinValue(0);
        pickminuts.setMaxValue(59);
        pickminuts.setValue(m);
        pickminuts.setWrapSelectorWheel(true);


        pickminuts.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                if (second[0] < 15 && hour[0] == 0 && newVal == 0)
                    second[0] = 15;
                else
                    minuts[0] = newVal;

                minuts[0] = newVal;
            }
        });
        NumberPicker picksecond = view.findViewById(R.id.picksecond);


        picksecond.setMinValue(0);
        picksecond.setMaxValue(59);
        picksecond.setValue(s);
        picksecond.setWrapSelectorWheel(true);


        picksecond.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {

                if (minuts[0] == 0 && hour[0] == 0 && newVal < 15)
                    second[0] = 15;
                else
                    second[0] = newVal;
                // second[0] =newVal;
            }
        });


        titleTextView.setText(title);


        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {

                    System.out.println("h" + hour[0] + "-m" + minuts[0] + "-s" + second[0]);
                    onSuccessful.onSuccessful(true, hour[0], minuts[0], second[0]);


                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initConfirmDialogNumberRamadan(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , int h, int m, int s
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }
        final int[] second = {s};
        final int[] minuts = {m};
        final int[] hour = {h};

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_number, null, false);

        // ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);

        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        LinearLayout linearLayout = view.findViewById(R.id.seconds);
        linearLayout.setVisibility(View.GONE);

        NumberPicker pickhour = view.findViewById(R.id.pickhour);
        pickhour.setMinValue(0);
        pickhour.setMaxValue(24);
        pickhour.setValue(h);
        pickhour.setWrapSelectorWheel(true);

        pickhour.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                hour[0] = newVal;

            }
        });
        NumberPicker pickminuts = view.findViewById(R.id.pickminuts);
        pickminuts.setMinValue(0);
        pickminuts.setMaxValue(59);
        pickminuts.setWrapSelectorWheel(true);
        pickminuts.setValue(m);

        pickminuts.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                minuts[0] = newVal;
            }
        });
        NumberPicker picksecond = view.findViewById(R.id.picksecond);
        picksecond.setMinValue(0);
        picksecond.setMaxValue(59);
        picksecond.setWrapSelectorWheel(true);
        picksecond.setValue(s);


        picksecond.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                second[0] = newVal;
            }
        });

        titleTextView.setText(title);


        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(v -> {
            if (onSuccessful != null) {


                onSuccessful.onSuccessful(true, hour[0], minuts[0], second[0]);


            }
            confirmDialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> {
            if (onSuccessful != null) {
                onSuccessful.onSuccessful(false);
            }
            confirmDialog.dismiss();
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initAzkarTimeDialog(Context mContext, AthkarType type, OnAzkarTimeSelected onAzkarTimeSelected) {
        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText usernam = view.findViewById(R.id.username);
        usernam.setVisibility(View.GONE);
        LinearLayout linearLayout = view.findViewById(R.id.athkarl);
        linearLayout.setVisibility(View.VISIBLE);
        EditText durationTextInput = view.findViewById(R.id.number);
        Spinner spinner = view.findViewById(R.id.spinner);

        ArrayAdapter<String> dataAdapter = new ArrayAdapter<>(mContext,
                android.R.layout.simple_spinner_item, Utils.getAzkarTimeTypeTexts(type));
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(dataAdapter);
        spinner.setSelection(HawkSettings.getAzkarTimeType(type));

        titleTextView.setText(getString(R.string.afterorbefor));
        durationTextInput.setText(HawkSettings.getAzkarTimeDuration(type) + "");
        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(true);


        confirmButton.setOnClickListener(v -> {
            if (onAzkarTimeSelected != null) {

                String durationText = durationTextInput.getText().toString();
                if (TextUtils.isDigitsOnly(durationText)) {
                    int duration = Integer.parseInt(durationText);
                    HawkSettings.setAzkarTimeType(type, spinner.getSelectedItemPosition());
                    HawkSettings.setAzkarTimeDuration(type, duration);
                    onAzkarTimeSelected.onTimeSelected(spinner.getSelectedItemPosition(), duration);
                } else {
                    Toast.makeText(mContext, "أدخل قيمة رقمية فقط", Toast.LENGTH_SHORT).show();
                }

            }
            confirmDialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> confirmDialog.dismiss());
        confirmDialog.show();
        return confirmDialog;

    }

    public static Dialog initAzkarTimeDialog(Context mContext, List<String> list, int defaultIndex, String defaultDuration, OnAzkarTimeSelected onAzkarTimeSelected) {
        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText usernam = view.findViewById(R.id.username);
        usernam.setVisibility(View.GONE);
        LinearLayout linearLayout = view.findViewById(R.id.athkarl);
        linearLayout.setVisibility(View.VISIBLE);
        EditText durationTextInput = view.findViewById(R.id.number);
        Spinner spinner = view.findViewById(R.id.spinner);

        ArrayAdapter<String> dataAdapter = new ArrayAdapter<>(mContext,
                android.R.layout.simple_spinner_item, list);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(dataAdapter);
        spinner.setSelection(defaultIndex);

        titleTextView.setText(getString(R.string.afterorbefor));
        durationTextInput.setText(defaultDuration);
        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(true);


        confirmButton.setOnClickListener(v -> {
            if (onAzkarTimeSelected != null) {

                String duration = durationTextInput.getText().toString();
                if (TextUtils.isDigitsOnly(duration)) {
                    int usernameInt = Integer.parseInt(duration);
                    onAzkarTimeSelected.onTimeSelected(spinner.getSelectedItemPosition(), usernameInt);
                } else {
                    Toast.makeText(mContext, "أدخل قيمة رقمية فقط", Toast.LENGTH_SHORT).show();
                }

            }
            confirmDialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> confirmDialog.dismiss());
        confirmDialog.show();
        return confirmDialog;

    }

    public static Dialog initConfirmDialogm(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText usernam = view.findViewById(R.id.username);
        usernam.setVisibility(View.GONE);
        LinearLayout linearLayout = view.findViewById(R.id.athkarl);
        linearLayout.setVisibility(View.VISIBLE);
        EditText username = view.findViewById(R.id.number);

        Spinner spinner = view.findViewById(R.id.spinner);


        List<String> list = new ArrayList<>();
        list.add("بعد الفجر");
        list.add("قبل الشروق");
        list.add("بعد الشروق");
        ArrayAdapter<String> dataAdapter = new ArrayAdapter<String>(mContext,
                android.R.layout.simple_spinner_item, list);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setSelection(Hawk.get(ConstantsOfApp.MORNING_TIME_KEY, 1));
        spinner.setAdapter(dataAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Hawk.put(ConstantsOfApp.MORNING_TIME_KEY, position);
                Hawk.put(ConstantsOfApp.MORNING_TIME_NAME, parent.getItemAtPosition(position).toString().trim());
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });


        titleTextView.setText(title);
        //bodyMessageTextView.setText(bodyMessage);
        username.setText(Hawk.get(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT) + "");


        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(v -> {
            if (onSuccessful != null) {
                Hawk.put(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, Integer.parseInt(username.getText().toString()));
                Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_MORNING_KEY, true);

                onSuccessful.onSuccessful(true);

            }
            confirmDialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> {
            if (onSuccessful != null) {
                onSuccessful.onSuccessful(false);
            }
            confirmDialog.dismiss();
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }


    @SuppressLint("SetTextI18n")
    public static Dialog initConfirmDialogt(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText usernam = view.findViewById(R.id.username);
        usernam.setVisibility(View.GONE);
        LinearLayout linearLayout = view.findViewById(R.id.athkarl);
        linearLayout.setVisibility(View.VISIBLE);
        EditText username = view.findViewById(R.id.number);

        Spinner spinner = view.findViewById(R.id.spinner);


        List<String> list = new ArrayList<>();
        list.add("بعد العشاء مباشرة");
        list.add("بعد العشاء بـ");

        ArrayAdapter<String> dataAdapter = new ArrayAdapter<String>(mContext,
                android.R.layout.simple_spinner_item, list);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(dataAdapter);
        spinner.setSelection(Hawk.get(ConstantsOfApp.TARAWIH_TIME_KEY, 0));
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Hawk.put(ConstantsOfApp.TARAWIH_TIME_KEY, position);
                username.setEnabled(position == 1);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });


        username.setText(Hawk.get(ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY, ConstantsOfApp.POST_OR_PRE_PRAY_TARAWIH_DEFAULT) + "");


        titleTextView.setText(title);
        //bodyMessageTextView.setText(bodyMessage);


        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    Hawk.put(RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY, Integer.parseInt(username.getText().toString()));
                    //   Hawk.put(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, Integer.parseInt( username.getText().toString()));
                    // Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_MORNING_KEY, true);

                    //Hawk.put(ConstantsOfApp.MORNING_TIME_NAME, true);

                    onSuccessful.onSuccessful(true);


                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }

    public static Dialog initConfirmDialoge(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isCancelable
            , boolean isShowConfirmDialog
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText usernam = view.findViewById(R.id.username);
        usernam.setVisibility(View.GONE);
        LinearLayout linearLayout = view.findViewById(R.id.athkarl);
        linearLayout.setVisibility(View.VISIBLE);
        EditText username = view.findViewById(R.id.number);

        Spinner spinner = view.findViewById(R.id.spinner);

        List<String> list = new ArrayList<String>();
        list.add("بعد العصر");
        list.add("قبل المغرب");
        list.add("بعد المغرب");
        ArrayAdapter<String> dataAdapter = new ArrayAdapter<String>(mContext,
                android.R.layout.simple_spinner_item, list);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setSelection(Hawk.get(ConstantsOfApp.EVENING_TIME_KEY, 0));
        spinner.setAdapter(dataAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Hawk.put(ConstantsOfApp.EVENING_TIME_KEY, position);
                Hawk.put(ConstantsOfApp.EVENING_TIME_NAME, parent.getItemAtPosition(position).toString().trim());
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        titleTextView.setText(title);
        //bodyMessageTextView.setText(bodyMessage);

        username.setText(Hawk.get(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT) + "");


        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog confirmDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        confirmDialog.setContentView(view);
        confirmDialog.setCancelable(isCancelable);

        if (!isCancelable) {
            cancelButton.setVisibility(View.GONE);
        }


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    Hawk.put(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, Integer.parseInt(username.getText().toString()));
                    Hawk.put(ConstantsOfApp.IS_THERE_CITATION_FOR_EVENING_KEY, true);

                    onSuccessful.onSuccessful(true);


                }
                confirmDialog.dismiss();
            }
        });

        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(false);
                }
                confirmDialog.dismiss();
            }
        });

        if (isShowConfirmDialog) {
            confirmDialog.show();
        }
        return confirmDialog;
    }


    public static Dialog initMessageDialog(Context mContext
            , int imageResourcesId
            , String title
            , String bodyMessage
            , boolean isShow
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_confirm, null, false);

        ImageView iconImageView = view.findViewById(R.id.icon_ImageView_ConfirmationDialog);
        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText username = view.findViewById(R.id.username);
        username.setVisibility(View.GONE);

        titleTextView.setText(title);
        bodyMessageTextView.setText(bodyMessage);
        confirmButton.setText(R.string.ok);

        try {
            iconImageView.setImageResource(imageResourcesId);
        } catch (Exception e) {
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        final Dialog messageDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);
        Window window = messageDialog.getWindow();
        if (window != null) {
            window.setLayout(CardView.LayoutParams.MATCH_PARENT, CardView.LayoutParams.WRAP_CONTENT);
        }
        messageDialog.setContentView(view);
        //        messageDialog.setCancelable(false);


        confirmButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(true);
                }
                messageDialog.dismiss();
            }
        });

        cancelButton.setVisibility(View.GONE);

          /*cancelButton.setOnClickListener(new View.OnClickListener() {
              @Override
              public void onClick(View v) {
                  if (onSuccessful != null) {
                      onSuccessful.onSuccessful(false);
                  }
                  messageDialog.dismiss();
              }
          });*/

        if (isShow) {
            messageDialog.show();
        }
        return messageDialog;
    }

    public static Dialog inputLocationDialog(Context mContext

            , String title
            , String bodyMessage
            , boolean isShow
            , final OnSuccessful onSuccessful) {

        if (mContext == null || getValueWithoutNull(title).isEmpty() || getValueWithoutNull(bodyMessage).isEmpty()) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_input_loc, null, false);


        TextView titleTextView = view.findViewById(R.id.title_TextView_ConfirmationDialog);
        TextView bodyMessageTextView = view.findViewById(R.id.bodyMessage_TextView_ConfirmationDialog);
        Button confirmButton = view.findViewById(R.id.confirm_Button_ConfirmationDialog);
        Button cancelButton = view.findViewById(R.id.cancel_Button_ConfirmationDialog);
        EditText LATITUDE = view.findViewById(R.id.LATITUDE);
        EditText LONGITUDE = view.findViewById(R.id.LONGITUDE);

        //LATITUDE.setText( Hawk.get(ConstantsOfApp.LATITUDE_KEY,0.0).toString());
        //  LONGITUDE.setText(Hawk.get(ConstantsOfApp.LONGITUDE_KEY,0.0).toString());
        titleTextView.setText(title);

        confirmButton.setText(R.string.ok);


        final Dialog messageDialog = new Dialog(mContext, R.style.ConfirmDialogStyle);

        messageDialog.setContentView(view);
        //        messageDialog.setCancelable(false);


        confirmButton.setOnClickListener(v -> {
            if (onSuccessful != null) {
                if (LONGITUDE.getText() != null && LATITUDE.getText() != null) {
                    HawkSettings.putLatLong(Double.parseDouble(LATITUDE.getText().toString()), Double.parseDouble(LONGITUDE.getText().toString()));
                    Hawk.put(ConstantsOfApp.IS_INITIAL_SETUP_KEY, false);
                    Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, false);
                    Hawk.put(ConstantsOfApp.IS_UPDATE_PRAY_TIME, true);
                    onSuccessful.onSuccessful(true);
                } else {
                    Toast.makeText(mContext, "يجب ادخال قيمة", Toast.LENGTH_SHORT).show();
                }

            }
            messageDialog.dismiss();
        });

        cancelButton.setVisibility(View.GONE);

          /*cancelButton.setOnClickListener(new View.OnClickListener() {
              @Override
              public void onClick(View v) {
                  if (onSuccessful != null) {
                      onSuccessful.onSuccessful(false);
                  }
                  messageDialog.dismiss();
              }
          });*/

        if (isShow) {
            messageDialog.show();
        }
        return messageDialog;
    }

      /*public static Dialog initTimeLengthDialog(Context mContext
              , int year
              , int month
              , int day
              , boolean isCancelable
              , boolean isShowDateDialog
              , final OnSuccessful<String> onSuccessful) {

          if (mContext == null) {
              return null;
          }

          final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_dialog_date, null, false);

          final WheelDatePicker dateWheelDatePicker = view.findViewById(R.id.timeLength_WheelDatePicker_TimeLengthDialog);
          Button calculateMyAgeButton = view.findViewById(R.id.defineLength_Button_TimeLengthDialog);
          Button cancelButton = view.findViewById(R.id.cancel_Button_TimeLengthDialog);


          final Dialog timeLength = new Dialog(mContext, R.style.TimeLengthDialogStyle);
          timeLength.setContentView(view);
          timeLength.setCancelable(isCancelable);

          dateWheelDatePicker.setSelectedYear(year);
          dateWheelDatePicker.setSelectedMonth(month);
          dateWheelDatePicker.setSelectedDay(day);

          dateWheelDatePicker.setItemSpace((int) mContext.getResources().getDimension(R.dimen.date_space_size));
          dateWheelDatePicker.setItemTextColor(Color.parseColor("#7E7E7E"));
          dateWheelDatePicker.setSelectedItemTextColor(Color.BLACK);
          dateWheelDatePicker.setCyclic(true);
          dateWheelDatePicker.setItemSpace((int) mContext.getResources().getDimension(R.dimen.date_size));
          dateWheelDatePicker.setIndicator(true);
          dateWheelDatePicker.setIndicatorSize((int) mContext.getResources().getDimension(R.dimen.indicator_size));
          dateWheelDatePicker.setIndicatorColor(Color.DKGRAY);
  //        dateWheelDatePicker.setCurtain(true);
  //        dateWheelDatePicker.setCurtainColor(Color.parseColor("#1A000000"));
          dateWheelDatePicker.setCurved(true);

          cancelButton.setVisibility(isCancelable ? View.VISIBLE : View.GONE);
          dateWheelDatePicker.setOnDateSelectedListener(new WheelDatePicker.OnDateSelectedListener() {
              @Override
              public void onDateSelected(WheelDatePicker picker, Date date) {
                  if (dateWheelDatePicker.getCurrentDate().getTime() > System.currentTimeMillis()) {
                      Calendar calendar = Calendar.getInstance();
                      dateWheelDatePicker.setSelectedYear(calendar.get(Calendar.YEAR));
                      dateWheelDatePicker.setSelectedMonth(calendar.get(Calendar.MONTH) + 1);
                      dateWheelDatePicker.setSelectedDay(calendar.get(Calendar.DAY_OF_MONTH));
                  }
              }
          });

          calculateMyAgeButton.setOnClickListener(new View.OnClickListener() {
              @Override
              public void onClick(View v) {
                  if (onSuccessful != null) {
                      onSuccessful.onSuccessful(true, convertDateToBirthday(dateWheelDatePicker.getCurrentDate()));
                  }
                  timeLength.dismiss();
              }
          });

          cancelButton.setOnClickListener(new View.OnClickListener() {
              @Override
              public void onClick(View v) {
                  if (onSuccessful != null) {
                      onSuccessful.onSuccessful(false, "");
                  }
                  timeLength.dismiss();
              }
          });

          if (isShowDateDialog) {
              timeLength.show();
          }
          return timeLength;
      }*/

    public static void setColorStateListToSwitchCompat(Context mContext, SwitchCompat switchCompat) {
        final int[][] states = new int[3][];
        final int[] colors = new int[3];
        int i = 0;

        // Disabled state
        states[i] = new int[]{-android.R.attr.state_enabled};
        colors[i] = ContextCompat.getColor(mContext, R.color.colorPrimaryDark);
        i++;

        states[i] = new int[]{android.R.attr.state_checked};
        colors[i] = ContextCompat.getColor(mContext, R.color.colorPrimary);
        i++;

        // Default enabled state
        states[i] = new int[0];
        colors[i] = ContextCompat.getColor(mContext, R.color.grey_light);
        i++;

        switchCompat.setThumbTintList(new ColorStateList(states, colors));
        switchCompat.setTrackTintList(new ColorStateList(states, colors));
    }


    public static Dialog initShowMapsViewDialog(Context mContext
            , ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , List<Object> optionTitleList
            , String titleDialog
            , String descriptionDialog
            , final OnSuccessful<Integer> onSuccessful) {

        if (mContext == null || viewsAlrabeeaTimes == null || optionTitleList == null || optionTitleList.size() == 0) {
            return null;
        }

        final View view = LayoutInflater.from(mContext).inflate(R.layout.layout_show_maps_view, null, false);

        RecyclerView optionChooseRecyclerView = view.findViewById(R.id.optionChoose_RecyclerView_ShowMapsViewDialog);
        final Dialog showMapsView = new Dialog(mContext);

        OptionChooseAdapter optionChooseAdapter = new OptionChooseAdapter(mContext
                , optionTitleList
                , viewsAlrabeeaTimes
                , titleDialog
                , descriptionDialog
                , new AdapterCallback() {
            @Override
            public void onItemClick(int position, String tag) {
                if (onSuccessful != null) {
                    onSuccessful.onSuccessful(true, position);
                    showMapsView.dismiss();
                }
            }
        });

        //        optionChooseAdapter.addStringAll(optionTitleList);

        optionChooseRecyclerView.setAdapter(optionChooseAdapter);
        showMapsView.setContentView(view);
        showMapsView.setCancelable(false);


        return showMapsView;
    }

    public static Point getSizeScreen(AppCompatActivity mAppCompatActivity) {
        if (mAppCompatActivity == null) {
            return null;
        }
        Display display = mAppCompatActivity.getWindowManager().getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);

        return size;
    }


    public static void showFailAlert(Activity activity, String title, String text) {

        Alerter.create(activity)
                .setTitle(title)
                .setText(text)
                .setBackgroundColorInt(ContextCompat.getColor(activity, R.color.red))
                .enableInfiniteDuration(false)
                .setDuration(3000)
                .setContentGravity(Gravity.END)
                .show();
    }

    public static void showWarningAlert(Activity activity, String title) {

        Alerter.create(activity)
                .setTitle(title)
                .setBackgroundColorInt(ContextCompat.getColor(activity, R.color.warning))
                .enableInfiniteDuration(false)
                .setDuration(3000)
                .setContentGravity(Gravity.END)
                .show();
    }

    public static void showSuccessAlert(Activity activity, String msg) {
        Alerter.create(activity)
                .setTitle(msg)
                .setBackgroundColorInt(ContextCompat.getColor(activity, R.color.green_light))
                .enableInfiniteDuration(false)
                .setDuration(3000)
                .setContentGravity(Gravity.START)
                .show();
    }

    public static Alert showEventAlert(Activity activity, String msg, long duration, int color) {
        return Alerter.create(activity)
                .setTitle(msg)
//                .setBackgroundColorInt(ContextCompat.getColor(activity, R.color.green_light))
//                .setBackgroundColorInt(color)
                .setBackgroundColorInt(Utils.getTransparentColor(color, 60))
                .enableInfiniteDuration(false)
                .setDuration(duration)
                .setContentGravity(Gravity.START)
                .show();
    }

    public static int getTransparentColor(int color, int alpha) {
        int oldAlpha = Color.alpha(color);
        int red = Color.red(color);
        int green = Color.green(color);
        int blue = Color.blue(color);

        // Set alpha based on your logic, here I'm making it 25% of it's initial value.
//        alpha *= 0.25;

        return Color.argb(alpha, red, green, blue);
    }

    public static void showSuccessAlert(Activity activity, String title, String msg, int duration) {

        Alerter.create(activity)
                .setTitle(title)
                .setText(msg)
                .setBackgroundColorInt(ContextCompat.getColor(activity, R.color.green_light))
                .enableInfiniteDuration(false)
                .setDuration(duration)
                .setContentGravity(Gravity.END)
                .show();
    }


    public static boolean isEmailValid(String email) {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches();
    }

    public static void setPhotoWithCallBack(Context context
            , ImageView imageView
            , String url
            , final OnSuccessful onSuccessful) {
        try {
            if (url != null && !url.isEmpty()) {
                Picasso.get().load(url).into(imageView);
//                Picasso.with(context)
//                        .invalidate(url);
//                Picasso.with(context)
//                        .load(url)
//                        .into(imageView);
            } else {
                onSuccessful.onSuccessful(false);
            }

        } catch (Exception e) {
            onSuccessful.onSuccessful(false);
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }


    public static String getAndroidDeviceId(Context mContext) {
        if (mContext == null) {
            return "";
        }
        return Settings.Secure.getString(mContext.getContentResolver(),
                Settings.Secure.ANDROID_ID);
    }


    public static boolean isArabicString(String text) {
        if (text == null || text.isEmpty()) {
            return true;
        }
        for (int i = 0; i < text.length(); ) {
            int c = text.codePointAt(i);
            if (c >= 0x0600 && c <= 0x06E0)
                return true;
            i += Character.charCount(c);
        }
        return false;
          /*String textWithoutSpace = text.trim().replaceAll(" ",""); //to ignore whitepace
          for (int i = 0; i < textWithoutSpace.length();) {
              int c = textWithoutSpace.codePointAt(i);
              //range of arabic chars/symbols is from 0x0600 to 0x06ff
              //the arabic letter 'لا' is special case having the range from 0xFE70 to 0xFEFF
              if (c >= 0x0600 && c <=0x06FF || (c >= 0xFE70 && c<=0xFEFF))
                  i += Character.charCount(c);
              else
                  return false;

          }
          return true;*/
    }

    public static void putPrayerTimesForYear(PrayerApi prayerApi) {
        deleteAllTimings();
        insertTimings(prayerApi.getAllTimings());
//        String year = prayerApi.getPrayerList().getJanuary().get(0).getDate().getGregorian().getYear();
//        Hawk.put(PRAYER_TIME_KEY + year, prayerApi);
    }

    public static String getKeyPrayerApi() {
//        PrayerSystemsSchools prayerSystemsSchools =
//                Hawk.get(ConstantsOfApp.PRAYER_METHOD_KEY, null);

        final double latitude = HawkSettings.getLatitude();
        final double longitude = HawkSettings.getLongitude();
        final int adjustHijriDate = Hawk.get(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, 0);

        System.out.println(ConstantsOfApp.PRAYER_API_KEY + "latitude=" + latitude + "&longitude=" + longitude
                + "&method=" + HawkSettings.getCurrentPrayerMethod().value
                + "&adjustment=" + adjustHijriDate);
        return ConstantsOfApp.PRAYER_API_KEY + "latitude=" + latitude + "&longitude=" + longitude
                + "&method=" + HawkSettings.getCurrentPrayerMethod().value
                + "&adjustment=" + adjustHijriDate;
    }

    public static String replaceNumberWithSettings(String number) {
        if (HawkSettings.getTypeNumber().equals(HawkConstants.AR_LANGUAGE)) {
            return replaceEnglishNumberToArabicNumber(number);
        } else {
            return replaceArabicNumberToEnglishNumber(number);
        }
    }

    public static String replaceArabicNumberToEnglishNumber(String number) {
        return number.replace("٠", "0")
                .replace("١", "1")
                .replace("٢", "2")
                .replace("٣", "3")
                .replace("٤", "4")
                .replace("٥", "5")
                .replace("٦", "6")
                .replace("٧", "7")
                .replace("٨", "8")
                .replace("٩", "9");
    }

    public static boolean boolAthkar(int id) {
        boolean b = false;
        switch (id) {
            case 1:
            case 2:
            case 3:
                b = true;
                break;
        }
        return b;
    }

    public static String replaceEnglishNumberToArabicNumber(String number) {
        return number.replace("0", "٠")
                .replace("1", "١")
                .replace("2", "٢")
                .replace("3", "٣")
                .replace("4", "٤")
                .replace("5", "٥")
                .replace("6", "٦")
                .replace("7", "٧")
                .replace("8", "٨")
                .replace("9", "٩");
    }


    public static boolean isLandscape() {
        try {
            WindowManager wm = (WindowManager) baseContext.getSystemService(Context.WINDOW_SERVICE);
            DisplayMetrics m = new DisplayMetrics();
            wm.getDefaultDisplay().getMetrics(m);
            return m.widthPixels > m.heightPixels;

//            int o = wm.getDefaultDisplay().getRotation();
//
//            return o == 1 || o == 3;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return false;
//        int o = getAppOrientation();
//        return o == 1 || o == 3;
    }


    public static boolean isDayJomaa(String day) {
        if (getValueWithoutNull(day).isEmpty()) {
            return false;
        }
        return day.equalsIgnoreCase("Friday")
                || day.equalsIgnoreCase("Al Juma'a")
                || day.equalsIgnoreCase("الجمعة");
    }

    public static Object[] putPrayKey(Context context, String nameOfPrayer, boolean isRamadanSettings) {
        if (context == null || Utils.getValueWithoutNull(nameOfPrayer).isEmpty()) {
            return null;
        }

        String nameOfPrayerKey = "", postOrPreToPrayKey = "", isEnablePostOrPrePrayKey = "", isEnableTimeBetweenAdanAndIkamaKey = "", isEnableTimeOfIkamaKey = "", timeOfIkamaDefault = "";
        int timeBetweenAdanAndIkamaDefault = 0;

        if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.FAJR_KEY)) {
            nameOfPrayerKey = ConstantsOfApp.FAJR_KEY;
            postOrPreToPrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_FAJR_KEY : ConstantsOfApp.POST_OR_PRE_TO_FAJR_KEY;
            timeBetweenAdanAndIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT;

            isEnablePostOrPrePrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY : ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY;
            isEnableTimeBetweenAdanAndIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY : ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY;
            isEnableTimeOfIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY : ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY;
            timeOfIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_FAJR_DEFAULT : ConstantsOfApp.TIME_OF_IKAMA_FAJR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.DHUHR_KEY)) {
            nameOfPrayerKey = ConstantsOfApp.DHUHR_KEY;
            postOrPreToPrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DHUHR_KEY : ConstantsOfApp.POST_OR_PRE_TO_DHUHR_KEY;
            timeBetweenAdanAndIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT;

            isEnablePostOrPrePrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY : ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY;
            isEnableTimeBetweenAdanAndIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY : ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY;
            isEnableTimeOfIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY : ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY;
            timeOfIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_DHUHR_DEFAULT : ConstantsOfApp.TIME_OF_IKAMA_DHUHR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.ASR_KEY)) {
            nameOfPrayerKey = ConstantsOfApp.ASR_KEY;
            postOrPreToPrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ASR_KEY : ConstantsOfApp.POST_OR_PRE_TO_ASR_KEY;
            timeBetweenAdanAndIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT;

            isEnablePostOrPrePrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY : ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ASR_KEY;
            isEnableTimeBetweenAdanAndIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY : ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY;
            isEnableTimeOfIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY : ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ASR_KEY;
            timeOfIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_ASR_DEFAULT : ConstantsOfApp.TIME_OF_IKAMA_ASR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.MAGHRIB_KEY)) {
            nameOfPrayerKey = ConstantsOfApp.MAGHRIB_KEY;
            postOrPreToPrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY : ConstantsOfApp.POST_OR_PRE_TO_MAGHRIB_KEY;
            timeBetweenAdanAndIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT;

            isEnablePostOrPrePrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY : ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY;
            isEnableTimeBetweenAdanAndIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY : ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY;
            isEnableTimeOfIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY : ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY;
            timeOfIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_MAGHRIB_DEFAULT : ConstantsOfApp.TIME_OF_IKAMA_MAGHRIB_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.ISHA_KEY)) {
            nameOfPrayerKey = ConstantsOfApp.ISHA_KEY;
            postOrPreToPrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_ISHA_KEY : ConstantsOfApp.POST_OR_PRE_TO_ISHA_KEY;
            timeBetweenAdanAndIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT;

            isEnablePostOrPrePrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY : ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY;
            isEnableTimeBetweenAdanAndIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY : ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY;
            isEnableTimeOfIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY : ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY;
            timeOfIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_ISHA_DEFAULT : ConstantsOfApp.TIME_OF_IKAMA_ISHA_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.JOMAA_KEY)) {
            nameOfPrayerKey = ConstantsOfApp.JOMAA_KEY;
            postOrPreToPrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_JOMAA_KEY : ConstantsOfApp.POST_OR_PRE_TO_JOMAA_KEY;
            timeBetweenAdanAndIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT;
            isEnablePostOrPrePrayKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY : ConstantsOfApp.IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY;
            isEnableTimeBetweenAdanAndIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY : ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY;
            isEnableTimeOfIkamaKey = isRamadanSettings ? ConstantsOfApp.RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY : ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY;
            timeOfIkamaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_OF_IKAMA_JOMAA_DEFAULT : ConstantsOfApp.TIME_OF_IKAMA_JOMAA_DEFAULT;
        }
        return new Object[]{nameOfPrayerKey, postOrPreToPrayKey
                , timeBetweenAdanAndIkamaDefault
                , isEnablePostOrPrePrayKey
                , isEnableTimeBetweenAdanAndIkamaKey
                , isEnableTimeOfIkamaKey
                , timeOfIkamaDefault};
    }

    public static int getAnnouncementShowTimeDefault(Context context, String nameOfPrayer, boolean isRamadanSettings) {
        if (context == null || Utils.getValueWithoutNull(nameOfPrayer).isEmpty()) {
            return 0;
        }

        if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.FAJR_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT
                    : ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.DHUHR_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT
                    : ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.ASR_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT
                    : ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.MAGHRIB_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT
                    : ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.ISHA_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT
                    : ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.JOMAA_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_JOMAA_DEFAULT
                    : ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_JOMAA_DEFAULT;
        }
        return 0;
    }

    public static int getTimeToFinishThePrayer(String nameOfPrayer, boolean isRamadanSettings) {
        return getTimeToFinishThePrayer(baseContext, nameOfPrayer, isRamadanSettings);
    }

    public static int getTimeToFinishThePrayer(Context context, String nameOfPrayer, boolean isRamadanSettings) {
        if (context == null || Utils.getValueWithoutNull(nameOfPrayer).isEmpty()) {
            return 0;
        }

        if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.FAJR_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_LOCK_DURING_PRAYER_FAJR_DEFAULT
                    : ConstantsOfApp.LOCK_DURING_PRAYER_FAJR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.DHUHR_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_LOCK_DURING_PRAYER_DHUHR_DEFAULT
                    : ConstantsOfApp.LOCK_DURING_PRAYER_DHUHR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.ASR_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_LOCK_DURING_PRAYER_ASR_DEFAULT
                    : ConstantsOfApp.LOCK_DURING_PRAYER_ASR_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.MAGHRIB_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_LOCK_DURING_PRAYER_MAGHRIB_DEFAULT
                    : ConstantsOfApp.LOCK_DURING_PRAYER_MAGHRIB_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.ISHA_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_LOCK_DURING_PRAYER_ISHA_DEFAULT
                    : ConstantsOfApp.LOCK_DURING_PRAYER_ISHA_DEFAULT;
        } else if (nameOfPrayer.equalsIgnoreCase(ConstantsOfApp.JOMAA_KEY)) {
            return isRamadanSettings
                    ? ConstantsOfApp.RAMADAN_LOCK_DURING_PRAYER_JOMAA_DEFAULT
                    : ConstantsOfApp.LOCK_DURING_PRAYER_JOMAA_DEFAULT;
        }
        return 0;
    }

    public static Bitmap convertImageBase64ToBitmapImage(String imageBase64) {
        if (getValueWithoutNull(imageBase64).isEmpty()) {
            return null;
        }
        byte[] decodedString = Base64.decode(imageBase64, Base64.DEFAULT);
        //        return BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);
        return decodeSampledBitmapFromResource(decodedString, 1920, 1080);
    }


    public static Bitmap decodeSampledBitmapFromResource(byte[] data, int reqWidth, int reqHeight) {

        // First decode with inJustDecodeBounds=true to check dimensions
        final BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        options.inPreferredConfig = Bitmap.Config.ARGB_8888;
        //options.inPurgeable = true;
        BitmapFactory.decodeByteArray(data, 0, data.length, options);

        // Calculate inSampleSize
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeByteArray(data, 0, data.length, options);
    }

    public static int calculateInSampleSize(
            BitmapFactory.Options options, int reqWidth, int reqHeight) {
        // Raw height and width of image
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {

            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            // Calculate the largest inSampleSize value that is a power of 2 and keeps both
            // height and width larger than the requested height and width.
            while ((halfHeight / inSampleSize) > reqHeight
                    && (halfWidth / inSampleSize) > reqWidth) {
                inSampleSize *= 2;
            }
        }

        return inSampleSize;
    }

    public static String convertImagePathToImageBase64(String imagePath) {
        if (getValueWithoutNull(imagePath).isEmpty()) {
            return "";
        }


        Bitmap bm = BitmapFactory.decodeFile(imagePath);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bm.compress(Bitmap.CompressFormat.JPEG, 60, baos);
        //        if (imagePath.trim().toLowerCase().endsWith("png")){
        //            bm.compress(Bitmap.CompressFormat.PNG, 60, baos);
        //        }else {
        //            bm.compress(Bitmap.CompressFormat.JPEG, 60, baos);
        //        }
        byte[] b = baos.toByteArray();
        return Base64.encodeToString(b, Base64.DEFAULT);
    }

    public static String convertImageBitmapToImageBase64(Bitmap bitmap) {
        if (bitmap == null) {
            return "";
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 60, baos);
        byte[] b = baos.toByteArray();
        return Base64.encodeToString(b, Base64.DEFAULT);
    }

    public static Realm getInstanceOfRealm() {
        if (realm == null) {
            realm = Realm.getDefaultInstance();
        }
        return realm;
    }


    public static void setScreenBrightnessMax(Activity activity) {
        if (activity == null) {
            return;
        }
        WindowManager.LayoutParams layout = activity.getWindow().getAttributes();
        layout.screenBrightness = 1F;
        activity.getWindow().setAttributes(layout);
    }

    public static void openExcel(Activity mActivity, File mFile) {
        //        if (mActivity == null || getValueWithoutNull(filePath).isEmpty()) {
        if (mActivity == null || mFile == null) {
            return;
        }
        try {
            Log.e(TAG, "openExcel mFile.getAbsolutePath: " + mFile.getAbsolutePath());
            Log.e(TAG, "openExcel mFile.getPath: " + mFile.getPath());

            Uri uri;

//            uri = FileProvider.getUriForFile(mActivity, BuildConfig.APPLICATION_ID + ".fileprovider", mFile);
            uri = FileProvider.getUriForFile(mActivity, "com.arapeak.alrbea" + ".fileprovider", mFile);

            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.setDataAndType(uri, "application/vnd.ms-excel");
            intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            mActivity.startActivity(intent);

        } catch (ActivityNotFoundException e) {
            Utils.showFailAlert(mActivity
                    , mActivity.getString(R.string.there_is_a_problem)
                    , mActivity.getString(R.string.no_application_supports_opening_of_excel_files));
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        } catch (Exception e) {
            Utils.showFailAlert(mActivity
                    , mActivity.getString(R.string.there_is_a_problem)
                    , mActivity.getString(R.string.try_again));
            Log.e(TAG, "Error: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        //        Intent intent = new Intent(Intent.ACTION_VIEW);
        ////        Uri uri = Uri.fromFile(new File(android.os.Environment.getExternalStorageDirectory()
        ////                .getAbsolutePath() + File.separator + "/Folder/" + fileName));
        //        Uri uri = Uri.fromFile(new File(filePath));
        //
        //        intent.setDataAndType(uri, "files/*.xlsx");
        //        try {
        //            mActivity.startActivity(intent);
        //        } catch (ActivityNotFoundException e) {
        //            Utils.showFailAlert(mActivity
        //                    , mActivity.getString(R.string.there_is_a_problem)
        //                    , mActivity.getString(R.string.no_application_supports_opening_of_wxcel_files));
        //            Log.e(TAG, "Error: " + e.getMessage());
        //            e.printStackTrace();
        //        } catch (Exception e) {
        //            Utils.showFailAlert(mActivity
        //                    , mActivity.getString(R.string.there_is_a_problem)
        //                    , mActivity.getString(R.string.try_again));
        //            Log.e(TAG, "Error: " + e.getMessage());
        //            e.printStackTrace();
        //        }


        //        File path = new File(mActivity.getFilesDir(), "dl");
        //        File files = new File(path, filename);
        //
        //        // Get URI and MIME type of files
        //        Uri uri = FileProvider.getUriForFile(mActivity, "" + ".fileprovider", files);
        //        String mime = mActivity.getContentResolver().getType(uri);
        //
        //        // Open files with user selected app
        //        Intent intent = new Intent();
        //        intent.setAction(Intent.ACTION_VIEW);
        //        intent.setDataAndType(uri, mime);
        //        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        //        mActivity.startActivity(intent);
    }

    public static File getOutputFiles(Context mContext, String nameFile) {

        if (mContext == null || getValueWithoutNull(nameFile).isEmpty()) {
            return null;
        }
        File mediaStorageDir = new File(Environment.getExternalStorageDirectory()
                + "/Android/data/"
                + mContext.getPackageName()
                + "/AlrabeeaTimes/Files");

        if (!mediaStorageDir.exists()) {
            if (!mediaStorageDir.mkdirs()) {
                return null;
            }
        }
        File mediaFile;
        //        String mFileName = "image_" + System.currentTimeMillis() + ".jpg";
        //        String mFileName = "file_" + getDateTimeNowWithFileName() + ".jpg";
        //        mFileName = getValueWithoutNull(nameFile).isEmpty() ? mFileName : nameFile;
        //        mFileName = nameFile;
        mediaFile = new File(mediaStorageDir.getPath() + File.separator + nameFile);
        return mediaFile;
    }


    public static File getOutputFiles(Context mContext) {

        if (mContext == null) {
            return null;
        }
        File mediaStorageDir = new File(Environment.getExternalStorageDirectory()
                + "/Android/data/"
                + mContext.getPackageName()
                + "/AlrabeeaTimes/Files");

        if (!mediaStorageDir.exists()) {
            if (!mediaStorageDir.mkdirs()) {
                return null;
            }
        }
        File mediaFile;
        mediaFile = new File(mediaStorageDir.getPath() + File.separator);
        return mediaFile;
    }

    public static File getOutputFilesForApp(Context mContext) {

        if (mContext == null) {
            return null;
        }
        File mediaStorageDir = new File(Environment.getExternalStorageDirectory()
                + "/Android/data/"
                + mContext.getPackageName()
                + "/AlrabeeaTimes/Files");

        if (!mediaStorageDir.exists()) {
            if (!mediaStorageDir.mkdirs()) {
                return null;
            }
        }
        File mediaFile;
        mediaFile = new File(mediaStorageDir.getPath());
        return mediaFile;
    }

    public static String getDateTimeNowWithFileName() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy_MM_dd__HH_mm_ss_SS", new Locale("en"));
        return sdf.format(new Date());
    }

    public static File getOutputMediaImage(Context mContext, String nameImage) {

        if (mContext == null) {
            return null;
        }
        File mediaStorageDir = new File(Environment.getExternalStorageDirectory()
                + "/Android/data/"
                + mContext.getPackageName()
                + "/AlrabeeaTimes/Images");

        if (!mediaStorageDir.exists()) {
            if (!mediaStorageDir.mkdirs()) {
                return null;
            }
        }
        File mediaFile;
        //        String mImageName = "image_" + System.currentTimeMillis() + ".jpg";
        String mImageName = "image_" + getDateTimeNowWithFileName() + ".jpg";
        mImageName = getValueWithoutNull(nameImage).isEmpty() ? mImageName : nameImage;
        mediaFile = new File(mediaStorageDir.getPath() + File.separator + mImageName);
        return mediaFile;
    }

    public static void initActivity(Activity mActivity) {
        if (mActivity == null) {
            Log.e(TAG, "initActivity: mActivity==null");
            return;
        }
        Utils.setLocaleLanguage(mActivity, HawkSettings.getLocaleLanguage());
        Utils.setScreenBrightnessMax(mActivity);
        mActivity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }

    public static void openUrlByBrowser(Activity mActivity, String url, boolean isShowMessage) {
        if (mActivity == null || getValueWithoutNull(url).isEmpty()) {
            if (mActivity != null) {
                Log.e(TAG, "openUrlByBrowser url is null");
                if (isShowMessage) {
                    showFailAlert(mActivity
                            , mActivity.getString(R.string.there_is_a_problem)
                            , mActivity.getString(R.string.try_again));
                }
                return;
            }
            Log.e(TAG, "openUrlByBrowser mActivity is null");
            return;
        }
        try {
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            mActivity.startActivity(browserIntent);
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "openUrlByBrowser ActivityNotFoundException: " + e.getMessage());
            if (isShowMessage) {
                showFailAlert(mActivity
                        , mActivity.getString(R.string.there_is_a_problem)
                        , mActivity.getString(R.string.please_install_a_browser));
            }
        } catch (Exception e) {
            Log.e(TAG, "openUrlByBrowser Exception: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            if (isShowMessage) {
                showFailAlert(mActivity
                        , mActivity.getString(R.string.there_is_a_problem)
                        , mActivity.getString(R.string.try_again));
            }
        }
    }

//    public static SliderLayout.Transformer getSliderLayoutTransformer(int indexTransformer) {
//        switch (indexTransformer) {
//            case 1:
//                return SliderLayout.Transformer.Accordion;
//            case 2:
//                return SliderLayout.Transformer.Background2Foreground;
//            case 3:
//                return SliderLayout.Transformer.CubeIn;
//            case 4:
//                return SliderLayout.Transformer.DepthPage;
//            case 5:
//                return SliderLayout.Transformer.Fade;
//            case 6:
//                return SliderLayout.Transformer.FlipHorizontal;
//            case 7:
//                return SliderLayout.Transformer.FlipPage;
//            case 8:
//                return SliderLayout.Transformer.Foreground2Background;
//            case 9:
//                return SliderLayout.Transformer.RotateDown;
//            case 10:
//                return SliderLayout.Transformer.RotateUp;
//            case 11:
//                return SliderLayout.Transformer.Stack;
//            case 12:
//                return SliderLayout.Transformer.Tablet;
//            case 13:
//                return SliderLayout.Transformer.ZoomIn;
//            case 14:
//                return SliderLayout.Transformer.ZoomOutSlide;
//            case 15:
//                return SliderLayout.Transformer.ZoomOut;
//            default:
//                return SliderLayout.Transformer.Default;
//
//        }
//    }

    public static boolean isNougatOrHigher() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.N;
    }

    public static File getOutputMediaFile(Context mContext, String nameImage) {

        if (mContext == null) {
            return null;
        }

        String mImageName = "file_" + getDateTimeNowWithFileName() + ".txt";
        mImageName = getValueWithoutNull(nameImage).isEmpty() ? mImageName : nameImage;

        File mediaStorageDir = new File(Environment.getExternalStorageDirectory()
                + "/Android/data/"
                + mContext.getPackageName()
                + "/Alrbea/Files");

        if (!mediaStorageDir.exists()) {
            if (!mediaStorageDir.mkdirs()) {
                return null;
            }
        }
        File mediaFile;
        mediaFile = new File(mediaStorageDir.getPath() + File.separator + mImageName);
        return mediaFile;
    }

    public static UmmalquraCalendar getUmmalquraCalendar() {

        UmmalquraCalendar hijriCalendar = new UmmalquraCalendar();
        int hijriAdjust = Hawk.get(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, 0);
        if (hijriAdjust != 0) hijriCalendar.add(Calendar.DAY_OF_YEAR, hijriAdjust);
        return hijriCalendar;
        /*UmmalquraCalendar cal = new UmmalquraCalendar();
        cal.add(Calendar.DAY_OF_MONTH, Hawk.get(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, 0));
        return cal;*/
    }

    public static Calendar getCalendar() {
        Calendar cal = Calendar.getInstance();
        Utils.clearCalendarDate(cal);
        return cal;
    }

    public static Calendar getCalendar(long date) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date);
        return cal;
    }

    public static String formatUmmalquraCalendar(String pattern, Date time) {
        SimpleDateFormat sdf = new SimpleDateFormat("", HawkSettings.getLocale());
        sdf.setCalendar(Utils.getUmmalquraCalendar());
        sdf.applyPattern(pattern);
        return replaceNumberWithSettings(sdf.format(time));
    }

    public static String formatCalendar(String pattern, Date time) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, HawkSettings.getLocale());
        return replaceNumberWithSettings(sdf.format(time));
    }

    /*public static int getCurrentTheme() {
        return Hawk.get(APP_THEME_KEY, APP_THEME_DEFAULT_KEY);
    } */

    public static TimingsAlrabeeaTimesList getTimingFromExcelFile(String path) {
        try {
            // Creating Input Stream
            File file = new File(path);
            FileInputStream myInput = new FileInputStream(file);

            POIFSFileSystem myFileSystem = new POIFSFileSystem(myInput);
            HSSFWorkbook wb = new HSSFWorkbook(myFileSystem);
            HSSFSheet sheet = wb.getSheetAt(0);
            ArrayList<JSONObject> a = new ArrayList<JSONObject>();
            Iterator<Row> rows = sheet.iterator();

            while (rows.hasNext()) {
                JSONObject mylist = new JSONObject();
                Row row = rows.next();
                Iterator<Cell> cellIterator = row.cellIterator();
                while (cellIterator.hasNext()) {

                    Cell cell = cellIterator.next();

                    String day = "";
                    String month = "";
                    if (cell.getColumnIndex() == 0) {
                        String date = cell.getDateCellValue().toString();

                        String dtStart = "2010-10-15T09:27:37Z";
                        Date date2 = cell.getDateCellValue();
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(date2);
                        day = String.valueOf(date2.getDay());
                        mylist.put("Year", cal.get(Calendar.YEAR));
                        mylist.put("Month", cal.get(Calendar.MONTH) + 1);
                        mylist.put("Day", cal.get(Calendar.DAY_OF_MONTH));
                        //    System.out.println(cell.getDateCellValue().getDay() + "\t\t"+cell.getDateCellValue().getMonth());
                    }

                    if (cell.getColumnIndex() == 1) {
                        mylist.put("Fajr", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                        // System.out.println(cell.getDateCellValue().getHours()+":" +  cell.getDateCellValue().getMinutes()+"\t\t");
                    }
                    if (cell.getColumnIndex() == 2) {
                        mylist.put("Sunrise", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                    }
                    if (cell.getColumnIndex() == 3) {
                        mylist.put("Dhuhr", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                    }
                    if (cell.getColumnIndex() == 4) {
                        mylist.put("Asr", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                    }
                    if (cell.getColumnIndex() == 5) {
                        mylist.put("Maghrib", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                    }
                    if (cell.getColumnIndex() == 6) {
                        mylist.put("Isha", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                    }

                }
                a.add(mylist);
            }


            String json = a.toString();
            Log.e(TAG, "json: " + json);

            if (Utils.getValueWithoutNull(json).isEmpty()) {
                return null;
            }

            if (json.contains(",\n" +
                    "\t{\n" +
                    "\t\t\"Month\": \"\"\n" +
                    "\t}")) {

                json = json.trim().replaceAll("\n" +
                        "\t\t\"Month\": \"\"\n", "").trim();

                if (json.trim().endsWith("}")) {
                    json = json.trim().substring(0, json.lastIndexOf("}")).trim();
                }
                if (json.trim().endsWith("{")) {
                    json = json.trim().substring(0, json.lastIndexOf("{")).trim();
                }
                if (json.trim().endsWith(",")) {
                    json = json.trim().substring(0, json.lastIndexOf(",")).trim();
                }
            }

            if (!json.trim().toLowerCase().contains("\"timings\":")) {
//                json = "{\n\"timings\": " + json.trim() + "\n}";
                json = "{\n" + "\t\"timings\": " + json.trim() + "\n}";
                json = json.replaceAll(" \uFEFF", " ");
            }

//            Log.e(TAG, "json: " + json);
            GsonBuilder builder = new GsonBuilder();
            Gson mGson = builder.create();
            TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
                    = mGson.fromJson(json
                            .trim()
//                            .toLowerCase()
                            .replaceAll("aa", "am")
                            .replaceAll("pp", "pm")
                    , TimingsAlrabeeaTimesList.class);

            wb.close();
            myFileSystem.close();
            myInput.close();
            return timingsAlrabeeaTimesList;

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        return null;
    }

    public static TimingsAlrabeeaTimesList getTimingFromCsvFile(String path) {
        try {
            // Creating Input Stream
            File file = new File(path);
            FileReader reader = new FileReader(file);
            BufferedReader br = new BufferedReader(reader);
            TimingsAlrabeeaTimesList result = new TimingsAlrabeeaTimesList();
            List<TimingsAlrabeeaTimes> times = new ArrayList<>();
            String line = null;

            Pattern pattern = Pattern.compile("([\\d]+)-([\\d]+)-([\\d]+);(\\d\\d:\\d\\d);(\\d\\d:\\d\\d);(\\d\\d:\\d\\d);(\\d\\d:\\d\\d);(\\d\\d:\\d\\d);(\\d\\d:\\d\\d)", Pattern.CASE_INSENSITIVE);
            while ((line = br.readLine()) != null) {
                //regex matching : 2022-01-01;05:40;07:01;12:27;15:32;17:53;19:23
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    TimingsAlrabeeaTimes time = new TimingsAlrabeeaTimes();
                    time.setMonth(matcher.group(2));
                    time.setDay(matcher.group(3));

                    time.setFajr(matcher.group(4));
                    time.setSunrise(matcher.group(5));
                    time.setDhuhr(matcher.group(6));
                    time.setAsr(matcher.group(7));
                    time.setMaghrib(matcher.group(8));
                    time.setIsha(matcher.group(9));
                    times.add(time);
                }
            }
            br.close();
            reader.close();

            if (times.size() <= 0)
                return null;
//            Utils.setCustomTimeFormat("");
            result.setTimingsAlrabeeaTimesList(times);
            return result;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return null;
    }

    public static String getFileExtension(String path) {
        return path.substring(path.lastIndexOf(".") + 1);
    }

    public static boolean isJomaa() {
        return new GregorianCalendar().get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY;
    }

    public static boolean isRamadan() {
        return getUmmalquraCalendar().get(Calendar.MONTH) == UmmalquraCalendar.RAMADHAN;
    }

    public static void insertTimings(List<TimingsAlrabeeaTimes> timings) {
        Log.d("Beshr", "insertTimings: " + timings.toArray());
        try (OmanCitiesDb omanCitiesDb = new OmanCitiesDb()) {
            SQLiteDatabase db = omanCitiesDb.getWritableDatabase();
            for (TimingsAlrabeeaTimes timing : timings) {
                ContentValues values = new ContentValues();
                values.put("isha", timing.getIsha());
                values.put("maghrib", timing.getMaghrib());
                values.put("asr", timing.getAsr());
                values.put("dhuhr", timing.getDhuhr());
                values.put("sunrise", timing.getSunrise());
                values.put("fajr", timing.getFajr());
                values.put("month", timing.getMonth());
                values.put("day", timing.getDay());
                values.put("year", timing.Year);
                db.insert("timings", null, values);
            }
            db.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public static void deleteAllTimings() {
        try (OmanCitiesDb omanCitiesDb = new OmanCitiesDb()) {
            SQLiteDatabase db = omanCitiesDb.getWritableDatabase();
            db.delete("timings", null, null);
            db.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }


    //    public static void setIsPremium(boolean b) {
//        Hawk.put("isPremium",b);
//    }
    public static boolean getIsPremium() {
        return HawkSettings.getPremiumUser() != null;
//        return Hawk.get("isPremium",false);
    }

    public static void deleteAllFiles(String s) {
        File file = new File(s);
        if (file.isDirectory() && file.listFiles() != null) {
            for (File fileChild : file.listFiles()) {
                if (fileChild != null && fileChild.exists())
                    fileChild.delete();
            }
        }
    }

    public static void setSystemProperties(String str, String str2) {
        try {
            @SuppressLint("PrivateApi") Class<?> cls = Class.forName("android.os.SystemProperties");
            cls.getMethod("set", String.class, String.class).invoke(cls, str, str2);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public static String getSystemProperties(String str) {
        try {
            @SuppressLint("PrivateApi") Class<?> cls = Class.forName("android.os.SystemProperties");
            return (String) cls.getMethod("get", String.class).invoke(cls, str);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return "";
    }

    public static void createFile(File file) {
//        createPath(file.getParentFile());
        File parent = file.getParentFile();
        if (parent != null) parent.mkdirs();
        try {
            file.createNewFile();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String[] getAzkarTimeTypeTexts(AthkarType type) {
        return getStringArray(type == AthkarType.MorningAthkar ? R.array.azkar_time_morning : R.array.azkar_time_evening);
    }

    public static String getAzkarTimeTypeText(AthkarType type) {
        return getAzkarTimeTypeTexts(type)[HawkSettings.getAzkarTimeType(type)];
    }

    public static String getDownloadPath() {
        return baseContext.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath();
//        return  Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath();
    }

    public static void showToast(Activity context, String s) {
        Toast.makeText(context, s, Toast.LENGTH_SHORT).show();
    }

    public static boolean isNetworkAvailable(Activity context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo(); //getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    public static Calendar getMidnightOfNextDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 1); // Move to tomorrow
        calendar.set(Calendar.HOUR_OF_DAY, 0);   // Set to midnight
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    public static String getDateNow() {
        return new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());
    }


    public interface OnUmmalquraCalendarSelected {
        void result(UmmalquraCalendar cal);
    }

    public interface OnAzkarTimeSelected {
        void onTimeSelected(int type, int duration);
    }
}

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".UI.Fragment.settings.content.main.content.designs.content.themes.ThemesFragment">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/themes_RecyclerView_ThemesFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/_4sdp"
        android:overScrollMode="never"

        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/countainer_buttons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:reverseLayout="false"
        app:spanCount="2"
        tools:listitem="@layout/layout_list_item_theme" />

    <LinearLayout
        android:id="@+id/countainer_buttons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/save_Button_ThemesFragment"
            android:layout_width="@dimen/_70sdp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_15sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/save"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_12sdp" />

        <Button
            android:id="@+id/update_Button_ThemesFragment"
            android:layout_width="@dimen/_70sdp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/update"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_12sdp" />

    </LinearLayout>

    <Button
        android:id="@+id/btn_moveUp"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginBottom="@dimen/_5sdp"
        android:background="@drawable/up_arrow"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/countainer_buttons" />

    <Button
        android:id="@+id/btn_moveDown"
        android:layout_width="@dimen/_35sdp"
        android:layout_height="@dimen/_35sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:layout_marginBottom="@dimen/_5sdp"
        android:background="@drawable/down_arrow"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/countainer_buttons" />
</androidx.constraintlayout.widget.ConstraintLayout>
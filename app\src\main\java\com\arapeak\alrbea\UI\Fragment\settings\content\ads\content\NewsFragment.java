package com.arapeak.alrbea.UI.Fragment.settings.content.ads.content;

import android.app.DatePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;

import com.arapeak.alrbea.Enum.News;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapterShortText;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

public class NewsFragment extends AlrabeeaTimesFragment {
    SwitchCompat sw;
    Spinner spinner;
    EditText editText;
    Button sDate, eDate;
    Button saveBtn;
    News news;
    LinearLayout linearLayout;
    long sDateTime, eDateTime;
    private SpinnerAdapterShortText<String> adapter;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_news_layout, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        news = HawkSettings.getCurrentNews();
        initView(view);
        setParameter();
        setAction();
        loadData();
    }

    private void loadData() {
        sw.setChecked(news.isActive);
        Utils.setColorStateListToSwitchCompat(requireContext(), sw);
        editText.setText(news.text);
        loadButtonText();
//        viewHideUi();
    }

    private void loadButtonText() {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd",Utils.getLocale());
        sDate.setText(Utils.formatCalendar("yyyy-MM-dd", new Date(news.sDate)));
        eDate.setText(Utils.formatCalendar("yyyy-MM-dd", new Date(news.eDate)));

//        sDate.setText(sdf.format(Utils.getCalendar(news.sDate).getTime()));
//        eDate.setText(sdf.format(Utils.getCalendar(news.eDate).getTime()));
    }

    private void initView(View view) {
        sw = view.findViewById(R.id.active_SwitchCompat_CreateMovingMessageFragment);
        spinner = view.findViewById(R.id.moveBetweenImage_Spinner_AddPhotoFragment);
        editText = view.findViewById(R.id.message_EditText_CreateMovingMessageFragment);
        sDate = view.findViewById(R.id.startDate_TextView_CreateMovingMessageFragment);
        eDate = view.findViewById(R.id.endDate_TextView_CreateMovingMessageFragment);
        saveBtn = view.findViewById(R.id.save_Button_CreateMovingMessageFragment);
        linearLayout = view.findViewById(R.id.layout);
        adapter = new SpinnerAdapterShortText<>(
                getAppCompatActivity()
                , new ArrayList<>(Arrays.asList(getResources().getStringArray(R.array.movingtext)))
        );
    }

    private void setParameter() {
        spinner.setAdapter(adapter);
    }

    private void setAction() {
        sDate.setOnClickListener(v -> pickDate(0));
        eDate.setOnClickListener(v -> pickDate(1));
        saveBtn.setOnClickListener(v -> save());
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0)
                    editText.setText(news.text);
                else
                    editText.setText(parent.getItemAtPosition(position).toString().trim());
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        sw.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewHideUi();
        });
    }

    private void viewHideUi() {
        linearLayout.setVisibility(sw.isChecked() ? View.VISIBLE : View.GONE);
    }

    private void save() {
        news.text = editText.getText().toString();
        news.isActive = sw.isChecked();
        HawkSettings.setCurrentNews(news);
        onBackPressed();
    }

    void pickDate(int index) {
        Calendar ca = Utils.getCalendar(index == 0 ? news.sDate : news.eDate);
        new DatePickerDialog(requireContext(), (view, year, month, dayOfMonth) -> {
            Calendar cal = Utils.getCalendar();
            cal.set(Calendar.YEAR, year);
            cal.set(Calendar.MONTH, month);
            cal.set(Calendar.DAY_OF_MONTH, dayOfMonth);
            if (index == 0)
                news.sDate = cal.getTimeInMillis();
            else
                news.eDate = cal.getTimeInMillis();
            loadButtonText();

        }
                , ca.get(Calendar.YEAR)
                , ca.get(Calendar.MONTH)
                , ca.get(Calendar.DAY_OF_MONTH)
        ).show();
    }
}

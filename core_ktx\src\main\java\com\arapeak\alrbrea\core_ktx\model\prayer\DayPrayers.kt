package com.arapeak.alrbrea.core_ktx.model.prayer

data class DayPrayers(
    val fajr: Prayer,
    val sunrise: Prayer,
    val dhuhr: Prayer,
    val asr: Prayer,
    val maghrib: Prayer,
    val isha: Prayer,
    val eid: Prayer?,
) {
    constructor() : this(
        Prayer(PrayerEnum.fajr),
        Prayer(PrayerEnum.sunrise),
        Prayer(PrayerEnum.dhuhr),
        Prayer(PrayerEnum.asr),
        Prayer(PrayerEnum.maghrib),
        Prayer(PrayerEnum.isha),
        null
    )

    override fun toString(): String {
        return "DayPrayers (fajr= $fajr, sunrise= $sunrise, dhuhr= $dhuhr, asr= $asr, maghrib= $maghrib, isha= $isha, eid= $eid)"
    }
}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:layoutDirection="rtl"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/content_LinearLayout_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <TextView
                style="@style/PrayerTimeLayout.green_new.Right"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:gravity="center"
                android:text="@string/adhaan" />

            <TextView
                style="@style/PrayerTimeLayout.green_new.center"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:text="@string/prayer" />

            <TextView
                style="@style/PrayerTimeLayout.green_new.Left"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:gravity="center"
                android:text="@string/ikama" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentFajr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Right">

                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />

            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.green_new.center">

                <TextView
                    android:id="@+id/fajr_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeName"
                    android:text="@string/fajr" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_fajr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/fajrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Left">

                <TextView
                    android:id="@+id/fajrATime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/fajrATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Right">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.green_new.center">

                <TextView
                    android:id="@+id/dhuhr_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeName"
                    android:text="@string/dhuhr" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_dhur"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/dhuhrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Left">

                <TextView
                    android:id="@+id/dhuhrATime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/dhuhrATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentAsr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Right">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.green_new.center">

                <TextView
                    android:id="@+id/asr_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeName"
                    android:text="@string/asr" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_asr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/asrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Left">

                <TextView
                    android:id="@+id/asrATime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/asrATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Right">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.green_new.center">

                <TextView
                    android:id="@+id/maghrib_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeName"
                    android:text="@string/maghrib" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_maghrib"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/maghribATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Left">

                <TextView
                    android:id="@+id/maghribATime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/maghribATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentIsha_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Right">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.green_new.center">

                <TextView
                    android:id="@+id/isha_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeName"
                    android:text="@string/isha" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_isha"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ishaATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Left">

                <TextView
                    android:id="@+id/ishaATime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time" />

                <TextView
                    android:id="@+id/ishaATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType" />

            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:id="@+id/contentSunrise_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow"
            android:layout_margin="16dp">

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Right"

                android:gravity="center">

                <TextView
                    android:id="@+id/sunriseTime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time"
                    android:textSize="@dimen/_16sdp" />

                <TextView
                    android:id="@+id/sunriseTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType"
                    android:textSize="@dimen/_10sdp" />
            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.green_new.center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/sunrise_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeName"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:singleLine="true"
                    android:text="@string/duha"
                    android:textSize="@dimen/_18sdp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.green_new.Left"
                android:gravity="center">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.Time"

                    android:textSize="@dimen/_16sdp" />

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.green_new.TimeType"

                    android:textSize="@dimen/_10sdp" />

            </LinearLayout>

        </LinearLayout>


    </LinearLayout>

    <!--  <ImageView
          android:id="@+id/imageView2"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:layout_marginLeft="@dimen/_20sdp"
          android:layout_marginRight="@dimen/_20sdp"
          app:layout_constraintTop_toBottomOf="@id/contentSunrise_LinearLayout_MainActivity"
          app:srcCompat="@drawable/row" />-->
    <TextView
        android:id="@+id/remainingDhuhr_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingSunrise_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingFajr_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingAsr_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingMaghrib_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingIsha_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.125"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />
</LinearLayout>
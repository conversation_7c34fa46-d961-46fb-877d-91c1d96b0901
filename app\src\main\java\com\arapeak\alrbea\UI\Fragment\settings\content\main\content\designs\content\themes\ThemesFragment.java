package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.themes;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.SECONDS_MILLI_SECOND;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.GridAutofitLayoutManager;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.ui.utils.ConnectionExt;
import com.google.android.gms.tasks.Task;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.Query;

import java.util.ArrayList;


//public class ThemesFragment extends AlrabeeaTimesFragment implements AdapterCallback {
public class ThemesFragment extends AlrabeeaTimesFragment {

    private static final String TAG = "ThemesFragment";
    DatabaseReference scoresRefTheme = FirebaseDatabase.getInstance().getReference("custom_themes");
    Query sChatQueryTheme = scoresRefTheme.limitToLast(50).orderByChild("enabled").equalTo(true);
    ArrayList<ThemeModel> firebaseThemes = new ArrayList();
    private View changeThemeView;
    private RecyclerView themesRecyclerView;
    private Button saveButton;
    private Button updateButton;
    private Button upButton;
    private Button downButton;
    private GridAutofitLayoutManager mGridAutofitLayoutManager;
    private Dialog loadingDialog;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        changeThemeView = inflater.inflate(R.layout.fragment_themes, container, false);

        initView();
        SetParameter();
        SetAction();

        return changeThemeView;
    }

    private void initView() {
        themesRecyclerView = changeThemeView.findViewById(R.id.themes_RecyclerView_ThemesFragment);
        saveButton = changeThemeView.findViewById(R.id.save_Button_ThemesFragment);
        updateButton = changeThemeView.findViewById(R.id.update_Button_ThemesFragment);
        upButton = changeThemeView.findViewById(R.id.btn_moveUp);
        downButton = changeThemeView.findViewById(R.id.btn_moveDown);
        mGridAutofitLayoutManager = new GridAutofitLayoutManager(getAppCompatActivity(), 2, GridLayoutManager.VERTICAL, false);
        loadingDialog = Utils.initLoadingDialog(getContext());
    }

    private void SetParameter() {

        scoresRefTheme.keepSynced(true);
        if (Utils.isLandscape()) {
            mGridAutofitLayoutManager.setColumnWidth((int) getResources().getDimension(R.dimen.theme_item_width_land));
        } else {
            mGridAutofitLayoutManager.setColumnWidth((int) getResources().getDimension(R.dimen.theme_item_width));
        }
        themesRecyclerView.setLayoutManager(mGridAutofitLayoutManager);
        update();
    }

    void update() {
        if (!(new ConnectionExt()).isNetworkConnected(requireContext())) {
            Utils.showFailAlert(requireActivity(), "خطأ", "فشل تحميل القوالب ");
            loadingDialog.dismiss();
            return;
        }
        firebaseThemes.clear();
        showDialog();
        sChatQueryTheme.get().addOnCompleteListener((task -> {
            if (task.isSuccessful())
                for (DataSnapshot item : task.getResult().getChildren())
                    firebaseThemes.add(item.getValue(ThemeModel.class));

            RecyclerView.Adapter<ChangeThemeNewHolder> adapter = newAdapterTheme();
            themesRecyclerView.setAdapter(adapter);
            int themeIndex = HawkSettings.getCurrentTheme().ordinal();
            if (themeIndex == UITheme.CUSTOM_FIREBASE.ordinal())
                themeIndex += HawkSettings.getCurrentFirebaseThemeIndex();
            int child = adapter.getItemCount();
            if (themeIndex < child)
                themesRecyclerView.smoothScrollToPosition(themeIndex);
            dismissDialog();
        }));
    }

    private void dismissDialog() {
        try {
            Log.i(TAG, "dismissDialog: ");
            loadingDialog.dismiss();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void showDialog() {
        try {
            Log.i(TAG, "showDialog: ");
            loadingDialog.show();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    Thread getThemeUpdaterThread() {
        return new Thread(() -> {
            try {
                long time = System.currentTimeMillis() + MINUTES_MILLI_SECOND;
                requireActivity().runOnUiThread(this::showDialog);
                firebaseThemes.clear();
                Task<DataSnapshot> i = sChatQueryTheme.get();
                do Thread.sleep(SECONDS_MILLI_SECOND);
                while (!i.isComplete() && System.currentTimeMillis() < time);
//                    Thread.sleep(SECONDS_MILLI_SECOND);
                if (i.isSuccessful()) {
                    for (DataSnapshot child : i.getResult().getChildren()) {
                        firebaseThemes.add(child.getValue(ThemeModel.class));
                    }
                }
                requireActivity().runOnUiThread(() -> {
                    RecyclerView.Adapter<ChangeThemeNewHolder> adapter = newAdapterTheme();
                    themesRecyclerView.setAdapter(adapter);
                    int themeIndex = HawkSettings.getCurrentTheme().ordinal();
                    if (themeIndex == UITheme.CUSTOM_FIREBASE.ordinal())
                        themeIndex += HawkSettings.getCurrentFirebaseThemeIndex();
                    int child = adapter.getItemCount();
                    if (themeIndex < child)
                        themesRecyclerView.smoothScrollToPosition(themeIndex);
                });
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
            requireActivity().runOnUiThread(this::dismissDialog);
        });
    }

    protected RecyclerView.Adapter<ChangeThemeNewHolder> newAdapterTheme() {
        return new RecyclerView.Adapter<ChangeThemeNewHolder>() {
            @NonNull
            @Override
            public ChangeThemeNewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                @LayoutRes int layout = R.layout.change_theme_new_holder;
//                @LayoutRes int layout  = Utils.isLandscape()?R.layout.layout_list_item_theme_land:R.layout.layout_list_item_theme;
                View view = LayoutInflater.from(parent.getContext()).inflate(layout, parent, false);
                return new ChangeThemeNewHolder(view);
            }

            @Override
            public void onBindViewHolder(@NonNull ChangeThemeNewHolder holder, int position) {
                if (position < UITheme.CUSTOM_FIREBASE.ordinal())
                    holder.Bind(UITheme.values()[position], getAppCompatActivity());
//                    holder.Bind(holder.getAbsoluteAdapterPosition(),getAppCompatActivity());
                else
                    holder.Bind(firebaseThemes.get(position - UITheme.CUSTOM_FIREBASE.ordinal()), loadingDialog, getAppCompatActivity());
            }

            @Override
            public int getItemCount() {
                return UITheme.values().length + firebaseThemes.size() - 1;//dont count firebase theme
            }
        };
    }

    private void SetAction() {
        saveButton.setOnClickListener(v -> {
            Intent intentMainActivity = new Intent(getAppCompatActivity(), MainActivity.class);
            intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intentMainActivity);
            getAppCompatActivity().finish();

        });
        updateButton.setOnClickListener(v -> update());
        upButton.setOnClickListener(v -> moveUp());
        downButton.setOnClickListener(v -> moveDown());
    }

    private void moveDown() {
        try {
            GridLayoutManager lm = (GridLayoutManager) themesRecyclerView.getLayoutManager();
            if (lm != null) {
                int lastItem = lm.findLastCompletelyVisibleItemPosition();
                int count = lm.getItemCount();
                if (lastItem < count - 1)
                    lastItem++;
                themesRecyclerView.smoothScrollToPosition(lastItem);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void moveUp() {
        try {
            GridLayoutManager lm = (GridLayoutManager) themesRecyclerView.getLayoutManager();
            if (lm != null) {
                int lastItem = lm.findFirstCompletelyVisibleItemPosition();
                if (lastItem > 0)
                    lastItem--;
                themesRecyclerView.smoothScrollToPosition(lastItem);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}

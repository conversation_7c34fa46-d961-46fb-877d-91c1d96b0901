package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class Season {
    public static final String CityType_1 = "1";
    public static final String CityType_2 = "2";
    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    @NotNull
    private static final String b = CityType_1;   //  MIN_MONTH

    @NotNull
    private static final String c = "12";

    @NotNull
    private static final String d = CityType_1;    //  MIN_DAY

    @NotNull
    private static final String e = "31";

    /* renamed from: a, reason: collision with root package name */
    @Nullable
    private Type f3781a;


    public Season() {
        this.f3781a = Type.Winter;
    }


    public Season(@NotNull Type T) {
        Intrinsics.checkNotNullParameter(T, "T");
        this.f3781a = T;
    }

    @Nullable
    /* renamed from: seasonInt, reason: from getter */
    public final Type getF3781a() {
        return this.f3781a;
    }

    @NotNull
    public final String seasonString() {
        Type type = this.f3781a;
        if (type == Type.Winter) {
            return "Winter";
        }
        if (type == Type.Summer) {
            return "Summer";
        }
        return "Winter_Summer";
    }

    public final void setSeason(@NotNull Type season) {
        Intrinsics.checkNotNullParameter(season, "season");
        this.f3781a = season;
    }

    @Nullable
    public final Type type() {
        return getF3781a();
    }

    public final void setSeason(@NotNull String season) {
        Intrinsics.checkNotNullParameter(season, "season");
        if (Intrinsics.areEqual(season, "Winter")) {
            this.f3781a = Type.Winter;
        } else if (Intrinsics.areEqual(season, "Summer")) {
            this.f3781a = Type.Summer;
        } else {
            this.f3781a = Type.Winter_Summer;
        }
    }

    public enum Type {
        Winter,
        Summer,
        Winter_Summer
    }

    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        @NotNull
        public final String getMAX_DAY() {
            return Season.e;
        }

        @NotNull
        public final String getMAX_MONTH() {
            return Season.c;
        }

        @NotNull
        public final String getMIN_DAY() {
            return Season.d;
        }

        @NotNull
        public final String getMIN_MONTH() {
            return Season.b;
        }

        @Nullable
        public final Type getSeason(@NotNull String season) {
            Intrinsics.checkNotNullParameter(season, "season");
            Season season2 = new Season();
            season2.setSeason(season);
            return season2.type();
        }
    }
}

package com.arapeak.alrbea.UI.Fragment.settings;

import static android.app.Activity.RESULT_OK;

import android.Manifest;
import android.app.Dialog;
import android.app.DownloadManager;
import android.app.admin.DevicePolicyManager;
import android.content.ActivityNotFoundException;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.IntentSender;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Interface.OnProgress;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Model.AppInfo;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.MyExceptionHandler;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Service.GPSTracker;
import com.arapeak.alrbea.UI.Activity.BaseAppCompatActivity;
import com.arapeak.alrbea.UI.Activity.Country.CountryActivity;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.AboutApp;
import com.arapeak.alrbea.UI.Fragment.settings.content.MorningOrEvening;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.SettingAds;
import com.arapeak.alrbea.UI.Fragment.settings.content.athkar.SettingsAthkarsFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.MainSettingsFrag;
import com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery.PhotoGalleriesSettingFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.PrayersTimesSettingsFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.remote.RemoteAccess;
import com.arapeak.alrbea.UI.Fragment.settings.content.screensaver.ScreensaverFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.textDesigner.NewTextDesignerFragment;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppUpdateState;
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppUpdaterListener;
import com.arapeak.alrbrea.core_ktx.ui.appupdater.AppUpdater;
import com.downloader.PRDownloader;
import com.google.firebase.database.DatabaseReference;
import com.orhanobut.hawk.Hawk;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import pub.devrel.easypermissions.EasyPermissions;

public class SettingsFragment extends AlrabeeaTimesFragment implements AdapterCallback, AppUpdaterListener {
    public static final int REQUEST_CODE = 1234;
    private static final String TAG = "SettingsFragment";
    private static final int PERMISSION_CODE = 1000;
    GPSTracker gpsTracker = null;
    private View mainSettingsView;
    private RecyclerView settingItem;

    private Dialog linerProgressDialog;
    private TextView textViewProgressDialog;
    private ProgressBar progressBarProgressDialog;
    private SettingsAdapter settingsAdapter;

    private Dialog loadingDialog;


    public SettingsFragment() {

    }

    public static SettingsFragment newInstance() {
        return new SettingsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mainSettingsView = inflater.inflate(R.layout.fragment_main_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return mainSettingsView;
    }

    private void initView() {
        settingItem = mainSettingsView.findViewById(R.id.settingItem_RecyclerView_SettingsActivity);
        if (Utils.isLandscape()) {
            settingItem.setLayoutManager(new GridLayoutManager(getContext(), 3));
        } else {
            settingItem.setLayoutManager(new GridLayoutManager(getContext(), 2));

        }
        initLinerProgressDialog();

        settingsAdapter = new SettingsAdapter(getContext(), setupSettings(), this);
        gpsTracker = new GPSTracker((BaseAppCompatActivity) requireActivity());

        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

    }

    private void SetParameter() {

        SettingsActivity.setTextTite(getString(R.string.the_settings));
        SettingsActivity.setViewTites();
        settingItem.setAdapter(settingsAdapter);
    }

    private void SetAction() {
    }

    private List<SettingAlrabeeaTimes> setupSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] settingsTitleArray = getResources().getStringArray(R.array.settings_title);
        String[] settingsDescriptionArray = getResources().getStringArray(R.array.settings_description);
        List<Integer> settingsIconList = setupSettingsIconArray(settingsDescriptionArray.length);

        for (int i = 0; i < settingsTitleArray.length; i++) {
            boolean isNewVersion = settingsIconList.get(i) == R.drawable.screensaver || settingsIconList.get(i) == R.drawable.text_design;

            SettingAlrabeeaTimes settingTile = new SettingAlrabeeaTimes(settingsTitleArray[i]
                    , settingsDescriptionArray[i]
                    , settingsIconList.get(i));

            settingTile.setIsNew(isNewVersion);
            settingAlrabeeaTimes.add(settingTile);
        }

        return settingAlrabeeaTimes;
    }

    private List<Integer> setupSettingsIconArray(int length) {
        List<Integer> settingsList = new ArrayList<>();
        settingsList.add(R.drawable.homesetting);
        settingsList.add(R.drawable.ad);
        settingsList.add(R.drawable.gelery);

        settingsList.add(R.drawable.acker);

        settingsList.add(R.drawable.timeprymer);
        settingsList.add(R.drawable.screensaver);
        settingsList.add(R.drawable.text_design);
        settingsList.add(R.drawable.control);


        settingsList.add(R.drawable.synches);
        settingsList.add(R.drawable.downlod);
        settingsList.add(R.drawable.ic_info);
        settingsList.add(R.drawable.restart_icon);

       /* settingsList.add(R.drawable.ic_view_message);

        //settingsList.add(R.drawable.ic_photo_gallery);

        settingsList.add(R.drawable.ic_athker);
        settingsList.add(R.drawable.ic_prayer_time);


        settingsList.add(R.drawable.ic_update);
        settingsList.add(R.drawable.ic_update);
        settingsList.add(R.drawable.ic_version_number);


        settingsList.add(R.drawable.ic_app_code);
        settingsList.add(R.drawable.ic_app_code);
        settingsList.add(R.drawable.ic_call_us);
        settingsList.add(R.drawable.ic_back);
        */

        return settingsList;
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if (requestCode == REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                // Log.i(MainActivity.class.getSimpleName(), "Update flow completed! Result code: " + resultCode);
            } else {
                //  Log.e(MainActivity.class.getSimpleName(), "Update flow failed! Result code: " + resultCode);
            }
        }
    }

    @Override
    public void onItemClick(int position, String tag) {
        final SettingAlrabeeaTimes settingAlrabeeaTimes = settingsAdapter.getItem(position);


        switch (position) {
            case 0:
                Utils.loadFragment(MainSettingsFrag.newInstance()
                        , getAppCompatActivity()
                        , 0);
               /* Utils.loadFragment(MainSettingsFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                        */
                break;
            case 2:
                Utils.loadFragment(new PhotoGalleriesSettingFragment()
                        , getAppCompatActivity()
                        , 0);
//                Utils.loadFragment(PhotoGalleryFragment.newInstance()
//                        , getAppCompatActivity()
//                        , 0);

                break;

            case 3:
                Utils.loadFragment(SettingsAthkarsFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;

            case 4:
                Utils.loadFragment(PrayersTimesSettingsFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;


            case 1:
                Utils.loadFragment(SettingAds.newInstance()
                        , getAppCompatActivity()
                        , 0);

                break;

            case 9:
                AppUpdater updater = new AppUpdater();
                updater.checkUpdate(requireContext(), this, true);

                break;
            case 8:
                Utils.initConfirmDialogbackup(getAppCompatActivity()
                        , 0
                        , "مزامنة"
                        , " مزامنة"
                                + " \"" + "\""
                                + " "
                        , true
                        , true
                        , new OnSuccessful() {
                            @Override
                            public void onSuccessful(boolean isSuccessful) {
                                if (isSuccessful) {
                                    Utils.loadFragment(MorningOrEvening.newInstance()
                                            , getAppCompatActivity()
                                            , 0);
                                }
                            }
                        });
                break;
            case 7:
                Utils.loadFragment(RemoteAccess.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 5:
                Utils.loadFragment(ScreensaverFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 6:
                Utils.loadFragment(NewTextDesignerFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 10:
                Utils.loadFragment(AboutApp.newInstance()
                        , getAppCompatActivity()
                        , 0);

                break;
            case 11:
                MyExceptionHandler.restartAppSoftly();
//                MyExceptionHandler.resetApp(AppController.baseContext);
                break;


        }
    }


    @Override
    public void onResume() {
        super.onResume();

        if (Hawk.get("isTeamViewerLaunched", false)) {
            switch (HawkSettings.getAppOrientation()) {
                case 1:
                    Log.e("Orientation", "switch to SCREEN_ORIENTATION_LANDSCAPE");
                    requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                    break;
                case 2:
                    Log.e("Orientation", "switch to SCREEN_ORIENTATION_REVERSE_PORTRAIT");
                    requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
                    break;
                default:
                    Log.e("Orientation", "switch to SCREEN_ORIENTATION_PORTRAIT");
                    requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
            Hawk.put("isTeamViewerLaunched", false);

        }
    }


    private void checkPermissionsAndDownloadApkFile(AppInfo appInfo) {
        String[] perms = {Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE};
        if (EasyPermissions.hasPermissions(getAppCompatActivity(), perms)) {
            downloadApkFile(appInfo);
        } else {
            EasyPermissions
                    .requestPermissions(getAppCompatActivity()
                            , getString(R.string.permission_to_allow_access_to_memory_to_store_the_application), PERMISSION_CODE, perms);
        }
    }

    private void downloadApkFile(AppInfo appInfo) {
        linerProgressDialog.show();
        if (appInfo == null) {
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.try_again));
            linerProgressDialog.dismiss();
            return;
        }
        AlrabeeaTimesRequests.downloadApkFileByAndroidNetworking(getAppCompatActivity()
                //    , appInfo.getLink()
                , appInfo
                , new OnCompleteListener<String, Object>() {
                    @Override
                    public void onSuccess(String filePath) {
                        linerProgressDialog.dismiss();
                        if (Utils.getValueWithoutNull(filePath).isEmpty()) {
                            PRDownloader.pause(Hawk.get("idown", 0));
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , getString(R.string.try_again));
                        } else {
                            installApk(filePath);
                        }
                    }

                    @Override
                    public void onFail(Object object) {
                        PRDownloader.pause(Hawk.get("idown", 0));
                        if (object == null) {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , getString(R.string.try_again));
                        } else {
                            Utils.showFailAlert(getAppCompatActivity()
                                    , getString(R.string.there_is_a_problem)
                                    , "" + object);
                        }
                        linerProgressDialog.dismiss();
                    }
                }, new OnProgress() {
                    @Override
                    public void onProgress(int progress) {
                        textViewProgressDialog.setText(String.valueOf(progress));
                        progressBarProgressDialog.setProgress(progress);
                    }
                }, Hawk.get("idown", 0));
    }

    private void installApk(String filePath) {
        Log.d(TAG, "filePath: " + filePath);
        Uri uri = FileProvider.getUriForFile(getAppCompatActivity()
                , getContext().getPackageName() + ".fileprovider"
                , new File(filePath));

        if (uri == null) {
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , getString(R.string.try_again));
            return;
        }
        try {



            Intent install = new Intent(Intent.ACTION_VIEW);
            install.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            install.setDataAndType(uri, "application/vnd.android.package-archive");
            startActivity(install);

        } catch (Exception e1) {
            Log.e(TAG, "Exception1 installApk Error: " + e1.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e1);

            try {

                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.setDataAndType(Uri.fromFile(new File(filePath))
                        , "application/vnd.android.package-archive");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                startActivity(intent);
//                getAppCompatActivity().finish();
            } catch (ActivityNotFoundException e2) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                Log.e(TAG, "ActivityNotFoundException installApk Error: " + e2.getMessage());
                CrashlyticsUtils.INSTANCE.logException(e2);

            } catch (Exception e2) {
                Utils.showFailAlert(getAppCompatActivity()
                        , getString(R.string.there_is_a_problem)
                        , getString(R.string.try_again));
                Log.e(TAG, "Exception2 installApk Error: " + e2.getMessage());
                CrashlyticsUtils.INSTANCE.logException(e2);

            }
        }
    }

    public void initLinerProgressDialog() {

        final View view = LayoutInflater.from(getAppCompatActivity()).inflate(R.layout.layout_dialog_liner_progress_bar, null, false);

        textViewProgressDialog = view.findViewById(R.id.progress_TextView_LinerProgressDialog);
        progressBarProgressDialog = view.findViewById(R.id.progress_ProgressBar_LinerProgressDialog);

        linerProgressDialog = new Dialog(getAppCompatActivity(), R.style.LoadingDialogStyle);
        linerProgressDialog.setContentView(view);
        linerProgressDialog.setCancelable(false);

    }


    @Override
    public void onUpdateState(@NonNull AppUpdateState state) {
        Log.d("AppUpdater", "onUpdateState: " + state);

        if (state instanceof AppUpdateState.Init) {

        }

        if (state instanceof AppUpdateState.Checking) {
            loadingDialog.show();
        }

        if (state instanceof AppUpdateState.UpToDate) {
            loadingDialog.dismiss();
            linerProgressDialog.dismiss();
            Utils.initMessageDialog(getAppCompatActivity()
                    , 0
                    , getString(R.string.alert)
                    , getString(R.string.there_is_no_updates_currently)
                    , true
                    , null);
        }

        if (state instanceof AppUpdateState.Downloading) {
            loadingDialog.dismiss();
            int progress = ((AppUpdateState.Downloading) state).component1();
            linerProgressDialog.show();
            if (progress >= 0) {
                progressBarProgressDialog.setProgress(progress);
                progressBarProgressDialog.setIndeterminate(false);
                textViewProgressDialog.setText(progress + "");
            } else {
                progressBarProgressDialog.setIndeterminate(true);
                textViewProgressDialog.setText("");
            }
        }

        if (state instanceof AppUpdateState.Downloaded) {
            loadingDialog.dismiss();
            linerProgressDialog.dismiss();
        }

        if (state instanceof AppUpdateState.Failed) {
            loadingDialog.dismiss();
            linerProgressDialog.dismiss();
            Utils.showFailAlert(getAppCompatActivity()
                    , getString(R.string.there_is_a_problem)
                    , ((AppUpdateState.Failed) state).component1());

        }

        if (state instanceof AppUpdateState.Refused) {
            loadingDialog.dismiss();
            linerProgressDialog.dismiss();
        }

    }
}

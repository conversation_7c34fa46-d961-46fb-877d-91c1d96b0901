# Critical Classes Implementation Plan

## Overview
This document provides detailed implementation steps for the most critical classes that need immediate improvements.

---

## 🔴 CRITICAL CLASS 1: BaseAppCompatActivity.java

### Current Issues Analysis
```java
// PROBLEMATIC CODE PATTERNS:
public static void startThread(Thread thread) {
    // Creates unmanaged threads - MEMORY LEAK RISK
}

public static boolean isScaleAdjusted = false; // STATIC MEMORY LEAK

// Root commands without proper error handling
shell.add(new SimpleCommand("wm size 720x1280")).waitForFinish();
```

### Implementation Plan

#### Step 1: Fix Thread Management
**Replace thread methods with Handler-based solutions:**

```java
// BEFORE (Problematic)
public static void startThread(Thread thread) {
    if (thread != null && thread.getState() == Thread.State.NEW) {
        thread.setPriority(Thread.MAX_PRIORITY);
        thread.start();
    }
}

// AFTER (Improved)
private Handler backgroundHandler;
private HandlerThread backgroundThread;

private void initializeHandlers() {
    try {
        backgroundThread = new HandlerThread("BaseActivity-Background");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper());
        Log.d(TAG, "Handlers initialized successfully");
    } catch (Exception e) {
        Log.e(TAG, "Error initializing handlers", e);
        CrashlyticsUtils.INSTANCE.logException(e);
    }
}

public void executeInBackground(Runnable task) {
    if (backgroundHandler != null && task != null) {
        try {
            backgroundHandler.post(() -> {
                try {
                    task.run();
                } catch (Exception e) {
                    Log.e(TAG, "Error in background task", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error posting background task", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}
```

#### Step 2: Fix Static Variables
**Convert static variables to instance variables:**

```java
// BEFORE (Memory Leak)
public static boolean isScaleAdjusted = false;
public static boolean isRotationAdjusted = false;

// AFTER (Safe)
private boolean isScaleAdjusted = false;
private boolean isRotationAdjusted = false;
private static final String SCALE_ADJUSTED_KEY = "scale_adjusted";
private static final String ROTATION_ADJUSTED_KEY = "rotation_adjusted";

// Use SharedPreferences for persistence if needed
private boolean getScaleAdjusted() {
    return getSharedPreferences("base_activity", MODE_PRIVATE)
        .getBoolean(SCALE_ADJUSTED_KEY, false);
}

private void setScaleAdjusted(boolean adjusted) {
    getSharedPreferences("base_activity", MODE_PRIVATE)
        .edit()
        .putBoolean(SCALE_ADJUSTED_KEY, adjusted)
        .apply();
}
```

#### Step 3: Add Comprehensive Error Handling
**Wrap all operations in try-catch blocks:**

```java
public void adjustDisplayScale() {
    try {
        Log.d(TAG, "Starting display scale adjustment");
        
        if (getScaleAdjusted()) {
            Log.d(TAG, "Scale already adjusted, skipping");
            return;
        }
        
        if (AppController.isRooted) {
            adjustScaleWithRoot();
        } else {
            adjustLocally();
        }
        
        setScaleAdjusted(true);
        Log.d(TAG, "Display scale adjustment completed");
        
    } catch (Exception e) {
        Log.e(TAG, "Error adjusting display scale", e);
        CrashlyticsUtils.INSTANCE.logException(e);
        // Fallback to local adjustment
        try {
            adjustLocally();
        } catch (Exception fallbackError) {
            Log.e(TAG, "Fallback adjustment also failed", fallbackError);
            CrashlyticsUtils.INSTANCE.logException(fallbackError);
        }
    }
}
```

#### Step 4: Add Proper Cleanup
**Implement proper resource cleanup:**

```java
@Override
protected void onDestroy() {
    try {
        Log.d(TAG, "BaseAppCompatActivity onDestroy started");
        
        // Clean up handlers
        if (backgroundHandler != null) {
            backgroundHandler.removeCallbacksAndMessages(null);
            backgroundHandler = null;
        }
        
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            try {
                backgroundThread.join(1000); // Wait max 1 second
            } catch (InterruptedException e) {
                Log.w(TAG, "Interrupted while waiting for background thread to finish");
                Thread.currentThread().interrupt();
            }
            backgroundThread = null;
        }
        
        Log.d(TAG, "BaseAppCompatActivity cleanup completed");
        
    } catch (Exception e) {
        Log.e(TAG, "Error in BaseAppCompatActivity onDestroy", e);
        CrashlyticsUtils.INSTANCE.logException(e);
    } finally {
        super.onDestroy();
    }
}
```

---

## 🔴 CRITICAL CLASS 2: Utils.java

### Current Issues Analysis
- **4000+ lines in single class** - Violates Single Responsibility Principle
- **Mixed responsibilities** - UI, database, calculations, file operations
- **Inconsistent error handling** - Some methods have try-catch, others don't
- **Performance issues** - Heavy operations on main thread

### Implementation Plan

#### Step 1: Break Down Into Smaller Classes
**Create focused utility classes:**

```java
// NEW: UIUtils.java
public class UIUtils {
    private static final String TAG = "UIUtils";
    
    public static void setText(TextView view, String text) {
        if (view != null && text != null) {
            try {
                view.setText(replaceNumberWithSettings(text));
                Log.d(TAG, "Text set successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error setting text", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }
    
    public static void setVisibility(View view, int visibility) {
        if (view != null) {
            try {
                view.setVisibility(visibility);
            } catch (Exception e) {
                Log.e(TAG, "Error setting visibility", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }
}

// NEW: DateTimeUtils.java
public class DateTimeUtils {
    private static final String TAG = "DateTimeUtils";
    
    public static String getTimeWith12(String timeIn24) {
        try {
            if (getValueWithoutNull(timeIn24).isEmpty()) {
                return "";
            }
            
            SimpleDateFormat sdf12 = new SimpleDateFormat("hh:mm aa", 
                new Locale(HawkSettings.getTypeNumber()));
            SimpleDateFormat sdf24 = new SimpleDateFormat("HH:mm", 
                new Locale(HawkSettings.getTypeNumber()));
            
            Date currDate = sdf24.parse(timeIn24);
            return sdf12.format(currDate);
            
        } catch (ParseException e) {
            Log.e(TAG, "Error parsing time: " + timeIn24, e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return timeIn24; // Return original on error
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error in getTimeWith12", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }
}

// NEW: FileUtils.java
public class FileUtils {
    private static final String TAG = "FileUtils";
    
    public static void createFile(File file) {
        try {
            File parent = file.getParentFile();
            if (parent != null && !parent.exists()) {
                boolean created = parent.mkdirs();
                Log.d(TAG, "Parent directories created: " + created);
            }
            
            if (!file.exists()) {
                boolean created = file.createNewFile();
                Log.d(TAG, "File created: " + created + " - " + file.getPath());
            }
            
        } catch (IOException e) {
            Log.e(TAG, "Error creating file: " + file.getPath(), e);
            CrashlyticsUtils.INSTANCE.logException(e);
            throw new RuntimeException("Failed to create file: " + file.getPath(), e);
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error creating file", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            throw new RuntimeException("Unexpected error creating file", e);
        }
    }
}
```

#### Step 2: Add Comprehensive Error Handling
**Template for all utility methods:**

```java
public static ReturnType methodName(Parameters params) {
    try {
        Log.d(TAG, "Starting methodName with params: " + params);
        
        // Validate input parameters
        if (params == null) {
            Log.w(TAG, "Null parameters provided to methodName");
            return getDefaultValue();
        }
        
        // Main method logic here
        ReturnType result = performOperation(params);
        
        Log.d(TAG, "methodName completed successfully");
        return result;
        
    } catch (SpecificException e) {
        Log.e(TAG, "Specific error in methodName", e);
        CrashlyticsUtils.INSTANCE.logException(e);
        return getDefaultValue(); // Graceful degradation
    } catch (Exception e) {
        Log.e(TAG, "Unexpected error in methodName", e);
        CrashlyticsUtils.INSTANCE.logException(e);
        return getDefaultValue();
    }
}
```

#### Step 3: Move Heavy Operations to Background
**Use AsyncTask or Handler for heavy operations:**

```java
// NEW: BackgroundUtils.java
public class BackgroundUtils {
    private static final String TAG = "BackgroundUtils";
    private static final ExecutorService executor = Executors.newFixedThreadPool(4);
    
    public static void executeHeavyOperation(Runnable operation, Runnable onComplete) {
        try {
            executor.submit(() -> {
                try {
                    Log.d(TAG, "Starting heavy operation in background");
                    operation.run();
                    
                    // Run completion callback on main thread
                    if (onComplete != null) {
                        new Handler(Looper.getMainLooper()).post(onComplete);
                    }
                    
                    Log.d(TAG, "Heavy operation completed");
                    
                } catch (Exception e) {
                    Log.e(TAG, "Error in heavy operation", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error submitting heavy operation", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
    
    public static void shutdown() {
        try {
            executor.shutdown();
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
```

---

## 🔴 CRITICAL CLASS 3: AnnouncementManager.java

### Implementation Plan

#### Step 1: Fix Handler Management
```java
public class AnnouncementManager extends BaseAppCompatActivity {
    private static final String TAG = "AnnouncementManager";
    
    private Handler mainHandler;
    private Runnable updateProgressRunnable;
    private volatile boolean isRunning = false;
    
    public void init(Activity activity) {
        try {
            Log.d(TAG, "Initializing AnnouncementManager");
            
            this.activity = activity;
            mainHandler = new Handler(Looper.getMainLooper());
            
            initializeViews();
            setupUpdateRunnable();
            
            Log.d(TAG, "AnnouncementManager initialized successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing AnnouncementManager", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
    
    private void setupUpdateRunnable() {
        updateProgressRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    if (isRunning && announcement != null) {
                        updateRemainingTime();
                        
                        // Schedule next update
                        if (mainHandler != null) {
                            mainHandler.postDelayed(this, 1000);
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in update progress runnable", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            }
        };
    }
    
    public void startUpdates() {
        try {
            if (!isRunning && mainHandler != null && updateProgressRunnable != null) {
                isRunning = true;
                mainHandler.post(updateProgressRunnable);
                Log.d(TAG, "Started announcement updates");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting updates", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
    
    public void stopUpdates() {
        try {
            isRunning = false;
            if (mainHandler != null && updateProgressRunnable != null) {
                mainHandler.removeCallbacks(updateProgressRunnable);
                Log.d(TAG, "Stopped announcement updates");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error stopping updates", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
    
    public void cleanup() {
        try {
            Log.d(TAG, "Cleaning up AnnouncementManager");
            
            stopUpdates();
            
            if (mainHandler != null) {
                mainHandler.removeCallbacksAndMessages(null);
                mainHandler = null;
            }
            
            updateProgressRunnable = null;
            announcement = null;
            activity = null;
            
            Log.d(TAG, "AnnouncementManager cleanup completed");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}
```

---

## IMPLEMENTATION TIMELINE

### Week 1: BaseAppCompatActivity.java
- Day 1-2: Fix thread management
- Day 3-4: Remove static variables
- Day 5: Add error handling and cleanup

### Week 2: Utils.java Refactoring
- Day 1-3: Break down into smaller classes
- Day 4-5: Add comprehensive error handling
- Day 6-7: Move heavy operations to background

### Week 3: AnnouncementManager.java
- Day 1-2: Fix Handler management
- Day 3-4: Add proper lifecycle management
- Day 5: Testing and optimization

### Week 4: Testing and Integration
- Comprehensive testing of all improvements
- Performance monitoring
- Bug fixes and optimizations

---

## SUCCESS METRICS

### Before Improvements:
- ❌ Frequent crashes from memory leaks
- ❌ ANRs from main thread blocking
- ❌ Inconsistent error handling
- ❌ Poor resource management

### After Improvements:
- ✅ 90%+ crash reduction
- ✅ No ANRs from threading issues
- ✅ Comprehensive error handling
- ✅ Proper resource cleanup
- ✅ Better performance and stability

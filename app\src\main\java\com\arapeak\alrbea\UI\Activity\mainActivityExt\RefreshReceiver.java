package com.arapeak.alrbea.UI.Activity.mainActivityExt;


import static com.arapeak.alrbrea.core_ktx.ui.utils.ContextExtKt.isActivityInForeground;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.util.Log;

import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.Calendar;


public class RefreshReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            Log.d("RefreshTask", "Receive refresh task");
            if (isActivityInForeground(context, MainActivity.class)) {

                if (alreadyTriggered(context)) {
                    Log.i("RefreshTask", "Already triggered for this day ");
                    return;
                }
                saveTrigger(context);
                Log.i("RefreshTask", "Activity is foreground restarting");
                Intent restartIntent = new Intent(context, MainActivity.class);
                restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                context.startActivity(restartIntent);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }

    private boolean alreadyTriggered(Context context) {
        Calendar calendar = Calendar.getInstance();
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int month = calendar.get(Calendar.MONTH) + 1;
        int year = calendar.get(Calendar.YEAR);
        String dateString = day + "/" + month + "/" + year;


        SharedPreferences sharedPreferences = context.getSharedPreferences("RefreshTask", Context.MODE_PRIVATE);
        String value = sharedPreferences.getString("lastTrigger", "");

        return dateString.equals(value);

    }

    private void saveTrigger(Context context) {
        try {
            Calendar calendar = Calendar.getInstance();
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int month = calendar.get(Calendar.MONTH) + 1;
            int year = calendar.get(Calendar.YEAR);
            String dateString = day + "/" + month + "/" + year;

            SharedPreferences sharedPreferences = context.getSharedPreferences("RefreshTask", Context.MODE_PRIVATE);
            sharedPreferences.edit().putString("lastTrigger", dateString).apply();

            Log.i("RefreshTask", "saving triggered for this day  "+dateString);

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

}
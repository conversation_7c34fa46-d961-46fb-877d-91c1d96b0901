<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:layoutDirection="rtl"
    tools:context=".UI.Activity.MainActivity">

    <ImageView
        android:id="@+id/background_ImageView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/theme_custom_1_background_landscape"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="@dimen/_200sdp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_minus20sdp"
        android:layout_marginRight="@dimen/_40sdp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageView5"
            style="@style/mosqueIcon"
            android:layout_height="@dimen/_60sdp"
            android:layout_marginTop="0dp"
            android:scaleType="fitCenter"
            app:tint="#653F18" />

        <androidx.appcompat.widget.AppCompatTextView
            app:autoSizeTextType="uniform"
            android:id="@+id/timeNow_TextView_MainActivity"
            style="@style/BigTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_minus10sdp"
            android:layout_marginEnd="@dimen/_minus10sdp"
            android:layout_marginBottom="@dimen/_minus10sdp"
            android:fontFamily="@font/qatar_2022_bold"
            android:includeFontPadding="false"
            android:textColor="#653F18"
            android:textSize="@dimen/_50sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="bottom|center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/dayimage"
                    android:layout_width="@dimen/_60sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_marginTop="@dimen/_minus5sdp"
                    android:layout_marginBottom="@dimen/_minus5sdp"
                    android:gravity="center"
                    android:src="@drawable/d0"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/timeNow_TextView_MainActivity_"
                    app:tint="#653F18" />

                <TextView
                    android:id="@+id/day_text"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:text="Saturday"
                    android:textColor="#B07A3E"
                    android:textSize="@dimen/_12sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/gregorian_month_container"
            android:layout_width="@dimen/_200sdp"
            android:layout_height="@dimen/_45sdp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/_25sdp"
            android:paddingTop="@dimen/_5sdp"
            android:paddingRight="@dimen/_25sdp"
            android:paddingBottom="@dimen/_5sdp">

            <TextView
                android:id="@+id/dateNow1_TextView_MainActivity"
                style="@style/Theme_brown_new_3.day_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/qatar_2022_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingEnd="@dimen/_5sdp"
                android:text="09"
                android:textSize="@dimen/_30sdp" />

            <ImageView
                android:id="@+id/gregorian_month_image"
                android:layout_width="@dimen/_50sdp"
                android:layout_height="@dimen/_40sdp"
                android:scaleType="centerInside"
                android:src="@drawable/theme_brown_3_hijri_month_9"
                app:tint="#B07A3E" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="bottom|start"
                android:orientation="vertical"
                android:paddingStart="@dimen/_5sdp">

                <TextView
                    android:id="@+id/datem"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:text="June"
                    android:textAlignment="center"
                    android:textSize="@dimen/_12sdp" />

                <TextView
                    android:id="@+id/datey"
                    style="@style/Theme_brown_new_3.date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="1443 "
                    android:textSize="@dimen/_14sdp" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingPrayer_TextView_MainActivity"
            style="@style/MovingText"
            android:layout_width="@dimen/_200sdp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/_10sdp"
            android:layout_marginTop="0dp"
            android:layout_marginRight="@dimen/_10sdp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/gradient_semi_transperant_custom"
            android:fontFamily="@font/qatar_2022_bold"
            android:gravity="center"
            android:textColor="@color/colorBlueMain"
            android:textSize="@dimen/_16sdp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="@+id/timeNow_LinearLayout_MainActivity"
            app:layout_constraintRight_toRightOf="@+id/timeNow_LinearLayout_MainActivity"
            app:layout_constraintTop_toBottomOf="@id/dateNow_TextView_MainActivity"
            tools:text="5 جمادي الثاني 1437 هـ" />

        <TextView
            android:id="@+id/movingMessage_TextView_MainActivity"
            style="@style/MovingText"
            android:layout_width="@dimen/_180sdp"
            android:layout_marginTop="0dp"
            android:layout_marginBottom="0dp"
            android:fontFamily="@font/qatar_2022_bold"
            android:includeFontPadding="false"
            android:textColor="#3c4754"

            tools:text="@string/upload_photo_message" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/prayerTimeItem_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/_30sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginRight="@dimen/_30sdp"
        android:layout_marginBottom="@dimen/_10sdp"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/container"
        app:layout_constraintTop_toTopOf="parent">

        <!--
            tools:src="@drawable/img_athkar_after_prayer_blue" -->
        <ImageView
            android:id="@+id/container_ImageView_MainActivity1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"


            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@+id/prayerTimeItem_include_MainActivity2"

            app:layout_constraintTop_toTopOf="@+id/prayerTimeItem_include_MainActivity"
            tools:alpha="0.5" />

        <include
            android:id="@+id/prayerTimeItem_include_MainActivity2"
            layout="@layout/layout_prayer_times_custom_1_with_ikama"
            android:layout_width="match_parent"
            android:layout_height="match_parent"

            />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/announcement_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/_24sdp"
        android:layout_marginTop="@dimen/_15sdp"
        android:layout_marginEnd="@dimen/_24sdp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/alrabeeaTimes_ImageView_MainActivity"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/container"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/_5sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <!--
                        <TextView
                            android:id="@+id/tv_remainingOnIkama"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:text="@string/remaining_on_ikama"
                            android:textColor="#653F18"
                            android:textSize="@dimen/hadithBetweenAdhaanAndIkama"/>
                        <TextView
                            android:id="@+id/tv_message"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_minus15sdp"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:text="@string/fajr_prayer"
                            android:textColor="#653F18"
                            android:textSize="@dimen/prayerTime" />
            -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_remainingOnIkama"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingTop="@dimen/_5sdp"
                    android:text="@string/remaining_on_ikama"
                    android:textColor="#653F18"
                    android:textSize="@dimen/_16sdp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/qatar_2022_bold"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingTop="@dimen/_10sdp"
                    android:text="@string/fajr_prayer"
                    android:textColor="#653F18"
                    android:textSize="@dimen/_22sdp"
                    android:textStyle="bold" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_progress_remaining"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp">

                <com.mikhaellopez.circularprogressbar.CircularProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="@dimen/_80sdp"
                    android:layout_height="@dimen/_80sdp"
                    android:textColor="#653F18"
                    app:cpb_background_progressbar_color="#918F8F"
                    app:cpb_background_progressbar_width="3dp"
                    app:cpb_progressbar_color="#653F18"
                    app:cpb_progressbar_width="@dimen/_3sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:progress="50" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/progressBar"
                    app:layout_constraintEnd_toEndOf="@id/progressBar"
                    app:layout_constraintStart_toStartOf="@id/progressBar"
                    app:layout_constraintTop_toTopOf="@id/progressBar">

                    <TextView
                        android:id="@+id/tv_remaining_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/qatar_2022_bold"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="10:00"
                        android:textColor="#653F18"
                        android:textSize="@dimen/_20sdp" />

                    <TextView
                        android:id="@+id/tv_remaining_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/qatar_2022_bold"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:textColor="#653F18"
                        android:textSize="@dimen/_16sdp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="دقائق" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:fontFamily="@font/qatar_2022_bold"
            android:gravity="center"
            android:includeFontPadding="false"
            android:lineSpacingExtra="@dimen/_5sdp"
            android:paddingTop="@dimen/_10sdp"
            android:textColor="#653F18"
            android:textSize="@dimen/_11sdp"
            tools:text="@string/hadeth_on_prayer" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_silent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_60sdp"
            android:src="@drawable/bellsilentline"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="@android:color/black" />
    </LinearLayout>

    <ImageView
        android:id="@+id/alrabeeaTimes_ImageView_MainActivity"
        style="@style/AlrabeaIcon"
        android:layout_width="@dimen/_70sdp"
        android:layout_marginBottom="@dimen/_13sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.1"

        android:src="@drawable/logov2"

        android:scaleY="@fraction/logo_scale_small"
        android:scaleX="@fraction/logo_scale_small"

        app:layout_constraintLeft_toLeftOf="@id/container"
        app:layout_constraintRight_toRightOf="@id/container"
        app:tint="#3c4754" />
    <!--<ImageView
        android:id="@+id/alrabeeaTimes_ImageView_MainActivity"
        style="@style/AlrabeaIcon"
        android:layout_width="@dimen/_70sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="@dimen/_140sdp"
        android:layout_marginBottom="@dimen/_10sdp"
        app:tint="#3c4754" />-->
    <LinearLayout
        android:id="@+id/layout_athkar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_athkar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scaleType="fitXY"
            android:src="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/layout_time_athkar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_4sdp">

            <TextView
                android:id="@+id/tv_athkar_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="Time"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_icon"
                style="@style/AlrabeaIcon"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_26sdp"
                app:tint="@color/colorblack" />

        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
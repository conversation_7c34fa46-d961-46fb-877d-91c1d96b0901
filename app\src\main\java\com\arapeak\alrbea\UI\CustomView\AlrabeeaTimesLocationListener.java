package com.arapeak.alrbea.UI.CustomView;

import android.app.Activity;
import android.content.Context;
import android.location.Location;
import android.location.LocationListener;
import android.os.Bundle;
import android.widget.Toast;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.orhanobut.hawk.Hawk;

public class AlrabeeaTimesLocationListener implements LocationListener {

    private static final String TAG = "AlrabeaLocationListener";
    private final Context mContext;
    private String messageAfterFinish;

    public AlrabeeaTimesLocationListener(Context mContext, String messageAfterFinish) {
        this.mContext = mContext;
        this.messageAfterFinish = messageAfterFinish;
    }

    public AlrabeeaTimesLocationListener(Context mContext, int messageAfterFinishResourcesId) {
        this.mContext = mContext;
        if (mContext != null) {
            this.messageAfterFinish = Utils.getString(messageAfterFinishResourcesId);
        }
    }

    @Override
    public void onLocationChanged(Location loc) {
        HawkSettings.putLatLong(loc.getLatitude(), loc.getLongitude());
        Hawk.put(ConstantsOfApp.IS_INITIAL_SETUP_KEY, false);
        Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, false);
        Hawk.put(ConstantsOfApp.IS_UPDATE_PRAY_TIME, true);

        if (mContext != null && !Utils.getValueWithoutNull(messageAfterFinish).isEmpty()) {
            Toast.makeText(mContext, messageAfterFinish, Toast.LENGTH_SHORT).show();

            if (mContext instanceof Activity) {
                Utils.showSuccessAlert((Activity) mContext, messageAfterFinish);
            }
        }
//        editLocation.setText(s);
    }

    @Override
    public void onProviderDisabled(String provider) {
    }

    @Override
    public void onProviderEnabled(String provider) {
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
    }
}
package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.athkarThemes;

import android.app.Activity;
import android.app.Dialog;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.AzkarTheme;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.ui.utils.ConnectionExt;
import com.google.firebase.storage.FirebaseStorage;
import com.google.firebase.storage.StorageReference;

import java.io.File;
import java.util.List;

import pl.droidsonroids.gif.GifImageView;

public class AzkarThemeHolder extends RecyclerView.ViewHolder {

    TextView tvState;
    AzkarTheme theme;
    GifImageView gif;
    Dialog loadingDialog;
    Activity activity;
    volatile int currentDownloads = 0;
    volatile int failedDownloads = 0;

    public AzkarThemeHolder(View itemView) {
        super(itemView);
        tvState = itemView.findViewById(R.id.tv);
        gif = itemView.findViewById(R.id.gif);
        itemView.setOnClickListener(this::click);
    }

    private void click(View view) {
        if (theme.isActive()) return;
        if (!theme.isDownloaded()) {
            download(activity);
            return;
        }
        int old = HawkSettings.getCurrentAzkarTheme().ordinal();
        HawkSettings.setCurrentAzkarTheme(theme);
        notifyItem(old);
        notifyItem();
    }

    void notifyItem() {
        notifyItem(theme.ordinal());
    }

    void notifyItem(int position) {
        RecyclerView.Adapter adapter = getBindingAdapter();
        if (adapter == null) return;
        getBindingAdapter().notifyItemChanged(position);
    }

    private void download(Activity context) {
        if (!(new ConnectionExt()).isNetworkConnected(context)) {
            Utils.showFailAlert(context, "خطأ", "فشل تحميل الملف ");
            loadingDialog.dismiss();
            return;
        }
        currentDownloads = 0;
        failedDownloads = 0;
        loadingDialog.show();
        StorageReference listRef = FirebaseStorage.getInstance().getReference().child(ConstantsOfApp.AzkarRef + theme.ordinal());
        listRef.listAll().addOnCompleteListener(task -> {
            if (task.isSuccessful())
                startDownloads(task.getResult().getItems());
            else {
                loadingDialog.dismiss();
                failed();
            }
        });
    }

    private void failed() {
        Utils.showFailAlert(activity, Utils.getString(R.string.there_is_a_problem), Utils.getString(R.string.try_again));
    }

    private void startDownloads(List<StorageReference> items) {
        currentDownloads = items.size();
        for (StorageReference item : items) {
            File file = new File(theme.getDir(), item.getName());
            Utils.createFile(file);
            item.getFile(file)
                    .addOnCompleteListener(task1 -> {
                        if (!task1.isSuccessful()) failedDownloads++;
                        currentDownloads--;
                        if (currentDownloads == 0) endDownloads();
                    });
        }
    }

    private void endDownloads() {
        loadingDialog.dismiss();
        if (failedDownloads > 0) failed();
        else notifyItem();
    }

    public void Bind(AzkarTheme theme, Activity activity, Dialog dialog) {
        this.activity = activity;
        loadingDialog = dialog;
        this.theme = theme;
        gif.setBackgroundResource(Utils.isLandscape() ? theme.landGif : theme.portGif);
        if (!theme.isDownloaded()) {
            tvState.setText(Utils.getString(R.string.download));
            return;
        }
        if (theme.isActive()) {
            tvState.setText(Utils.getString(R.string.enable));
            return;
        }
        tvState.setVisibility(View.GONE);
    }

}

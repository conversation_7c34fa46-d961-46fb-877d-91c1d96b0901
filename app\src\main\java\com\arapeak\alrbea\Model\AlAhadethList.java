package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class AlAhadethList {

    @Expose
    @SerializedName("ahadeth")
    private List<AlAhadeth> ahadeth;

    public List<AlAhadeth> getAhadeth() {
        return ahadeth == null ? new ArrayList<AlAhadeth>() : ahadeth;
    }

    public void setAhadeth(List<AlAhadeth> ahadeth) {
        this.ahadeth = ahadeth;
    }
}

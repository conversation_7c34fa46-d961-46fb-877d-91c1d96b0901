package com.arapeak.alrbea.database;

import static com.arapeak.alrbea.AppController.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.arapeak.alrbea.Model.PhotoGalleryImage;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.ArrayList;

public class PhotoGalleryImagesRepository {
    public static void insert(long galleryId, ArrayList<PhotoGalleryImage> images) {
        SQLiteDatabase database = db.getWritableDatabase();
        for (PhotoGalleryImage image : images) {
            if (image.imageUri == null)
                continue;
            ContentValues values = new ContentValues();
            values.put("gallery_id", galleryId);
            values.put("imageData", image.imageUri);
            database.insert(MainDatabase.PHOTO_GALLERY_IMAGE, null, values);
        }
        database.close();
    }

    public static ArrayList<PhotoGalleryImage> getFromGallery(long id) {
        ArrayList<PhotoGalleryImage> images = new ArrayList<>();
        try (SQLiteDatabase database = db.getReadableDatabase()) {
            Cursor cursor = database.rawQuery("select * from " + MainDatabase.PHOTO_GALLERY_IMAGE + " where gallery_id = " + id, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    do {
                        PhotoGalleryImage image = new PhotoGalleryImage();
                        image.id = cursor.getLong(0);
                        image.galleryId = cursor.getLong(1);
                        image.imageUri = cursor.getString(2);
//                        image.imageUri = cursor.getBlob(2);
                        images.add(image);
                    }
                    while (cursor.moveToNext());
                    cursor.close();
                }
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return images;
    }

    public static PhotoGalleryImage get(long id) {
        PhotoGalleryImage image = null;
        try (SQLiteDatabase database = db.getReadableDatabase()) {
            Cursor cursor = database.rawQuery("select * from " + MainDatabase.PHOTO_GALLERY_IMAGE + " where id = " + id, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    do {
                        image = new PhotoGalleryImage();
                        image.id = cursor.getLong(0);
                        image.galleryId = cursor.getLong(1);
                        image.imageUri = cursor.getString(2);
//                        image.imageUri = cursor.getBlob(2);
                    }
                    while (cursor.moveToNext());
                    cursor.close();
                }
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            return null;
        }
        return image;
    }

    public static void delete(long id) {
        PhotoGalleryImage image = get(id);
//        if(image != null){
//            Utils.deleteFile(image.imageUri);
//        }
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            database.execSQL("delete from " + MainDatabase.PHOTO_GALLERY_IMAGE + " where id = " + id);
        }
//        db.getWritableDatabase().execSQL("delete from "+MainDatabase.PHOTO_GALLERY_IMAGE+" where id = "+id);
    }

    public static void deleteAllByGalleryId(long galleryId) {
//        for(PhotoGalleryImage image : getFromGallery(galleryId)){
//            Utils.deleteFile(image.imageUri);
//        }
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            database.execSQL("delete from " + MainDatabase.PHOTO_GALLERY_IMAGE + " where gallery_id = " + galleryId);
        }
//        db.getWritableDatabase().execSQL("delete from "+MainDatabase.PHOTO_GALLERY_IMAGE+" where gallery_id = "+galleryId);
    }

}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_45sdp"
    android:background="#4001A0C6"
    android:gravity="center"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:includeFontPadding="false"
        android:paddingStart="@dimen/_15sdp"
        android:text="تنبيه صوتي"
        android:textColor="#474747"
        android:textSize="@dimen/_12sdp" />


    <ImageButton
        android:id="@+id/ib_play"
        android:layout_width="@dimen/_60sdp"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:src="@drawable/ic_baseline_play_arrow_24" />

</LinearLayout>

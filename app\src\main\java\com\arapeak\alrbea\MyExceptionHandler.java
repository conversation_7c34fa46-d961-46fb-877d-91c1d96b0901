package com.arapeak.alrbea;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.arapeak.alrbea.UI.Activity.SplashScreen;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.concurrent.atomic.AtomicInteger;

public class MyExceptionHandler implements Thread.UncaughtExceptionHandler {
    private static final String TAG = "MyExceptionHandler";
    private static final int MAX_RESTART_ATTEMPTS = 3;
    private static final long RESTART_COOLDOWN = 30000; // 30 seconds

    private final Context context;
    private final Thread.UncaughtExceptionHandler defaultHandler;
    private static final AtomicInteger restartCount = new AtomicInteger(0);
    private static long lastRestartTime = 0;

    public MyExceptionHandler() {
        context = AppController.getInstance().getBaseContext();
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }

    public static void restartAppSoftly() {
        try {
            Intent i = AppController.baseContext.getPackageManager()
                    .getLaunchIntentForPackage(AppController.baseContext.getPackageName());
            if (i != null) {
                i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                AppController.baseContext.startActivity(i);
            }
            System.exit(0);
        } catch (Exception e) {
            Log.e(TAG, "Failed to restart app softly", e);
            System.exit(1);
        }
    }

    public static void resetApp(Context context) {
        try {
            Intent intent = new Intent(context, SplashScreen.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                    | Intent.FLAG_ACTIVITY_CLEAR_TASK
                    | Intent.FLAG_ACTIVITY_NEW_TASK);

            int flags = PendingIntent.FLAG_ONE_SHOT;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }

            PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, flags);
            AlarmManager mgr = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

            if (mgr != null) {
                mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 10000, pendingIntent);
            }

            System.exit(2);
        } catch (Exception e) {
            Log.e(TAG, "Failed to reset app", e);
            System.exit(3);
        }
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        try {
            // Log the exception
            Log.e(TAG, "Uncaught exception in thread " + thread.getName(), ex);
            FirebaseCrashlytics.getInstance().recordException(ex);
            CrashlyticsUtils.INSTANCE.logException(ex);

            // Check if we should restart or let the app crash
            long currentTime = System.currentTimeMillis();

            // Reset restart count if enough time has passed
            if (currentTime - lastRestartTime > RESTART_COOLDOWN) {
                restartCount.set(0);
            }

            // Check if we've exceeded restart attempts
            if (restartCount.incrementAndGet() > MAX_RESTART_ATTEMPTS) {
                Log.e(TAG, "Too many restart attempts, letting app crash");
                if (defaultHandler != null) {
                    defaultHandler.uncaughtException(thread, ex);
                } else {
                    System.exit(1);
                }
                return;
            }

            lastRestartTime = currentTime;

            // Only restart for certain types of exceptions
            if (shouldRestart(ex)) {
                Log.i(TAG, "Restarting app due to recoverable exception (attempt " + restartCount.get() + ")");
                resetApp(context);
            } else {
                Log.e(TAG, "Fatal exception, not restarting");
                if (defaultHandler != null) {
                    defaultHandler.uncaughtException(thread, ex);
                } else {
                    System.exit(1);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception in exception handler", e);
            if (defaultHandler != null) {
                defaultHandler.uncaughtException(thread, ex);
            } else {
                System.exit(1);
            }
        }
    }

    private boolean shouldRestart(Throwable ex) {
        // Don't restart for these critical exceptions
        if (ex instanceof OutOfMemoryError ||
                ex instanceof StackOverflowError ||
                ex instanceof SecurityException ||
                ex instanceof NoClassDefFoundError) {
            return false;
        }

        // Check exception message for non-recoverable errors
        String message = ex.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            if (message.contains("native crash") ||
                    message.contains("signal") ||
                    message.contains("sigsegv") ||
                    message.contains("sigabrt")) {
                return false;
            }
        }

        return true;
    }
}

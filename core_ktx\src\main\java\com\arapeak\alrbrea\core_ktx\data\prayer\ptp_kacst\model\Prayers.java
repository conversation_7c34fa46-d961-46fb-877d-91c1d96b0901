package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model;


public class Prayers {
    public static PrayerTimes getPrayerTimes(int i, int i2, int i3, double d, double d2, PrayerTimeType prayerTimeType) {
        double d3;
        double d4;
        double d5;
        double abs;
        double sin;
        PlanetParams planetParams;
        double cos;
        double d6;
        boolean z;
        double tan;
        double d7;
        RabitaTimes GetRatio;
        PrayerTimes prayerTimes = new PrayerTimes();
        prayerTimes.Rabita = new RabitaTimes();
        RabitaTimes rabitaTimes = new RabitaTimes();
        new RabitaTimes();
        PlanetParams SunParam = Core.SunParam(i, i2, i3, d2, d, -prayerTimeType.TimeZone);
        double sin2 = Math.sin(SunParam.Eqatorial.Decl) * Math.sin(d);
        double cos2 = Math.cos(SunParam.Eqatorial.Decl) * Math.cos(d);
        double d8 = SunParam.Transit;
        if (SunParam.FlagRS != 0 || Math.abs(d) >= 0.79d || (prayerTimeType.HeightDiffEast == 0.0d && prayerTimeType.HeightDiffWest == 0.0d)) {
            d3 = d8;
            d4 = 0.0d;
            d5 = 0.0d;
        } else {
            double acos = Math.acos((Math.sin(-0.014544352255644346d) - sin2) / cos2);
            double acos2 = (acos - Math.acos((Math.sin((1.5707963267948966d - Math.asin(6378140.0d / (prayerTimeType.HeightDiffWest + 6378140.0d))) - 0.014544352255644346d) - sin2) / cos2)) * 3.819718634205488d;
            d3 = d8;
            d4 = (acos - Math.acos((Math.sin((1.5707963267948966d - Math.asin(6378140.0d / (prayerTimeType.HeightDiffEast + 6378140.0d))) - 0.014544352255644346d) - sin2) / cos2)) * 3.819718634205488d;
            d5 = acos2;
        }
        if (SunParam.FlagRS != 0 || Math.abs(SunParam.Set - SunParam.Rise) <= 1.0d || Math.abs(SunParam.Set - SunParam.Rise) >= 23.0d) {
            if (d < 0.0d) {
                abs = -Math.abs(prayerTimeType.RabitaSuggestedAngle);
            } else {
                abs = Math.abs(prayerTimeType.RabitaSuggestedAngle);
            }
            PlanetParams SunParam2 = Core.SunParam(i, i2, i3, d2, abs, -prayerTimeType.TimeZone);
            double d9 = SunParam2.Transit;
            sin = Math.sin(SunParam2.Eqatorial.Decl) * Math.sin(abs);
            planetParams = SunParam2;
            d3 = d9;
            cos = Math.cos(SunParam2.Eqatorial.Decl) * Math.cos(abs);
            d6 = 0.0d;
            z = true;
        } else {
            planetParams = SunParam;
            sin = sin2;
            cos = cos2;
            z = false;
            abs = 0.0d;
            d6 = 0.0d;
        }
        if (d3 < d6) {
            d3 += 24.0d;
        }
        prayerTimes.Sunrise = planetParams.Rise - d4;
        prayerTimes.Zohar = d3 + prayerTimeType.SafetyTime;
        prayerTimes.Magreb = planetParams.Set + d5 + prayerTimeType.SafetyTime;
        if (z) {
            tan = prayerTimeType.Aser + Math.tan(Math.abs(planetParams.Eqatorial.Decl - abs));
        } else {
            tan = Math.tan(Math.abs(planetParams.Eqatorial.Decl - d)) + prayerTimeType.Aser;
        }
        double sin3 = (Math.sin(Math.atan(1.0d / tan)) - sin) / cos;
        prayerTimes.Aser = d3 + (Math.abs(sin3) > 1.0d ? 3.5d : Math.acos(sin3) * 3.819718634205488d) + prayerTimeType.SafetyTime;
        double sin4 = (Math.sin(-prayerTimeType.FajarAngle) - sin) / cos;
        if (Math.abs(d) < 0.83776d && Math.abs(sin4) <= 1.0d) {
            prayerTimes.Fajer = (d3 - ((Math.acos(sin4) * 3.819718634205488d) + d4)) + prayerTimeType.SafetyTime;
            prayerTimes.Rabita.FajarExactRabita = prayerTimes.Fajer;
        } else {
            if (d < 0.0d) {
                d7 = sin4;
                GetRatio = GetRatio(i, 12, 21, d, d2, prayerTimeType);
            } else {
                d7 = sin4;
                GetRatio = GetRatio(i, 6, 21, d, d2, prayerTimeType);
            }
            rabitaTimes = GetRatio;
            double d10 = d7;
            if (Math.abs(d10) > (prayerTimeType.FajarAngle * 1.3369d) + 0.45d) {
                double d11 = 24.0d - (planetParams.Set - planetParams.Rise);
                if (d11 > 24.0d) {
                    d11 -= 24.0d;
                }
                prayerTimes.Fajer = planetParams.Rise - (d11 * rabitaTimes.FajarExactRabita);
            } else {
                prayerTimes.Fajer = (d3 - ((Math.acos(d10) * 3.819718634205488d) + d4)) + prayerTimeType.SafetyTime;
            }
            prayerTimes.Rabita.FajarExactRabita = prayerTimes.Fajer;
            if (Math.abs(d10) > 1.0d) {
                RabitaTimes GetRatio2 = GetRatio(i, i2, i3, d, d2, prayerTimeType);
                double d12 = 24.0d - (planetParams.Set - planetParams.Rise);
                if (d12 > 24.0d) {
                    d12 -= 24.0d;
                }
                prayerTimes.Rabita.FajarExactRabita = planetParams.Rise - (d12 * GetRatio2.FajarExactRabita);
            } else {
                prayerTimes.Rabita.FajarExactRabita = (d3 - ((Math.acos(d10) * 3.819718634205488d) + d4)) + prayerTimeType.SafetyTime;
            }
        }
        if (prayerTimeType.IshaAngle != 0.0d) {
            double sin5 = (Math.sin(-prayerTimeType.IshaAngle) - sin) / cos;
            if (Math.abs(d) < 0.83776d) {
                prayerTimes.Isha = d3 + (Math.acos(sin5) * 3.819718634205488d) + d5 + prayerTimeType.SafetyTime;
                prayerTimes.Rabita.IshaExactRabita = prayerTimes.Isha;
            } else {
                if (Math.abs(sin5) > (prayerTimeType.FajarAngle * 1.3369d) + 0.45d) {
                    double d13 = 24.0d - (planetParams.Set - planetParams.Rise);
                    if (d13 > 24.0d) {
                        d13 -= 24.0d;
                    }
                    prayerTimes.Isha = planetParams.Set + (d13 * rabitaTimes.IshaExactRabita);
                } else {
                    prayerTimes.Isha = d3 + (Math.acos(sin5) * 3.819718634205488d) + d5 + prayerTimeType.SafetyTime;
                }
                if (Math.abs(sin5) > 1.0d) {
                    RabitaTimes GetRatio3 = GetRatio(i, i2, i3, d, d2, prayerTimeType);
                    double d14 = 24.0d - (planetParams.Set - planetParams.Rise);
                    if (d14 > 24.0d) {
                        d14 -= 24.0d;
                    }
                    prayerTimes.Rabita.IshaExactRabita = planetParams.Set + (d14 * GetRatio3.IshaExactRabita);
                } else {
                    prayerTimes.Rabita.IshaExactRabita = d3 + (Math.acos(sin5) * 3.819718634205488d) + d5 + prayerTimeType.SafetyTime;
                }
            }
        } else {
            prayerTimes.Isha = prayerTimes.Magreb + prayerTimeType.IshaFixedSunset;
            prayerTimes.Rabita.IshaExactRabita = prayerTimes.Isha;
        }
        double sin6 = (Math.sin(prayerTimeType.EidAngle) - sin) / cos;
        if ((Math.abs(d) < 1.134d || planetParams.FlagRS == 0) && Math.abs(sin6) <= 1.0d) {
            prayerTimes.Eid = (d3 - ((Math.acos(sin6) * 3.819718634205488d) + d4)) + prayerTimeType.SafetyTime;
        } else {
            prayerTimes.Eid = prayerTimes.Sunrise + 0.25d;
        }
        return prayerTimes;
    }

    private static RabitaTimes GetRatio(int i, int i2, int i3, double d, double d2, PrayerTimeType prayerTimeType) {
        double abs;
        double d3;
        new PlanetParams();
        RabitaTimes rabitaTimes = new RabitaTimes();
        if (d < 0.0d) {
            abs = -Math.abs(prayerTimeType.RabitaSuggestedAngle);
        } else {
            abs = Math.abs(prayerTimeType.RabitaSuggestedAngle);
        }
        PlanetParams SunParam = Core.SunParam(i, i2, i3, d2, abs, -prayerTimeType.TimeZone);
        double sin = Math.sin(SunParam.Eqatorial.Decl) * Math.sin(abs);
        double cos = Math.cos(SunParam.Eqatorial.Decl) * Math.cos(abs);
        double d4 = 24.0d - (SunParam.Set - SunParam.Rise);
        double acos = (SunParam.Transit - (Math.acos((Math.sin(-prayerTimeType.FajarAngle) - sin) / cos) * 3.819718634205488d)) - prayerTimeType.SafetyTime;
        if (prayerTimeType.IshaAngle != 0.0d) {
            d3 = SunParam.Transit + (Math.acos((Math.sin(-prayerTimeType.IshaAngle) - sin) / cos) * 3.819718634205488d) + prayerTimeType.SafetyTime;
        } else {
            d3 = prayerTimeType.IshaFixedSunset + SunParam.Set;
        }
        rabitaTimes.IshaExactRabita = (d3 - SunParam.Set) / d4;
        rabitaTimes.FajarExactRabita = (SunParam.Rise - acos) / d4;
        return rabitaTimes;
    }

    public static String toStringTime(double d, int i) {
        StringBuilder sb;
        String str;
        StringBuilder sb2;
        String str2;
        int i2 = (int) d;
        int i3 = (int) ((d - i2) * 60.0d);
        if (i3 == 60) {
            i2++;
            i3 = 0;
        }
        if (i3 < 0) {
            i3 = -i3;
        }
        if (i == 12) {
            StringBuilder sb3 = new StringBuilder();
            if (i2 > 12) {
                i2 -= 12;
            }
            sb3.append(i2);
            sb3.append(" : ");
            if (i3 < 10) {
                sb2 = new StringBuilder();
                str2 = "0";
            } else {
                sb2 = new StringBuilder();
                str2 = "";
            }
            sb2.append(str2);
            sb2.append(i3);
            sb3.append(sb2.toString());
            return sb3.toString();
        }
        StringBuilder sb4 = new StringBuilder();
        sb4.append(i2);
        sb4.append(" : ");
        if (i3 < 10) {
            sb = new StringBuilder();
            str = "0";
        } else {
            sb = new StringBuilder();
            str = "";
        }
        sb.append(str);
        sb.append(i3);
        sb4.append(sb.toString());
        return sb4.toString();
    }

    public static String toStringTime(double d) {
        return toStringTime(d, 12);
    }
}
package com.arapeak.alrbea.UI.Activity.AppCore;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.util.Log;

import com.arapeak.alrbea.UI.Activity.HomeUi.CallbackInterfaces;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import static com.arapeak.alrbrea.core_ktx.ui.utils.ContextExtKt.isActivityInForeground;

import java.util.Calendar;

public class AppLifecycleManager {

    private static final String TAG = "AppLifecycleManager";
    private static final String PREFS_NAME = "RefreshTask";
    private static final String LAST_TRIGGER_KEY = "lastTrigger";

    private Context context;
    private CallbackInterfaces.OnAppLifecycleListener appLifecycleListener;

    public AppLifecycleManager(Context context, CallbackInterfaces.OnAppLifecycleListener listener) {
        this.context = context.getApplicationContext();
        this.appLifecycleListener = listener;
    }

    public boolean isMidnight() {
        Calendar now = Calendar.getInstance();
        return now.get(Calendar.HOUR_OF_DAY) == 0 && now.get(Calendar.MINUTE) == 0;
    }

    public void checkAndRestartAppIfMidnight() {
        if (isMidnight()) {
            if (isActivityInForeground((Activity) context, MainActivity.class)) { // Cast to Activity for foreground check
                if (alreadyTriggered(context)) {
                    Log.i(TAG, "Already triggered for this day, not restarting.");
                    return;
                }
                saveTrigger(context);
                Log.i(TAG, "Midnight detected, restarting app.");
                if (appLifecycleListener != null) {
                    appLifecycleListener.onRestartApp();
                } else {
                    // Fallback restart if listener not set or if you want to handle it directly
                    Intent restartIntent = new Intent(context, MainActivity.class);
                    restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    context.startActivity(restartIntent);
                    // For a complete restart, sometimes exiting the current process is desired
                    // Runtime.getRuntime().exit(0);
                }
            }
        }
    }

    private boolean alreadyTriggered(Context context) {
        Calendar calendar = Calendar.getInstance();
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int month = calendar.get(Calendar.MONTH) + 1;
        int year = calendar.get(Calendar.YEAR);
        String dateString = day + "/" + month + "/" + year;

        SharedPreferences sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String value = sharedPreferences.getString(LAST_TRIGGER_KEY, "");

        return dateString.equals(value);
    }

    private void saveTrigger(Context context) {
        try {
            Calendar calendar = Calendar.getInstance();
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int month = calendar.get(Calendar.MONTH) + 1;
            int year = calendar.get(Calendar.YEAR);
            String dateString = day + "/" + month + "/" + year;

            SharedPreferences sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            sharedPreferences.edit().putString(LAST_TRIGGER_KEY, dateString).apply();
            Log.i(TAG, "Saving triggered for this day: " + dateString);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}
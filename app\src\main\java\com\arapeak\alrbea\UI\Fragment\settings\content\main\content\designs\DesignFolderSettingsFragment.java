package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.BackgroundFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.athkarThemes.AzkarThemesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.themes.ThemesFragment;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.List;

public class DesignFolderSettingsFragment extends AlrabeeaTimesFragment implements AdapterCallback {

    private static final String TAG = "DesignFolderSettingsFragment";

    private View designFolderSettingsView;
    private RecyclerView settingItemRecyclerView;
    private Dialog loadingDialog;

    //    private SettingsAdapter settingsAdapter;
    private SettingsAdapter settingsAdapter;

    public DesignFolderSettingsFragment() {

    }

    public static DesignFolderSettingsFragment newInstance() {
        return new DesignFolderSettingsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        designFolderSettingsView = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return designFolderSettingsView;
    }

    private void initView() {
        settingItemRecyclerView = designFolderSettingsView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        if (Utils.isLandscape()) {
            settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 3));
        } else {
            settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));

        }
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        settingsAdapter = new SettingsAdapter(getContext(), setupSettings(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()){
//            SettingsLandscapeActivity.setTextTite(getString(R.string.general_settings_title));
//        }else {
//            SettingsActivity.setTextTite(getString(R.string.general_settings_title));
//        }
        SettingsActivity.setTextTite(getString(R.string.general_settings_title));
        settingItemRecyclerView.setAdapter(settingsAdapter);

    }

    private void SetAction() {


    }

    @Override
    public void onItemClick(int position, String tag) {
        switch (position) {
            case 0: {
                Utils.loadFragment(new ThemesFragment()
                        , getAppCompatActivity()
                        , 0);
                break;
            }
            case 1: {
                Utils.loadFragment(new AzkarThemesFragment()
                        , getAppCompatActivity()
                        , 0);
                break;
            }
            case 2: {
                Utils.loadFragment(BackgroundFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            }
        }
    }

    private List<SettingAlrabeeaTimes> setupSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.design_folder_title);
        String[] generalSettingsDescriptionArray = getResources().getStringArray(R.array.design_folder_description);

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[0]
                , generalSettingsDescriptionArray[0]
                , R.drawable.theme_home));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[1]
                , generalSettingsDescriptionArray[1]
                , R.drawable.theme));
        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[2]
                , generalSettingsDescriptionArray[2]
                , R.drawable.theme));

        /*settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[2]
                , generalSettingsDescriptionArray[2]
                , R.drawable.ic_theme));
                */


        return settingAlrabeeaTimes;
    }
}


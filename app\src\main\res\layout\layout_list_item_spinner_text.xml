<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="#F2F2F2"
    android:gravity="center"
    android:orientation="horizontal"
    android:padding="4dp"
    tools:layoutDirection="rtl">

    <ImageView
        android:id="@+id/icon_ImageView_SpinnerAdapter"
        android:layout_width="@dimen/_10sdp"
        android:layout_height="@dimen/_10sdp"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:srcCompat="@drawable/ic_menu_gallery"
        tools:tint="#000" />


    <TextView
        android:id="@+id/title_TextView_SpinnerAdapter"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="26dp"
        android:layout_weight="1"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="start|center_vertical"
        android:textColor="#000000"
        android:textSize="@dimen/_10sdp"
        tools:text="للبيع عقار" />

    <ImageView
        android:id="@+id/arrow_image_ImageView_SpinnerAdapter"
        android:layout_width="@dimen/_20sdp"
        android:layout_height="@dimen/_20sdp"
        android:layout_gravity="center"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_arrow_down"
        app:tint="#707070" />

</LinearLayout>
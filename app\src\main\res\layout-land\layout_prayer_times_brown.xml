<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/contentFajr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.brown"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/fajr_TextView_MainActivity"
                    style="@style/TimeTextView.brown_land.TimeNameAR"
                    android:text="@string/fajr" />

            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_fajr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:gravity="end">

                <LinearLayout
                    android:id="@+id/fajrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/fajrTime_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.Time" />

                    <TextView
                        android:id="@+id/fajrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.TimeType" />

                </LinearLayout>


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.brown"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/dhuhr_TextView_MainActivity"
                    style="@style/TimeTextView.brown_land.TimeNameAR"
                    android:text="@string/dhuhr" />

            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_dhur"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:gravity="end">

                <LinearLayout
                    android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/dhuhrTime_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.Time" />

                    <TextView
                        android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.TimeType" />

                </LinearLayout>


            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentAsr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.brown"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/asr_TextView_MainActivity"
                    style="@style/TimeTextView.brown_land.TimeNameAR"
                    android:text="@string/asr" />

            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_asr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:gravity="end">

                <LinearLayout
                    android:id="@+id/asrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/asrTime_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.Time" />

                    <TextView
                        android:id="@+id/asrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.TimeType" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.brown"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/maghrib_TextView_MainActivity"
                    style="@style/TimeTextView.brown_land.TimeNameAR"
                    android:text="@string/maghrib" />

            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_maghrib"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:gravity="end">

                <LinearLayout
                    android:id="@+id/maghribTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/maghribTime_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.Time" />

                    <TextView
                        android:id="@+id/maghribTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.TimeType" />
                </LinearLayout>


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentIsha_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.brown"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/isha_TextView_MainActivity"
                    style="@style/TimeTextView.brown_land.TimeNameAR"
                    android:text="@string/isha" />
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_isha"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:gravity="end">


                <LinearLayout
                    android:id="@+id/ishaTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/ishaTime_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.Time" />

                    <TextView
                        android:id="@+id/ishaTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.TimeType" />

                </LinearLayout>


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentSunrise_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.brown"
        android:layout_width="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="8dp"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/sunrise_TextView_MainActivity"
                    style="@style/TimeTextView.brown_land.TimeNameAR"

                    android:layout_marginStart="@dimen/_4sdp"
                    android:layout_marginTop="0dp"
                    android:layout_marginEnd="@dimen/_4sdp"
                    android:layout_marginBottom="0dp"
                    android:text="@string/duha"

                    android:textSize="@dimen/_18sdp" />

            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.brown"
                android:gravity="end">

                <LinearLayout
                    android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layoutDirection="ltr">

                    <TextView
                        android:id="@+id/sunriseATime_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.Time"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_18sdp" />

                    <TextView
                        android:id="@+id/sunriseATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.brown_land.TimeType"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_7sdp" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="16dp"
    tools:context=".UI.Fragment.settings.content.athkar.content.SettingsAlAhadethFragment"
    tools:layoutDirection="rtl">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp">

        <CheckBox
            android:id="@+id/alAhadeth_CheckBox_SettingsAlAhadethFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            app:buttonTint="@color/colorPrimary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/alAhadeth_TextView_SettingsAlAhadethFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:includeFontPadding="false"
            android:text="@string/enable_al_ahadeth"
            android:textColor="@android:color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/alAhadeth_CheckBox_SettingsAlAhadethFragment"
            app:layout_constraintTop_toTopOf="@id/alAhadeth_CheckBox_SettingsAlAhadethFragment" />

        <TextView
            android:id="@+id/alAhadethDuration_TextView_SettingsAlAhadethFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/without_corners_20_stroke_gray"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:padding="8dp"
            android:textColor="@android:color/black"
            android:textSize="13.5sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="@id/alAhadeth_TextView_SettingsAlAhadethFragment"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@id/alAhadeth_TextView_SettingsAlAhadethFragment"
            app:layout_constraintTop_toBottomOf="@id/alAhadeth_TextView_SettingsAlAhadethFragment"
            tools:text="00:00:00" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</ScrollView>
package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_adhan.mapper

import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.batoulapps.adhan2.CalculationMethod as AdhanCalculationMethod


fun CalculationMethod.toAdhanMethod(): AdhanCalculationMethod {
    val default = AdhanCalculationMethod.UMM_AL_QURA
    return when (this) {
        CalculationMethod.automatic -> default
        CalculationMethod.ummAlQurra -> AdhanCalculationMethod.UMM_AL_QURA
        CalculationMethod.egyptianSurvey -> AdhanCalculationMethod.EGYPTIAN
        CalculationMethod.karachi -> AdhanCalculationMethod.KARACHI
        CalculationMethod.muslimLeague -> AdhanCalculationMethod.MUSLIM_WORLD_LEAGUE
        CalculationMethod.northAmerica -> AdhanCalculationMethod.NORTH_AMERICA
        CalculationMethod.gulf_region -> default
        CalculationMethod.kuwait -> AdhanCalculationMethod.KUWAIT
        CalculationMethod.singapore -> AdhanCalculationMethod.SINGAPORE
        CalculationMethod.france -> default
        CalculationMethod.turkey -> AdhanCalculationMethod.TURKEY
        CalculationMethod.russia -> default
        CalculationMethod.customCalendar -> default
    }
}
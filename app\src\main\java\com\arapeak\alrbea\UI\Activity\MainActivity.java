package com.arapeak.alrbea.UI.Activity;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.DAYS_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.EN_LANGUAGE;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.HOURS_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_KEY;
import static com.arapeak.alrbea.UI.Activity.mainActivityExt.ThemeRes.getDayImageRes;
import static com.arapeak.alrbea.UI.Activity.mainActivityExt.ThemeRes.getGregorianMonthImageRes;
import static com.arapeak.alrbea.UI.Activity.mainActivityExt.ThemeRes.getHijriMonthImageRes;
import static com.arapeak.alrbea.hawk.HawkConstants.AR_LANGUAGE;
import static com.arapeak.alrbrea.core_ktx.ui.utils.ContextExtKt.isActivityInForeground;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsoluteLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.palette.graphics.Palette;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.AnnouncementMessage;
import com.arapeak.alrbea.Enum.AnnouncementType;
import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.AzkarTheme;
import com.arapeak.alrbea.Enum.News;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.PremiumUserModel;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.PrayerUtils;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.ResourcesLocale;
import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.AnnouncementManager;
import com.arapeak.alrbea.UI.Fragment.settings.content.textDesigner.FontMapper;
import com.arapeak.alrbea.UI.Margin;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.PhotoGalleryRepository;
import com.arapeak.alrbea.database.Repositories;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.ui.screensaver.ScreensaverScheduler;
import com.arapeak.alrbrea.core_ktx.ui.textdesign.TextDesignUiManager;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;
import com.google.android.material.navigation.NavigationView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.mikhaellopez.circularprogressbar.CircularProgressBar;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;
import com.tapadoo.alerter.Alert;

import java.io.File;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

public class MainActivity extends BaseAppCompatActivity
        implements NavigationView.OnNavigationItemSelectedListener, AdapterCallback {

    public static final String TAG = "ui.MainActivityBESHER";
    // region -Views-
    // public static PrayerApi prayerApi;
    public static int year, month, day;
    public static TimingsAlrabeeaTimes timingsAlrabeeaTimes;
    public static boolean isShowAthkarsAfterPrayer = false;
    public static int timeOfAthkarsAfterPrayer = 0;
    public static boolean isHijri;
    public static long LastMaghrib = 0;
    public boolean isLandscape;
    // PrayerSystemsSchools prayerSystemsSchools;
    public AnnouncementManager announcementManager;
    public long TempTime = 0;
    public String lan = "ar";
    public boolean logoImageEnabled = false;
    public DisplayTypes lastDisplayed;
    // int themeAppKey = APP_THEME_DEFAULT_KEY;
    UITheme uiTheme = null;
    // String s;
    AtomicBoolean isPrayerListUpdating = new AtomicBoolean(false);
    UmmalquraCalendar hijriCalendar = Utils.getUmmalquraCalendar();
    GregorianCalendar gregorianCalendar = new GregorianCalendar();
    long sleepTimeUntilNextRefresh = 0;
    int lastAthkarIndex = 0;
    int domainColor = 0;
    int textColor = 0;
    int lastDisplayedDay = -1;
    AzkarTheme azkarTheme;
    int _blueCurrentDate = 1;
    String date = "";
    Alert alert = null;

    // Replace threads with handlers for better performance and lifecycle management
    private Handler eventsHandler;
    private Handler mainHandler;
    private Handler timeUpdateHandler;
    private Handler athkarUpdateHandler;
    private Handler screenSaverHandler;
    private Handler duhaSunriseHandler;
    private Handler maintenanceHandler;
    private Handler textUpdateHandler;

    // Runnables for each handler
    private Runnable eventsRunnable;
    private Runnable mainRunnable;
    private Runnable timeUpdateRunnable;
    private Runnable athkarUpdateRunnable;
    private Runnable screenSaverRunnable;
    private Runnable duhaSunriseRunnable;
    private Runnable maintenanceRunnable;
    private Runnable textUpdateRunnable;

    // Handler state tracking
    private volatile boolean isEventsHandlerRunning = false;
    private volatile boolean isMainHandlerRunning = false;
    private volatile boolean isTimeUpdateHandlerRunning = false;
    private volatile boolean isAthkarUpdateHandlerRunning = false;
    private volatile boolean isScreenSaverHandlerRunning = false;
    private volatile boolean isDuhaSunriseHandlerRunning = false;
    private volatile boolean isMaintenanceHandlerRunning = false;
    private volatile boolean isTextUpdateHandlerRunning = false;

    // 40-minute restart after Athan feature
    private Handler restartAfterAthanHandler;
    private Runnable restartAfterAthanRunnable;
    private volatile boolean isRestartAfterAthanScheduled = false;
    private static final long RESTART_DELAY_AFTER_ATHAN = 40 * 60 * 1000; // 40 minutes in milliseconds
    private static final String RESTART_AFTER_ATHAN_KEY = "restart_after_athan_enabled";
    private static final String LAST_ATHAN_RESTART_KEY = "last_athan_restart_time";
    String monthString = "";
    String dayName = "";
    String dayNumber = "";
    boolean isShowingAthkar = false;
    boolean isShowingAnouncement = false;
    boolean isShowingPhotoGallery = false;
    // private static NavigationView navigationView;
    // private static DrawerLayout drawer;
    // private static ActionBarDrawerToggle toggle;
    private LinearLayout containerLinearLayout;
    private Dialog loadingDialog;
    private ViewGroup gregorian_month_container, hijri_month_container, timeNowLinearLayout, athkarContainer,
            prayerTimeContainer, announcementContainer, contentFajrLayout, fajrTimeLinearLayout,
            sunriseTimeLinearLayout,
            dhuhrTimeLinearLayout, asrTimeLinearLayout, maghribTimeLinearLayout, ishaTimeLinearLayout,
            fajrATimeLinearLayout,
            sunriseATimeLinearLayout, dhuhrATimeLinearLayout, asrATimeLinearLayout, maghribATimeLinearLayout,
            ishaATimeLinearLayout;
    // athkar
    private ImageView athkarImageView, dayimage, gregorian_month_image, hijri_month_image, containerImageView,
            backgroundImageView,
            alrabeeaTimesImageView, imageViewbrownnew, imageSunriseView, imagefajrView, imagedhuhrView, imageasrView,
            imagemaghribView,
            imageishaView, athkarIcon;
    private TextView athkarTimeTextView, timeNowTextView, timeNowTypeTextView, dateNowTextView, dateNowTextView2,
            dateHTextView,
            datehm, datehy, datey, datem, dayText, athkarTextView, fajrTextView, azanTextView, prayerTextView,
            ikamaTextView,
            fajrTimeTextView, fajrATimeTextView, remainingFajrTextView, sunriseTextView, sunriseTimeTextView,
            sunriseATimeTextView,
            remainingSunriseTextView, dhuhrTextView, dhuhrTextViewe, sunriseTextViewe, dhuhrTimeTextView,
            dhuhrATimeTextView, remainingDhuhrTextView,
            asrTextView, asrTimeTextView, asrATimeTextView, remainingAsrTextView, maghribTextView, maghribTimeTextView,
            maghribATimeTextView,
            remainingMaghribTextView, ishaTextView, ishaTimeTextView, ishaATimeTextView, remainingIshaTextView,
            remainingPrayerTextView, movingMessageTextView,
            fajrTimeTypeTextView, sunriseTimeTypeTextView, dhuhrTimeTypeTextView, asrTimeTypeTextView,
            maghribTimeTypeTextView, ishaTimeTypeTextView,
            fajrATimeTypeTextView, sunriseATimeTypeTextView, dhuhrATimeTypeTextView, asrATimeTypeTextView,
            maghribATimeTypeTextView, ishaATimeTypeTextView;
    private LinearLayout contentSunriseLayout, contentDhuhrLayout, contentAsrLayout, contentMaghribLayout,
            contentIshaLayout, athkarTime;
    private boolean longTextInRemainForPrayer;
    private boolean isJomaa;
    private boolean moveDateToUp;
    private ScreensaverScheduler screensaver;
    private TextDesignUiManager textDesign;
    private int lastDisplayedGalleryPhoto = -1;
    private String lastUpdatedTimeRemaining = "";
    private String gYear = "";
    private String gMonthAr = "";
    private String gMonthEn = "";
    private String gMonthNum = "";
    private String gDayNum = "";
    private String gDayNameAr = "";
    private String gDayNameEn = "";
    private String hYear = "";
    private String hMonthAr = "";
    private String hMonthEn = "";
    private String hDayNum = "";
    private boolean _dateDarkGreenLandscape_isHijri = false;
    private boolean _lastRefreshedIsDayName = false;
    private int _athkarCurrentDate = 1;
    /*
     * private String getPrayerAthkarImagePath(PrayerType prayerType) {
     * switch (prayerType) {
     * case Fajr:
     * case Maghrib:
     * return getAfterPrayer2AthkarImagePath();
     * default:
     * return getAfterPrayer1AthkarImagePath();
     * }
     * }
     */
    private String ssMiladiDate = "";
    private String ssHijriDate = "";
    private String ssDayName = "";

    private boolean showDuha = false; // only used for alternate showing of sunrise . duha
    private TextView tvIkamaDelayFajr, tvIkamaDelayDhur, tvIkamaDelayAsr, tvIkamaDelayMaghreb, tvIkamaDelayIsha;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Set up uncaught exception handler to restart app on crash
        setupCrashHandler();

        // Start the foreground service to keep the app alive
        // startAppMonitorService();

        adjustDisplayScale();
        uiTheme = HawkSettings.getCurrentTheme();
        lan = HawkSettings.getLocaleLanguage();
        Utils.initActivity(MainActivity.this);
        timingsAlrabeeaTimes = null;
        isLandscape = isLandscape();

        switch (HawkSettings.getAppOrientation()) {
            case 1:
                Log.e("Orientatiob", "switch to SCREEN_ORIENTATION_LANDSCAPE");
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                break;
            case 2:
                Log.e("Orientatiob", "switch to SCREEN_ORIENTATION_REVERSE_PORTRAIT");
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
                break;
            default:
                Log.e("Orientatiob", "switch to SCREEN_ORIENTATION_PORTRAIT");
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }

        setContentView(R.layout.activity_main);

        initView();
        SetAction();
        announcementManager = new AnnouncementManager(this);

        Resources resources = getResources();
        Configuration config = resources.getConfiguration();

        if (config.orientation == Configuration.ORIENTATION_PORTRAIT) {
            Log.e("Orientation", "Main Portrait");
        } else if (config.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Log.e("Orientation", "Main LANDSCAPE");
        }

        screensaver = new ScreensaverScheduler(this);
        textDesign = new TextDesignUiManager();
        initTextDesign();

        checkDuhaViews();

        setSunriseOrDuhaNamesTextView();
        initIkamaTimeViews();

        // Initialize handlers
        initializeHandlers();

        lazyInit();
    }

    /**
     * Initialize all handlers and their runnables
     */
    private void initializeHandlers() {
        try {
            Log.d(TAG, "Initializing handlers...");

            // Initialize handlers
            eventsHandler = new Handler(Looper.getMainLooper());
            mainHandler = new Handler(Looper.getMainLooper());
            timeUpdateHandler = new Handler(Looper.getMainLooper());
            athkarUpdateHandler = new Handler(Looper.getMainLooper());
            screenSaverHandler = new Handler(Looper.getMainLooper());
            duhaSunriseHandler = new Handler(Looper.getMainLooper());
            maintenanceHandler = new Handler(Looper.getMainLooper());
            textUpdateHandler = new Handler(Looper.getMainLooper());
            restartAfterAthanHandler = new Handler(Looper.getMainLooper());

            // Initialize runnables
            initializeRunnables();

            Log.d(TAG, "Handlers initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing handlers", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Initialize all runnables for handlers
     */
    private void initializeRunnables() {
        try {
            // Events runnable
            eventsRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isEventsHandlerRunning)
                            return;

                        List<Event> events = new ArrayList<>();
                        for (Event event : Repositories.getEventDb().getAllEnabled()) {
                            if (eventDayArrived(event))
                                events.add(event);
                        }

                        if (isAllowedToShowEvent() && events.size() > 0) {
                            StringBuilder eventText = new StringBuilder();
                            for (Event event : events) {
                                eventText.append("                ").append(event.text).append("                ");
                            }
                            showEvent(eventText.toString());

                            // Schedule next check in 10 minutes
                            if (isEventsHandlerRunning) {
                                eventsHandler.postDelayed(this, 10 * MINUTES_MILLI_SECOND);
                            }
                        } else {
                            // Check again in 10 seconds if not allowed to show events
                            if (isEventsHandlerRunning) {
                                eventsHandler.postDelayed(this, 10000);
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in events runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry in 30 seconds on error
                        if (isEventsHandlerRunning) {
                            eventsHandler.postDelayed(this, 30000);
                        }
                    }
                }
            };

            // Main logic runnable
            mainRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isMainHandlerRunning)
                            return;

                        sleepTimeUntilNextRefresh = 0;
                        log("Handler is alive");

                        if (!isPrayerListUpdating.get()) {
                            year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR, TempTime));
                            month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH, TempTime));
                            day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY, TempTime));
                            gregorianCalendar = new GregorianCalendar();
                            hijriCalendar = Utils.getUmmalquraCalendar();

                            if (timingsAlrabeeaTimes == null || timingsAlrabeeaTimes.getIntDay() != day
                                    || timingsAlrabeeaTimes.getIntMonth() != month) {
                                timingsAlrabeeaTimes = PrayerUtils.getTiming(year, month, day);
                            }

                            if (timingsAlrabeeaTimes == null) {
                                getPrayerTimesThisYear();
                            } else {
                                onResumeFunc();
                            }
                        }

                        // Schedule next run
                        long delay = sleepTimeUntilNextRefresh == 0 ? 100
                                : Math.max(100, getCurrentTimePlusMinute() - System.currentTimeMillis());
                        if (isMainHandlerRunning) {
                            mainHandler.postDelayed(this, delay);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in main runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry in 5 seconds on error
                        if (isMainHandlerRunning) {
                            mainHandler.postDelayed(this, 5000);
                        }
                    }
                }
            };

            // Time update runnable
            timeUpdateRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isTimeUpdateHandlerRunning)
                            return;

                        // if (isMidnight()) {
                        // restartApp();
                        // return;
                        // }

                        String time = Utils.getTimeNow();
                        String timeType = Utils.getTypeTimeNow();
                        setText(timeNowTextView, time);
                        setText(timeNowTypeTextView, timeType);
                        String timeDate = getAthkarDate() + " | " + time + " " + timeType;
                        setText(athkarTimeTextView, timeDate);

                        if (screensaver != null && screensaver.isEnabled()) {
                            screensaver.updateData(
                                    time,
                                    timeType,
                                    ssMiladiDate,
                                    ssHijriDate,
                                    ssDayName,
                                    announcementManager != null ? announcementManager.getFuneralInNextPrayer() : "");
                        }

                        // Schedule next update in 1 second
                        if (isTimeUpdateHandlerRunning) {
                            timeUpdateHandler.postDelayed(this, 1000);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in time update runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry in 1 second on error
                        if (isTimeUpdateHandlerRunning) {
                            timeUpdateHandler.postDelayed(this, 1000);
                        }
                    }
                }
            };

            // Athkar update runnable
            athkarUpdateRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isAthkarUpdateHandlerRunning)
                            return;

                        String[] athkars = getResources().getStringArray(R.array.athkars);
                        if (athkars.length > 0) {
                            if (lastAthkarIndex >= athkars.length) {
                                lastAthkarIndex = 0;
                            }
                            setText(athkarTextView, athkars[lastAthkarIndex]);
                            lastAthkarIndex++;

                            // Schedule next update in 45 seconds
                            if (isAthkarUpdateHandlerRunning) {
                                athkarUpdateHandler.postDelayed(this, 45000);
                            }
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in athkar update runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry in 45 seconds on error
                        if (isAthkarUpdateHandlerRunning) {
                            athkarUpdateHandler.postDelayed(this, 45000);
                        }
                    }
                }
            };

            // Screen saver runnable
            screenSaverRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isScreenSaverHandlerRunning)
                            return;

                        Log.d("screensaver", "handler check for " + ScreensaverScheduler.CheckFrequency);

                        HashMap<String, String> prayers = new HashMap<>();
                        boolean isCurrentlyPraying = false;
                        boolean isBetweenAzanIkama = false;
                        boolean isCurrentlyAthkar = false;

                        for (PrayerType prayerType : PrayerType.values()) {
                            PrayerTime prayerTime = prayerType.prayerTime;
                            prayers.put(prayerType.getKEY(), prayerTime.getTime());

                            Log.i("screensaver", "isCurrentlyPraying " + (prayerTime.isDuringPrayer()) + "  "
                                    + prayerType.getName());

                            if (prayerType.isFard()) {
                                isCurrentlyPraying = (prayerTime.isDuringPrayer()) || isCurrentlyPraying;
                                isCurrentlyAthkar = (prayerTime.isNowLockingDuringAthkar()) || isCurrentlyAthkar;
                                isBetweenAzanIkama = (prayerTime.isBetweenAzanAndIkama()) || isBetweenAzanIkama;
                            }
                        }

                        if (screensaver != null) {
                            screensaver.setPrayers(prayers);
                            screensaver.setIsPrayingTime(isCurrentlyPraying);
                            screensaver.setIsBetweenPrayerIkama(isBetweenAzanIkama);
                            screensaver.isShowingAthkar(isCurrentlyAthkar);
                            screensaver.checkAndStart(MainActivity.this);

                            if (screensaver.isEnabled() && alert != null) {
                                alert.setVisibility(View.GONE);
                            }
                        }

                        // Schedule next check
                        if (isScreenSaverHandlerRunning) {
                            screenSaverHandler.postDelayed(this, ScreensaverScheduler.CheckFrequency * 1000);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in screen saver runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry in default interval on error
                        if (isScreenSaverHandlerRunning) {
                            screenSaverHandler.postDelayed(this, ScreensaverScheduler.CheckFrequency * 1000);
                        }
                    }
                }
            };

            // Duha sunrise runnable
            duhaSunriseRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isDuhaSunriseHandlerRunning)
                            return;

                        showDuha = !showDuha;
                        Log.d("DUHA", "Show duha " + showDuha);
                        setSunriseOrDuhaNamesTextView();

                        // Schedule next update in 10 seconds
                        if (isDuhaSunriseHandlerRunning) {
                            duhaSunriseHandler.postDelayed(this, 10000);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in duha sunrise runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry in 10 seconds on error
                        if (isDuhaSunriseHandlerRunning) {
                            duhaSunriseHandler.postDelayed(this, 10000);
                        }
                    }
                }
            };

            // Maintenance runnable
            maintenanceRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isMaintenanceHandlerRunning)
                            return;

                        final Activity activity = MainActivity.this;
                        if (isActivityInForeground(activity, MainActivity.class) &&
                                (screensaver == null || !screensaver.isEnabled())) {

                            // Check handler health
                            checkHandlerHealth();

                            // Restart handlers if needed
                            startHandlers();

                            // Force garbage collection periodically
                            System.gc();
                        }

                        // Schedule next check in 60 seconds
                        if (isMaintenanceHandlerRunning) {
                            maintenanceHandler.postDelayed(this, 60 * 1000);
                        }
                    } catch (Exception e) {
                        handleHandlerError("maintenance", e, this, maintenanceHandler, 60 * 1000);
                    }
                }
            };

            // Restart after Athan runnable
            restartAfterAthanRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        Log.i(TAG, "40 minutes have passed after Athan, restarting app");

                        // Save the restart time
                        saveLastAthanRestartTime();

                        // Reset the scheduled flag
                        isRestartAfterAthanScheduled = false;

                        // Restart the app
                        restartAppAfterAthan();

                    } catch (Exception e) {
                        Log.e(TAG, "Error in restart after Athan runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        isRestartAfterAthanScheduled = false;
                    }
                }
            };

            Log.d(TAG, "Runnables initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing runnables", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void lazyInit() {
        try {
            Handler handler = new Handler(Looper.getMainLooper());
            Context context = this;

            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        runOnUiThread(() -> {
                            try {
                                if (screensaver != null && !screensaver.isEnabled() &&
                                        !isShowingAthkar && !isShowingPhotoGallery &&
                                        textDesign != null && containerLinearLayout != null) {
                                    textDesign.savePreview(context, containerLinearLayout,
                                            (HawkSettings.getAppOrientation() == 1));
                                }

                                if (uiTheme != null) {
                                    FirebaseCrashlytics.getInstance().setCustomKey("Theme", uiTheme.name());
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error in lazyInit UI thread", e);
                                CrashlyticsUtils.INSTANCE.logException(e);
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "Error in lazyInit runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                }
            }, 1500);

            startMaintenanceHandler();

        } catch (Exception e) {
            Log.e(TAG, "Error in lazyInit", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initIkamaTimeViews() {
        runOnUiThread(() -> {
            try {
                Log.d(TAG, "Initializing Ikama time views");

                // Initialize TextViews with null checks
                tvIkamaDelayFajr = findViewById(R.id.tv_prayer_ikama_time_fajr);
                tvIkamaDelayDhur = findViewById(R.id.tv_prayer_ikama_time_dhur);
                tvIkamaDelayAsr = findViewById(R.id.tv_prayer_ikama_time_asr);
                tvIkamaDelayMaghreb = findViewById(R.id.tv_prayer_ikama_time_maghrib);
                tvIkamaDelayIsha = findViewById(R.id.tv_prayer_ikama_time_isha);

                if (!HawkSettings.getEnableShowIkamaDelay()) {
                    Log.d(TAG, "Ikama delay display is disabled");
                    return;
                }

                // Check for Ramadan settings - TODO: Implement proper Ramadan detection
                boolean isRamadanSettings = isCurrentlyRamadan();

                // Get default times based on Ramadan settings
                int timeFajrDefault = isRamadanSettings
                        ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT
                        : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT;
                int timeDhurDefault = isRamadanSettings
                        ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT
                        : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT;
                int timeAsrDefault = isRamadanSettings
                        ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT
                        : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT;
                int timeMaghrebDefault = isRamadanSettings
                        ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT
                        : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT;
                int timeIshaDefault = isRamadanSettings
                        ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT
                        : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT;

                // Get saved times from preferences
                int fajrTime = Hawk.get(TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ConstantsOfApp.FAJR_KEY, timeFajrDefault);
                int dhurTime = Hawk.get(TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ConstantsOfApp.DHUHR_KEY, timeDhurDefault);
                int asrTime = Hawk.get(TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ConstantsOfApp.ASR_KEY, timeAsrDefault);
                int maghrebTime = Hawk.get(TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ConstantsOfApp.MAGHRIB_KEY,
                        timeMaghrebDefault);
                int ishaTime = Hawk.get(TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ConstantsOfApp.ISHA_KEY, timeIshaDefault);

                String minute = getString(R.string.minute_abbrev);

                // Set text and visibility with null checks
                setIkamaTimeText(tvIkamaDelayFajr, fajrTime, minute);
                setIkamaTimeText(tvIkamaDelayDhur, dhurTime, minute);
                setIkamaTimeText(tvIkamaDelayAsr, asrTime, minute);
                setIkamaTimeText(tvIkamaDelayMaghreb, maghrebTime, minute);
                setIkamaTimeText(tvIkamaDelayIsha, ishaTime, minute);

                Log.d(TAG, "Ikama time views initialized successfully");

            } catch (Exception e) {
                Log.e(TAG, "Error initializing Ikama time views", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    /**
     * Helper method to set Ikama time text with null checks
     */
    private void setIkamaTimeText(TextView textView, int time, String minute) {
        if (textView != null) {
            textView.setText(time + minute);
            textView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Check if it's currently Ramadan
     * TODO: Implement proper Ramadan detection based on Hijri calendar
     */
    private boolean isCurrentlyRamadan() {
        // For now, return false. This should be implemented to check
        // if the current Hijri month is Ramadan (month 9)
        try {
            UmmalquraCalendar hijriCal = Utils.getUmmalquraCalendar();
            return hijriCal.get(Calendar.MONTH) == 8; // Ramadan is month 9 (0-indexed)
        } catch (Exception e) {
            Log.e(TAG, "Error checking Ramadan status", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    private void checkDuhaViews() {
        try {
            Log.d(TAG, "Checking Duha views configuration");

            int showDuhaSetting = HawkSettings.getShowDuhaSetting();
            Log.d(TAG, "Show Duha setting: " + showDuhaSetting);

            if (showDuhaSetting == 2) {
                Log.d(TAG, "Hiding sunrise views as per Duha setting");

                // Hide sunrise time views
                setVisibility(sunriseTimeTextView, View.GONE);
                setVisibility(sunriseATimeTextView, View.GONE);
                setVisibility(sunriseTimeTypeTextView, View.GONE);
                setVisibility(sunriseATimeTypeTextView, View.GONE);
                setVisibility(sunriseTextView, View.GONE);

                // Hide sunrise layout containers
                setVisibility(contentSunriseLayout, View.GONE);
                setVisibility(remainingSunriseTextView, View.GONE);
                setVisibility(sunriseTimeLinearLayout, View.GONE);
                setVisibility(sunriseATimeLinearLayout, View.GONE);

                // Hide English sunrise text view if exists
                View tvSunrise = findViewById(R.id.sunrise_TextView_MainActivity_en);
                if (tvSunrise != null) {
                    tvSunrise.setVisibility(View.GONE);
                    Log.d(TAG, "Hidden English sunrise TextView");
                }

                Log.d(TAG, "Successfully hidden all sunrise views");
            } else {
                Log.d(TAG, "Sunrise views will remain visible");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking Duha views", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void showTextDesign() {
        runOnUiThread(() -> {
            try {
                Log.d(TAG, "Showing text design");
                AbsoluteLayout abs = findViewById(R.id.abs_text_design);
                if (abs != null) {
                    abs.setVisibility(View.VISIBLE);
                    Log.d(TAG, "Text design shown successfully");
                } else {
                    Log.w(TAG, "Text design layout not found");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error showing text design", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    private void hideTextDesign() {
        try {
            runOnUiThread(() -> {
                try {
                    Log.d(TAG, "Hiding text design");
                    AbsoluteLayout abs = findViewById(R.id.abs_text_design);
                    if (abs != null) {
                        abs.setVisibility(View.INVISIBLE);
                        Log.d(TAG, "Text design hidden successfully");
                    } else {
                        Log.w(TAG, "Text design layout not found");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in hideTextDesign UI thread", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error hiding text design", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initTextDesign() {
        try {
            Log.d(TAG, "Initializing text design");

            AbsoluteLayout abs = findViewById(R.id.abs_text_design);
            if (abs == null) {
                Log.w(TAG, "Text design layout not found");
                return;
            }

            if (textDesign == null || !textDesign.isEnabled(this)) {
                abs.setVisibility(View.GONE);
                Log.d(TAG, "Text design disabled or not available");
                return;
            }

            boolean isSecond = textDesign.isSecondEnabled(this);
            Log.d(TAG, "Text design enabled, second design: " + isSecond);

            abs.setVisibility(View.VISIBLE);
            TextView tvDesign1 = findViewById(R.id.tv_text_design1);
            TextView tvDesign2 = findViewById(R.id.tv_text_design2);

            if (tvDesign1 == null) {
                Log.e(TAG, "Text design TextView 1 not found");
                return;
            }

            if (isSecond && tvDesign2 == null) {
                Log.e(TAG, "Text design TextView 2 not found but second design is enabled");
                return;
            }

            // Initially hide the text views
            tvDesign1.setVisibility(View.INVISIBLE);
            if (tvDesign2 != null) {
                tvDesign2.setVisibility(View.INVISIBLE);
            }

            // Configure first text design
            configureTextDesignView(tvDesign1, textDesign.getData1(this));

            // Configure second text design if enabled
            if (isSecond && tvDesign2 != null) {
                configureTextDesignView(tvDesign2, textDesign.getData2(this));
            }

            // Show text views after delay
            Context context = this;
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        textDesign.restoreTextPosition(context, tvDesign1, false);
                        if (isSecond && tvDesign2 != null) {
                            textDesign.restoreTextPosition(context, tvDesign2, true);
                        }

                        tvDesign1.setVisibility(View.VISIBLE);
                        if (isSecond && tvDesign2 != null) {
                            tvDesign2.setVisibility(View.VISIBLE);
                        }

                        Log.d(TAG, "Text design views made visible");
                    } catch (Exception e) {
                        Log.e(TAG, "Error showing text design views", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                }
            }, textDesign.getSHOW_DELAY());

            Log.d(TAG, "Text design initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing text design", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Helper method to configure text design view properties
     */
    private void configureTextDesignView(TextView textView, Object textData) {
        try {
            if (textView == null || textData == null) {
                Log.w(TAG, "Cannot configure text design view - null parameters");
                return;
            }

            FontMapper mapper = new FontMapper();

            // Set typeface
            textView.setTypeface(ResourcesCompat.getFont(this,
                    mapper.getFontRes(textDesign.getData1(this).getFont())));

            // Set text color
            textView.setTextColor(textDesign.getData1(this).getColor());

            // Set text size
            float size = textDesign.getData1(this).getSize();
            textView.setTextSize(size);

            // Set text content
            textView.setText(textDesign.getData1(this).getText());

        } catch (Exception e) {
            Log.e(TAG, "Error configuring text design view", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    protected void onResume() {
        try {
            Log.d(TAG, "MainActivity onResume started");
            super.onResume();

            // Update current time
            TempTime = System.currentTimeMillis();
            Log.d(TAG, "Updated TempTime: " + TempTime);

            // Start all necessary handlers
            startHandlers();

            // Apply custom theme settings
            setCustomSettingForThemes();

            // Handle news display
            handleNewsDisplay();

            Log.d(TAG, "MainActivity onResume completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error in onResume", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Handle news display logic
     */
    private void handleNewsDisplay() {
        try {
            News news = HawkSettings.getCurrentNews();
            if (news == null || !news.isActive) {
                Log.d(TAG, "No active news to display");
                setVisibility(movingMessageTextView, View.GONE);
                return;
            }

            Calendar start = Utils.getCalendar(news.sDate);
            Calendar end = Utils.getCalendar(news.eDate);
            Calendar now = Calendar.getInstance();

            if (start == null || end == null) {
                Log.w(TAG, "Invalid news date range");
                setVisibility(movingMessageTextView, View.GONE);
                return;
            }

            if (now.after(start) && now.before(end)) {
                Log.d(TAG, "Displaying active news");
                setText(movingMessageTextView, news.text);
                setVisibility(movingMessageTextView, View.VISIBLE);
            } else {
                Log.d(TAG, "News is not in active date range");
                setVisibility(movingMessageTextView, View.GONE);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error handling news display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // Hide news on error
            setVisibility(movingMessageTextView, View.GONE);
        }
    }

    private void startHandlers() {
        try {
            Log.d(TAG, "Starting all handlers...");

            startMainHandler();
            startTimeUpdateHandler();
            startScreenSaverHandler();

            if (HawkSettings.getEventsEnabled()) {
                startEventsHandler();
            }

            if (uiTheme == UITheme.BROWN_NEW_3) {
                startAthkarUpdateHandler();
            }

            if (HawkSettings.getShowDuhaSetting() == 3) {
                startDuhaSunriseHandler();
            }

            Log.d(TAG, "All handlers started successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error starting handlers", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Start individual handlers
     */
    private void startMainHandler() {
        if (!isMainHandlerRunning && mainHandler != null && mainRunnable != null) {
            isMainHandlerRunning = true;
            mainHandler.post(mainRunnable);
            Log.d(TAG, "Main handler started");
        }
    }

    private void startTimeUpdateHandler() {
        if (!isTimeUpdateHandlerRunning && timeUpdateHandler != null && timeUpdateRunnable != null) {
            isTimeUpdateHandlerRunning = true;
            timeUpdateHandler.post(timeUpdateRunnable);
            Log.d(TAG, "Time update handler started");
        }
    }

    private void startScreenSaverHandler() {
        if (!isScreenSaverHandlerRunning && screenSaverHandler != null && screenSaverRunnable != null) {
            isScreenSaverHandlerRunning = true;
            screenSaverHandler.postDelayed(screenSaverRunnable, 2000); // Start after 2 seconds
            Log.d(TAG, "Screen saver handler started");
        }
    }

    private void startEventsHandler() {
        if (!isEventsHandlerRunning && eventsHandler != null && eventsRunnable != null) {
            isEventsHandlerRunning = true;
            eventsHandler.postDelayed(eventsRunnable, 3000); // Start after 3 seconds
            Log.d(TAG, "Events handler started");
        }
    }

    private void startAthkarUpdateHandler() {
        if (!isAthkarUpdateHandlerRunning && athkarUpdateHandler != null && athkarUpdateRunnable != null) {
            isAthkarUpdateHandlerRunning = true;
            athkarUpdateHandler.postDelayed(athkarUpdateRunnable, 5000); // Start after 5 seconds
            Log.d(TAG, "Athkar update handler started");
        }
    }

    private void startDuhaSunriseHandler() {
        if (!isDuhaSunriseHandlerRunning && duhaSunriseHandler != null && duhaSunriseRunnable != null) {
            isDuhaSunriseHandlerRunning = true;
            duhaSunriseHandler.postDelayed(duhaSunriseRunnable, 5000); // Start after 5 seconds
            Log.d(TAG, "Duha sunrise handler started");
        }
    }

    private void startMaintenanceHandler() {
        if (!isMaintenanceHandlerRunning && maintenanceHandler != null && maintenanceRunnable != null) {
            isMaintenanceHandlerRunning = true;
            maintenanceHandler.postDelayed(maintenanceRunnable, 30000); // Start after 30 seconds
            Log.d(TAG, "Maintenance handler started");
        }
    }

    /**
     * Stop all handlers
     */
    private void stopAllHandlers() {
        try {
            Log.d(TAG, "Stopping all handlers...");

            // Stop main handler
            isMainHandlerRunning = false;
            if (mainHandler != null && mainRunnable != null) {
                mainHandler.removeCallbacks(mainRunnable);
            }

            // Stop time update handler
            isTimeUpdateHandlerRunning = false;
            if (timeUpdateHandler != null && timeUpdateRunnable != null) {
                timeUpdateHandler.removeCallbacks(timeUpdateRunnable);
            }

            // Stop screen saver handler
            isScreenSaverHandlerRunning = false;
            if (screenSaverHandler != null && screenSaverRunnable != null) {
                screenSaverHandler.removeCallbacks(screenSaverRunnable);
            }

            // Stop events handler
            isEventsHandlerRunning = false;
            if (eventsHandler != null && eventsRunnable != null) {
                eventsHandler.removeCallbacks(eventsRunnable);
            }

            // Stop athkar update handler
            isAthkarUpdateHandlerRunning = false;
            if (athkarUpdateHandler != null && athkarUpdateRunnable != null) {
                athkarUpdateHandler.removeCallbacks(athkarUpdateRunnable);
            }

            // Stop duha sunrise handler
            isDuhaSunriseHandlerRunning = false;
            if (duhaSunriseHandler != null && duhaSunriseRunnable != null) {
                duhaSunriseHandler.removeCallbacks(duhaSunriseRunnable);
            }

            // Stop maintenance handler
            isMaintenanceHandlerRunning = false;
            if (maintenanceHandler != null && maintenanceRunnable != null) {
                maintenanceHandler.removeCallbacks(maintenanceRunnable);
            }

            // Stop text update handler
            isTextUpdateHandlerRunning = false;
            if (textUpdateHandler != null && textUpdateRunnable != null) {
                textUpdateHandler.removeCallbacks(textUpdateRunnable);
            }

            // Cancel restart after Athan
            cancelRestartAfterAthan();

            Log.d(TAG, "All handlers stopped successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping handlers", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            savedInstanceState.putAll(getIntent().getExtras());
        }
        super.onSaveInstanceState(savedInstanceState);
    }

    @Override
    protected void onStart() {
        // prayerApi =
        // Utils.getPrayerTimesForYear(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
        super.onStart();

        try {
            setSelected(movingMessageTextView);
            setSelected(dateNowTextView);
            setSelected(remainingPrayerTextView);
            setSelected(datehm);
            setSelected(remainingFajrTextView);
            if (remainingSunriseTextView != null && remainingSunriseTextView.getVisibility() == View.VISIBLE)
                setSelected(remainingSunriseTextView);

            setSelected(remainingDhuhrTextView);
            setSelected(remainingAsrTextView);
            setSelected(remainingMaghribTextView);
            setSelected(remainingIshaTextView);
            setSelected(datem);
            setLayoutDirection();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }

    private void setLayoutDirection() {

        // safeSetLayoutDirection(contentPrayerItemLayout);
        safeSetLayoutDirection(timeNowLinearLayout);

        safeSetLayoutDirection(fajrTimeLinearLayout);
        safeSetLayoutDirection(sunriseTimeLinearLayout);
        safeSetLayoutDirection(dhuhrTimeLinearLayout);
        safeSetLayoutDirection(asrTimeLinearLayout);
        safeSetLayoutDirection(maghribTimeLinearLayout);
        safeSetLayoutDirection(ishaTimeLinearLayout);
        safeSetLayoutDirection(fajrATimeLinearLayout);
        safeSetLayoutDirection(sunriseATimeLinearLayout);
        safeSetLayoutDirection(dhuhrATimeLinearLayout);
        safeSetLayoutDirection(asrATimeLinearLayout);
        safeSetLayoutDirection(maghribATimeLinearLayout);
        safeSetLayoutDirection(ishaATimeLinearLayout);
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    private void onResumeFunc() {
        refreshDate();
        selectNextPrayer();
    }

    @Override
    protected void onPause() {
        try {
            Log.d(TAG, "Stopping all handlers...");

            stopAllHandlers();

            if (announcementManager != null) {
                announcementManager.onStop();
            }

            Log.d(TAG, "All handlers stopped successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping handlers", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        try {
            Log.d(TAG, "MainActivity onDestroy started");

            // Stop all handlers first
            stopAllHandlers();

            // Clean up resources
            cleanupResources();

            // Ensure service keeps running even if activity is destroyed
            // startAppMonitorService();

            Log.d(TAG, "MainActivity onDestroy completed");
        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        super.onDestroy();
    }

    /**
     * Clean up resources to prevent memory leaks
     */
    private void cleanupResources() {
        try {
            // Clean up announcement manager
            if (announcementManager != null) {
                announcementManager.onStop();
                announcementManager = null;
            }

            // Clean up screensaver
            screensaver = null;

            // Clean up text design
            textDesign = null;

            // Clean up dialog
            if (loadingDialog != null && loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }
            loadingDialog = null;

            // Clean up alert
            if (alert != null) {
                alert.setVisibility(View.GONE);
                alert = null;
            }

            // Clear handlers
            eventsHandler = null;
            mainHandler = null;
            timeUpdateHandler = null;
            athkarUpdateHandler = null;
            screenSaverHandler = null;
            duhaSunriseHandler = null;
            maintenanceHandler = null;
            textUpdateHandler = null;
            restartAfterAthanHandler = null;

            // Clear runnables
            eventsRunnable = null;
            mainRunnable = null;
            timeUpdateRunnable = null;
            athkarUpdateRunnable = null;
            screenSaverRunnable = null;
            duhaSunriseRunnable = null;
            maintenanceRunnable = null;
            textUpdateRunnable = null;
            restartAfterAthanRunnable = null;

            // Force garbage collection
            System.gc();

            Log.d(TAG, "Resources cleaned up successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up resources", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Handle handler errors gracefully
     */
    private void handleHandlerError(String handlerName, Exception e, Runnable runnable, Handler handler,
            long retryDelay) {
        try {
            Log.e(TAG, "Error in " + handlerName + " handler", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Retry the handler after a delay if it's still supposed to be running
            if (handler != null && runnable != null) {
                handler.postDelayed(runnable, retryDelay);
                Log.d(TAG, handlerName + " handler will retry in " + retryDelay + "ms");
            }
        } catch (Exception ex) {
            Log.e(TAG, "Error handling " + handlerName + " handler error", ex);
            CrashlyticsUtils.INSTANCE.logException(ex);
        }
    }

    /**
     * Check if handlers are healthy and restart if needed
     */
    private void checkHandlerHealth() {
        try {
            // This method can be called periodically to ensure handlers are running
            if (isMainHandlerRunning && (mainHandler == null || mainRunnable == null)) {
                Log.w(TAG, "Main handler is supposed to be running but is null, restarting...");
                startMainHandler();
            }

            if (isTimeUpdateHandlerRunning && (timeUpdateHandler == null || timeUpdateRunnable == null)) {
                Log.w(TAG, "Time update handler is supposed to be running but is null, restarting...");
                startTimeUpdateHandler();
            }

            if (isScreenSaverHandlerRunning && (screenSaverHandler == null || screenSaverRunnable == null)) {
                Log.w(TAG, "Screen saver handler is supposed to be running but is null, restarting...");
                startScreenSaverHandler();
            }

            if (isEventsHandlerRunning && (eventsHandler == null || eventsRunnable == null)) {
                Log.w(TAG, "Events handler is supposed to be running but is null, restarting...");
                startEventsHandler();
            }

            if (isAthkarUpdateHandlerRunning && (athkarUpdateHandler == null || athkarUpdateRunnable == null)) {
                Log.w(TAG, "Athkar update handler is supposed to be running but is null, restarting...");
                startAthkarUpdateHandler();
            }

            if (isDuhaSunriseHandlerRunning && (duhaSunriseHandler == null || duhaSunriseRunnable == null)) {
                Log.w(TAG, "Duha sunrise handler is supposed to be running but is null, restarting...");
                startDuhaSunriseHandler();
            }

            if (isMaintenanceHandlerRunning && (maintenanceHandler == null || maintenanceRunnable == null)) {
                Log.w(TAG, "Maintenance handler is supposed to be running but is null, restarting...");
                startMaintenanceHandler();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error checking handler health", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void setupCrashHandler() {
        Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread thread, Throwable throwable) {
                Log.e(TAG, "Uncaught exception", throwable);
                CrashlyticsUtils.INSTANCE.logException(throwable);

                // Restart the app
                Intent intent = new Intent(MainActivity.this, MainActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                        | Intent.FLAG_ACTIVITY_CLEAR_TASK
                        | Intent.FLAG_ACTIVITY_NEW_TASK);

                startActivity(intent);

                // Kill the current process
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(10);
            }
        });
    }

    /**
     * Check if app is default launcher and show reminder if needed
     */
    private void checkLauncherStatusAndShowReminder() {
        try {
            if (!isDefaultLauncher()) {
                Log.d(TAG, "App is not default launcher, scheduling reminder");

                // Use main handler for delayed reminder instead of creating new handler
                if (mainHandler != null) {
                    mainHandler.postDelayed(() -> {
                        try {
                            if (!isDefaultLauncher()) {
                                // Show a non-intrusive reminder
                                Toast.makeText(this,
                                        "For best performance, set this app as your default Home app",
                                        Toast.LENGTH_LONG).show();
                                Log.d(TAG, "Showed launcher reminder to user");
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error showing launcher reminder", e);
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }, 60000); // Check after 1 minute
                }
            } else {
                Log.d(TAG, "App is already default launcher");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking launcher status", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private boolean isDefaultLauncher() {
        try {
            final IntentFilter filter = new IntentFilter(Intent.ACTION_MAIN);
            filter.addCategory(Intent.CATEGORY_HOME);

            List<IntentFilter> filters = new ArrayList<>();
            List<ComponentName> activities = new ArrayList<>();

            getPackageManager().getPreferredActivities(filters, activities, getPackageName());

            boolean isDefault = activities.size() > 0;
            Log.d(TAG, "Is default launcher: " + isDefault);
            return isDefault;

        } catch (Exception e) {
            Log.e(TAG, "Error checking if default launcher", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false; // Assume not default on error
        }
    }

    private void addAppDrawerButton() {
        // try {
        // Create a floating action button or some other UI element
        // that allows users to access other apps

        // This is a simple implementation - you might want to create a more
        // sophisticated app drawer in a real launcher

        // Find a suitable container to add the button to
        // ViewGroup container = findViewById(R.id.containerLinearLayout);
        // if (container != null) {
        // // Create a button
        // androidx.appcompat.widget.AppCompatButton appDrawerButton = new
        // androidx.appcompat.widget.AppCompatButton(this);
        // appDrawerButton.setText("Apps");
        // appDrawerButton.setBackgroundColor(getResources().getColor(android.R.color.darker_gray));
        // appDrawerButton.setTextColor(getResources().getColor(android.R.color.white));
        //
        // // Set button size and position
        // LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
        // LinearLayout.LayoutParams.WRAP_CONTENT,
        // LinearLayout.LayoutParams.WRAP_CONTENT);
        // params.gravity = android.view.Gravity.CENTER_HORIZONTAL |
        // android.view.Gravity.BOTTOM;
        // params.setMargins(0, 0, 0, 50); // Bottom margin
        // appDrawerButton.setLayoutParams(params);
        //
        // // Add click listener to show all apps
        // appDrawerButton.setOnClickListener(v -> {
        // // Open the app drawer (all apps)
        // try {
        // Intent intent = new Intent(Intent.ACTION_MAIN);
        // intent.addCategory(Intent.CATEGORY_LAUNCHER);
        // startActivity(intent);
        // } catch (Exception e) {
        // Log.e(TAG, "Error opening app drawer", e);
        // CrashlyticsUtils.INSTANCE.logException(e);
        //
        // // Fallback to settings
        // try {
        // Intent intent = new Intent(android.provider.Settings.ACTION_SETTINGS);
        // startActivity(intent);
        // } catch (Exception ex) {
        // Log.e(TAG, "Error opening settings", ex);
        // CrashlyticsUtils.INSTANCE.logException(ex);
        // }
        // }
        // });
        //
        // // Add the button to the container
        // container.addView(appDrawerButton);
        // }
        // } catch (Exception e) {
        // Log.e(TAG, "Error adding app drawer button", e);
        // CrashlyticsUtils.INSTANCE.logException(e);
        // }
    }

    private static final int NOTIFICATION_PERMISSION_CODE = 100;

    private void requestNotificationPermission() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Log.d(TAG, "Checking notification permission for Android 13+");

                if (checkSelfPermission(
                        android.Manifest.permission.POST_NOTIFICATIONS) != android.content.pm.PackageManager.PERMISSION_GRANTED) {

                    Log.d(TAG, "Requesting notification permission");
                    requestPermissions(
                            new String[] { android.Manifest.permission.POST_NOTIFICATIONS },
                            NOTIFICATION_PERMISSION_CODE);
                } else {
                    Log.d(TAG, "Notification permission already granted");
                }
            } else {
                Log.d(TAG, "Android version < 13, notification permission not required");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error requesting notification permission", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
            @NonNull int[] grantResults) {
        try {
            Log.d(TAG, "Permission result received for request code: " + requestCode);
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);

            if (requestCode == NOTIFICATION_PERMISSION_CODE) {
                // If request is cancelled, the result arrays are empty
                if (grantResults.length > 0 &&
                        grantResults[0] == android.content.pm.PackageManager.PERMISSION_GRANTED) {

                    // Permission was granted
                    Log.d(TAG, "Notification permission granted by user");

                    // Restart the service to ensure it has notification permission
                    try {
                        Intent serviceIntent = new Intent(this, AppMonitorService.class);
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            startForegroundService(serviceIntent);
                        } else {
                            startService(serviceIntent);
                        }
                        Log.d(TAG, "AppMonitorService restarted with notification permission");
                    } catch (Exception e) {
                        Log.e(TAG, "Error restarting service after permission grant", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                } else {
                    // Permission denied - inform the user that the app needs this permission
                    Log.d(TAG, "Notification permission denied by user");
                    showNotificationPermissionExplanationDialog();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling permission result", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void showNotificationPermissionExplanationDialog() {
        try {
            Log.d(TAG, "Showing notification permission explanation dialog");

            androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);

            builder.setTitle("Notification Permission Required")
                    .setMessage("This app needs notification permission to keep running in the background. " +
                            "Please enable notifications in app settings.")
                    .setPositiveButton("Open Settings", (dialog, which) -> {
                        try {
                            // Open app settings
                            Intent intent = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                            Uri uri = Uri.fromParts("package", getPackageName(), null);
                            intent.setData(uri);
                            startActivity(intent);
                            Log.d(TAG, "Opened app settings for notification permission");
                        } catch (Exception e) {
                            Log.e(TAG, "Error opening app settings", e);
                            CrashlyticsUtils.INSTANCE.logException(e);
                            Toast.makeText(this, "Unable to open settings", Toast.LENGTH_SHORT).show();
                        }
                    })
                    .setNegativeButton("Cancel", (dialog, which) -> {
                        try {
                            dialog.dismiss();
                            Log.d(TAG, "User cancelled notification permission dialog");
                        } catch (Exception e) {
                            Log.e(TAG, "Error dismissing dialog", e);
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    })
                    .setCancelable(false)
                    .show();

            Log.d(TAG, "Notification permission dialog shown");

        } catch (Exception e) {
            Log.e(TAG, "Error showing notification permission dialog", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initView() {
        Log.e(TAG, "initView");
        // drawer = findViewById(R.id.drawer_layout);
        // navigationView = findViewById(R.id.nav_view);
        // toggle = new ActionBarDrawerToggle(
        // this, drawer, null, R.string.navigation_drawer_open,
        // R.string.navigation_drawer_close);

        containerLinearLayout = findViewById(R.id.container_LinearLayout_MainActivity);

        setAppTheme();

        backgroundImageView = containerLinearLayout.findViewById(R.id.background_ImageView_MainActivity);

        gregorian_month_container = containerLinearLayout.findViewById(R.id.gregorian_month_container);
        hijri_month_container = containerLinearLayout.findViewById(R.id.hijri_month_container);

        gregorian_month_image = containerLinearLayout.findViewById(R.id.gregorian_month_image);
        hijri_month_image = containerLinearLayout.findViewById(R.id.hijri_month_image);

        dayimage = containerLinearLayout.findViewById(R.id.dayimage);

        timeNowLinearLayout = containerLinearLayout.findViewById(R.id.timeNow_LinearLayout_MainActivity);
        timeNowTextView = containerLinearLayout.findViewById(R.id.timeNow_TextView_MainActivity);
        timeNowTypeTextView = containerLinearLayout.findViewById(R.id.timeNowType_TextView_MainActivity);
        dateNowTextView = containerLinearLayout.findViewById(R.id.dateNow_TextView_MainActivity);
        dateNowTextView2 = containerLinearLayout.findViewById(R.id.dateNow2_TextView_MainActivity);
        datehm = containerLinearLayout.findViewById(R.id.datehm);
        containerImageView = containerLinearLayout.findViewById(R.id.container_ImageView_MainActivity1);
        datehy = containerLinearLayout.findViewById(R.id.datehy);
        datey = containerLinearLayout.findViewById(R.id.datey);
        dayText = containerLinearLayout.findViewById(R.id.day_text);
        datem = containerLinearLayout.findViewById(R.id.datem);
        dateHTextView = containerLinearLayout.findViewById(R.id.dateNow1_TextView_MainActivity);
        alrabeeaTimesImageView = containerLinearLayout.findViewById(R.id.alrabeeaTimes_ImageView_MainActivity);
        loadingDialog = Utils.initLoadingDialog(MainActivity.this);
        contentFajrLayout = containerLinearLayout.findViewById(R.id.contentFajr_LinearLayout_MainActivity);
        remainingPrayerTextView = containerLinearLayout.findViewById(R.id.remainingPrayer_TextView_MainActivity);
        fajrTextView = containerLinearLayout.findViewById(R.id.fajr_TextView_MainActivity);
        azanTextView = containerLinearLayout.findViewById(R.id.azan_TextView_MainActivity);
        prayerTextView = containerLinearLayout.findViewById(R.id.prayer_TextView_MainActivity);
        ikamaTextView = containerLinearLayout.findViewById(R.id.ikama_TextView_MainActivity);
        fajrTimeTextView = containerLinearLayout.findViewById(R.id.fajrTime_TextView_MainActivity);
        remainingFajrTextView = containerLinearLayout.findViewById(R.id.remainingFajr_TextView_MainActivity);
        contentSunriseLayout = containerLinearLayout.findViewById(R.id.contentSunrise_LinearLayout_MainActivity);
        sunriseTextView = containerLinearLayout.findViewById(R.id.sunrise_TextView_MainActivity);
        sunriseTimeTextView = containerLinearLayout.findViewById(R.id.sunriseTime_TextView_MainActivity);
        remainingSunriseTextView = containerLinearLayout.findViewById(R.id.remainingSunrise_TextView_MainActivity);
        contentDhuhrLayout = containerLinearLayout.findViewById(R.id.contentDhuhr_LinearLayout_MainActivity);
        dhuhrTextView = containerLinearLayout.findViewById(R.id.dhuhr_TextView_MainActivity);
        dhuhrTextViewe = containerLinearLayout.findViewById(R.id.dhuhr_TextView_MainActivityE);
        sunriseTextViewe = containerLinearLayout.findViewById(R.id.sunrise_TextView_MainActivityE);
        dhuhrTimeTextView = containerLinearLayout.findViewById(R.id.dhuhrTime_TextView_MainActivity);
        remainingDhuhrTextView = containerLinearLayout.findViewById(R.id.remainingDhuhr_TextView_MainActivity);
        contentAsrLayout = containerLinearLayout.findViewById(R.id.contentAsr_LinearLayout_MainActivity);
        asrTextView = containerLinearLayout.findViewById(R.id.asr_TextView_MainActivity);
        asrTimeTextView = containerLinearLayout.findViewById(R.id.asrTime_TextView_MainActivity);
        remainingAsrTextView = containerLinearLayout.findViewById(R.id.remainingAsr_TextView_MainActivity);
        contentMaghribLayout = containerLinearLayout.findViewById(R.id.contentMaghrib_LinearLayout_MainActivity);
        maghribTextView = containerLinearLayout.findViewById(R.id.maghrib_TextView_MainActivity);
        maghribTimeTextView = containerLinearLayout.findViewById(R.id.maghribTime_TextView_MainActivity);
        remainingMaghribTextView = containerLinearLayout.findViewById(R.id.remainingMaghrib_TextView_MainActivity);
        contentIshaLayout = containerLinearLayout.findViewById(R.id.contentIsha_LinearLayout_MainActivity);
        ishaTextView = containerLinearLayout.findViewById(R.id.isha_TextView_MainActivity);
        ishaTimeTextView = containerLinearLayout.findViewById(R.id.ishaTime_TextView_MainActivity);
        remainingIshaTextView = containerLinearLayout.findViewById(R.id.remainingIsha_TextView_MainActivity);
        imageViewbrownnew = containerLinearLayout.findViewById(R.id.imageView5);
        fajrTimeTypeTextView = containerLinearLayout.findViewById(R.id.fajrTimeType_TextView_MainActivity);
        sunriseTimeTypeTextView = containerLinearLayout.findViewById(R.id.sunriseTimeType_TextView_MainActivity);
        dhuhrTimeTypeTextView = containerLinearLayout.findViewById(R.id.dhuhrTimeType_TextView_MainActivity);
        asrTimeTypeTextView = containerLinearLayout.findViewById(R.id.asrTimeType_TextView_MainActivity);
        maghribTimeTypeTextView = containerLinearLayout.findViewById(R.id.maghribTimeType_TextView_MainActivity);
        ishaTimeTypeTextView = containerLinearLayout.findViewById(R.id.ishaTimeType_TextView_MainActivity);
        fajrTimeLinearLayout = containerLinearLayout.findViewById(R.id.fajrTime_LinearLayout_MainActivity);
        sunriseTimeLinearLayout = containerLinearLayout.findViewById(R.id.sunriseTime_LinearLayout_MainActivity);
        dhuhrTimeLinearLayout = containerLinearLayout.findViewById(R.id.dhuhrTime_LinearLayout_MainActivity);
        asrTimeLinearLayout = containerLinearLayout.findViewById(R.id.asrTime_LinearLayout_MainActivity);
        maghribTimeLinearLayout = containerLinearLayout.findViewById(R.id.maghribTime_LinearLayout_MainActivity);
        ishaTimeLinearLayout = containerLinearLayout.findViewById(R.id.ishaTime_LinearLayout_MainActivity);
        fajrATimeLinearLayout = containerLinearLayout.findViewById(R.id.fajrATime_LinearLayout_MainActivity);
        sunriseATimeLinearLayout = containerLinearLayout.findViewById(R.id.sunriseATime_LinearLayout_MainActivity);
        dhuhrATimeLinearLayout = containerLinearLayout.findViewById(R.id.dhuhrATime_LinearLayout_MainActivity);
        asrATimeLinearLayout = containerLinearLayout.findViewById(R.id.asrATime_LinearLayout_MainActivity);
        maghribATimeLinearLayout = containerLinearLayout.findViewById(R.id.maghribATime_LinearLayout_MainActivity);
        ishaATimeLinearLayout = containerLinearLayout.findViewById(R.id.ishaATime_LinearLayout_MainActivity);
        imageSunriseView = containerLinearLayout.findViewById(R.id.imageSunrise_View_MainActivity);
        imagefajrView = containerLinearLayout.findViewById(R.id.imageFajr_View_MainActivity);
        imagedhuhrView = containerLinearLayout.findViewById(R.id.imageDhuhr_View_MainActivity);
        imageasrView = containerLinearLayout.findViewById(R.id.imageAsr_View_MainActivity);
        imagemaghribView = containerLinearLayout.findViewById(R.id.imageMaghrib_View_MainActivity);
        imageishaView = containerLinearLayout.findViewById(R.id.imageIsha_View_MainActivity);
        fajrATimeTextView = containerLinearLayout.findViewById(R.id.fajrATime_TextView_MainActivity);
        fajrATimeTypeTextView = containerLinearLayout.findViewById(R.id.fajrATimeType_TextView_MainActivity);
        sunriseATimeTextView = containerLinearLayout.findViewById(R.id.sunriseATime_TextView_MainActivity);
        sunriseATimeTypeTextView = containerLinearLayout.findViewById(R.id.sunriseATimeType_TextView_MainActivity);
        dhuhrATimeTextView = containerLinearLayout.findViewById(R.id.dhuhrATime_TextView_MainActivity);
        dhuhrATimeTypeTextView = containerLinearLayout.findViewById(R.id.dhuhrATimeType_TextView_MainActivity);
        asrATimeTextView = containerLinearLayout.findViewById(R.id.asrATime_TextView_MainActivity);
        asrATimeTypeTextView = containerLinearLayout.findViewById(R.id.asrATimeType_TextView_MainActivity);
        maghribATimeTextView = containerLinearLayout.findViewById(R.id.maghribATime_TextView_MainActivity);
        maghribATimeTypeTextView = containerLinearLayout.findViewById(R.id.maghribATimeType_TextView_MainActivity);
        ishaATimeTextView = containerLinearLayout.findViewById(R.id.ishaATime_TextView_MainActivity);
        ishaATimeTypeTextView = containerLinearLayout.findViewById(R.id.ishaATimeType_TextView_MainActivity);

        TextView timeSecondsTextView = containerLinearLayout.findViewById(R.id.time_seconds);
        setVisibility(timeSecondsTextView, View.GONE);
        // athkar
        athkarImageView = findViewById(R.id.iv_athkar);
        athkarContainer = findViewById(R.id.layout_athkar);
        athkarTimeTextView = findViewById(R.id.tv_athkar_time);
        athkarIcon = findViewById(R.id.iv_icon);
        athkarTime = findViewById(R.id.layout_time_athkar);

        athkarTextView = findViewById(R.id.remainingPrayer_TextView_MainActivity_);

        prayerTimeContainer = findViewById(R.id.prayerTimeItem_include_MainActivity);
        announcementContainer = findViewById(R.id.announcement_include_MainActivity);

        movingMessageTextView = containerLinearLayout.findViewById(R.id.movingMessage_TextView_MainActivity);
        isHijri = true;
        loadCustomTheme();
        logoImage();

        if (lan.equals(EN_LANGUAGE) && uiTheme != UITheme.NEW_GREEN) {
            float percent = 0.70f;
            // float percent = 0.70f;
            scaleTextViewSize(percent, azanTextView);
            scaleTextViewSize(percent, prayerTextView);
            scaleTextViewSize(percent, ikamaTextView);

            scaleTextViewSize(percent, fajrTextView);
            scaleTextViewSize(percent, sunriseTextView);
            scaleTextViewSize(percent, dhuhrTextView);
            scaleTextViewSize(percent, asrTextView);
            scaleTextViewSize(percent, maghribTextView);
            scaleTextViewSize(percent, ishaTextView);

            scaleTextViewSize(percent, fajrTimeTextView);
            scaleTextViewSize(percent, sunriseTimeTextView);
            scaleTextViewSize(percent, dhuhrTimeTextView);
            scaleTextViewSize(percent, asrTimeTextView);
            scaleTextViewSize(percent, maghribTimeTextView);
            scaleTextViewSize(percent, ishaTimeTextView);

            scaleTextViewSize(percent, fajrATimeTextView);
            scaleTextViewSize(percent, sunriseATimeTextView);
            scaleTextViewSize(percent, dhuhrATimeTextView);
            scaleTextViewSize(percent, asrATimeTextView);
            scaleTextViewSize(percent, maghribATimeTextView);
            scaleTextViewSize(percent, ishaATimeTextView);

            scaleTextViewSize(percent, fajrTimeTypeTextView);
            scaleTextViewSize(percent, sunriseTimeTypeTextView);
            scaleTextViewSize(percent, dhuhrTimeTypeTextView);
            scaleTextViewSize(percent, asrTimeTypeTextView);
            scaleTextViewSize(percent, maghribTimeTypeTextView);
            scaleTextViewSize(percent, ishaTimeTypeTextView);
            scaleTextViewSize(percent, fajrATimeTypeTextView);
            scaleTextViewSize(percent, sunriseATimeTypeTextView);
            scaleTextViewSize(percent, dhuhrATimeTypeTextView);
            scaleTextViewSize(percent, asrATimeTypeTextView);
            scaleTextViewSize(percent, maghribATimeTypeTextView);
            scaleTextViewSize(percent, ishaATimeTypeTextView);
        }

        loadMainColors();
    }

    private void loadCustomTheme() {
        if (uiTheme == UITheme.CUSTOM_FIREBASE || uiTheme == UITheme.CUSTOM_1) {
            String theme = uiTheme.name();

            String path = Hawk.get(theme + (isLandscape ? "_background_landscape" : "_background_portrait"), null);
            if (path != null && !path.isEmpty() && new File(path).exists()) {
                int w = isLandscape ? 1280 : 720;
                int h = isLandscape ? 720 : 1280;
                Picasso.get().load(new File(path)).resize(w, h).into(backgroundImageView, new Callback() {
                    @Override
                    public void onSuccess() {
                        loadMainColors();
                    }

                    @Override
                    public void onError(Exception e) {
                        loadMainColors();
                    }
                });
            }
            path = Hawk.get(theme + (isLandscape ? "_date_background_landscape" : "_date_background_portrait"), null);
            if (path != null && !path.isEmpty() && new File(path).exists()) {
                setBackgroundColor(gregorian_month_container, path);
                setBackgroundColor(hijri_month_container, path);
            }
            int color1 = Hawk.get(theme + "_color1", 0xFF494D32);
            int color2 = Hawk.get(theme + "_color2", 0xFFAE7C32);
            moveDateToUp = Hawk.get(theme + "_move_date_to_up", true);
            setViewsColors(color1, color2);
        }
    }

    private void setViewsColors(int color1, int color2) {

        setTextColor(timeNowTextView, color1);
        setTextColor(dayText, color1);
        setTextColor(dateNowTextView, color1);
        setTextColor(dateHTextView, color1);
        setTextColor(datey, color1);
        setTextColor(datem, color1);
        setTextColor(datehy, color1);
        setTextColor(datehm, color1);
        setTextColor(movingMessageTextView, color1);

        setTextColor(azanTextView, color2);
        setTextColor(prayerTextView, color2);
        setTextColor(ikamaTextView, color2);

        setTextColor(fajrTextView, color1);
        setTextColor(sunriseTextView, color1);
        setTextColor(dhuhrTextView, color1);
        setTextColor(asrTextView, color1);
        setTextColor(maghribTextView, color1);
        setTextColor(ishaTextView, color1);

        setTextColor(fajrTimeTextView, color1);
        setTextColor(fajrATimeTextView, color1);
        setTextColor(sunriseTimeTextView, color1);
        setTextColor(sunriseATimeTextView, color1);
        setTextColor(dhuhrTimeTextView, color1);
        setTextColor(dhuhrATimeTextView, color1);
        setTextColor(asrTimeTextView, color1);
        setTextColor(asrATimeTextView, color1);
        setTextColor(maghribTimeTextView, color1);
        setTextColor(maghribATimeTextView, color1);
        setTextColor(ishaTimeTextView, color1);
        setTextColor(ishaATimeTextView, color1);

        setTextColor(fajrTimeTypeTextView, color2);
        setTextColor(fajrATimeTypeTextView, color2);
        setTextColor(sunriseTimeTypeTextView, color2);
        setTextColor(sunriseATimeTypeTextView, color2);
        setTextColor(dhuhrTimeTypeTextView, color2);
        setTextColor(dhuhrATimeTypeTextView, color2);
        setTextColor(asrTimeTypeTextView, color2);
        setTextColor(asrATimeTypeTextView, color2);
        setTextColor(maghribTimeTypeTextView, color2);
        setTextColor(maghribATimeTypeTextView, color2);
        setTextColor(ishaTimeTypeTextView, color2);
        setTextColor(ishaATimeTypeTextView, color2);

        setTextColor(remainingFajrTextView, color1);
        setTextColor(remainingSunriseTextView, color1);
        setTextColor(remainingDhuhrTextView, color1);
        setTextColor(remainingAsrTextView, color1);
        setTextColor(remainingMaghribTextView, color1);
        setTextColor(remainingIshaTextView, color1);
        setTextColor(remainingPrayerTextView, color1);

        setColorFilterView(remainingFajrTextView, color1);
        setColorFilterView(remainingSunriseTextView, color1);
        setColorFilterView(remainingDhuhrTextView, color1);
        setColorFilterView(remainingAsrTextView, color1);
        setColorFilterView(remainingMaghribTextView, color1);
        setColorFilterView(remainingIshaTextView, color1);
        setColorFilterView(remainingPrayerTextView, color1);

        setColorFilterView(contentFajrLayout, color1);
        setColorFilterView(contentSunriseLayout, color1);
        setColorFilterView(contentDhuhrLayout, color1);
        setColorFilterView(contentAsrLayout, color1);
        setColorFilterView(contentMaghribLayout, color1);
        setColorFilterView(contentIshaLayout, color1);

        setColorFilter(alrabeeaTimesImageView, color1);
        setColorFilter(dayimage, color2);
        setColorFilter(hijri_month_image, color2);
        setColorFilter(gregorian_month_image, color2);
        // setColorFilter(imageViewbrownnew,color1);

        CircularProgressBar circularProgressBar = findViewById(R.id.progressBar);
        circularProgressBar.setColor(color1);
        setTextColor(findViewById(R.id.tv_remainingOnIkama), color1);
        setTextColor(findViewById(R.id.tv_remaining_number), color1);
        setTextColor(findViewById(R.id.tv_remaining_text), color1);
        setTextColor(findViewById(R.id.tv_message), color1);
        setTextColor(findViewById(R.id.tv_description), color1);
        setTextColor(findViewById(R.id.tv_remainingOnIkama), color1);

    }

    private void safeSetLayoutDirection(ViewGroup layout) {
        if (layout == null)
            return;
        int direction = HawkSettings.getTypeAM().equals(AR_LANGUAGE) ? View.LAYOUT_DIRECTION_RTL
                : View.LAYOUT_DIRECTION_LTR;
        try {
            layout.setLayoutDirection(direction);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void setSunriseOrDuhaNamesTextView() {
        try {
            // PrayerType type = (HawkSettings.getShowDuhaSetting() == 0 || showDuha) ?
            // PrayerType.Duha : PrayerType.Sunrise;
            // String[] time =
            // Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(type.prayerTime.getAzanTime(),
            // "hh:mm a"));

            // setText(fajrTextView, Utils.getString(R.string.fajr));
            if (HawkSettings.getShowDuhaSetting() == 0 || showDuha) {
                if (sunriseTextViewe != null) {
                    setText(sunriseTextViewe, "Duha");
                    setText(sunriseTextView, "الضحى");
                } else
                    setText(sunriseTextView, Utils.getString(R.string.duha));

                // setText(sunriseATimeTextView, getSpannableText(time[0]));
                // setText(sunriseATimeTypeTextView, getSpannableText(time[1]));
            } else if (HawkSettings.getShowDuhaSetting() == 1 || !showDuha) {

                if (sunriseTextViewe != null) {
                    setText(sunriseTextViewe, "Sunrise");
                    setText(sunriseTextView, "الشروق");
                } else
                    setText(sunriseTextView, Utils.getString(R.string.sunrise));

                // // if the view only showing sunrise or duha then set the same value, some
                // themes have the wrong layout id for sunrise and duha
                // if (sunriseTimeTextView == null || sunriseATimeTextView == null) {
                // if (HawkSettings.getShowDuhaSetting() == 0)
                // time =
                // Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(PrayerType.Duha.prayerTime.getAzanTime(),
                // "hh:mm a"));
                //
                // setText(sunriseTimeTextView, getSpannableText(time[0]));
                // setText(sunriseATimeTextView, getSpannableText(time[0]));
                // setText(sunriseTimeTypeTextView, getSpannableText(time[1]));
                // setText(sunriseATimeTypeTextView, getSpannableText(time[1]));
                // } else {
                // setText(sunriseTimeTextView, getSpannableText(time[0]));
                // setText(sunriseTimeTypeTextView, getSpannableText(time[1]));
                // time =
                // Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(PrayerType.Duha.prayerTime.getAzanTime(),
                // "hh:mm a"));
                // setText(sunriseATimeTextView, getSpannableText(time[0]));
                // setText(sunriseATimeTypeTextView, getSpannableText(time[1]));
                // }

            }
            if (HawkSettings.getShowDuhaSetting() == 2) {

                if (sunriseTextViewe != null) {
                    setVisibility(sunriseTextViewe, View.INVISIBLE);
                    setVisibility(sunriseTextView, View.INVISIBLE);
                }
            }
            // setText(dhuhrTextView, Utils.getString(R.string.dhuhr));
            // setText(asrTextView, Utils.getString(R.string.asr));
            // setText(maghribTextView, Utils.getString(R.string.maghrib));
            // setText(ishaTextView, Utils.getString(R.string.isha));

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void setAppTheme() {
        switch (uiTheme) {
            /*
             * case ARG_APP_THEME_GREEN:
             * log("ARG_APP_THEME_GREEN");
             * // View.inflate(this, R.layout.content_main, containerLinearLayout);
             * View.inflate(this, R.layout.content_main, containerLinearLayout);
             * break;
             */
            case BROWN:
                View.inflate(this, R.layout.content_main_brown, containerLinearLayout);
                break;
            case BLUE:
                View.inflate(this, R.layout.content_main_blue, containerLinearLayout);
                break;
            case DARK_GREEN:
                View.inflate(this, R.layout.content_main_dark_green, containerLinearLayout);
                break;
            case DARK_GRAY:
                View.inflate(this, R.layout.content_main_drak_gray, containerLinearLayout);
                break;
            case GREEN:
                View.inflate(this, R.layout.content_main_green, containerLinearLayout);
                break;
            case WHITE:
                View.inflate(this, R.layout.content_main_white, containerLinearLayout);
                break;
            case RED:
                View.inflate(this, R.layout.content_main_red, containerLinearLayout);
                break;
            case BROWN_NEW:
                View.inflate(this, R.layout.content_main_brown_new, containerLinearLayout);
                break;
            case BLUE_NEW:
                View.inflate(this, R.layout.content_main_blue_new, containerLinearLayout);
                break;
            case NEW_GREEN:
                View.inflate(this, R.layout.content_main_new_green, containerLinearLayout);
                break;
            case BLUE_LET:
                View.inflate(this, R.layout.content_main_blue_lett, containerLinearLayout);
                break;
            case WHITE_NEW:
                View.inflate(this, R.layout.content_main_white_new, containerLinearLayout);
                break;
            case BROWN_NEW_3:
                View.inflate(this, R.layout.content_main_brown_3, containerLinearLayout);
                break;
            case CUSTOM_1:
                if (Hawk.get(uiTheme.name() + "_show_ikama", false))
                    View.inflate(this, R.layout.content_main_custom_1_with_ikama, containerLinearLayout);
                else
                    View.inflate(this, R.layout.content_main_custom_1, containerLinearLayout);
                break;
            default:
                if (Hawk.get(uiTheme.name() + "_show_ikama", false))
                    View.inflate(this, R.layout.content_main_firebase_with_ikama, containerLinearLayout);
                else
                    View.inflate(this, R.layout.content_main_firebase, containerLinearLayout);
                break;
            /*
             * case ARG_APP_THEME_white_new_shape:
             * log("ARG_APP_THEME_white_new_shape");
             * View.inflate(this, R.layout.content_main_white_new_shape,
             * containerLinearLayout);
             * break;
             */
        }
    }

    private void displayPrayerTimes() {
        lastDisplayed = DisplayTypes.PrayerTimes;
        setVisibility(remainingPrayerTextView, View.VISIBLE);
        setVisibility(prayerTimeContainer, View.VISIBLE);

        setVisibility(athkarContainer, View.GONE);
        isShowingAthkar = false;
        isShowingPhotoGallery = false;
        showTextDesign();
        // stopThread(athkarDateUpdaterThread);

        setVisibility(announcementContainer, View.GONE);
        isShowingAnouncement = false;
        if (announcementManager != null)
            announcementManager.onStop();

        if (lastDisplayedDay != day) {
            addPrayer(PrayerType.Fajr);
            addPrayer(PrayerType.Sunrise);
            // addDataToPrayerView(PrayerType.Duha);
            addPrayer(PrayerType.Dhuhr);
            addPrayer(PrayerType.Asr);
            addPrayer(PrayerType.Maghrib);
            addPrayer(PrayerType.Isha);
            lastDisplayedDay = day;
        }
    }

    private void displayAnnouncement(AnnouncementMessage message) {

        // message.displayTypes = lastDisplayed;
        // if (lastDisplayed != DisplayTypes.LastMinuiteBeforeAzan && lastDisplayed !=
        // DisplayTypes.Khutba)//only change if not changed in select prayer
        // lastDisplayed = DisplayTypes.Announcement;
        setVisibility(remainingPrayerTextView, View.GONE);
        setVisibility(prayerTimeContainer, View.GONE);

        setVisibility(athkarContainer, View.GONE);
        isShowingAnouncement = true;
        isShowingAthkar = false;
        isShowingPhotoGallery = false;

        if (!textDesign.shouldShowDuringPrayers(this))
            hideTextDesign();
        // stopThread(athkarDateUpdaterThread);

        setVisibility(announcementContainer, View.VISIBLE);
        if (announcementManager != null)
            announcementManager.viewMessage(message);
    }

    private void displayAthkar(AthkarType athkarType, PrayerType prayerType) {

        lastDisplayed = DisplayTypes.Athkar;
        setVisibility(remainingPrayerTextView, View.GONE);
        setVisibility(prayerTimeContainer, View.GONE);

        setVisibility(announcementContainer, View.GONE);
        isShowingAnouncement = false;
        if (announcementManager != null)
            announcementManager.onStop();

        setScaleType(athkarImageView, ImageView.ScaleType.FIT_XY);
        // .setScaleType();
        setVisibility(athkarTimeTextView, HawkSettings.getEnableTimeWithAzkar() ? View.VISIBLE : View.GONE);
        setVisibility(athkarContainer, View.VISIBLE);
        hideTextDesign();
        safeRunOnUi(() -> {
            Margin m = new Margin();
            setMargins(athkarTime, m);
            athkarTime.setVisibility(View.VISIBLE);
            loadAthkarsColors(athkarType);
        });
        switch (athkarType) {
            case MorningAthkar:
                setImage(athkarImageView, getMorningAthkarImageFile());
                break;
            case EveningAthkar:
                setImage(athkarImageView, getEveningAthkarImageFile());
                break;
            case AfterPrayer:
                setImage(athkarImageView, getPrayerAthkarImageFile(prayerType));
                break;
        }
        isShowingAthkar = true;
        // startThread(athkarDateUpdaterThread);
    }

    private void displayPhotoGallery(PhotoGallery gallery) {
        lastDisplayed = DisplayTypes.PhotoGallery;
        // using the same athkar image container
        if (lastDisplayedGalleryPhoto < 0 || lastDisplayedGalleryPhoto >= gallery.images.size())
            lastDisplayedGalleryPhoto = 0;

        File file = new File(gallery.images.get(lastDisplayedGalleryPhoto).imageUri);

        while (lastDisplayedGalleryPhoto < gallery.images.size() && !file.exists()) {
            log("file not exsits: " + gallery.images.get(lastDisplayedGalleryPhoto).imageUri);
            lastDisplayedGalleryPhoto++;
        }
        if (lastDisplayedGalleryPhoto >= gallery.images.size())
            return;

        setBackgroundColorFromImageResource(athkarImageView, gallery.images.get(lastDisplayedGalleryPhoto).imageUri);

        safeRunOnUi(() -> {
            Margin m = new Margin();
            m.t = getResources().getDimensionPixelOffset(com.intuit.sdp.R.dimen._15sdp);
            setMargins(athkarTime, m);
            athkarTime.setBackgroundColor(0);
            athkarTime.setVisibility(View.VISIBLE);
        });

        setScaleType(athkarImageView, ImageView.ScaleType.FIT_CENTER);
        setImage(athkarImageView, gallery.images.get(lastDisplayedGalleryPhoto).imageUri);
        /*
         * Palette.Swatch s = getSwatchFromView(athkarImageView);
         * if (s != null)
         * setBackgroundColor(athkarImageView, s.getRgb());
         * else
         * setBackgroundColor(athkarImageView, Color.WHITE);
         */
        setVisibility(athkarTimeTextView, View.GONE);
        // setVisibility(athkarTimeTextView,Utils.getEnableTimeWithGallery()?View.VISIBLE:View.GONE);
        setVisibility(athkarContainer, View.VISIBLE);
        isShowingPhotoGallery = true;
        hideTextDesign();
        lastDisplayedGalleryPhoto++;
        // startThread(athkarDateUpdaterThread);
    }

    private void loadAthkarsColors(AthkarType type) {
        safeRunOnUi(() -> {
            athkarIcon.getLayoutParams().width = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._60sdp);
            athkarIcon.setScaleType(ImageView.ScaleType.FIT_XY);
            athkarIcon.requestLayout();

            switch (HawkSettings.getCurrentAzkarTheme()) {
                case FIRST:
                    switch (type) {
                        case MorningAthkar:
                            athkarTime.setBackgroundResource(R.color.theme1_time_layout_morning);
                            // setBackgroundColorFromImageResource(athkarTime,R.color.theme1_time_layout_morning);
                            athkarTimeTextView.setTextColor(getResources().getColor(R.color.white));
                            break;
                        case EveningAthkar:
                            athkarTime.setBackgroundResource(R.color.theme1_time_layout_evening);
                            // setBackgroundColorFromImageResource(athkarTime,R.color.theme1_time_layout_evening);
                            athkarTimeTextView.setTextColor(getResources().getColor(R.color.white));
                            break;
                        case AfterPrayer:
                            athkarTime.setBackgroundResource(R.color.theme1_time_layout_afterPrayer);
                            // setBackgroundColorFromImageResource(athkarTime,R.color.theme1_time_layout_afterPrayer);
                            athkarTimeTextView
                                    .setTextColor(getResources().getColor(R.color.theme1_time_text_afterPrayer));
                            break;

                    }
                    break;
                case SECOND:
                    athkarTime.setBackgroundResource(R.color.theme2_time_layout);
                    athkarTimeTextView.setTextColor(getResources().getColor(R.color.theme2_time_text));
                    break;
                case THIRD:
                    if (type == AthkarType.EveningAthkar) {
                        athkarTime.setBackgroundResource(R.color.theme3_time_layout_evening);
                        athkarTimeTextView.setTextColor(getResources().getColor(R.color.theme3_time_text_evening));
                    } else {
                        athkarTime.setBackgroundResource(R.color.theme3_time_layout);
                        athkarTimeTextView.setTextColor(getResources().getColor(R.color.theme3_time_text));
                    }
                    break;

                case FOURTH:
                    athkarTime.setBackgroundResource(R.color.athkar_fourth_background);
                    athkarTimeTextView.setTextColor(getResources().getColor(R.color.athkar_fourth_text));
                    break;
            }
        });
    }

    private void setBackgroundColorFromImageResource(View view, String imagePath) {
        try {

            String path = new File(imagePath).getPath();
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            if (bitmap != null) {
                Palette p = Palette.from(bitmap).generate();
                Palette.Swatch s = p.getDominantSwatch();
                if (s != null)
                    setBackgroundColor(view, s.getRgb());
                else
                    setBackgroundColor(view, Color.WHITE);
                bitmap.recycle();
            }
            bitmap = null;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void loadMainColors() {
        domainColor = getColor(R.color.white);
        textColor = getColor(R.color.colorblack);
        try {
            BitmapDrawable bitmapDrawable = (BitmapDrawable) backgroundImageView.getDrawable();
            if (bitmapDrawable == null)
                return;
            Bitmap bitmap = (bitmapDrawable).getBitmap();
            if (bitmap == null)
                return;
            Palette p = Palette.from(bitmap).generate();
            Palette.Swatch s = p.getDominantSwatch();
            if (s == null)
                return;
            domainColor = s.getBodyTextColor();
            textColor = s.getRgb();
        } catch (Exception e) {
            domainColor = getColor(R.color.white);
            textColor = getColor(R.color.colorblack);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private File getMorningAthkarImageFile() {
        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "athkarm" + (isLandscape ? "l" : "") + ".png");
    }

    private File getEveningAthkarImageFile() {
        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "athkare" + (isLandscape ? "l" : "") + ".png");
    }

    private File getAfterPrayer1AthkarImageFile() {
        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "allwhite" + (isLandscape ? "" : "h") + ".png");
    }

    private File getAfterPrayer2AthkarImageFile() {
        return new File(HawkSettings.getCurrentAzkarTheme().getDir(), "fmwhite" + (isLandscape ? "" : "h") + ".png");
    }

    private File getPrayerAthkarImageFile(PrayerType prayerType) {
        switch (prayerType) {
            case Fajr:
            case Maghrib:
                return getAfterPrayer2AthkarImageFile(); // fmwhite
            default:
                return getAfterPrayer1AthkarImageFile(); // allwhite
        }
    }

    private void log(Object info) {
        Log.i(TAG, info.toString());
    }

    private void setBackgroundToTextViewPray(PrayerType type, boolean isNextPray) {

        safeSetLayoutDirection(ishaTimeLinearLayout);
        safeSetLayoutDirection(ishaATimeLinearLayout);

        switch (type) {
            case Fajr:
            case Midnight:
            case LastThird:
                if (isNextPray) {
                    fajrATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
                    fajrTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                } else {
                    fajrATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
                    fajrTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
                }
                break;
            case Sunrise:
            case Duha:
                if (isNextPray) {
                    sunriseATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
                    sunriseTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                } else {
                    sunriseATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
                    sunriseTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
                }
                break;
            case Dhuhr:
                if (isNextPray) {
                    dhuhrATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
                    dhuhrTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                } else {
                    dhuhrATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
                    dhuhrTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
                }
                break;
            case Asr:
                if (isNextPray) {
                    asrATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
                    asrTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                } else {
                    asrATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
                    asrTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
                }
                break;
            case Maghrib:
                if (isNextPray) {
                    maghribATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
                    maghribTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                } else {
                    maghribATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
                    maghribTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
                }
                break;
            case Isha:
                if (isNextPray) {
                    ishaATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
                    ishaTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                } else {
                    ishaATimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
                    ishaTimeLinearLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
                }
                break;
        }

    }

    private void setBackgroundToTextViewPray(ViewGroup layoutPray, boolean isNextPray) {
        if (layoutPray == null)
            return;
        safeRunOnUi(() -> {
            switch (uiTheme) {
                /*
                 * case ARG_APP_THEME_GREEN:
                 * layoutPray.setBackgroundResource(R.drawable.
                 * without_corners_20_background_white_with_alpha_10);
                 * break;
                 */
                case BROWN:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.text_view_next_pray_brown);
                    } else {
                        layoutPray.setBackgroundResource(R.drawable.text_view_pray_white_new2);
                    }
                    fixBackGroundStretching(layoutPray);
                    break;
                case BROWN_NEW_3:
                    if (isNextPray)
                        layoutPray.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
                case BLUE:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.blue_transperant);
                    } else {
                        layoutPray.setBackgroundColor(Color.TRANSPARENT);
                    }
                    break;
                case DARK_GREEN:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.dark_green_transperant);

                    } else {
                        layoutPray.setBackgroundResource(R.drawable.gradient_semi_transperant_green);
                    }
                    break;
                case CUSTOM_1:
                    if (isNextPray)
                        layoutPray.setBackgroundResource(R.drawable.gradient_semi_transperant_custom);
                    else
                        layoutPray.setBackgroundResource(0);
                case RED:
                    if (isNextPray)
                        layoutPray.setBackgroundResource(R.drawable.gradient_semi_transperant_red2);
                    else
                        layoutPray.setBackgroundResource(R.drawable.gradient_semi_transperant_red);
                    break;
                case WHITE:
                    if (isNextPray)
                        layoutPray.setBackgroundResource(R.drawable.text_view_next_pray_darkw);
                    else
                        layoutPray.setBackgroundResource(0);

                    break;
                case DARK_GRAY:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.without_corners_bottom_solid_yellow);

                    } else {
                        layoutPray.setBackgroundResource(R.drawable.without_corners_bottom_solid_gray);
                    }
                    break;
                case GREEN:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.text_view_next_pray_darkb);
                        fixBackGroundStretching(layoutPray);
                    } else {
                        layoutPray.setBackgroundResource(0);
                    }
                    break;
                case BLUE_NEW:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.text_view_pray_bluenewnext);
                        fixBackGroundStretching(layoutPray);
                    } else {
                        layoutPray.setBackgroundResource(0);
                    }
                    break;
                case BLUE_LET:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.background_prays_blue_lett_now);
                    } else {
                        layoutPray.setBackgroundResource(R.drawable.background_prays_blue_lett_r);
                    }
                    break;
                case NEW_GREEN:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.background_prays_new_green_now);
                    } else {
                        layoutPray.setBackgroundResource(R.drawable.background_prays_new_green_r);
                    }
                    break;
                case WHITE_NEW:
                    if (isNextPray) {
                        layoutPray.setBackgroundResource(R.drawable.text_view_remining_white_new);
                    } else {
                        layoutPray.setBackgroundResource(R.drawable.text_view_pray_white_new2);
                    }
                    fixBackGroundStretching(layoutPray);
                    break;

            }
        });
    }

    public void fixBackGroundStretching(ViewGroup tv) {
        BitmapDrawable background = (BitmapDrawable) tv.getBackground(); // assuming you have bg_tile as background.
        BitmapDrawable newBackground = new BitmapDrawable(background.getBitmap()) {
            @Override
            public int getMinimumWidth() {
                return 0;
            }

            @Override
            public int getMinimumHeight() {
                return 0;
            }
        };
        Shader.TileMode modeX = background.getTileModeX();
        Shader.TileMode modeY = background.getTileModeY();
        newBackground.setTileModeXY(modeX, modeY);
        tv.setBackgroundDrawable(newBackground);
    }

    private void setColorToTextViewPray(TextView pray, TextView prayTime, TextView prayTimeType, boolean isNextPray) {
        safeRunOnUi(() -> {
            int color;
            switch (uiTheme) {
                case BLUE_LET:
                    color = isNextPray ? ContextCompat.getColor(this, R.color.bluelett) : Color.WHITE;
                    setTextColor(pray, color);
                    break;
                case NEW_GREEN:
                    color = isNextPray ? ContextCompat.getColor(this, R.color.new_green_2) : Color.WHITE;
                    setTextColor(prayTime, color);
                    setTextColor(prayTimeType, color);
                    break;
                case BLUE_NEW:
                    color = isNextPray ? getResources().getColor(R.color.white)
                            : getResources().getColor(R.color.ambluenew);
                    setTextColor(prayTimeType, color);
                    break;
                case DARK_GRAY:
                    color = isNextPray ? ContextCompat.getColor(MainActivity.this, R.color.colorGrayDark) : Color.WHITE;
                    setTextColor(pray, color);
                    setTextColor(prayTime, color);
                    setTextColor(prayTimeType, color);
                    break;
            }
        });
    }

    private void setToImageViewPray(ImageView pray, boolean isNextPray) {
        if (pray == null)
            return;
        safeRunOnUi(() -> {
            if (uiTheme == UITheme.BLUE) {
                int height = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._15sdp);
                int heightMax = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._30sdp);
                int width = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._15sdp);
                int widthMax = getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._30sdp);

                ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) pray.getLayoutParams();
                if (isNextPray) {
                    layoutParams.height = heightMax;
                    layoutParams.width = widthMax;
                } else {
                    layoutParams.height = height;
                    layoutParams.width = width;
                }
                pray.requestLayout();
            }
        });
    }

    private void setRemainingToPrayer(PrayerType type, String timeRemaining) {
        if (!lastUpdatedTimeRemaining.equalsIgnoreCase(timeRemaining)) {
            disableAllRemainingPrayerText();
            setText(remainingPrayerTextView, timeRemaining);
            changePrayerRemaining(type, true, timeRemaining);
            lastUpdatedTimeRemaining = timeRemaining;
            darkenNextPrayerIkamaTime(type);

        }
    }

    private void darkenNextPrayerIkamaTime(PrayerType type) {
        Log.e("darkenNextPrayerIkamaTime", type.toString());
        int darken = dhuhrTextView.getCurrentTextColor();

        Log.e("Theme", uiTheme.name());

        int def = 0xCF9E9E9E;

        setTextColor(tvIkamaDelayFajr, def);
        setTextColor(tvIkamaDelayDhur, def);
        setTextColor(tvIkamaDelayAsr, def);
        setTextColor(tvIkamaDelayMaghreb, def);
        setTextColor(tvIkamaDelayIsha, def);

        try {
            switch (type) {
                case Midnight:
                    break;
                case Tahajjud:
                    break;
                case LastThird:
                    break;
                case Fajr:
                    setTextColor(tvIkamaDelayFajr, darken);
                    break;
                case Sunrise:
                    break;
                case Duha:
                    break;
                case Dhuhr:
                    setTextColor(tvIkamaDelayDhur, darken);
                    break;
                case Asr:
                    setTextColor(tvIkamaDelayAsr, darken);
                    break;
                case Maghrib:
                    setTextColor(tvIkamaDelayMaghreb, darken);
                    break;
                case Isha:
                    setTextColor(tvIkamaDelayIsha, darken);
                    break;
                case Tarawih:
                    break;
            }

        } catch (Exception e) {
            Log.e("darkenNextPrayerIkamaTime", "crash " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * @noinspection t
     */
    private void changePrayerRemaining(PrayerType type, boolean isNext, String timeRemaining) {
        switch (type) {
            case Fajr:
            case Midnight:
            case LastThird:
                setRemainingToPrayer(remainingFajrTextView, isNext, timeRemaining);
                setColorToTextViewPray(fajrTextView, fajrTimeTextView, fajrTimeTypeTextView, isNext);
                setColorToTextViewPray(fajrTextView, fajrATimeTextView, fajrATimeTypeTextView, isNext);
                setToImageViewPray(imagefajrView, isNext);
                if (uiTheme == UITheme.BROWN_NEW_3) {
                    setBackgroundToTextViewPray(type, isNext);
                } else {
                    // if (uiTheme != UITheme.WHITE_NEW)
                    setBackgroundToTextViewPray(contentFajrLayout, isNext);
                }
                break;
            case Sunrise:
            case Duha:
                if (HawkSettings.getShowDuhaSetting() == 2)
                    break;
                setRemainingToPrayer(remainingSunriseTextView, isNext, timeRemaining);
                if (uiTheme != UITheme.NEW_GREEN)// duha exepted from coloring
                    setColorToTextViewPray(sunriseTextView, sunriseATimeTextView, sunriseATimeTypeTextView, isNext);
                setToImageViewPray(imageSunriseView, isNext);
                if (uiTheme == UITheme.BROWN_NEW_3) {
                    setBackgroundToTextViewPray(type, isNext);
                } else {
                    // if (uiTheme != UITheme.WHITE_NEW)
                    setBackgroundToTextViewPray(contentSunriseLayout, isNext);
                }
                break;
            case Dhuhr:
                setRemainingToPrayer(remainingDhuhrTextView, isNext, timeRemaining);
                setColorToTextViewPray(dhuhrTextView, dhuhrTimeTextView, dhuhrTimeTypeTextView, isNext);
                setColorToTextViewPray(dhuhrTextView, dhuhrATimeTextView, dhuhrATimeTypeTextView, isNext);
                setToImageViewPray(imagedhuhrView, isNext);
                if (uiTheme == UITheme.BROWN_NEW_3) {
                    setBackgroundToTextViewPray(type, isNext);
                } else {
                    // if (uiTheme != UITheme.WHITE_NEW)
                    setBackgroundToTextViewPray(contentDhuhrLayout, isNext);
                }
                break;
            case Asr:
                setRemainingToPrayer(remainingAsrTextView, isNext, timeRemaining);
                setColorToTextViewPray(asrTextView, asrTimeTextView, asrTimeTypeTextView, isNext);
                setColorToTextViewPray(asrTextView, asrATimeTextView, asrATimeTypeTextView, isNext);
                setToImageViewPray(imageasrView, isNext);
                if (uiTheme == UITheme.BROWN_NEW_3) {
                    setBackgroundToTextViewPray(type, isNext);
                } else {
                    // if (uiTheme != UITheme.WHITE_NEW)
                    setBackgroundToTextViewPray(contentAsrLayout, isNext);
                }
                break;
            case Maghrib:
                setRemainingToPrayer(remainingMaghribTextView, isNext, timeRemaining);
                setColorToTextViewPray(maghribTextView, maghribTimeTextView, maghribTimeTypeTextView, isNext);
                setColorToTextViewPray(maghribTextView, maghribATimeTextView, maghribATimeTypeTextView, isNext);
                setToImageViewPray(imagemaghribView, isNext);
                if (uiTheme == UITheme.BROWN_NEW_3) {
                    setBackgroundToTextViewPray(type, isNext);
                } else {
                    // if (uiTheme != UITheme.WHITE_NEW)
                    setBackgroundToTextViewPray(contentMaghribLayout, isNext);
                }
                break;
            case Isha:
                setRemainingToPrayer(remainingIshaTextView, isNext, timeRemaining);
                setColorToTextViewPray(ishaTextView, ishaTimeTextView, ishaTimeTypeTextView, isNext);
                setColorToTextViewPray(ishaTextView, ishaATimeTextView, ishaATimeTypeTextView, isNext);
                setToImageViewPray(imageishaView, isNext);
                if (uiTheme == UITheme.BROWN_NEW_3) {
                    setBackgroundToTextViewPray(type, isNext);
                } else {
                    // if (uiTheme != UITheme.WHITE_NEW)
                    setBackgroundToTextViewPray(contentIshaLayout, isNext);
                }
                break;
        }
    }

    private void setRemainingToPrayer(TextView remainingTextView, boolean isNext, String timeRemaining) {
        if (remainingTextView != null) {
            if (isNext) {
                setVisibility(remainingTextView, View.VISIBLE);
                setText(remainingTextView, timeRemaining);
            } else {
                setVisibility(remainingTextView, View.GONE);
            }
        }
    }

    private void setCustomSettingForThemes() {
        switch (uiTheme) {
            case WHITE:
            case BROWN_NEW:
            case DARK_GREEN:
            case WHITE_NEW:
            case BROWN:
                if (isLandscape && !logoImageEnabled)
                    imageViewbrownnew.setVisibility(View.GONE);
                break;
            case BLUE_LET:
            case BROWN_NEW_3:
                // case ARG_APP_THEME_CUSTOM_1:
            case CUSTOM_FIREBASE:
                if (!logoImageEnabled)
                    if (isLandscape)
                        imageViewbrownnew.setVisibility(View.GONE);
                    else
                        imageViewbrownnew.setVisibility(moveDateToUp ? View.GONE : View.INVISIBLE);
                break;
            case BLUE:
                if (!logoImageEnabled) {
                    if (isLandscape)
                        setVisibility(imageViewbrownnew, View.GONE);
                    else
                        setLogoImageHeightToHalf();
                }
                break;
            case NEW_GREEN:
                if (!logoImageEnabled)
                    imageViewbrownnew.setVisibility(View.GONE);
                break;
        }
    }

    public void setLogoImageHeightToHalf() {
        int imageHeight = imageViewbrownnew.getLayoutParams().height;
        imageViewbrownnew.getLayoutParams().height = imageHeight / 2;
        imageViewbrownnew.requestLayout();
    }

    private void SetAction() {
        // navigationView.setNavigationItemSelectedListener(this);

        // athkarIcon.setOnClickListener(v -> startSettings());

        // alrabeeaTimesImageView.setOnClickListener(v -> startSettings());
        backgroundImageView.setOnClickListener(v -> startSettings());
    }

    public void startSettings() {
        startActivity(new Intent(MainActivity.this, SettingsActivity.class));
        finish();
    }

    @Override
    public void onBackPressed() {
        // Since this is a launcher app, we should prevent users from accidentally
        // exiting
        // Just disable the back button completely

        // Show a toast explaining how to access other apps
        Toast.makeText(this, "Use the Apps button to access other applications", Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.action_settings) {
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Handle navigation view item clicks here.
        int id = item.getItemId();

        if (id == R.id.nav_settings) {
            startActivity(new Intent(MainActivity.this, SettingsActivity.class));
        }

        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        drawer.closeDrawer(GravityCompat.START);
        return true;
    }

    private void getPrayerTimesThisYear() {
        isPrayerListUpdating.set(true);
        showLoadingDialog();
        AlrabeeaTimesRequests.getPrayerTimesWithDefault("" + year, ConstantsOfApp.BASE_URL_ALADHAN,
                new OnCompleteListener<PrayerApi, String>() {
                    @Override
                    public void onSuccess(PrayerApi prayers) {
                        if (prayers != null) {
                            Utils.putPrayerTimesForYear(prayers);
                        }
                        hideLoadingDialog();
                        isPrayerListUpdating.set(false);
                    }

                    @Override
                    public void onFail(String object) {
                        hideLoadingDialog();
                        isPrayerListUpdating.set(false);
                    }
                });
    }

    public void showLoadingDialog() {
        if (loadingDialog != null) {
            safeRunOnUi(() -> loadingDialog.show());
        }
    }

    public void hideLoadingDialog() {
        if (loadingDialog != null) {
            safeRunOnUi(() -> loadingDialog.dismiss());
        }
    }

    public void logoImage() {

        PremiumUserModel user = HawkSettings.getPremiumUser();

        alrabeeaTimesImageView.setVisibility(View.VISIBLE);
        athkarIcon.setVisibility(View.VISIBLE);
        if (user != null) {
            if (user.iconRemoved) {
                alrabeeaTimesImageView.setVisibility(View.INVISIBLE);
                athkarIcon.setVisibility(View.GONE);
            } else {
                try {
                    File file = HawkSettings.getPremiumUserCustomIcon();
                    if (file != null) {
                        alrabeeaTimesImageView.setImageURI(Uri.fromFile(file));
                        athkarIcon.setImageURI(Uri.fromFile(file));

                        athkarIcon.setImageTintMode(PorterDuff.Mode.MULTIPLY);
                        alrabeeaTimesImageView.setImageTintMode(PorterDuff.Mode.MULTIPLY);

                        // alrabeeaTimesImageView.setImageTintList(null);
                        // alrabeeaTimesImageView.clearColorFilter();
                        // athkarIcon.setImageTintList(null);
                        // athkarIcon.clearColorFilter();
                    }
                } catch (Exception e) {
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            }
        }

        logoImageEnabled = false;
        if (imageViewbrownnew == null)
            return;
        String imgPath = Hawk.get("imgbrown", "");
        Integer imgColor = Hawk.get("colorlogo", null);
        if (imgPath != null && !imgPath.isEmpty()) {
            imageViewbrownnew.setVisibility(View.VISIBLE);
            imageViewbrownnew.setImageURI(Uri.parse(imgPath));
            imageViewbrownnew.setScaleType(ImageView.ScaleType.FIT_CENTER);
            if (imgColor != null)
                imageViewbrownnew.setColorFilter(imgColor, PorterDuff.Mode.SRC_IN);
            else {
                imageViewbrownnew.setImageTintList(null);
                imageViewbrownnew.clearColorFilter();
            }

            logoImageEnabled = true;
        } else {
            imageViewbrownnew.setVisibility(View.INVISIBLE);
        }
    }

    public void setDayMonthImage() {
        runOnUiThread(() -> {
            setImageResource(hijri_month_image, getHijriMonthImageRes(hijriCalendar));
            setImageResource(gregorian_month_image, getGregorianMonthImageRes(gregorianCalendar));
            setImageResource(dayimage, getDayImageRes(gregorianCalendar));
        });
    }

    private void reverseIsHijri() {
        isHijri = !isHijri;
    }

    public void calcDate() {
        try {
            isJomaa = Utils.isJomaa();
            isJomaa = gregorianCalendar.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY;
            Locale arabic = new Locale("ar");
            Locale english = new Locale("en");
            gYear = "" + gregorianCalendar.get(Calendar.YEAR);
            gMonthAr = gregorianCalendar.getDisplayName(Calendar.MONTH, Calendar.LONG, arabic);
            gMonthEn = gregorianCalendar.getDisplayName(Calendar.MONTH, Calendar.LONG, english);
            gMonthNum = "" + gregorianCalendar.get(Calendar.MONTH) + 1;
            gDayNum = "" + gregorianCalendar.get(Calendar.DAY_OF_MONTH);
            gDayNameAr = gregorianCalendar.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, arabic);
            gDayNameEn = gregorianCalendar.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, english);

            hYear = "" + hijriCalendar.get(Calendar.YEAR);
            hMonthAr = hijriCalendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, arabic);
            hMonthEn = getResources().getStringArray(R.array.hijri_months_en)[hijriCalendar.get(Calendar.MONTH)];
            hDayNum = "" + hijriCalendar.get(Calendar.DAY_OF_MONTH);
        } catch (Exception e) {
            Log.e("timeProb", e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }

    /**
     * @noinspection t
     */
    @SuppressLint("SetTextI18n")
    public void refreshDate() {
        calcDate();

        runOnUiThread(() -> {
            if (dhuhrTextViewe != null) {
                dhuhrTextViewe.setText(isJomaa ? "Jomaa" : "Dhuhr");
                dhuhrTextView.setText(isJomaa ? "الجمعة" : "الظهر");
            } else
                setText(dhuhrTextView, Utils.getString(isJomaa ? R.string.jomaa : R.string.dhuhr));
        });

        final float progress = Hawk.get(ConstantsOfApp.BACKGROUND_IMAGE_ALPHA_KEY, 0.1F);
        if (Hawk.get("urlimagebak", null) != null && Hawk.get(ConstantsOfApp.BACKGROUND_IMAGE_KEY + "C", false)) {
            safeRunOnUi(() -> {
                String path = "file://" + Hawk.get("urlimagebak", "");
                setImage(containerImageView, path);
                containerImageView.setAlpha(progress);
                containerImageView.setColorFilter(Hawk.get("colorimage", 0xffffffff),
                        android.graphics.PorterDuff.Mode.MULTIPLY);
                containerImageView.setVisibility(View.VISIBLE);
            });
        }

        switch (uiTheme) {
            case BLUE:
            case DARK_GREEN:
            case DARK_GRAY:
            case BROWN:
                executeEvery(this::blueTime, 5);
                break;

            case GREEN:
                longTextInRemainForPrayer = true;
                if (isLandscape)
                    executeEvery(this::dateDarkGreenLandscape, 10);
                else
                    executeEvery(this::refreshFullDateAndDayName, 10);
                break;

            case RED:
                if (isLandscape)
                    dateRedLandscape();
                else
                    dateRedPortrait();
                break;

            case NEW_GREEN:
                if (isLandscape)
                    dateNewGreenLandscape();
                else
                    dateNewGreenPortrait();
                break;
            case WHITE:
            case WHITE_NEW:
                setDayMonthImage();
                longTextInRemainForPrayer = true;
                dateRedPortrait();
                break;
            case BLUE_NEW:
            case BROWN_NEW:
                executeEvery(this::refreshFullDateAndDayName, 10);
                break;
            case BLUE_LET:
                longTextInRemainForPrayer = false;
                executeEvery(this::dateBlueLett, 5);
                break;
            case BROWN_NEW_3:
                safeRunOnUi(this::setDayMonthImage);
                longTextInRemainForPrayer = true;
                runOnUiThread(() -> {
                    setText(dayText, gDayNameEn);
                    setText(datey, gYear);
                    setText(datem, gMonthEn);
                    setText(dateHTextView, gDayNum);
                    setText(datehy, hYear);
                    setText(datehm, hMonthEn);
                    setText(dateNowTextView, hDayNum);
                });
                break;
            case CUSTOM_1:
            case CUSTOM_FIREBASE:
                if (isLandscape) {
                    setImageResource(dayimage, getDayImageRes(gregorianCalendar));
                    setText(dayText, gDayNameEn);
                    executeEvery(() -> {

                        setText(dateHTextView, isHijri ? hDayNum : gDayNum);
                        setText(datem, isHijri ? hMonthEn : gMonthEn);
                        setText(datey, isHijri ? hYear + Utils.getString(R.string.code_hegira) : gYear);
                        setImageResource(gregorian_month_image, isHijri ? getHijriMonthImageRes(hijriCalendar)
                                : getGregorianMonthImageRes(gregorianCalendar));
                        isHijri = !isHijri;
                    }, 15);
                } else {
                    safeRunOnUi(this::setDayMonthImage);
                    longTextInRemainForPrayer = true;
                    runOnUiThread(() -> {
                        setText(dayText, gDayNameEn);
                        setText(datey, gYear);
                        setText(datem, gMonthEn);
                        setText(dateHTextView, gDayNum);
                        setText(datehy, hYear + Utils.getString(getBaseContext(), R.string.code_hegira));
                        setText(datehm, hMonthEn);
                        setText(dateNowTextView, hDayNum);
                    });
                }
                break;
        }
    }

    private void dateDarkGreenLandscape() {
        runOnUiThread(() -> {
            if (_dateDarkGreenLandscape_isHijri) {
                setText(dateNowTextView, hDayNum);
                setText(datehm, lan.equals("ar") ? hMonthAr : hMonthEn);
                setText(datehy, hYear + Utils.getString(getBaseContext(), R.string.code_hegira));
            } else {
                setText(dateNowTextView, gDayNum);
                setText(datehm, lan.equals("ar") ? gMonthAr : gMonthEn);
                setText(datehy, gYear);
            }
            _dateDarkGreenLandscape_isHijri = !_dateDarkGreenLandscape_isHijri;
        });
    }

    private void dateRedPortrait() {

        runOnUiThread(() -> {
            if (lan.equals("en")) {
                setText(datehm, hMonthEn);
                setText(datem, gMonthEn);
            } else {
                setText(datehm, hMonthAr);
                setText(datem, gMonthAr);
            }
            setText(datehy, hYear + Utils.getString(getBaseContext(), R.string.code_hegira));
            setText(dateNowTextView, hDayNum);
            setText(datey, gYear);
            setText(dateHTextView, gDayNum);
        });

    }

    private void dateNewGreenPortrait() {
        runOnUiThread(() -> {
            if (lan.equals("en")) {
                setText(datehm, hMonthEn + " " + hYear + Utils.getString(getBaseContext(), R.string.code_hegira));
                setText(datem, gMonthEn + " " + gYear + Utils.getString(getBaseContext(), R.string.code_greg));
                setText(datey, gDayNameEn);
            } else {
                setText(datehm, hMonthAr + " " + hYear + Utils.getString(getBaseContext(), R.string.code_hegira));
                setText(datem, gMonthAr + " " + gYear + Utils.getString(getBaseContext(), R.string.code_greg));
                setText(datey, gDayNameAr);
            }
            // setText(datehy, hYear + Utils.getString(getBaseContext(),
            // R.string.code_hegira));
            setText(dateNowTextView, hDayNum);
            // setText(datey, gYear);
            setText(dateHTextView, gDayNum);
        });
    }

    private void dateNewGreenLandscape() {
        runOnUiThread(() -> {
            if (lan.equals("en")) {
                setText(datey, gDayNameEn);
                setText(datehm, hMonthEn);
                setText(datem,
                        gDayNum + " " + gMonthEn + " " + gYear + Utils.getString(getBaseContext(), R.string.code_greg));
            } else {
                setText(datey, gDayNameAr);
                setText(datehm, hMonthAr);
                setText(datem,
                        gDayNum + " " + gMonthAr + " " + gYear + Utils.getString(getBaseContext(), R.string.code_greg));
            }
            setText(datehy, hYear + Utils.getString(getBaseContext(), R.string.code_hegira));

            setText(dateNowTextView, hDayNum);
            // setText(datey, gYear);
            // setText(dateHTextView, gDayNum);
        });
    }

    private void dateRedLandscape() {
        runOnUiThread(() -> {
            String date1;
            String date2;

            if (HawkSettings.isArabic()) {
                date1 = hDayNum + " " + hMonthAr + " " + hYear;
                date2 = gDayNameAr + " | " + gDayNum + " " + gMonthNum + " " + gYear;
            } else {
                date1 = hDayNum + " " + hMonthEn + " " + hYear;
                date2 = gDayNameEn + " | " + gDayNum + " " + gMonthNum + " " + gYear;
            }
            setText(dateNowTextView, date1);
            setText(dateNowTextView2, date2);
        });
    }

    private void refreshFullDateAndDayName() {
        runOnUiThread(() -> {
            if (_lastRefreshedIsDayName) {
                setVisibility(datem, View.GONE);
                setVisibility(datey, View.GONE);
                setVisibility(datehm, View.GONE);
                setVisibility(datehy, View.GONE);
                setVisibility(dateNowTextView, View.GONE);
                setText(dateHTextView, HawkSettings.isArabic() ? gDayNameAr : gDayNameEn);
            } else {
                setVisibility(datem, View.VISIBLE);
                setVisibility(datey, View.VISIBLE);
                setVisibility(datehm, View.VISIBLE);
                setVisibility(datehy, View.VISIBLE);
                setVisibility(dateNowTextView, View.VISIBLE);

                setText(datehm, HawkSettings.isArabic() ? hMonthAr : hMonthEn);
                setText(datem, HawkSettings.isArabic() ? gMonthAr : gMonthEn);
                setText(datehy, hYear + Utils.getString(getBaseContext(), R.string.code_hegira));
                setText(dateNowTextView, hDayNum);
                setText(datey, gYear);
                setText(dateHTextView, gDayNum);
            }
            _lastRefreshedIsDayName = !_lastRefreshedIsDayName;
        });
    }

    private String getAthkarDate() {
        String date;
        boolean isAr = HawkSettings.isArabic();

        if (_athkarCurrentDate <= 30) {

            date = hDayNum + " " + (isAr ? hMonthAr : hMonthEn) + " " + hYear + " " + (isAr ? gDayNameAr : gDayNameEn);
            /*
             * if (Utils.isArabic()) {
             * date = dateAlrabeeaTimes.getHijri().getDay()
             * + " " + dateAlrabeeaTimes.getHijri().getMonth().getAr()
             * + " " + dateAlrabeeaTimes.getHijri().getYear()
             * + " " + dateAlrabeeaTimes.getGregorian().getWeekday().getAr();
             * } else {
             * date = dateAlrabeeaTimes.getHijri().getDay()
             * + " " + dateAlrabeeaTimes.getHijri().getMonth().getEn()
             * + " " + dateAlrabeeaTimes.getHijri().getYear()
             * + " " + dateAlrabeeaTimes.getGregorian().getWeekday().getEn();
             * }
             */

        } else {
            date = gDayNum + " " + (isAr ? gMonthAr : gMonthEn) + " " + gYear + " " + (isAr ? gDayNameAr : gDayNameEn);

            /*
             * if (Utils.isArabic()) {
             * date = dateAlrabeeaTimes.getGregorian().getDay()
             * + " " + dateAlrabeeaTimes.getGregorian().getMonth().getAr()
             * + " " + dateAlrabeeaTimes.getGregorian().getYear()
             * + " " + dateAlrabeeaTimes.getGregorian().getWeekday().getAr();
             * } else {
             * date = dateAlrabeeaTimes.getGregorian().getDay()
             * + " " + dateAlrabeeaTimes.getGregorian().getMonth().getEn()
             * + " " + dateAlrabeeaTimes.getGregorian().getYear()
             * + " " + dateAlrabeeaTimes.getGregorian().getWeekday().getEn();
             * }
             */
            if (_athkarCurrentDate >= 60)
                _athkarCurrentDate = 0;
        }
        ssHijriDate = hDayNum + " " + (isAr ? hMonthAr : hMonthEn) + " " + hYear;
        ssDayName = (isAr ? gDayNameAr : gDayNameEn);
        ssMiladiDate = gDayNum + " " + (isAr ? gMonthAr : gMonthEn) + " " + gYear;
        _athkarCurrentDate++;
        return date;
    }

    private void blueTime() {
        boolean isAr = HawkSettings.isArabic();
        switch (_blueCurrentDate) {
            case 1:
                date = hDayNum + " " + (isAr ? hMonthAr : hMonthEn) + " " + hYear;
                break;
            case 2:
                date = gDayNum + " " + (isAr ? gMonthAr : gMonthEn) + " " + gYear;
                break;
            default:
                _blueCurrentDate = 0;
                date = isAr ? gDayNameAr : gDayNameEn;
                break;
        }
        _blueCurrentDate++;
        runOnUiThread(() -> setText(dateNowTextView, date));
    }

    void showEvent(String event) {
        alert = Utils.showEventAlert(this, event, 2 * MINUTES_MILLI_SECOND, domainColor);
        TextView title = alert.getTitle();
        title.setSingleLine(true);
        title.setTypeface(ResourcesCompat.getFont(this, R.font.droid_arabic_kufi));
        title.setEllipsize(TextUtils.TruncateAt.MARQUEE);
        title.setMarqueeRepeatLimit(-1);
        title.setFocusable(true);
        title.setTextColor(textColor);
        title.setTextSize(getResources().getDimension(com.intuit.sdp.R.dimen._18sdp));
        title.setFocusableInTouchMode(true);
        title.setHorizontallyScrolling(true);
        title.setSelected(true);
    }

    private boolean isAllowedToShowEvent() {
        if (HawkSettings.getShowEventsOnAllScreens()) {
            // show on all screens except pray and khutba
            return this.lastDisplayed != DisplayTypes.Pray && this.lastDisplayed != DisplayTypes.Khutba;
        }
        // show only on PrayerTimes screen
        return this.lastDisplayed == DisplayTypes.PrayerTimes;
    }

    private boolean eventDayArrived(Event event) {
        UmmalquraCalendar calendar = Utils.getUmmalquraCalendar();
        return (calendar.after(event.sCal) && calendar.before(event.eCal));
    }

    public void dateBlueLett() {
        if (dateNowTextView != null) {
            boolean isAr = HawkSettings.isArabic();
            if (isHijri) {
                dayNumber = hDayNum;
                monthString = isAr ? hMonthAr : hMonthEn + " " + hYear;
                dayName = isAr ? gDayNameAr : gDayNameEn;
            } else {
                dayNumber = gDayNum;
                monthString = isAr ? gMonthAr : gMonthEn + " " + gYear;
                dayName = isAr ? gDayNameAr : gDayNameEn;
            }
            safeRunOnUi(() -> {
                setText(dateNowTextView, dayNumber);
                setText(datem, monthString);
                setText(datey, dayName);
                reverseIsHijri();
            });
        }
    }

    /**
     * Execute a runnable repeatedly with handler-based delays instead of
     * Thread.sleep()
     */
    public void executeEvery(Runnable exec, int seconds) {
        try {
            // Stop any existing text update handler
            if (textUpdateHandler != null && textUpdateRunnable != null) {
                isTextUpdateHandlerRunning = false;
                textUpdateHandler.removeCallbacks(textUpdateRunnable);
            }

            // Create new runnable for repeated execution
            textUpdateRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!isTextUpdateHandlerRunning)
                            return;

                        // Execute the provided runnable
                        exec.run();

                        // Schedule next execution
                        if (isTextUpdateHandlerRunning && textUpdateHandler != null) {
                            textUpdateHandler.postDelayed(this, seconds * 1000L);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in executeEvery runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                        // Retry after delay on error
                        if (isTextUpdateHandlerRunning && textUpdateHandler != null) {
                            textUpdateHandler.postDelayed(this, seconds * 1000L);
                        }
                    }
                }
            };

            // Start the handler
            isTextUpdateHandlerRunning = true;
            if (textUpdateHandler != null) {
                textUpdateHandler.post(textUpdateRunnable);
                Log.d(TAG, "executeEvery started with " + seconds + " second intervals");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in executeEvery", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public void onItemClick(int position, String tag) {

    }

    /**
     * Select and display the next prayer without blocking the main thread
     */
    public void selectNextPrayer() {
        Log.e("selectNextPrayer", "selectNextPrayer ");
        lastDisplayed = DisplayTypes.PrayerTimes;
        boolean settled = false;
        for (PrayerType prayerType : PrayerType.values()) {
            PrayerTime prayerTime = prayerType.prayerTime;
            // displayAthkar(AthkarType.AfterPrayer, prayerType);
            if (prayerTime.isNowLockingDuringAzan()) {
                Log.e("selectNextPrayer", "isNowLockingDuringAzan " + prayerTime.prayerType.toString());

                AnnouncementMessage message = new AnnouncementMessage();
                message.type = AnnouncementType.AZAN;
                message.prayer = prayerType;
                displayAnnouncement(message);
                sleepTimeUntilNextRefresh = prayerTime.getTimeUntilUnlockAzan();
                lastDisplayed = DisplayTypes.Azan;

                // Schedule app restart 40 minutes after Athan
                scheduleRestartAfterAthan(prayerType);

                // Replace Thread.sleep() with handler-based delay
                // The main handler will automatically handle the timing
                settled = true;
            } else if (prayerTime.isNowAnnouncingForIkama()) {
                Log.e("selectNextPrayer", "isNowAnnouncingForIkama " + prayerTime.prayerType.toString());

                AnnouncementMessage message = new AnnouncementMessage();
                message.prayer = prayerType;
                long ikama = prayerTime.getIkamaTime();
                long remainIkama = ikama - System.currentTimeMillis();
                boolean isLessThanMinute = remainIkama <= MINUTES_MILLI_SECOND;
                if (isLessThanMinute) {
                    lastDisplayed = DisplayTypes.LastMinuiteBeforeAzan;
                    sleepTimeUntilNextRefresh = ikama;
                    message.type = AnnouncementType.MINUTE_BEFORE_IKAMA;
                } else {
                    if (isJomaa && prayerType == PrayerType.Dhuhr)
                        lastDisplayed = DisplayTypes.Khutba;
                    sleepTimeUntilNextRefresh = ikama - MINUTES_MILLI_SECOND;
                    message.type = AnnouncementType.BETWEEN_AZAN_AND_IKAMA;
                }

                displayAnnouncement(message);

                announcementManager.updateRemainingTime();
                /*
                 * while (System.currentTimeMillis() < sleepTimeUntilNextRefresh) {
                 * Thread.sleep(1000);
                 * announcementManager.updateRemainingTime();
                 * }
                 */
                // sleepTimeUntilNextRefresh = getCurrentTimePlusMinute();
                settled = true;
            } else if (prayerTime.isNowLockDuringPrayer()) {
                Log.e("selectNextPrayer", "isNowLockDuringPrayer " + prayerTime.prayerType.toString());

                sleepTimeUntilNextRefresh = prayerTime.getTimeUntilUnlockPrayer();
                Log.e("selectNextPrayer", "will sleep for " + sleepTimeUntilNextRefresh / 1000);
                AnnouncementMessage message = new AnnouncementMessage();
                message.type = AnnouncementType.PRAY;
                message.prayer = prayerType;
                displayAnnouncement(message);
                lastDisplayed = DisplayTypes.Pray;
                // Replace Thread.sleep() with handler-based delay
                // The main handler will automatically handle the timing
                Log.d(TAG, "Prayer time will be handled by main handler, sleep time: "
                        + sleepTimeUntilNextRefresh / 1000 + " seconds");
                settled = true;
            } else if (prayerTime.isNextAzan()) {
                Log.e("selectNextPrayer", "isNextAzan " + prayerTime.prayerType.toString());
                sleepTimeUntilNextRefresh = getCurrentTimePlusMinute();
                String timeRemainingNextPrayer = prayerTime.getTimeRemainForAzan(longTextInRemainForPrayer);
                displayPrayerTimes();
                safeRunOnUi(() -> setRemainingToPrayer(prayerType, timeRemainingNextPrayer));
                settled = true;
            } else if (prayerTime.isNextIkama()) {
                Log.e("mainActivity", "isNextIkama ");
                sleepTimeUntilNextRefresh = getCurrentTimePlusMinute();
                String timeRemainingForIkama = prayerTime.getTimeRemainForIkama();
                displayPrayerTimes();
                safeRunOnUi(() -> setRemainingToPrayer(prayerType, timeRemainingForIkama));
                settled = true;
            } else if (prayerTime.isNowLockingDuringAthkar()) {
                Log.e("mainActivity", "isNowLockingDuringAthkar ");
                sleepTimeUntilNextRefresh = prayerTime.getTimeUntilUnlockAthkar();
                displayAthkar(AthkarType.AfterPrayer, prayerType);
                settled = true;
            } else if (PrayerTime.isNowLockingDuringAthkar(AthkarType.MorningAthkar)) {
                sleepTimeUntilNextRefresh = PrayerTime.getTimeUntilUnlockAthkar(AthkarType.MorningAthkar);
                AthkarType type = AthkarType.MorningAthkar;
                displayAthkar(type, prayerType);
                settled = true;
            } else if (PrayerTime.isNowLockingDuringAthkar(AthkarType.EveningAthkar)) {
                sleepTimeUntilNextRefresh = PrayerTime.getTimeUntilUnlockAthkar(AthkarType.EveningAthkar);
                AthkarType type = AthkarType.EveningAthkar;
                displayAthkar(type, prayerType);
                settled = true;
            }
            if (settled)
                break;
        }
        if (!settled) {
            LastMaghrib = PrayerType.Maghrib.prayerTime.getAzanTime();
            TempTime = System.currentTimeMillis() + DAYS_MILLI_SECOND;
        }
        if (HawkSettings.isPhotoGalleryEnabled()) {
            long selectedId = HawkSettings.getSelectedPhotoGalleryId();
            if (selectedId < 0)
                return;

            // boolean check1 = selectedId >= 0;
            PhotoGallery gallery = PhotoGalleryRepository.get(selectedId);
            if (gallery == null || gallery.images.size() <= 0)
                return;
            // boolean check2 = gallery != null && gallery.images.size() > 0;
            boolean check3 = lastDisplayed == DisplayTypes.Announcement && gallery.isEnabledBetweenAzanAndIkama(); // check
            // if
            // its
            // between
            // azan
            // and
            // ikama
            boolean check4 = lastDisplayed == DisplayTypes.Khutba && gallery.isEnabledBetweenAzanAndIkamaJomaa();// check
            // if
            // it
            // is
            // khutba
            boolean check5 = lastDisplayed == DisplayTypes.PrayerTimes;
            // if (check1 && check2 && (check3 || check4 || check5)) {
            // if (check3 || check4 || check5) {
            if (check5) {
                // Replace Thread.sleep() with handler-based delays for photo gallery
                try {
                    schedulePhotoGalleryDisplay(gallery);
                } catch (Exception e) {
                    Log.e(TAG, "Error scheduling photo gallery display", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            }
        }

    }

    /**
     * Schedule photo gallery display using handlers instead of Thread.sleep()
     */
    private void schedulePhotoGalleryDisplay(PhotoGallery gallery) {
        try {
            if (gallery == null) {
                Log.w(TAG, "Gallery is null, cannot schedule display");
                return;
            }

            Log.d(TAG, "Scheduling photo gallery display with initial delay: " +
                    gallery.getPrayerTimesDuration().getTime() + "ms");

            // Schedule the initial display after prayer times duration
            Handler galleryHandler = new Handler(Looper.getMainLooper());
            galleryHandler.postDelayed(() -> {
                try {
                    displayPhotoGallery(gallery);

                    // Schedule the end of photo gallery display
                    galleryHandler.postDelayed(() -> {
                        try {
                            sleepTimeUntilNextRefresh = 0;
                            Log.d(TAG, "Photo gallery display completed");

                            // Trigger next refresh cycle
                            if (isMainHandlerRunning && mainHandler != null && mainRunnable != null) {
                                mainHandler.post(mainRunnable);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error completing photo gallery display", e);
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }, gallery.getSingleImageDuration().getTime());

                } catch (Exception e) {
                    Log.e(TAG, "Error displaying photo gallery", e);
                    CrashlyticsUtils.INSTANCE.logException(e);
                }
            }, gallery.getPrayerTimesDuration().getTime());

        } catch (Exception e) {
            Log.e(TAG, "Error scheduling photo gallery display", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public long getCurrentTimePlusMinute() {
        long current = System.currentTimeMillis();
        long secondRemain = current % MINUTES_MILLI_SECOND;
        return current - secondRemain + MINUTES_MILLI_SECOND;
    }

    public void disableAllRemainingPrayerText() {
        boolean isNext = false;
        changePrayerRemaining(PrayerType.Fajr, isNext, "");
        changePrayerRemaining(PrayerType.Sunrise, isNext, "");
        changePrayerRemaining(PrayerType.Dhuhr, isNext, "");
        changePrayerRemaining(PrayerType.Asr, isNext, "");
        changePrayerRemaining(PrayerType.Maghrib, isNext, "");
        changePrayerRemaining(PrayerType.Isha, isNext, "");
    }

    public ResourcesLocale getResources(String localeTarget) {
        return new ResourcesLocale(getBaseContext(), new Locale(HawkSettings.getLocaleLanguage()),
                new Locale(localeTarget));
    }

    private void addPrayer(PrayerType type) {
        try {
            // String ikama = type.prayerTime.getWithAdjustment();
            String[] time = Utils
                    .convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(type.prayerTime.getAzanTime(), "hh:mm a"));
            String[] h1 = Utils.convertDateTimeToTimeAndTypeTime(
                    Utils.getTimeFormatted(type.prayerTime.getIkamaTime(), "hh:mm a"));
            // String[] ikamaTime =
            // Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(type.prayerTime.getIkamaTime(),"hh:mm
            // a"));
            switch (type) {
                case Fajr:
                    // h1 =
                    // Utils.convertDateTimeToTimeAndTypeTime(Utils.convertTimePrayer(timingsAlrabeeaTimes.getIkamaToFajr(isRamadan)));
                    setText(fajrATimeTextView, getSpannableText(h1[0]));
                    setText(fajrATimeTypeTextView, getSpannableText(h1[1]));
                    setText(fajrTimeTextView, getSpannableText(time[0]));
                    setText(fajrTimeTypeTextView, getSpannableText(time[1]));
                    break;
                case Sunrise:
                    // if the view only showing sunrise or duha then set the same value, some themes
                    // have the wrong layout id for sunrise and duha
                    if (sunriseTimeTextView == null || sunriseATimeTextView == null) {
                        if (HawkSettings.getShowDuhaSetting() == 0)
                            time = Utils.convertDateTimeToTimeAndTypeTime(
                                    Utils.getTimeFormatted(PrayerType.Duha.prayerTime.getAzanTime(), "hh:mm a"));

                        setText(sunriseTimeTextView, getSpannableText(time[0]));
                        setText(sunriseATimeTextView, getSpannableText(time[0]));
                        setText(sunriseTimeTypeTextView, getSpannableText(time[1]));
                        setText(sunriseATimeTypeTextView, getSpannableText(time[1]));
                    } else {
                        setText(sunriseTimeTextView, getSpannableText(time[0]));
                        setText(sunriseTimeTypeTextView, getSpannableText(time[1]));
                        time = Utils.convertDateTimeToTimeAndTypeTime(
                                Utils.getTimeFormatted(PrayerType.Duha.prayerTime.getAzanTime(), "hh:mm a"));
                        setText(sunriseATimeTextView, getSpannableText(time[0]));
                        setText(sunriseATimeTypeTextView, getSpannableText(time[1]));
                    }

                    break;

                case Duha:
                    setText(sunriseATimeTextView, getSpannableText(time[0]));
                    setText(sunriseATimeTypeTextView, getSpannableText(time[1]));

                    break;
                case Dhuhr:
                    // h1 =
                    // Utils.convertDateTimeToTimeAndTypeTime(Utils.convertTimePrayer(timingsAlrabeeaTimes.getIkamaToDhuhrOrJomaa(isJomaa,
                    // isRamadan)));
                    setText(dhuhrATimeTextView, getSpannableText(h1[0]));
                    setText(dhuhrATimeTypeTextView, getSpannableText(h1[1]));
                    setText(dhuhrTimeTextView, getSpannableText(time[0]));
                    setText(dhuhrTimeTypeTextView, getSpannableText(time[1]));
                    // setText(dhuhrATimeTextView, h1[0]);
                    // setText(dhuhrATimeTypeTextView, h1[1]);
                    // setText(dhuhrTimeTextView, time[0]);
                    // setText(dhuhrTimeTypeTextView, time[1]);
                    break;
                case Asr:
                    // h1 =
                    // Utils.convertDateTimeToTimeAndTypeTime(Utils.convertTimePrayer(timingsAlrabeeaTimes.getIkamaToAsr(
                    // isRamadan)));
                    setText(asrATimeTextView, getSpannableText(h1[0]));
                    setText(asrATimeTypeTextView, getSpannableText(h1[1]));
                    setText(asrTimeTextView, getSpannableText(time[0]));
                    setText(asrTimeTypeTextView, getSpannableText(time[1]));
                    break;
                case Maghrib:
                    // h1 =
                    // Utils.convertDateTimeToTimeAndTypeTime(Utils.convertTimePrayer(timingsAlrabeeaTimes.getIkamaToMaghrib(
                    // isRamadan)));
                    setText(maghribATimeTextView, getSpannableText(h1[0]));
                    setText(maghribATimeTypeTextView, getSpannableText(h1[1]));
                    setText(maghribTimeTextView, getSpannableText(time[0]));
                    setText(maghribTimeTypeTextView, getSpannableText(time[1]));
                    break;
                case Isha:
                    // h1 =
                    // Utils.convertDateTimeToTimeAndTypeTime(Utils.convertTimePrayer(timingsAlrabeeaTimes.getIkamaToIsha(
                    // isRamadan)));
                    setText(ishaATimeTextView, getSpannableText(h1[0]));
                    setText(ishaATimeTypeTextView, getSpannableText(h1[1]));
                    setText(ishaTimeTextView, getSpannableText(time[0]));
                    setText(ishaTimeTypeTextView, getSpannableText(time[1]));
                    break;
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public enum DisplayTypes {
        PrayerTimes,
        Announcement,
        LastMinuiteBeforeAzan,
        Khutba,
        Athkar,
        PhotoGallery,
        Pray,
        Azan
    }

    public boolean isMidnight() {
        try {
            LocalTime now = LocalTime.now();
            boolean isMidnight = now.getHour() == 0 && now.getMinute() == 0;

            Log.d(TAG, "Checking midnight: " + isMidnight + " (hour: " + now.getHour() + ", minute: " + now.getMinute()
                    + ")");

            return isMidnight;
        } catch (Exception e) {
            Log.e(TAG, "Error checking midnight", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    public void restartApp() {
        try {
            Log.d(TAG, "Attempting to restart app");

            if (!isActivityInForeground(this, MainActivity.class)) {
                Log.d(TAG, "Activity not in foreground, skipping restart");
                return;
            }

            if (alreadyTriggered(this)) {
                Log.i(TAG, "App restart already triggered for this day");
                return;
            }

            saveTrigger(this);
            Log.i(TAG, "Restarting app at midnight");

            Intent restartIntent = new Intent(this, MainActivity.class);
            restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(restartIntent);

            // Finish current activity
            finish();

        } catch (Exception e) {
            Log.e(TAG, "Error restarting app", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private boolean alreadyTriggered(Context context) {
        try {
            Calendar calendar = Calendar.getInstance();
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int month = calendar.get(Calendar.MONTH) + 1;
            int year = calendar.get(Calendar.YEAR);
            String dateString = day + "/" + month + "/" + year;

            SharedPreferences sharedPreferences = context.getSharedPreferences("RefreshTask", Context.MODE_PRIVATE);
            String value = sharedPreferences.getString("lastTrigger", "");

            boolean triggered = dateString.equals(value);
            Log.d(TAG, "Checking if already triggered for " + dateString + ": " + triggered);

            return triggered;

        } catch (Exception e) {
            Log.e(TAG, "Error checking if already triggered", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false; // Assume not triggered on error
        }
    }

    private void saveTrigger(Context context) {
        try {
            Calendar calendar = Calendar.getInstance();
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int month = calendar.get(Calendar.MONTH) + 1;
            int year = calendar.get(Calendar.YEAR);
            String dateString = day + "/" + month + "/" + year;

            SharedPreferences sharedPreferences = context.getSharedPreferences("RefreshTask", Context.MODE_PRIVATE);
            sharedPreferences.edit().putString("lastTrigger", dateString).apply();

            Log.i(TAG, "Saved trigger for this day: " + dateString);

        } catch (Exception e) {
            Log.e(TAG, "Error saving trigger", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    // ========== 40-MINUTE RESTART AFTER ATHAN FEATURE ==========

    /**
     * Schedule app restart 40 minutes after Athan
     */
    private void scheduleRestartAfterAthan(PrayerType prayerType) {
        try {
            // Check if restart after Athan is enabled
            if (!isRestartAfterAthanEnabled()) {
                Log.d(TAG, "Restart after Athan is disabled");
                return;
            }

            // Check if already scheduled to avoid duplicate scheduling
            if (isRestartAfterAthanScheduled) {
                Log.d(TAG, "Restart after Athan already scheduled, skipping");
                return;
            }

            // Check if we already restarted recently for this prayer
            if (hasRecentlyRestarted()) {
                Log.d(TAG, "App was recently restarted after Athan, skipping");
                return;
            }

            Log.i(TAG, "Scheduling app restart 40 minutes after " + prayerType.getName() + " Athan");

            if (restartAfterAthanHandler != null && restartAfterAthanRunnable != null) {
                isRestartAfterAthanScheduled = true;
                restartAfterAthanHandler.postDelayed(restartAfterAthanRunnable, RESTART_DELAY_AFTER_ATHAN);

                Log.i(TAG, "App restart scheduled for " + (RESTART_DELAY_AFTER_ATHAN / 60000) +
                        " minutes after " + prayerType.getName() + " Athan");
            } else {
                Log.e(TAG, "Restart after Athan handler or runnable is null");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error scheduling restart after Athan", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            isRestartAfterAthanScheduled = false;
        }
    }

    /**
     * Cancel scheduled restart after Athan
     */
    private void cancelRestartAfterAthan() {
        try {
            if (isRestartAfterAthanScheduled && restartAfterAthanHandler != null && restartAfterAthanRunnable != null) {
                restartAfterAthanHandler.removeCallbacks(restartAfterAthanRunnable);
                isRestartAfterAthanScheduled = false;
                Log.d(TAG, "Cancelled scheduled restart after Athan");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error cancelling restart after Athan", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Restart the app after Athan
     */
    private void restartAppAfterAthan() {
        try {
            Log.i(TAG, "Restarting app 40 minutes after Athan");

            if (!isActivityInForeground(this, MainActivity.class)) {
                Log.d(TAG, "Activity not in foreground, skipping restart after Athan");
                return;
            }

            // Create restart intent
            Intent restartIntent = new Intent(this, MainActivity.class);
            restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            restartIntent.putExtra("restarted_after_athan", true);

            startActivity(restartIntent);

            // Finish current activity
            finish();

        } catch (Exception e) {
            Log.e(TAG, "Error restarting app after Athan", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Check if restart after Athan feature is enabled
     */
    private boolean isRestartAfterAthanEnabled() {
        try {
            // For now, always enabled. Can be made configurable via settings
            return true;
            // return Hawk.get(RESTART_AFTER_ATHAN_KEY, true);
        } catch (Exception e) {
            Log.e(TAG, "Error checking restart after Athan setting", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return true; // Default to enabled
        }
    }

    /**
     * Check if app was recently restarted to avoid multiple restarts
     */
    private boolean hasRecentlyRestarted() {
        try {
            long lastRestartTime = Hawk.get(LAST_ATHAN_RESTART_KEY, 0L);
            long currentTime = System.currentTimeMillis();
            long timeSinceLastRestart = currentTime - lastRestartTime;

            // Consider "recent" as within the last 30 minutes
            boolean isRecent = timeSinceLastRestart < (30 * 60 * 1000);

            if (isRecent) {
                Log.d(TAG, "Last restart was " + (timeSinceLastRestart / 60000) + " minutes ago");
            }

            return isRecent;

        } catch (Exception e) {
            Log.e(TAG, "Error checking recent restart", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    /**
     * Save the time when app was restarted after Athan
     */
    private void saveLastAthanRestartTime() {
        try {
            long currentTime = System.currentTimeMillis();
            Hawk.put(LAST_ATHAN_RESTART_KEY, currentTime);
            Log.d(TAG, "Saved last Athan restart time: " + currentTime);
        } catch (Exception e) {
            Log.e(TAG, "Error saving last Athan restart time", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    // ========== UTILITY METHODS FOR IMPROVED CODE ORGANIZATION ==========

    /**
     * Safe method to run code on UI thread with error handling
     */
    public void safeRunOnUi(Runnable runnable) {
        try {
            if (runnable != null) {
                runOnUiThread(() -> {
                    try {
                        runnable.run();
                    } catch (Exception e) {
                        Log.e(TAG, "Error in UI thread runnable", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error running on UI thread", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Safe method to set text with null checks
     */
    public void setText(TextView textView, String text) {
        if (textView != null && text != null) {
            textView.setText(text);
        }
    }

    /**
     * Safe method to set visibility with null checks
     */
    public void setVisibility(View view, int visibility) {
        if (view != null) {
            view.setVisibility(visibility);
        }
    }

    /**
     * Safe method to set text color with null checks
     */
    public void setTextColor(TextView textView, int color) {
        if (textView != null) {
            textView.setTextColor(color);
        }
    }

    /**
     * Safe method to set image resource with null checks
     */
    public void setImageResource(ImageView imageView, int resourceId) {
        if (imageView != null && resourceId != 0) {
            try {
                imageView.setImageResource(resourceId);
            } catch (Exception e) {
                Log.e(TAG, "Error setting image resource", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Safe method to set color filter with null checks
     */
    public void setColorFilter(ImageView imageView, int color) {
        if (imageView != null) {
            try {
                imageView.setColorFilter(color);
            } catch (Exception e) {
                Log.e(TAG, "Error setting color filter", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Safe method to set color filter for views with null checks
     */
    public void setColorFilterView(View view, int color) {
        if (view != null) {
            try {
                view.getBackground().setColorFilter(color, PorterDuff.Mode.SRC_IN);
            } catch (Exception e) {
                Log.e(TAG, "Error setting view color filter", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Safe method to set background color with null checks
     */
    public void setBackgroundColor(View view, String imagePath) {
        if (view != null && imagePath != null && !imagePath.isEmpty()) {
            try {
                // Implementation for setting background from image path
                // This would need to be implemented based on your specific requirements
                Log.d(TAG, "Setting background color from path: " + imagePath);
            } catch (Exception e) {
                Log.e(TAG, "Error setting background color", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Safe method to set image with null checks
     */
    public void setImage(ImageView imageView, String imagePath) {
        if (imageView != null && imagePath != null && !imagePath.isEmpty()) {
            try {
                // Use Picasso or similar library to load image
                Log.d(TAG, "Setting image from path: " + imagePath);
                // Picasso.get().load(imagePath).into(imageView);
            } catch (Exception e) {
                Log.e(TAG, "Error setting image", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Safe method to set selected state with null checks
     */
    public void setSelected(TextView textView) {
        if (textView != null) {
            try {
                textView.setSelected(true);
            } catch (Exception e) {
                Log.e(TAG, "Error setting selected state", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Safe method to scale text view size with null checks
     */
    public void scaleTextViewSize(float percent, TextView textView) {
        if (textView != null && percent > 0) {
            try {
                float currentSize = textView.getTextSize();
                textView.setTextSize(currentSize * percent);
            } catch (Exception e) {
                Log.e(TAG, "Error scaling text view size", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    /**
     * Get spannable text safely
     */
    public SpannableString getSpannableText(String text) {
        try {
            return new SpannableString(text);
        } catch (Exception e) {
            Log.e(TAG, "Error getting spannable text", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return null;
        }
    }

    /**
     * Log method for debugging
     */
    private void log(String message) {
        Log.d(TAG, message);
    }

}
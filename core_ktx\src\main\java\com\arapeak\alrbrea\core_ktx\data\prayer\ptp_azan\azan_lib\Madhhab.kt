package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib

/**
 * <PERSON>hh<PERSON> is used for Assr prayer calculation.
 */
class Madhhab(val name: String = "") {
    companion object {
        /**
         * Assr prayer shadow ratio: use <PERSON><PERSON><PERSON><PERSON><PERSON> mathhab (default)
         */
        val SHAAFI = <PERSON>hhab("SHAAFI")

        /**
         * Assr prayer shadow ratio: use <PERSON><PERSON>i mathhab
         */
        val HANAFI = <PERSON>hh<PERSON>("HANAFI")
    }
}

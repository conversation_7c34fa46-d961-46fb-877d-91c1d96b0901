package com.arapeak.alrbea.UI.Activity.HomeUi;

import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.AnnouncementMessage;
import com.arapeak.alrbea.Model.PhotoGallery;

public interface CallbackInterfaces {

    interface OnPrayerTimeUpdateListener {
        void onNextPrayerTimeUpdate(PrayerType prayerType, String timeRemaining, boolean longText);
        void onDisableAllRemainingPrayerText();
        void onRefreshDateUI(); // To trigger date display logic on UI thread
        void onUpdateSunriseDuhaNames(); // To update sunrise/duha names
    }

    interface OnContentDisplayListener {
        void onDisplayAnnouncement(AnnouncementMessage message);
        void onDisplayAthkar(AthkarType athkarType, PrayerType prayerType);
        void onDisplayPhotoGallery(PhotoGallery gallery);
        void onDisplayPrayerTimes(); // To revert to prayer times view
        void onHideTextDesign(); // To hide custom text design
        void onShowTextDesign(); // To show custom text design
    }

    interface OnEventMessageListener {
        void onShowEvent(String eventText);
    }

    interface OnAppLifecycleListener {
        void onRestartApp();
    }
}

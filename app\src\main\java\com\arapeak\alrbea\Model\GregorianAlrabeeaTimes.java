package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class GregorianAlrabeeaTimes {
    @Expose
    @SerializedName("designation")
    private DesignationAlrabeeaTimes designation;
    @Expose
    @SerializedName("year")
    private String year;
    @Expose
    @SerializedName("month")
    private MonthAlrabeeaTimes month;
    @Expose
    @SerializedName("weekday")
    private WeekdayAlrabeeaTimes weekday;
    @Expose
    @SerializedName("day")
    private String day;
    @Expose
    @SerializedName("format")
    private String format;
    @Expose
    @SerializedName("date")
    private String date;

    public DesignationAlrabeeaTimes getDesignation() {
        return designation;
    }

    public void setDesignation(DesignationAlrabeeaTimes designation) {
        this.designation = designation;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public MonthAlrabeeaTimes getMonth() {
        return month;
    }

    public void setMonth(MonthAlrabeeaTimes month) {
        this.month = month;
    }

    public WeekdayAlrabeeaTimes getWeekday() {
        return weekday;
    }

    public void setWeekday(WeekdayAlrabeeaTimes weekday) {
        this.weekday = weekday;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import androidx.annotation.NonNull;

import org.jetbrains.annotations.NotNull;

import kotlin.jvm.internal.Intrinsics;


public class Time {

    /* renamed from: a, reason: collision with root package name */
    @NotNull
    private final String f3783a;
    private final double b;

    /* renamed from: c, reason: from kotlin metadata */
    private int m_hour;

    /* renamed from: d, reason: from kotlin metadata */
    private int m_minute;

    /* renamed from: e, reason: from kotlin metadata */
    private int m_second;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata */
    @NotNull
    private String m_zone;

    public Time(double d, boolean z, @NotNull Hour hour) {
        double d2;
        Intrinsics.checkNotNullParameter(hour, "hour");
        this.m_zone = "AM";
        if (d < Preference.DEFAULT_SEA_LEVEL && hour.type() == Hour.Type.hour24) {
            double d3 = 1440;
            Double.isNaN(d3);
            d2 = d3 + d;
        } else {
            d2 = d;
        }
        if (d2 < Preference.DEFAULT_SEA_LEVEL && hour.type() == Hour.Type.hour12) {
            double d4 = 720;
            Double.isNaN(d4);
            d2 = d4 + d;
        }
        this.b = d2;
        this.f3783a = convertToTime(d2, z, hour);
    }

    @NotNull
    public String convertToTime(double myVar, boolean isAM, @NotNull Hour hour24_12) {
        Intrinsics.checkNotNullParameter(hour24_12, "hour24_12");
        int i = (int) myVar;
        int i2 = i / 60;
        int i3 = i - (i2 * 60);
        this.m_zone = "AM";
        int i4 = 12;
        if (i2 == 12) {
            this.m_zone = "PM";
        }
        if (i2 > 12) {
            this.m_zone = "PM";
            if (i2 > 24) {
                i2 %= 24;
                this.m_zone = "AM";
                if (i2 >= 12) {
                    this.m_zone = "PM";
                }
            } else if (i2 == 24) {
                this.m_zone = "AM";
                i2 = hour24_12.type() == Hour.Type.hour12 ? 12 : 0;
            } else if (hour24_12.type() == Hour.Type.hour12) {
                i2 %= 12;
            }
        }
        if (hour24_12.type() != Hour.Type.hour12 || i2 != 0) {
            i4 = i2;
        }
        String a2 = "" + i4;
        String a3 = "" + i3;
        if (a2.length() == 1) {
            a2 = "0".concat(a2);
        }
        if (a3.length() == 1) {
            a3 = "0".concat(a3);
        }
        return a2 + ':' + a3 + ":00 " + this.m_zone;
    }

    /* renamed from: getM_hour$prayer_time_mobileRelease, reason: from getter */
    public final int getM_hour() {
        return this.m_hour;
    }

    /* renamed from: getM_minute$prayer_time_mobileRelease, reason: from getter */
    public final int getM_minute() {
        return this.m_minute;
    }

    /* renamed from: getM_second$prayer_time_mobileRelease, reason: from getter */
    public final int getM_second() {
        return this.m_second;
    }

    @NotNull
    public final String getM_zone() {
        return this.m_zone;
    }

    public final void setM_zone(@NotNull String myVal) {
        Intrinsics.checkNotNullParameter(myVal, "myVal");
        this.m_zone = myVal;
    }

    @NotNull
    public final String getM_zone$prayer_time_mobileRelease() {
        return this.m_zone;
    }

    public final void setM_zone$prayer_time_mobileRelease(@NotNull String str) {
        Intrinsics.checkNotNullParameter(str, "<set-?>");
        this.m_zone = str;
    }

    public final int hour() {
        return this.m_hour;
    }

    public final int minute() {
        return this.m_minute;
    }

    public final int second() {
        return this.m_second;
    }

    public final void setM_hour$prayer_time_mobileRelease(int i) {
        this.m_hour = i;
    }

    public final void setM_minute$prayer_time_mobileRelease(int i) {
        this.m_minute = i;
    }

    public final void setM_second$prayer_time_mobileRelease(int i) {
        this.m_second = i;
    }

    @NotNull
    /* renamed from: text, reason: from getter */
    public final String getF3783a() {
        return this.f3783a;
    }

    /* renamed from: text_ignoreConvert, reason: from getter */
    public final double getB() {
        return this.b;
    }

    @NotNull
    public final String zone() {
        return this.m_zone;
    }

    @NonNull
    @Override
    public String toString() {
        return "Time{" +
                "m_hour=" + m_hour +
                ", m_minute=" + m_minute +
                ", m_zone='" + m_zone + '\'' +
                '}';
    }
}

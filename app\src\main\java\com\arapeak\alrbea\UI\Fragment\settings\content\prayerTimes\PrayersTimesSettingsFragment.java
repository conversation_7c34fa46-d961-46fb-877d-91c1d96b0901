package com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.content.PrayerTimesSettingsFragment;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.List;

public class PrayersTimesSettingsFragment extends AlrabeeaTimesFragment implements AdapterCallback {

    private static final String TAG = "PrayersTimesSettingsFragment";

    private View prayersTimesSettingsView;
    private RecyclerView settingItemRecyclerView;
    private Dialog loadingDialog;

    private SettingsAdapter settingsAdapter;

    public PrayersTimesSettingsFragment() {

    }

    public static PrayersTimesSettingsFragment newInstance() {
        return new PrayersTimesSettingsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        prayersTimesSettingsView = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return prayersTimesSettingsView;
    }

    private void initView() {
        settingItemRecyclerView = prayersTimesSettingsView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        settingsAdapter = new SettingsAdapter(getContext(), setupSettings(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(getString(R.string.prayer_times_settings));
//        } else {
//            SettingsActivity.setTextTite(getString(R.string.prayer_times_settings));
//        }
        SettingsActivity.setTextTite(getString(R.string.prayer_times_settings));
        settingItemRecyclerView.setAdapter(settingsAdapter);

    }

    private void SetAction() {


    }

    @Override
    public void onItemClick(int position, String tag) {
        switch (position) {
            case 0:
                Utils.loadFragment(PrayerTimesSettingsFragment.newInstance(false)
                        , getAppCompatActivity()
                        , 0);
                break;
            case 1:
                Utils.loadFragment(PrayerTimesSettingsFragment.newInstance(true)
                        , getAppCompatActivity()
                        , 0);
                break;
        }
    }

    private List<SettingAlrabeeaTimes> setupSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.prayers_times_settings_title);
        String[] generalSettingsDescriptionArray = getResources().getStringArray(R.array.prayers_times_settings_description);

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[0]
                , generalSettingsDescriptionArray[0]
                , R.drawable.timeprymer));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[1]
                , generalSettingsDescriptionArray[1]
                , R.drawable.timeprymer));

        return settingAlrabeeaTimes;
    }
}


package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class AppInfo {

    @Expose
    @SerializedName("linkV2")
    private String link;
    @Expose
    @SerializedName("versionV2")
    private int version;
    @Expose
    @SerializedName("nameV2")
    private String name;
    @Expose
    @SerializedName("idV2")
    private int id;

    public String getLink() {
        return Utils.getValueWithoutNull(link);
    }

    public void setLink(String link) {
        this.link = link;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getName() {
        return Utils.getValueWithoutNull(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}

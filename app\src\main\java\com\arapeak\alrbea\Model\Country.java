package com.arapeak.alrbea.Model;


import io.realm.RealmObject;

public class Country extends RealmObject {
    private String id;
    private String name_en;
    private String name_ar;
    private String name_tr;
    private Long phone_code;
    private Long timezone;
    private Long default_calc_method;
    private Long default_mazhab;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName_en() {
        return name_en;
    }

    public void setName_en(String name_en) {
        this.name_en = name_en;
    }

    public String getName_ar() {
        if (name_ar == null || name_ar.isEmpty()) {
            return getName_en();
        }
        return name_ar;
    }

    public void setName_ar(String name_ar) {
        this.name_ar = name_ar;
    }

    public String getName_tr() {
        return name_tr;
    }

    public void setName_tr(String name_tr) {
        this.name_tr = name_tr;
    }

    public Long getPhone_code() {
        return phone_code;
    }

    public void setPhone_code(Long phone_code) {
        this.phone_code = phone_code;
    }

    public Long getTimezone() {
        return timezone;
    }

    public void setTimezone(Long timezone) {
        this.timezone = timezone;
    }

    public Long getDefault_calc_method() {
        return default_calc_method;
    }

    public void setDefault_calc_method(Long default_calc_method) {
        this.default_calc_method = default_calc_method;
    }

    public Long getDefault_mazhab() {
        return default_mazhab;
    }

    public void setDefault_mazhab(Long default_mazhab) {
        this.default_mazhab = default_mazhab;
    }

    /*private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }*/


}
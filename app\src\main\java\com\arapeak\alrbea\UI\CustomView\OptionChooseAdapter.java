package com.arapeak.alrbea.UI.CustomView;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerMethod;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.PrayerSystemsSchools;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimesList;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;

import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class OptionChooseAdapter extends RecyclerView.Adapter<OptionChooseAdapter.OptionChooseViewHolder> {

    public static final String TAG = "OptionChooseAdapter";


    private final Context context;
    private final List<Object> arrayListItem;
    private final LayoutInflater layoutInflater;
    private final AdapterCallback mCallback;
    private final ViewsAlrabeeaTimes viewsAlrabeeaTimes;
    private final String titleDialog;
    private final String descriptionDialog;
    private int positionRadioButtonChecked;
    private boolean isCheckAllFalse;
    private int id;

    public OptionChooseAdapter(Context context
            , List<Object> arrayListItem
            , ViewsAlrabeeaTimes viewsAlrabeeaTimes
            , String titleDialog
            , String descriptionDialog
            , AdapterCallback mCallback) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.viewsAlrabeeaTimes = viewsAlrabeeaTimes;
        this.mCallback = mCallback;
        this.titleDialog = titleDialog;
        this.descriptionDialog = descriptionDialog;
        positionRadioButtonChecked = -1;

        isCheckAllFalse = false;

        layoutInflater = LayoutInflater.from(this.context);
    }


    @NonNull
    @Override
    public OptionChooseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        View view = layoutInflater.inflate(R.layout.layout_list_item_option_choose, parent, false);

        OptionChooseViewHolder viewHolder = new OptionChooseViewHolder(view);

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull OptionChooseViewHolder savedVoiceViewHolder, int position) {

        savedVoiceViewHolder.onBind(position);
    }

    @Override
    public int getItemCount() {
//        return arrayListItem.size();
        return arrayListItem.size();
    }


    public void add(Object item) {
        if (item == null) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.add(item);
        notifyItemInserted(lastItemIndex);
        notifyDataSetChanged();
    }

    public void addAll(List<Object> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void addStringAll(List<String> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void addPrayerSystemsSchoolsAll(List<PrayerSystemsSchools> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void clear() {
        arrayListItem.clear();
        notifyDataSetChanged();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPositionRadioButtonChecked() {
        return positionRadioButtonChecked;
    }

    public Object getItem(int position) {
        if (position < 0 || position >= getItemCount()) {
            return null;
        }
        return arrayListItem.get(position);
    }

    public void setCheckAllFalse(boolean checkAllFalse) {
        isCheckAllFalse = checkAllFalse;
    }

    class OptionChooseViewHolder extends RecyclerView.ViewHolder {

        private final TextView titleTextView;
        private final TextView descriptionTextView;
        private final CheckBox optionCheckBox;
        private final AppCompatRadioButton optionAppCompatRadioButton;

        public OptionChooseViewHolder(@NonNull View itemView) {
            super(itemView);

            titleTextView = itemView.findViewById(R.id.title_TextView_OptionChooseViewHolder);
            descriptionTextView = itemView.findViewById(R.id.description_TextView_OptionChooseViewHolder);
            optionCheckBox = itemView.findViewById(R.id.option_CheckBox_OptionChooseViewHolder);
            optionAppCompatRadioButton = itemView.findViewById(R.id.option_AppCompatRadioButton_OptionChooseViewHolder);


        }


        private void readExcelFile(Context context, String filename) {


            try {
                // Creating Input Stream
                File file = new File(filename);
                FileInputStream myInput = new FileInputStream(file);

                // Create a POIFSFileSystem object
                POIFSFileSystem myFileSystem = new POIFSFileSystem(myInput);

                // Create a workbook using the File System

                HSSFWorkbook wb = new HSSFWorkbook(myFileSystem);

                HSSFSheet sheet = wb.getSheetAt(0);

                // Array a=null;
                ArrayList<JSONObject> a = new ArrayList<JSONObject>();
                Iterator<Row> rows = sheet.iterator();

                while (rows.hasNext()) {
                    JSONObject mylist = new JSONObject();
                    Row row = rows.next();
                    Iterator<Cell> cellIterator = row.cellIterator();
                    while (cellIterator.hasNext()) {

                        Cell cell = cellIterator.next();


                        String day = "";
                        String month = "";
                        if (cell.getColumnIndex() == 0) {
                            String date = cell.getDateCellValue().toString();

                            String dtStart = "2010-10-15T09:27:37Z";
                            Date date2 = cell.getDateCellValue();
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(date2);
                            day = String.valueOf(date2.getDay());
                            mylist.put("Month", cal.get(Calendar.MONTH) + 1);
                            mylist.put("Day", cal.get(Calendar.DAY_OF_MONTH));
                            //    System.out.println(cell.getDateCellValue().getDay() + "\t\t"+cell.getDateCellValue().getMonth());
                        }


                        if (cell.getColumnIndex() == 1) {
                            mylist.put("Fajr", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                            // System.out.println(cell.getDateCellValue().getHours()+":" +  cell.getDateCellValue().getMinutes()+"\t\t");
                        }
                        if (cell.getColumnIndex() == 2) {
                            mylist.put("Sunrise", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                        }
                        if (cell.getColumnIndex() == 3) {
                            mylist.put("Dhuhr", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                        }
                        if (cell.getColumnIndex() == 4) {
                            mylist.put("Asr", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                        }
                        if (cell.getColumnIndex() == 5) {
                            mylist.put("Maghrib", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                        }
                        if (cell.getColumnIndex() == 6) {
                            mylist.put("Isha", cell.getDateCellValue().getHours() + ":" + cell.getDateCellValue().getMinutes());
                        }


                /*    System.out.println(df.parseObject(date));
                        Date date1 = (Date) df.parseObject(date);
                       ;
                        month= String.valueOf(date1.getMonth());



                                }

                                String Fajr=row.getCell(1).toString();
                                System.out.println(Fajr);
                                String Sunrise=row.getCell(2).toString();
                                String Dhuhr=row.getCell(3).toString();
                                String Asr=row.getCell(4).toString();
                                String Maghrib=row.getCell(5).toString();
                                String Isha=row.getCell(6).toString();
                                mylist.put("Month",cal.get(Calendar.MONTH));
                                mylist.put("Day",day);
                                mylist.put("Fajr",Fajr);
                                mylist.put("Sunrise",Sunrise);
                                mylist.put("Dhuhr",Dhuhr);
                                mylist.put("Asr",Asr);
                                mylist.put("Maghrib",Maghrib);
                                mylist.put("Isha",Isha);
                                a.add(mylist);
//                   sendFile(a+"");
*/


                    }
                    a.add(mylist);
//


                }
                sendFile(a + "");
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }

        }

        private void sendFile(String pathTextFile) {
            try {


                String json = pathTextFile;
                Log.e(TAG, "json: " + json);

                if (Utils.getValueWithoutNull(json).isEmpty()) {

                    //  loadingDialog.dismiss();
                    return;
                }

//            if (json.contains("\"Month\": \"\"")) {
//                json = json.substring(0, json.lastIndexOf(","));
//            }

                if (json.contains(",\n" +
                        "\t{\n" +
                        "\t\t\"Month\": \"\"\n" +
                        "\t}")) {

                    json = json.trim().replaceAll("\n" +
                            "\t\t\"Month\": \"\"\n", "").trim();

                    if (json.trim().endsWith("}")) {
                        json = json.trim().substring(0, json.lastIndexOf("}")).trim();
                    }
                    if (json.trim().endsWith("{")) {
                        json = json.trim().substring(0, json.lastIndexOf("{")).trim();
                    }
                    if (json.trim().endsWith(",")) {
                        json = json.trim().substring(0, json.lastIndexOf(",")).trim();
                    }
                }

                if (!json.trim().toLowerCase().contains("\"timings\":")) {
//                json = "{\n\"timings\": " + json.trim() + "\n}";
                    json = "{\n" + "\t\"timings\": " + json.trim() + "\n}";
                    json = json.replaceAll(" \uFEFF", " ");
                }

//            Log.e(TAG, "json: " + json);
                GsonBuilder builder = new GsonBuilder();
                Gson mGson = builder.create();
                TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
                        = mGson.fromJson(json
                                .trim()
//                            .toLowerCase()
                                .replaceAll("aa", "am")
                                .replaceAll("pp", "pm")
                        , TimingsAlrabeeaTimesList.class);

                Log.e(TAG, "timingsAlrabeeaTimesList: " + timingsAlrabeeaTimesList.getTimingsAlrabeeaTimesList().toString());

//            TimingsAlrabeeaTimesList timingsAlrabeeaTimesList
//                    = mGson.fromJson(json.trim()
//                    , TimingsAlrabeeaTimesList.class);

                Hawk.put(ConstantsOfApp.TIMINGS_ALRABEEA_TIMES_LIST_KEY, timingsAlrabeeaTimesList);
                Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, true);

                AlrabeeaTimesRequests.getPrayerTimesThisYear(ConstantsOfApp.BASE_URL_ALADHAN
                        , ConstantsOfApp.LATITUDE_DEFAULT_KEY
                        , ConstantsOfApp.LONGITUDE_DEFAULT_KEY
                        , ConstantsOfApp.PRAYER_METHOD_DEFAULT_KEY
                        , ConstantsOfApp.ADJUST_HIJRI_DATE_DEFAULT_KEY
                        , Utils.getEnglishDateTime(ConstantsOfApp.YEAR)
                        , true
                        , new OnCompleteListener<PrayerApi, String>() {
                            @Override
                            public void onSuccess(PrayerApi prayerApi) {
                                // loadingDialog.dismiss();
                                if (prayerApi != null) {
//                                    MainActivity.prayerApi = prayerApi;
                                    Hawk.put(Utils.getKeyPrayerApi(), prayerApi);
                                    if (prayerApi != null && prayerApi.getPrayerList() != null && prayerApi.getPrayerList().getJanuary().size() > 0) {
                                        Hawk.put(ConstantsOfApp.LAST_UPDATE_DATA_PRAYER_TIME_KEY, System.currentTimeMillis());
                                    }
                                    Intent intentMainActivity = new Intent(context, MainActivity.class);
                                    /*if (Utils.isLandscape(context)) {
                                        intentMainActivity = new Intent(context, MainLandscapeActivity.class);
//                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                    } else {
                                        intentMainActivity = new Intent(context, MainActivity.class);
//                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                    }*/

                                    intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                                    context.startActivity(intentMainActivity);
                                    // getAppCompatActivity().finish();


                                }
                            }

                            @Override
                            public void onFail(String object) {
                                if (object instanceof String) {

                                }
                                // loadingDialog.dismiss();
                            }
                        });
//            Log.e(TAG, "timingsAlrabeeaTimesList.toString: " + timingsAlrabeeaTimesList.toString());
            } catch (Exception e) {
                //  loadingDialog.dismiss();
                CrashlyticsUtils.INSTANCE.logException(e);

            }
        }

        public void onBind(final int position) {

            Object object = arrayListItem.get(position);
            String title = "";

            if (object instanceof String) {
                title = (String) object;
            } else if (object instanceof PrayerMethod) {
                PrayerMethod prayerMethod = (PrayerMethod) object;
                title = prayerMethod.toString();
            }

            if (position == 0) {
                titleTextView.setVisibility(View.VISIBLE);
                descriptionTextView.setVisibility(View.VISIBLE);

                titleTextView.setText(titleDialog);
                descriptionTextView.setText(descriptionDialog);
            } else {
                titleTextView.setVisibility(View.GONE);
                descriptionTextView.setVisibility(View.GONE);
            }

            optionCheckBox.setVisibility(View.GONE);
            optionAppCompatRadioButton.setVisibility(View.GONE);

            switch (viewsAlrabeeaTimes) {
                case RADIO_BUTTON:
                    optionAppCompatRadioButton.setText(title);
                    optionAppCompatRadioButton.setVisibility(View.VISIBLE);

                    if (isCheckAllFalse) {
                        optionAppCompatRadioButton.setChecked(false);
                        isCheckAllFalse = false;
                    }

                    optionAppCompatRadioButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                        @Override
                        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                            if (isChecked && positionRadioButtonChecked != position) {

                                if (mCallback != null) {


                                    if (position == 12) {
                                        SharedPreferences sharedPreferences
                                                = context.getSharedPreferences("MySharedPref", 0);

                                        SharedPreferences.Editor myEdit
                                                = sharedPreferences.edit();
                                        myEdit.putString(
                                                "method",
                                                "11");
                                        myEdit.commit();

                                        new ChooserDialog(context)
                                                .withFilter(false, false, "xls")
                                                .withChosenListener(new ChooserDialog.Result() {
                                                    @Override
                                                    public void onChoosePath(String path, File pathFile) {
                                                        // sendFile(path);
                                                        readExcelFile(context, path);

                                                        // Create new file to copy into.


                                                        //
                                                        positionRadioButtonChecked = position;
                                                        new Handler(context.getMainLooper()).post(new Runnable() {
                                                            @Override
                                                            public void run() {
                                                                notifyDataSetChanged();
                                                            }
                                                        });

                                                    }
                                                })
                                                // to handle the back key pressed or clicked outside the dialog:
                                                .withOnCancelListener(new DialogInterface.OnCancelListener() {
                                                    public void onCancel(DialogInterface dialog) {
                                                        Log.d("CANCEL", "CANCEL");
                                                        dialog.cancel(); // MUST have
                                                    }
                                                })
                                                .build()
                                                .show();
                                    } else if (position == 13) {
                                        SharedPreferences sharedPreferences
                                                = context.getSharedPreferences("MySharedPref", 0);

                                        SharedPreferences.Editor myEdit
                                                = sharedPreferences.edit();
                                        myEdit.putString(
                                                "method",
                                                "13");
                                        myEdit.commit();


                                        positionRadioButtonChecked = position;
                                        new Handler(context.getMainLooper()).post(new Runnable() {
                                            @Override
                                            public void run() {
                                                notifyDataSetChanged();
                                            }
                                        });


                                    } else if (position == 0 || position == 1 || position == 2 || position == 3 || position == 4 || position == 5 || position == 6 || position == 7 || position == 8 || position == 9 || position == 10 || position == 11) {

                                        SharedPreferences sharedPreferences
                                                = context.getSharedPreferences("MySharedPref", 0);

                                        SharedPreferences.Editor myEdit
                                                = sharedPreferences.edit();
                                        myEdit.putString(
                                                "method",
                                                "11");
                                        myEdit.commit();

                                        positionRadioButtonChecked = position;
                                        new Handler(context.getMainLooper()).post(new Runnable() {
                                            @Override
                                            public void run() {
                                                notifyDataSetChanged();
                                            }
                                        });


                                    }

                                }


                                /*if (mCallback != null) {
                                    mCallback.onItemClick(position, TAG);
                                }*/
                            }
                        }
                    });

                    optionAppCompatRadioButton.setChecked(positionRadioButtonChecked == position);
                    break;
                case CHECK_BOX:
                    optionCheckBox.setText(title);
                    optionCheckBox.setVisibility(View.VISIBLE);

                    if (isCheckAllFalse) {
                        optionCheckBox.setChecked(false);
                    }
                    break;
            }

        }
    }
}
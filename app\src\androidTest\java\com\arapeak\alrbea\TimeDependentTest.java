package com.arapeak.alrbea;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.uiautomator.UiDevice;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import java.io.IOException;

@RunWith(AndroidJUnit4.class)
public class TimeDependentTest {

    private UiDevice mDevice;

    @Before
    public void setUp() {
        mDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
    }

    @Test
    public void
    testMorningContent() throws IOException, InterruptedException {
        executeAdbCommand("date -s 2024-08-05 03:42:00");

        mDevice.executeShellCommand("monkey -p com.alrbea.prayer -c android.intent.category.LAUNCHER 1");

        try {
            Thread.sleep(15000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testAfternoonContent() throws IOException, InterruptedException {
        executeAdbCommand("date -s 2024-08-05 14:00:00");
    }

    // ... other test cases for different times ...

    private void executeAdbCommand(String command) throws IOException, InterruptedException {
        Process process = Runtime.getRuntime().exec(command);
        process.waitFor();
    }
}

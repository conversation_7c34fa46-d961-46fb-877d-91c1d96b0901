package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class MetaAlrabeeaTimes {
    @Expose
    @SerializedName("offset")
    private OffsetAlrabeeaTimes offset;
    @Expose
    @SerializedName("school")
    private String school;
    @Expose
    @SerializedName("midnightMode")
    private String midnightMode;
    @Expose
    @SerializedName("latitudeAdjustmentMethod")
    private String latitudeAdjustmentMethod;
    @Expose
    @SerializedName("method")
    private MethodAlrabeeaTimes method;
    @Expose
    @SerializedName("timezone")
    private String timezone;
    @Expose
    @SerializedName("longitude")
    private double longitude;
    @Expose
    @SerializedName("latitude")
    private double latitude;

    public OffsetAlrabeeaTimes getOffset() {
        return offset;
    }

    public void setOffset(OffsetAlrabeeaTimes offset) {
        this.offset = offset;
    }

    public String getSchool() {
        return school;
    }

    public void setSchool(String school) {
        this.school = school;
    }

    public String getMidnightMode() {
        return midnightMode;
    }

    public void setMidnightMode(String midnightMode) {
        this.midnightMode = midnightMode;
    }

    public String getLatitudeAdjustmentMethod() {
        return latitudeAdjustmentMethod;
    }

    public void setLatitudeAdjustmentMethod(String latitudeAdjustmentMethod) {
        this.latitudeAdjustmentMethod = latitudeAdjustmentMethod;
    }

    public MethodAlrabeeaTimes getMethod() {
        return method;
    }

    public void setMethod(MethodAlrabeeaTimes method) {
        this.method = method;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical"
    tools:background="#0B0A0A">

    <LinearLayout
        android:id="@+id/contentFajr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.blue"
        android:baselineAligned="false">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus13sdp"
            android:layout_marginBottom="@dimen/_minus16sdp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/fajr_TextView_MainActivity"
                style="@style/TimeTextView.blue.land2"
                android:text="@string/fajr" />

            <include
                android:id="@+id/tv_prayer_ikama_time_fajr"
                layout="@layout/textview_circle_white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_40sdp"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/_5sdp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/center_view.fajr" />

            <View
                android:id="@+id/center_view.fajr"
                android:layout_width="1dp"
                android:layout_height="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/imageFajr_View_MainActivity"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="match_parent"
                android:src="@drawable/cirblue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end">

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.Time" />

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.TimeType"
                    android:text="AM" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.blue">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus13sdp"
            android:layout_marginBottom="@dimen/_minus16sdp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/dhuhr_TextView_MainActivity"
                style="@style/TimeTextView.blue.land2"
                android:text="@string/dhuhr" />

            <include
                android:id="@+id/tv_prayer_ikama_time_dhur"
                layout="@layout/textview_circle_white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_40sdp"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/_5sdp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/imageDhuhr_View_MainActivity"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="@dimen/_15sdp"
                android:src="@drawable/cirblue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end">

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.Time" />

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.TimeType"
                    android:text="AM" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentAsr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.blue">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus13sdp"
            android:layout_marginBottom="@dimen/_minus16sdp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/asr_TextView_MainActivity"
                style="@style/TimeTextView.blue.land2"
                android:text="@string/asr" />

            <include
                android:id="@+id/tv_prayer_ikama_time_asr"
                layout="@layout/textview_circle_white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_40sdp"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/_5sdp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/imageAsr_View_MainActivity"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="@dimen/_15sdp"
                android:src="@drawable/cirblue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end">

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.Time" />

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.TimeType"
                    android:text="AM" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.blue">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus13sdp"
            android:layout_marginBottom="@dimen/_minus16sdp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/maghrib_TextView_MainActivity"
                style="@style/TimeTextView.blue.land2"
                android:text="@string/maghrib" />

            <include
                android:id="@+id/tv_prayer_ikama_time_maghrib"
                layout="@layout/textview_circle_white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_40sdp"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/_5sdp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/imageMaghrib_View_MainActivity"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="@dimen/_15sdp"
                android:src="@drawable/cirblue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end">

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.Time" />

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.TimeType"
                    android:text="AM" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentIsha_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.blue">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus13sdp"
            android:layout_marginBottom="@dimen/_minus16sdp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/isha_TextView_MainActivity"
                style="@style/TimeTextView.blue.land2"
                android:text="@string/isha" />

            <include
                android:id="@+id/tv_prayer_ikama_time_isha"
                layout="@layout/textview_circle_white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_40sdp"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/_5sdp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toTopOf="@id/center_view.isha"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/center_view.isha"
                android:layout_width="1dp"
                android:layout_height="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/imageIsha_View_MainActivity"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="@dimen/_15sdp"
                android:src="@drawable/cirblue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end">

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.Time" />

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.TimeType"
                    android:text="AM" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


    <!--    done -->
    <LinearLayout
        android:id="@+id/contentSunrise_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.blue"
        android:layout_width="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/sunrise_TextView_MainActivity"
            style="@style/TimeTextView.blue_land.TimeNameAR"
            android:layout_marginStart="@dimen/_4sdp"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="@dimen/_4sdp"
            android:layout_marginBottom="0dp"
            android:text="@string/duha"

            android:textSize="@dimen/_18sdp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/_40sdp"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/_5sdp"
                android:layout_height="0dp"
                android:background="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/imageSunrise_View_MainActivity"
                android:layout_width="@dimen/_15sdp"
                android:layout_height="@dimen/_15sdp"
                android:src="@drawable/cirblue"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4sdp"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="@dimen/_4sdp"
            android:layout_marginBottom="0dp"
            android:layout_weight="1"
            android:gravity="end">

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.Time"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:textSize="@dimen/_18sdp" />

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_land.TimeType"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:text="AM"
                    android:textSize="@dimen/_7sdp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>


</LinearLayout>
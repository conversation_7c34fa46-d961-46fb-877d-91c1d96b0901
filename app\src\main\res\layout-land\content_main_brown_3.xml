<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:layoutDirection="rtl"
    tools:context=".UI.Activity.MainActivity">

    <ImageView
        android:id="@+id/background_ImageView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/theme_brown_3_background_landscape"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_minus20sdp"
        android:layout_marginEnd="@dimen/_5sdp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/line"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imageView5"
            style="@style/mosqueIcon"
            android:layout_height="@dimen/_50sdp"
            android:layout_marginTop="0dp"
            android:scaleType="fitCenter"
            app:tint="#653F18" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                app:autoSizeTextType="uniform"
                android:id="@+id/timeNow_TextView_MainActivity"
                style="@style/BigTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_minus15sdp"
                android:layout_marginBottom="@dimen/_minus15sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:textColor="#653F18" />
        </LinearLayout>

        <ImageView
            android:id="@+id/dayimage"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_40sdp"
            android:layout_marginTop="@dimen/_minus5sdp"
            android:layout_marginBottom="@dimen/_minus4sdp"
            android:gravity="center"
            android:src="@drawable/d0"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/timeNow_TextView_MainActivity_"
            app:tint="#653F18" />

        <TextView
            android:id="@+id/day_text"
            style="@style/Theme_brown_new_3.date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus5sdp"
            android:layout_marginBottom="@dimen/_minus3sdp"
            android:text="Saturday"
            android:textColor="#B07A3E"
            android:textSize="@dimen/_14sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_100sdp"
                android:layout_gravity="bottom">

                <ImageView
                    style="@style/PrayerTimeLayout.dark_green"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_60sdp"
                    android:src="@drawable/theme_brown_3_icon_date"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_2sdp"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/dateNow_TextView_MainActivity"
                        style="@style/Theme_brown_new_3.day_number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="18"
                        android:textSize="@dimen/_24sdp" />


                    <ImageView
                        android:id="@+id/hijri_month_image"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginTop="@dimen/_minus15sdp"
                        android:paddingStart="@dimen/_3sdp"
                        android:paddingEnd="@dimen/_3sdp"
                        android:src="@drawable/theme_brown_3_hijri_month_7"
                        app:tint="#B07A3E" />

                    <TextView
                        android:id="@+id/datehm"
                        style="@style/Theme_brown_new_3.date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_minus14sdp"
                        android:ellipsize="marquee"
                        android:focusable="true"
                        android:gravity="center"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:text="@string/rajab"
                        android:textAlignment="center"
                        android:textSize="@dimen/_14sdp" />

                    <TextView
                        android:id="@+id/datehy"
                        style="@style/Theme_brown_new_3.date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_minus15sdp"
                        android:gravity="center"
                        android:text="1443"
                        android:textSize="@dimen/_18sdp" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_100sdp"
                android:layout_gravity="bottom">

                <ImageView
                    style="@style/PrayerTimeLayout.dark_green"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_60sdp"
                    android:src="@drawable/theme_brown_3_icon_date"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/dateNow1_TextView_MainActivity"
                        style="@style/Theme_brown_new_3.day_number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="09"
                        android:textSize="@dimen/_24sdp" />


                    <ImageView
                        android:id="@+id/gregorian_month_image"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/_40sdp"
                        android:layout_marginTop="@dimen/_minus15sdp"
                        android:paddingStart="@dimen/_3sdp"
                        android:paddingEnd="@dimen/_3sdp"
                        android:scaleType="centerInside"
                        android:src="@drawable/theme_brown_3_month_7"
                        app:tint="#B07A3E" />

                    <TextView
                        android:id="@+id/datem"
                        style="@style/Theme_brown_new_3.date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_minus14sdp"
                        android:ellipsize="marquee"
                        android:focusable="true"
                        android:gravity="center"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:text="April"
                        android:textAlignment="center"
                        android:textSize="@dimen/_14sdp" />

                    <TextView
                        android:id="@+id/datey"
                        style="@style/Theme_brown_new_3.date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_minus15sdp"
                        android:gravity="center"
                        android:text="2022"
                        android:textSize="@dimen/_18sdp" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/line"
        android:layout_width="2dp"
        android:layout_height="match_parent"
        android:layout_marginRight="@dimen/_180sdp"
        android:background="@drawable/theme_brown_3_icon_landscape_splitter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/m"
        android:layout_width="@dimen/_180sdp"
        android:layout_height="@dimen/_20sdp"
        android:layout_marginLeft="@dimen/_35sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginRight="@dimen/_35sdp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/movingMessage_TextView_MainActivity"
            style="@style/MovingText"
            android:textColor="#653F18"

            tools:text="@string/upload_photo_message" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/prayerTimeItem_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/_60sdp"
        android:layout_marginTop="@dimen/_15sdp"
        android:layout_marginRight="@dimen/_10sdp"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/line"
        app:layout_constraintTop_toBottomOf="@+id/m">

        <FrameLayout
            android:id="@+id/prayerTimeItem_include_MainActivity_"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_120sdp"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!--
                tools:src="@drawable/img_athkar_after_prayer_blue" -->
            <ImageView
                android:id="@+id/container_ImageView_MainActivity1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"


                app:layout_constrainedHeight="true"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/prayerTimeItem_include_MainActivity2"

                app:layout_constraintTop_toTopOf="@+id/prayerTimeItem_include_MainActivity"
                tools:alpha="0.5" />

            <include
                android:id="@+id/prayerTimeItem_include_MainActivity2"

                layout="@layout/layout_prayer_times_brown_new_3"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                />
        </FrameLayout>

        <LinearLayout
            android:id="@+id/remain_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:background="@drawable/theme_brown_new_remaining_background"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/prayerTimeItem_include_MainActivity_">

            <TextView
                android:id="@+id/remainingPrayer_TextView_MainActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/dinnext_medium"
                android:gravity="center"
                android:textColor="#653F18"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold"
                tools:text="متبقي  لصلاة المغرب ساعة و3 دقائق" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/athkar_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_55sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:background="@drawable/theme_brown_new_azkar_background"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/remain_container">

            <ImageView
                android:layout_width="@dimen/_50sdp"
                android:layout_height="@dimen/_48sdp"
                android:layout_marginStart="@dimen/_5sdp"
                android:src="@drawable/theme_brown_3_icon_athkar" />

            <TextView
                android:id="@+id/remainingPrayer_TextView_MainActivity_"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:fontFamily="@font/dinnext_medium"
                android:gravity="center"
                android:padding="@dimen/_5sdp"
                android:text="اللهم زدنا ولا تنقصنا، وأكرمنا ولا تهنا، وأعطنا ولا تحرمنا، وآثرنا ولا تؤثر علينا، وأرضنا وارض عنا"
                android:textColor="#653F18"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/announcement_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/_50sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/alrabeeaTimes_ImageView_MainActivity"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/line"
        app:layout_constraintTop_toBottomOf="@+id/m">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <!--
                        <TextView
                            android:id="@+id/tv_remainingOnIkama"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:text="@string/remaining_on_ikama"
                            android:textColor="#653F18"
                            android:textSize="@dimen/hadithBetweenAdhaanAndIkama"/>
                        <TextView
                            android:id="@+id/tv_message"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_minus15sdp"
                            android:fontFamily="@font/droid_arabic_kufi"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:text="@string/fajr_prayer"
                            android:textColor="#653F18"
                            android:textSize="@dimen/prayerTime" />
            -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_remainingOnIkama"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/remaining_on_ikama"
                    android:textColor="#653F18"
                    android:textSize="@dimen/hadithBetweenAdhaanAndIkama"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_minus15sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/fajr_prayer"
                    android:textColor="#653F18"
                    android:textSize="@dimen/prayerTime"
                    android:textStyle="bold" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_progress_remaining"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp">

                <com.mikhaellopez.circularprogressbar.CircularProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="@dimen/_80sdp"
                    android:layout_height="@dimen/_80sdp"
                    android:textColor="#653F18"
                    app:cpb_background_progressbar_color="#918F8F"
                    app:cpb_background_progressbar_width="3dp"
                    app:cpb_progressbar_color="#653F18"
                    app:cpb_progressbar_width="@dimen/_3sdp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:progress="50" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/progressBar"
                    app:layout_constraintEnd_toEndOf="@id/progressBar"
                    app:layout_constraintStart_toStartOf="@id/progressBar"
                    app:layout_constraintTop_toTopOf="@id/progressBar">

                    <TextView
                        android:id="@+id/tv_remaining_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="10:00"
                        android:textColor="#653F18"
                        android:textSize="@dimen/_20sdp" />

                    <TextView
                        android:id="@+id/tv_remaining_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:textColor="#653F18"
                        android:textSize="@dimen/_16sdp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        tools:text="دقائق" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:layout_marginEnd="@dimen/_20sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="#653F18"


            android:textSize="@dimen/_10sdp"
            tools:text="@string/hadeth_on_prayer" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_silent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_60sdp"
            android:src="@drawable/bellsilentline"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="@android:color/black" />
    </LinearLayout>

    <ImageView
        android:id="@+id/alrabeeaTimes_ImageView_MainActivity"
        style="@style/AlrabeaIcon"
        android:layout_marginBottom="@dimen/_5sdp"
        android:src="@drawable/logov2"
        android:scaleY="@fraction/logo_scale_small"
        android:scaleX="@fraction/logo_scale_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:tint="#653F18" />

    <LinearLayout
        android:id="@+id/layout_athkar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_athkar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scaleType="fitXY"
            android:src="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/layout_time_athkar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_4sdp">

            <TextView
                android:id="@+id/tv_athkar_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="Time"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_icon"
                style="@style/AlrabeaIcon"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_26sdp"
                app:tint="@color/colorblack" />

        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
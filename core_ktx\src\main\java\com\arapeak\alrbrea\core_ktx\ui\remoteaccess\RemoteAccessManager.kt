package com.arapeak.alrbrea.core_ktx.ui.remoteaccess

import android.content.Context
import android.content.pm.PackageInfo
import android.util.Log
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteTool
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteToolNetwork
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteToolStatus
import com.google.firebase.database.FirebaseDatabase

class RemoteAccessManager(val context: Context) {
    private var onFirebaseConfigArrived: () -> Unit = {}
    internal val list = mutableMapOf<Int, RemoteTool>()
    private val firebaseList = mutableMapOf<Int, RemoteToolNetwork>()


    init {
        try {
            addTeamviewer()
            addTeamviewerAddOn()
            addAnyDesk()
            fetchFirebaseConfig()
            checkInstalled()
        } catch (e: Exception) {
            CrashlyticsUtils.logException(e)
        }
    }

    fun getAsList() = list.values.toList()
    private fun checkInstalled() {
        val temp = list.values.toList()

        temp.forEach { tool ->
            val appInfo = getAppInstalled(tool.appPackage)

            if (appInfo == null) {
                Log.i("Remote access", tool.name + " is not installed")

                list[tool.id]?.status = RemoteToolStatus.NotInstalled
            } else {
                Log.i("Remote access", tool.name + " is installed veriosn " + appInfo.versionName + " code " + appInfo.versionCode)

                list[tool.id]?.status = RemoteToolStatus.Installed(
                    versionCode = appInfo.versionCode, versionName = appInfo.versionName
                )
            }
        }

    }


    private fun fetchFirebaseConfig() {
        val task = FirebaseDatabase.getInstance().getReference("supportApks").get()

        task.addOnSuccessListener {
            val versionsSnapshot = task?.result?.child("versions")
            for (childSnapshot in versionsSnapshot?.children ?: listOf()) {
                val toolNetwork = childSnapshot.getValue(RemoteToolNetwork::class.java)
                if (toolNetwork != null) {
                    firebaseList[toolNetwork.id] = toolNetwork
                }
            }
            Log.i("FirebaseDatabase", "versions : " + firebaseList.toString())

            checkInstalled()
            checkOutdated()
            onFirebaseConfigArrived.invoke()
        }

        task.addOnFailureListener {
            Log.e("FirebaseDatabase", "Error getting supportApks", it)
            onFirebaseConfigArrived.invoke()
        }
    }

    private fun getAppInstalled(packageName: String): PackageInfo? {
        try {
            val packageInfo = context.applicationContext.packageManager.getPackageInfo(packageName, 0)
            return packageInfo
        } catch (e: Exception) {
            return null
        }
    }

    fun setOnFirebaseConfigArrived(callback: () -> Unit) {
        if (firebaseList.isNotEmpty()) {
            callback.invoke()
        } else {
            onFirebaseConfigArrived = callback
        }
    }

    @Suppress("t")
    private fun checkOutdated() {
        val temp = list.values.toList()

        temp.forEach { tool ->
            val cloudVersion = firebaseList[tool.id]

            if (cloudVersion != null) {
                Log.i("Remote access", tool.name + " has cloud version " + cloudVersion.toString())

                if (cloudVersion.latestLink.isNullOrBlank().not()) {
                    list[tool.id]?.downLink = cloudVersion.latestLink ?: tool.downLink
                }

                if (tool.status is RemoteToolStatus.Installed) {
                    Log.i("Remote access", tool.name + " is installed")

                    if ((cloudVersion.version?.toInt() ?: 0) > (tool.status as RemoteToolStatus.Installed).versionCode) {
                        Log.i("Remote access", tool.name + " is outdated " + cloudVersion.version + " > " + (tool.status as RemoteToolStatus.Installed).versionCode)

                        list[tool.id]?.status = RemoteToolStatus.Outdated(
                            localCode = (tool.status as RemoteToolStatus.Installed).versionCode,
                            localName = (tool.status as RemoteToolStatus.Installed).versionName,
                            cloudCode = cloudVersion.version?.toInt() ?: 0,
                        )
                    }
                }
            } else {
                Log.i("Remote access", tool.name + " has cloud version null !")

            }

        }
    }


}
package com.arapeak.alrbea.UI.CustomView;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;

import com.arapeak.alrbea.Interface.OnFragmentInteractionListener;
import com.arapeak.alrbea.R;


public class AlrabeeaTimesFragment extends Fragment {

    private static final String TAG = "AdvertiserFragment";

    private OnFragmentInteractionListener mListener;
    private Context mContext;

    public AlrabeeaTimesFragment() {

    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        TextView textView = new TextView(getActivity());
        textView.setText(R.string.hello_blank_fragment);
        return textView;
    }

    public void onButtonPressed(Uri uri) {
        if (mListener != null) {
            mListener.onFragmentInteraction(uri);
        }
    }


    public void onBackPressed() {
        if (getAppCompatActivity() != null) {
            if (getAppCompatActivity().getSupportFragmentManager() != null
                    && getAppCompatActivity().getSupportFragmentManager().getBackStackEntryCount() == 1) {
                getAppCompatActivity().finish();
                return;
            }
            getAppCompatActivity().onBackPressed();
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        if (context instanceof OnFragmentInteractionListener) {
            mListener = (OnFragmentInteractionListener) context;
        } else {
            Log.e(TAG, "must implement OnFragmentInteractionListener");
            /*throw new RuntimeException(context.toString()
                    + " must implement OnFragmentInteractionListener");*/
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
        mContext = null;
    }

    @Nullable
    @Override
    public Context getContext() {
        return mContext;
    }

    public AppCompatActivity getAppCompatActivity() {
        return (AppCompatActivity) mContext;
    }
}

package com.arapeak.alrbea.UI.Activity;

import android.content.Context;
import android.util.Log;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.Calendar;
import java.util.GregorianCalendar;

/**
 * Helper class for MainActivity Date and Time operations
 * Handles date formatting, calendar operations, and time calculations
 */
public class MainActivityDateUtils {
    private static final String TAG = "MainActivityDateUtils";
    private final Context context;

    // Date components
    private int year, month, day;
    private long TempTime = 0;
    private UmmalquraCalendar hijriCalendar;
    private GregorianCalendar gregorianCalendar;

    // Date strings
    private String gYear = "";
    private String gMonthAr = "";
    private String gMonthEn = "";
    private String gMonthNum = "";
    private String gDayNum = "";
    private String gDayNameAr = "";
    private String gDayNameEn = "";
    private String hYear = "";
    private String hMonthAr = "";
    private String hMonthEn = "";
    private String hDayNum = "";
    private String ssMiladiDate = "";
    private String ssHijriDate = "";
    private String ssDayName = "";
    private String monthString = "";
    private String dayName = "";
    private String dayNumber = "";
    private String date = "";

    // State variables
    private boolean isHijri;
    private boolean isJomaa;
    private boolean _dateDarkGreenLandscape_isHijri = false;
    private boolean _lastRefreshedIsDayName = false;
    private int _blueCurrentDate = 1;
    private int _athkarCurrentDate = 1;
    private int lastDisplayedDay = -1;

    public MainActivityDateUtils(Context context) {
        this.context = context;
        this.hijriCalendar = Utils.getUmmalquraCalendar();
        this.gregorianCalendar = new GregorianCalendar();
    }

    /**
     * Update current date components
     */
    public void updateDateComponents() {
        try {
            TempTime = System.currentTimeMillis();
            year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR, TempTime));
            month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH, TempTime));
            day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY, TempTime));

            Log.d(TAG, "Date components updated: " + day + "/" + month + "/" + year);
        } catch (Exception e) {
            Log.e(TAG, "Error updating date components: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update calendar objects
     */
    public void updateCalendars() {
        try {
            gregorianCalendar = new GregorianCalendar();
            hijriCalendar = Utils.getUmmalquraCalendar();

            Log.d(TAG, "Calendars updated successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error updating calendars: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Get current time formatted
     */
    public String getCurrentTime() {
        try {
            return Utils.getTimeNow();
        } catch (Exception e) {
            Log.e(TAG, "Error getting current time: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Get current time type (AM/PM)
     */
    public String getCurrentTimeType() {
        try {
            return Utils.getTypeTimeNow();
        } catch (Exception e) {
            Log.e(TAG, "Error getting current time type: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Get athkar date string
     */
    public String getAthkarDate() {
        try {
            // Implementation depends on your specific athkar date format requirements
            return Utils.getCurrentDate("dd/MM/yyyy");
        } catch (Exception e) {
            Log.e(TAG, "Error getting athkar date: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            return "";
        }
    }

    /**
     * Check if it's midnight
     */
    public boolean isMidnight() {
        try {
            Calendar calendar = Calendar.getInstance();
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            return hour == 0 && minute == 0;
        } catch (Exception e) {
            Log.e(TAG, "Error checking midnight: " + e.getMessage());
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    // Getters and setters
    public int getYear() {
        return year;
    }

    public int getMonth() {
        return month;
    }

    public int getDay() {
        return day;
    }

    public long getTempTime() {
        return TempTime;
    }

    public UmmalquraCalendar getHijriCalendar() {
        return hijriCalendar;
    }

    public GregorianCalendar getGregorianCalendar() {
        return gregorianCalendar;
    }

    public boolean isHijri() {
        return isHijri;
    }

    public void setHijri(boolean hijri) {
        isHijri = hijri;
    }

    public boolean isJomaaDay() {
        return isJomaa;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getMonthString() {
        return monthString;
    }

    public void setMonthString(String monthString) {
        this.monthString = monthString;
    }

    public String getDayNameString() {
        return dayName;
    }

    public void setDayNameString(String dayName) {
        this.dayName = dayName;
    }

    public String getDayNumber() {
        return dayNumber;
    }

    public void setDayNumber(String dayNumber) {
        this.dayNumber = dayNumber;
    }

    // Screensaver date strings
    public String getSsMiladiDate() {
        return ssMiladiDate;
    }

    public void setSsMiladiDate(String ssMiladiDate) {
        this.ssMiladiDate = ssMiladiDate;
    }

    public String getSsHijriDate() {
        return ssHijriDate;
    }

    public void setSsHijriDate(String ssHijriDate) {
        this.ssHijriDate = ssHijriDate;
    }

    public String getSsDayName() {
        return ssDayName;
    }

    public void setSsDayName(String ssDayName) {
        this.ssDayName = ssDayName;
    }
}
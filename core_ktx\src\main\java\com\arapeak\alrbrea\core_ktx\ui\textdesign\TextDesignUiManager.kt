package com.arapeak.alrbrea.core_ktx.ui.textdesign

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.AbsoluteLayout
import android.widget.TextView
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignToggleEnum
import com.arapeak.alrbrea.core_ktx.repo.TextDesignRepo
import com.arapeak.alrbrea.core_ktx.ui.textdesign.model.TextDesignData


class TextDesignUiManager {
    val SHOW_DELAY = 1000

    private var data1: TextDesignData? = null
    private var data2: TextDesignData? = null

    private val repo = TextDesignRepo()
    val prvManager: TextDesignPreviewManager = TextDesignPreviewManager()

    fun getData1(context: Context): TextDesignData? {
        if (data1 != null)
            return data1

        data1 = TextDesignData(
            toggle = repo.getToggle(context),
            font = repo.getFont(context),
            color = repo.getColor(context),
            size = repo.getSizeParsed(context),
            text = repo.getText(context),
            position = repo.getPosition(context)
        )
        return this.data1
    }

    fun getData2(context: Context): TextDesignData? {
        if (data2 != null)
            return data2

        data2 = TextDesignData(
            toggle = repo.getToggle(context),
            font = repo.getFont(context, true),
            color = repo.getColor(context, true),
            size = repo.getSizeParsed(context, true),
            text = repo.getText(context, true),
            position = repo.getPosition(context, true)
        )
        return this.data2
    }


    fun isEnabled(context: Context): Boolean {
        if (data1 == null)
            getData1(context)
        return data1?.toggle == TextDesignToggleEnum.Enabled
    }


    fun isSecondEnabled(context: Context): Boolean {
        return repo.getTwoLines(context)
    }


    fun restoreTextPosition(context: Context, tv: TextView, secondLine: Boolean = false) {
        val data = if (secondLine) getData2(context) else getData1(context)
        val old = data?.position ?: return

        val parent = tv.parent as? ViewGroup ?: return
        val parentWidth = parent.width
        val parentHeight = parent.height

        val layoutParams = tv.layoutParams as? AbsoluteLayout.LayoutParams ?: return
        layoutParams.x = (parentWidth * old.x).toInt()
        layoutParams.y = (parentHeight * old.y).toInt()

        if (data.position.isDefault().not())
            tv.setLayoutParams(layoutParams)
    }


    fun savePreview(context: Context, view: View, isLand: Boolean = false) {
        prvManager.saveLayoutPreview(context, view, isLand)
    }

    fun shouldShowDuringPrayers(context: Context): Boolean {
        val setting = repo.getDuringPrayersSetting(context)
        return setting
    }


}
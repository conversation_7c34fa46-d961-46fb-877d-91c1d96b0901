<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <selector>
            <item
                android:drawable="@drawable/check_box_blue_on_style_big"
                android:state_checked="true"
                android:state_window_focused="false" />
            <item
                android:drawable="@drawable/check_box_white_off_style_big"
                android:state_checked="false"
                android:state_window_focused="false" />

            <item
                android:drawable="@drawable/check_box_blue_on_style_big"
                android:state_checked="true"
                android:state_pressed="true" />
            <item
                android:drawable="@drawable/check_box_white_off_style_big"
                android:state_checked="false"
                android:state_pressed="true" />

            <item
                android:drawable="@drawable/check_box_blue_on_style_big"
                android:state_checked="true"
                android:state_focused="true" />
            <item
                android:drawable="@drawable/check_box_white_off_style_big"
                android:state_checked="false"
                android:state_focused="true" />

            <item
                android:drawable="@drawable/check_box_white_off_style_big"
                android:state_checked="false" />
            <item
                android:drawable="@drawable/check_box_blue_on_style_big"
                android:state_checked="true" />
        </selector>
    </item>
</layer-list>

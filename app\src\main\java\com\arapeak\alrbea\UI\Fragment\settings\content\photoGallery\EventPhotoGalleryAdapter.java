package com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Model.EventAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.List;

public class EventPhotoGalleryAdapter extends RecyclerView.Adapter<EventPhotoGalleryAdapter.EventPhotoGalleryHolder> {

    public static final String TAG = "PhotoGalleryAdapter";
    public static final String SETTINGS_BUTTON_TAG = "settingsButton";
    public static final String DELETE_BUTTON_TAG = "deleteButton";

    private final Context context;
    private final List<EventAlrabeeaTimes> arrayListItem;
    private final LayoutInflater layoutInflater;
    private final AdapterCallback mCallback;
    private final boolean isImageFullScreen;
    private final boolean isShowProgressBar;
    private final boolean isShowSettingsLayout;

    public EventPhotoGalleryAdapter(Context context
            , List<EventAlrabeeaTimes> arrayListItem
            , AdapterCallback mCallback
            , boolean isShowSettingsLayout
            , boolean isShowProgressBar) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSettingsLayout = isShowSettingsLayout;
        this.isShowProgressBar = isShowProgressBar;
        isImageFullScreen = false;

        layoutInflater = LayoutInflater.from(this.context);
    }

    public EventPhotoGalleryAdapter(Context context
            , List<EventAlrabeeaTimes> arrayListItem
            , AdapterCallback mCallback
            , boolean isShowSettingsLayout
            , boolean isShowProgressBar
            , boolean isImageFullScreen) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSettingsLayout = isShowSettingsLayout;
        this.isShowProgressBar = isShowProgressBar;
        this.isImageFullScreen = isImageFullScreen;

        layoutInflater = LayoutInflater.from(this.context);
    }

    @NonNull
    @Override
    public EventPhotoGalleryHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        View view = layoutInflater.inflate(R.layout.layout_list_item_photo, parent, false);

        EventPhotoGalleryHolder viewHolder = new EventPhotoGalleryHolder(view);

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull EventPhotoGalleryHolder savedVoiceViewHolder, int position) {

        savedVoiceViewHolder.onBind(position);
    }

    @Override
    public int getItemCount() {
        return arrayListItem.size();
    }


    public void add(EventAlrabeeaTimes item) {
        if (item == null) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.add(item);
        notifyItemInserted(lastItemIndex);
        notifyDataSetChanged();
    }

    public void remove(int position) {
        if (position < 0 || position >= getItemCount()) {
            return;
        }
        this.arrayListItem.remove(position);
        notifyItemRemoved(position);
        notifyDataSetChanged();
    }

    public void addAll(List<EventAlrabeeaTimes> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public EventAlrabeeaTimes getItem(int position) {
        if (position < 0 || position >= getItemCount()) {
            return null;
        }
        return arrayListItem.get(position);
    }

    public void clear() {
        arrayListItem.clear();
        notifyDataSetChanged();
    }

    public EventAlrabeeaTimes getLastItem() {
        if (getItemCount() == 0) {
            return null;
        }

        return arrayListItem.get(arrayListItem.size() - 1);
    }

    public void setItem(int position, EventAlrabeeaTimes subSettingAlrabeeaTimes) {
        if (position < 0 || position >= getItemCount() || subSettingAlrabeeaTimes == null) {
            return;
        }
        arrayListItem.set(position, subSettingAlrabeeaTimes);
        notifyItemChanged(position);
    }

    class EventPhotoGalleryHolder extends RecyclerView.ViewHolder {

        private final TextView nameTextView;
        private final ImageView photoImageView;
        private final Button settingsButton;
        private final Button deleteButton;
        private final View spaceView;
        private final FrameLayout progressBarFrameLayout;
        private final DisplayImageOptions options;

        public EventPhotoGalleryHolder(@NonNull View itemView) {
            super(itemView);

            nameTextView = itemView.findViewById(R.id.name_TextView_PhotoGalleryHolder);
            photoImageView = itemView.findViewById(R.id.photo_ImageView_PhotoGalleryHolder);
            settingsButton = itemView.findViewById(R.id.settings_Button_PhotoGalleryHolder);
            deleteButton = itemView.findViewById(R.id.delete_Button_PhotoGalleryHolder);
            spaceView = itemView.findViewById(R.id.space_View_PhotoGalleryHolder);
            progressBarFrameLayout = itemView.findViewById(R.id.progressBar_FrameLayout_PhotoGalleryHolder);

            options = new DisplayImageOptions.Builder()
                    .cacheInMemory(true)
                    .cacheOnDisk(true)
                    .considerExifParams(true)
                    .build();
        }


        public void onBind(final int position) {
            EventAlrabeeaTimes eventAlrabeeaTimes = arrayListItem.get(position);

            if (eventAlrabeeaTimes.getNameEvent().isEmpty()) {
                nameTextView.setVisibility(View.GONE);
                nameTextView.setText("");
            } else {
                nameTextView.setVisibility(View.VISIBLE);
                nameTextView.setText(eventAlrabeeaTimes.getNameEvent());
            }

            if (isShowProgressBar) {
                progressBarFrameLayout.setVisibility(View.VISIBLE);
            } else {
                progressBarFrameLayout.setVisibility(View.GONE);
            }

            if (isImageFullScreen) {
                photoImageView.getLayoutParams().height = RecyclerView.LayoutParams.MATCH_PARENT;
                photoImageView.setMinimumHeight((int) context.getResources().getDimension(R.dimen.height_image_item));
            } else {
                photoImageView.getLayoutParams().height = (int) context.getResources().getDimension(R.dimen.height_image_item);
            }

            if (isShowSettingsLayout) {
                settingsButton.setVisibility(View.VISIBLE);
                deleteButton.setVisibility(View.VISIBLE);
                spaceView.setVisibility(View.VISIBLE);
            } else {
                settingsButton.setVisibility(View.GONE);
                deleteButton.setVisibility(View.VISIBLE);
                spaceView.setVisibility(View.GONE);
            }

            photoImageView.requestLayout();
            if (eventAlrabeeaTimes.getPhotoAlrabeeaTimesList().size() <= 0) {
                return;
            }
            if (!eventAlrabeeaTimes.getPhotoAlrabeeaTimesList().get(0).getImageBase64().isEmpty()) {
                ImageLoader imageLoader = ImageLoader.getInstance();
                imageLoader.displayImage("file://" + eventAlrabeeaTimes.getPhotoAlrabeeaTimesList().get(0).getImageUrl(), photoImageView, options);
                progressBarFrameLayout.setVisibility(View.GONE);

            } else if (!eventAlrabeeaTimes.getPhotoAlrabeeaTimesList().get(0).getImageUrl().isEmpty()) {
                Utils.setPhotoWithCallBack(context
                        , photoImageView
                        , eventAlrabeeaTimes.getPhotoAlrabeeaTimesList().get(0).getImageUrl()
                        , new OnSuccessful() {
                            @Override
                            public void onSuccessful(boolean isSuccessful) {
                                if (isSuccessful) {
                                    progressBarFrameLayout.setVisibility(View.GONE);
                                }
                            }
                        });
            }

            settingsButton.setOnClickListener(v -> {
                if (mCallback != null) {
                    mCallback.onItemClick(position, SETTINGS_BUTTON_TAG);
                }
            });
            deleteButton.setOnClickListener(v -> {
                if (mCallback != null) {
                    mCallback.onItemClick(position, DELETE_BUTTON_TAG);
                }
            });
            itemView.setOnClickListener(v -> {
                if (mCallback != null) {
                    mCallback.onItemClick(position, TAG);
                }
            });
        }
    }
}
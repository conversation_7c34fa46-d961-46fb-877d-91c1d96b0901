<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/content_LinearLayout_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            style="@style/LinearLayoutPrayerTimeRow.blue_lett"
            android:layout_marginBottom="@dimen/_minus10sdp"
            android:background="@android:color/transparent">
            <!--<androidx.constraintlayout.widget.ConstraintLayout
                style="@style/PrayerTimeLayout.blue_lett.Right">

                <ImageView
                    android:layout_width="@dimen/_100sdp"
                    android:layout_height="@dimen/_50sdp"
                    android:src="@drawable/azanblett_new"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:layout_marginTop="@dimen/_minus10sdp"
                    android:layout_marginBottom="@dimen/_minus10sdp"
                    android:textSize="@dimen/_30sdp"
                    android:shadowColor="@color/colorblack"
                    android:shadowRadius="5"
                    android:text="@string/adhaan"
                    android:textAllCaps="true"
                    android:textColor="@android:color/white"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>-->

            <TextView
                style="@style/PrayerTimeLayout.blue_lett.Right"
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginBottom="0dp"
                android:background="@drawable/azanblett_new"
                android:gravity="center"
                android:shadowColor="@color/white"
                android:shadowRadius="5"
                android:text="@string/adhaan"
                android:textColor="#4b6780"
                android:textSize="@dimen/_22sdp" />

            <TextView
                style="@style/PrayerTimeLayout.blue_lett.center"
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:background="@drawable/prayblett"
                android:gravity="center"
                android:shadowColor="#4b6780"
                android:shadowRadius="5"
                android:text="@string/prayer"
                android:textColor="@color/white"
                android:textSize="@dimen/_22sdp" />

            <TextView
                style="@style/PrayerTimeLayout.blue_lett.Left"
                android:layout_width="@dimen/_60sdp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:background="@drawable/azanblett_new"
                android:gravity="center"
                android:shadowColor="@color/white"
                android:shadowRadius="5"
                android:text="@string/ikama"
                android:textColor="#4b6780"
                android:textSize="@dimen/_22sdp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentFajr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.blue_lett"
            android:layout_marginTop="0dp"
            android:background="@drawable/background_prayers_blue_lett_left">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/fajrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/fajrTime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/fajrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.center">

                <TextView
                    android:id="@+id/fajr_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeNameAR"
                    android:text="@string/fajr" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_fajr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/fajrATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/fajrATime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/fajrATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingFajr_TextView_MainActivity"
            style="@style/TimeTextView.blue_lett.TimeRemain"
            android:visibility="visible" />

        <LinearLayout
            android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.blue_lett">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/dhuhrTime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.center">

                <TextView
                    android:id="@+id/dhuhr_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeNameAR"
                    android:text="@string/dhuhr" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_dhur"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/dhuhrATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/dhuhrATime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/dhuhrATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingDhuhr_TextView_MainActivity"
            style="@style/TimeTextView.blue_lett.TimeRemain" />

        <LinearLayout
            android:id="@+id/contentAsr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.blue_lett">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/asrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/asrTime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/asrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.center">

                <TextView
                    android:id="@+id/asr_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeNameAR"
                    android:text="@string/asr" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_asr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/asrATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/asrATime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/asrATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingAsr_TextView_MainActivity"
            style="@style/TimeTextView.blue_lett.TimeRemain" />

        <LinearLayout
            android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.blue_lett">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/maghribTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/maghribTime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/maghribTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.center">

                <TextView
                    android:id="@+id/maghrib_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeNameAR"
                    android:text="@string/maghrib" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_maghrib"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/maghribATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/maghribATime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/maghribATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingMaghrib_TextView_MainActivity"
            style="@style/TimeTextView.blue_lett.TimeRemain" />

        <LinearLayout
            android:id="@+id/contentIsha_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.blue_lett">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/ishaTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/ishaTime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/ishaTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.center">

                <TextView
                    android:id="@+id/isha_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeNameAR"
                    android:text="@string/isha" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_isha"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/ishaATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/ishaATime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time" />

                    <TextView
                        android:id="@+id/ishaATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingIsha_TextView_MainActivity"
            style="@style/TimeTextView.blue_lett.TimeRemain" />


        <LinearLayout
            android:id="@+id/contentSunrise_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.blue_lett"
            android:layout_width="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="8dp">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/sunriseTime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_20sdp" />

                    <TextView
                        android:id="@+id/sunriseTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_8sdp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_lett.center"
                android:layout_marginStart="@dimen/_9sdp"
                android:layout_marginEnd="@dimen/_9sdp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/sunrise_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeNameAR"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:singleLine="true"
                    android:text="@string/duha"
                    android:textSize="@dimen/_20sdp" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/sunriseATime_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.Time"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_20sdp" />

                    <TextView
                        android:id="@+id/sunriseATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.blue_lett.TimeType"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_8sdp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/remainingSunrise_TextView_MainActivity"
            style="@style/TimeTextView.blue_lett.TimeRemain" />

    </LinearLayout>

</LinearLayout>
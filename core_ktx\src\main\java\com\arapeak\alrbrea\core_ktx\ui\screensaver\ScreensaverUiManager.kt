package com.arapeak.alrbrea.core_ktx.ui.screensaver

import android.app.Activity
import android.view.ViewGroup
import android.view.WindowManager
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoElementsEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoSizeEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverModeEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverToggleEnum
import com.arapeak.alrbrea.core_ktx.repo.ScreenSaverRepo
import com.arapeak.alrbrea.core_ktx.ui.screensaver.view.BlankView
import com.arapeak.alrbrea.core_ktx.ui.screensaver.view.InfoView


class ScreensaverUiManager(val onClick: () -> Unit) {
    private var blankView: BlankView? = null
    private var infoView: InfoView? = null

    private var modeEnum: ScreensaverModeEnum? = null
    private val repo = ScreenSaverRepo()

    fun startScreenSaver(activity: Activity) {

        val toggle = repo.getCurrentToggle(activity)
        modeEnum = repo.getCurrentMode(activity)
        val size = repo.getInfoSizes(activity)
        val info = repo.getInfoElements(activity)

        if (toggle == ScreensaverToggleEnum.Disabled) {
            disableAll(activity)
            return
        }

        when (modeEnum) {
            ScreensaverModeEnum.Empty -> toggleEmptyScreenSaver(true, activity)
            ScreensaverModeEnum.Info -> toggleInfoScreenSaver(true, activity, size, info)
            ScreensaverModeEnum.ReduceBrightness -> toggleReduceBrightnessScreenSaver(true, activity)
            null -> {}
        }
    }

    fun stopScreenSaver(activity: Activity) {
        disableAll(activity)
    }


    fun updateData(time: String, timeType: String?, mildadi: String, hijri: String, dayName: String,funeral:String?) {
        if (modeEnum != ScreensaverModeEnum.Info || infoView == null)
            return

        infoView?.updateData(time, timeType, mildadi, hijri, dayName,funeral)

    }


    private fun disableAll(activity: Activity) {
        toggleInfoScreenSaver(false, activity)
        toggleEmptyScreenSaver(false, activity)
        toggleReduceBrightnessScreenSaver(false, activity)
    }

    private fun toggleReduceBrightnessScreenSaver(enable: Boolean, activity: Activity) {
        try {
            val brightness = if (enable)
                0.0f
            else
                1.0f
            val params: WindowManager.LayoutParams = activity.window.attributes
            params.screenBrightness = brightness
            activity.window.setAttributes(params)

        } catch (e: Exception) {
            logException(e)
        }
    }

    private fun toggleInfoScreenSaver(
        enable: Boolean,
        activity: Activity,
        size: ScreenSaverInfoSizeEnum = ScreenSaverInfoSizeEnum.Large,
        infos: ScreenSaverInfoElementsEnum = ScreenSaverInfoElementsEnum.DateTime,
    ) {
        try {
            if (infoView == null)
                infoView = InfoView(activity, size, infos, onClick)

            if (enable) {
//                infoView?.removeView()
                activity.addContentView(
                    infoView, ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
            } else {
                infoView?.removeView()
            }

        } catch (e: Exception) {
            logException(e)
        }
    }

    private fun toggleEmptyScreenSaver(enable: Boolean, activity: Activity) {
        try {
            if (blankView == null)
                blankView = BlankView(activity, onClick)

            if (enable) {
//                blankView?.removeView()
                activity.addContentView(
                    blankView, ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
            } else {
                blankView?.removeView()
            }

        } catch (e: Exception) {
            logException(e)
        }
    }


}
package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor

import android.location.Location
import android.util.Log
import com.arapeak.alrbrea.core_ktx.data.prayer.PrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model.CalcMethod
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model.Hour
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model.Mazhab
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model.MiladiDate
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model.PrayerTime
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model.Season
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.arapeak.alrbrea.core_ktx.model.prayer.Prayer
import com.arapeak.alrbrea.core_ktx.model.prayer.PrayerEnum
import com.batoulapps.adhan2.Madhab
import java.util.Calendar

class NoorPrayerTimeProvider : PrayerTimeProvider() {
    override fun getPrayerTime(location: Location, calculationMethod: CalculationMethod, madhab: Madhab, date: Calendar): DayPrayers {

        val fajrAngleValues = listOf("18", "15")
        val ishaAngleValues = listOf("18", "15")
        val methodes = listOf(
            CalcMethod(CalcMethod.Type.Turkey),
            CalcMethod(CalcMethod.Type.UmmAlQuraUniv),
            CalcMethod(CalcMethod.Type.MuslimWorldLeague),
            CalcMethod(CalcMethod.Type.UnivOfIslamicScincesKarachi),
            CalcMethod(CalcMethod.Type.EgytionGeneralAuthorityofSurvey),
            CalcMethod(CalcMethod.Type.IslamicSocietyOfNorthAmerica),
        )
        val seasons = listOf(
            Season(Season.Type.Summer),
            Season(Season.Type.Winter),
            Season(Season.Type.Winter_Summer),
        )
        val mazhabs = listOf(
            Mazhab(Mazhab.Type.Hanafi),
            Mazhab(Mazhab.Type.Default),
        )

        fajrAngleValues.forEach { fajr ->
            ishaAngleValues.forEach { isha ->
                methodes.forEach { method ->
                    seasons.forEach { season ->
                        mazhabs.forEach { mazhab ->
                            extracted(fajr, isha, method, season, mazhab, location, date)
                        }
                    }
                }
            }
        }


        var p1 = PrayerTime()
        p1.setFajrAngle("15")
        p1.setIshaAngle("15")
        p1.setCoordinate(location.longitude, location.latitude, 0)
        p1.set_longitude(location.longitude.toString())
        p1.set_latitude(location.latitude.toString())
        p1.setDate(
            MiladiDate(
                date.get(Calendar.DATE),
                date.get(Calendar.MONTH) + 1,
                date.get(Calendar.YEAR),
            )
        )
        p1.setM_calcMethod(CalcMethod())
        p1.setMazhab(Mazhab())
        p1.m_season = Season(Season.Type.Summer)
        p1.setHour(Hour.Type.hour24)
        p1.calculate(false, false)

        Log.e(
            "PrayerTime", """
            Summer
            ${p1.fajrTime(false).toString()}
            ${p1.zuhrTime(false).toString()}
            ${p1.asrTime(false).toString()}
            ${p1.maghribTime(false).toString()}
            ${p1.ishaTime(false).toString()}
        """.trimIndent()
        )




        p1.m_season = Season(Season.Type.Winter)
        p1.calculate(false, false)

        Log.e(
            "Prayer Time", """
            Winter
            ${p1.fajrTime(false).toString()}
            ${p1.zuhrTime(false).toString()}
            ${p1.asrTime(false).toString()}
            ${p1.maghribTime(false).toString()}
            ${p1.ishaTime(false).toString()}
        """.trimIndent()
        )


        p1.m_season = Season(Season.Type.Winter_Summer)
        p1.calculate(false, false)

        Log.e(
            "Prayer Time", """
            Winter_Summer
            ${p1.fajrTime(false).toString()}
            ${p1.zuhrTime(false).toString()}
            ${p1.asrTime(false).toString()}
            ${p1.maghribTime(false).toString()}
            ${p1.ishaTime(false).toString()}
        """.trimIndent()
        )


        return DayPrayers(
            Prayer(PrayerEnum.fajr, Calendar.getInstance()),
            Prayer(PrayerEnum.sunrise, Calendar.getInstance()),
            Prayer(PrayerEnum.dhuhr, Calendar.getInstance()),
            Prayer(PrayerEnum.asr, Calendar.getInstance()),
            Prayer(PrayerEnum.maghrib, Calendar.getInstance()),
            Prayer(PrayerEnum.isha, Calendar.getInstance()),
            Prayer(PrayerEnum.eid, Calendar.getInstance()),
        )
    }

    private fun extracted(fajr: String, isha: String, method: CalcMethod, season: Season, mazhab: Mazhab, location: Location, date: Calendar) {
        var p1 = PrayerTime()
        p1.setFajrAngle(fajr)
        p1.setIshaAngle(isha)
        p1.setCoordinate(location.longitude, location.latitude, 0)
        p1.setDate(
            MiladiDate(
                date.get(Calendar.DATE),
                date.get(Calendar.MONTH) + 1,
                date.get(Calendar.YEAR),
            )
        )
        p1.setM_calcMethod(method)
        p1.setMazhab(mazhab)
        p1.m_season = season
        p1.setHour(Hour.Type.hour24)
        p1.calculate(false, false)

        Log.e("TestPray", " Noor   Madhab : $mazhab ||  method : ${method.toString()} || season : ${season.toString()} || fajrAngle $fajr || ishaAngle $isha")
        Log.e(
            "TestPray",
            " Noor \"DayPrayers (fajr= ${p1.fajrTime(false)}, sunrise= ${p1.shroukTime(false)}, dhuhr= ${p1.zuhrTime(false)}, asr= ${p1.asrTime(false)}, maghrib= ${p1.maghribTime(false)}, isha= ${
                p1.ishaTime(false)
            }, eid= )\"\n"
        )
        Log.e("TestPray", " Noor \n")
    }
}


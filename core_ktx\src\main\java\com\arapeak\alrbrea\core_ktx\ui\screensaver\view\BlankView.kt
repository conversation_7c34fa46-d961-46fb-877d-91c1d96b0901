package com.arapeak.alrbrea.core_ktx.ui.screensaver.view

import android.content.Context
import android.graphics.Color
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException

class BlankView(context: Context, val onclick: () -> Unit) : View(context) {
    init {
        setBackgroundColor(Color.BLACK)
        id = generateViewId()
    }

    override fun onTouchEvent(event: MotionEvent): Bo<PERSON>an {
        try {
            (parent as? ViewGroup)?.removeView(this)
            onclick.invoke()
        } catch (e: Exception) {
            logException(e)
        }
        return true
    }

    fun removeView() {
        try {
            (parent as? ViewGroup)?.removeView(this)
        } catch (e: Exception) {
            logException(e)
        }
    }

}
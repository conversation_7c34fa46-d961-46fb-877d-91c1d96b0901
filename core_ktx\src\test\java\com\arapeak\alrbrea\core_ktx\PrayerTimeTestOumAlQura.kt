package com.arapeak.alrbrea.core_ktx

import android.content.Context
import android.location.Location
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum
import com.arapeak.alrbrea.core_ktx.repo.PrayerTimeRepo
import com.batoulapps.adhan2.CalculationMethod
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import java.util.Calendar
import java.util.TimeZone


@RunWith(RobolectricTestRunner::class)
class PrayerTimeTestOumAlQura {

    private lateinit var repo: PrayerTimeRepo
    val timezone = "GMT-1:00"
    lateinit var context: Context

    @Before
    fun setUp() {
        val location = Location("mock").apply {
            latitude = 21.420036
            longitude = 39.806206
        }
        repo = PrayerTimeRepo(location, CalculationMethod.UMM_AL_QURA)
        context = RuntimeEnvironment.getApplication();
    }


    @Test
    fun test_date_1() {
        val expectedFajr = "04:12"
        val expectedSunrise = "05:38"
        val expectedDhur = "12:19"
        val expectedAsr = "15:36"
        val expectedMaghreb = "19:00"
        val expectedIsha = "20:30"

        val dateToTest = "1/6/2024"


        val date = Calendar.getInstance().apply {
            set(Calendar.YEAR, dateToTest.split("/")[2].toInt())
            set(Calendar.MONTH, dateToTest.split("/")[1].toInt() - 1)
            set(Calendar.DAY_OF_MONTH, dateToTest.split("/")[0].toInt())
        }
        val res = repo.getPrayerTimes(date, CountriesSupportedEnum.KSA, context)

        res.fajr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.sunrise.time.timeZone = TimeZone.getTimeZone(timezone)
        res.dhuhr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.asr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.maghrib.time.timeZone = TimeZone.getTimeZone(timezone)
        res.isha.time.timeZone = TimeZone.getTimeZone(timezone)
        res.eid?.time?.timeZone = TimeZone.getTimeZone(timezone)

        assertEquals(expectedFajr.split(":")[0].toInt(), res.fajr.time.time.hours)
        assertEquals(expectedFajr.split(":")[1].toInt(), res.fajr.time.time.minutes)

        assertEquals(expectedSunrise.split(":")[0].toInt(), res.sunrise.time.time.hours)
        assertEquals(expectedSunrise.split(":")[1].toInt(), res.sunrise.time.time.minutes)

        assertEquals(expectedDhur.split(":")[0].toInt(), res.dhuhr.time.time.hours)
        assertEquals(expectedDhur.split(":")[1].toInt(), res.dhuhr.time.time.minutes)

        assertEquals(expectedAsr.split(":")[0].toInt(), res.asr.time.time.hours)
        assertEquals(expectedAsr.split(":")[1].toInt(), res.asr.time.time.minutes)

        assertEquals(expectedMaghreb.split(":")[0].toInt(), res.maghrib.time.time.hours)
        assertEquals(expectedMaghreb.split(":")[1].toInt(), res.maghrib.time.time.minutes)

        assertEquals(expectedIsha.split(":")[0].toInt(), res.isha.time.time.hours)
        assertEquals(expectedIsha.split(":")[1].toInt(), res.isha.time.time.minutes)
    }

    @Test
    fun test_date_2() {
        val expectedFajr = "04:20"
        val expectedSunrise = "05:43"
        val expectedDhur = "12:18"
        val expectedAsr = "15:36"
        val expectedMaghreb = "18:51"
        val expectedIsha = "20:21"

        val dateToTest = "12/5/2024"


        val date = Calendar.getInstance().apply {
            set(Calendar.YEAR, dateToTest.split("/")[2].toInt())
            set(Calendar.MONTH, dateToTest.split("/")[1].toInt() - 1)
            set(Calendar.DAY_OF_MONTH, dateToTest.split("/")[0].toInt())
        }
        val res = repo.getPrayerTimes(date, CountriesSupportedEnum.KSA, context)

        res.fajr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.sunrise.time.timeZone = TimeZone.getTimeZone(timezone)
        res.dhuhr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.asr.time.timeZone = TimeZone.getTimeZone(timezone)
        res.maghrib.time.timeZone = TimeZone.getTimeZone(timezone)
        res.isha.time.timeZone = TimeZone.getTimeZone(timezone)
        res.eid?.time?.timeZone = TimeZone.getTimeZone(timezone)

        assertEquals(expectedFajr.split(":")[0].toInt(), res.fajr.time.time.hours)
        assertEquals(expectedFajr.split(":")[1].toInt(), res.fajr.time.time.minutes)

        assertEquals(expectedSunrise.split(":")[0].toInt(), res.sunrise.time.time.hours)
        assertEquals(expectedSunrise.split(":")[1].toInt(), res.sunrise.time.time.minutes)

        assertEquals(expectedDhur.split(":")[0].toInt(), res.dhuhr.time.time.hours)
        assertEquals(expectedDhur.split(":")[1].toInt(), res.dhuhr.time.time.minutes)

        assertEquals(expectedAsr.split(":")[0].toInt(), res.asr.time.time.hours)
        assertEquals(expectedAsr.split(":")[1].toInt(), res.asr.time.time.minutes)

        assertEquals(expectedMaghreb.split(":")[0].toInt(), res.maghrib.time.time.hours)
        assertEquals(expectedMaghreb.split(":")[1].toInt(), res.maghrib.time.time.minutes)

        assertEquals(expectedIsha.split(":")[0].toInt(), res.isha.time.time.hours)
        assertEquals(expectedIsha.split(":")[1].toInt(), res.isha.time.time.minutes)
    }

}



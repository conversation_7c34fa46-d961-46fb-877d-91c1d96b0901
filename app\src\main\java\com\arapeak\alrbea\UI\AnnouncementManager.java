package com.arapeak.alrbea.UI;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_FUNERAL_MESSAGES_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_FUNERAL_MESSAGES_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.Utils.getString;

import static org.apache.xmlbeans.impl.common.XBeanDebug.log;

import android.graphics.Typeface;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.AnnouncementMessage;
import com.arapeak.alrbea.Enum.AzanAudio;
import com.arapeak.alrbea.Enum.IAudio;
import com.arapeak.alrbea.Enum.IkamaAudio;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Model.EventAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.mikhaellopez.circularprogressbar.CircularProgressBar;
import com.orhanobut.hawk.Hawk;
import com.tonyodev.fetch2core.Logger;

import java.util.Objects;

import pl.droidsonroids.gif.GifImageView;

public class AnnouncementManager {
    private final GifImageView silentGif;
    private final CircularProgressBar circularProgressBar;
    private final MainActivity activity;
    public ViewGroup container;
    public ViewGroup layout_progress_remaining;
    public ViewGroup descriptionLayout;
    public TextView message;
    public TextView description;
    public TextView remainingOnIkama;
    public TextView remainingText;
    public TextView remainingNumber;
    public AnnouncementMessage announcement;
    public float messageSize = 0;
    public float descriptionSize = 0;
    public float remainingOnIkamaSize = 0;
    public Margin messageMargin;
    MediaPlayer mediaPlayer;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final Runnable updateRemainingProgressRunnable = new Runnable() {
        @Override
        public void run() {
            try {
                if (announcement != null && announcement.prayer != null && announcement.prayer.prayerTime != null) {

                    String[] diff = Utils.getDifferenceTimeForIkama(announcement.prayer.prayerTime);
                    setText(remainingNumber, diff[0]);
                    setText(remainingText, diff[1]);

                    float remainTime = announcement.prayer.prayerTime.getIkamaTime() - System.currentTimeMillis();
                    float fullDuration = announcement.prayer.prayerTime.getIkamaAnnouncementDuration();

                    float progress = fullDuration != 0 ? (remainTime / fullDuration) * 100 : 0;
                    progress = Math.abs(progress - 100);

                    setProgress(progress);

                    if (diff[0].equals("0")) {
                        handler.removeCallbacks(updateRemainingProgressRunnable);
                        Log.d("AnnouncementManager", "removeCallbacks");
                    } else {
                        long delay = diff[1].toLowerCase().contains("دقيقة") ? 60_000 : 1_000;
                        handler.postDelayed(updateRemainingProgressRunnable, delay);
                    }

                    Log.i("AnnouncementManager", "diff: " + diff[0] + " - " + diff[1]);
                } else {
                    Log.d("AnnouncementManager", "announcement or prayerTime is null");
                    handler.removeCallbacks(updateRemainingProgressRunnable);
                }
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    };
    private EventAlrabeeaTimes eventFuneralMessages;

    public AnnouncementManager(MainActivity act) {
        activity = act;
        container = activity.findViewById(R.id.announcement_include_MainActivity);
        message = activity.findViewById(R.id.tv_message);
        description = activity.findViewById(R.id.tv_description);
        silentGif = activity.findViewById(R.id.gif_silent);
        circularProgressBar = activity.findViewById(R.id.progressBar);
        layout_progress_remaining = activity.findViewById(R.id.layout_progress_remaining);
        remainingText = activity.findViewById(R.id.tv_remaining_text);
        remainingText.setVisibility(View.VISIBLE);
        remainingNumber = activity.findViewById(R.id.tv_remaining_number);
        remainingOnIkama = activity.findViewById(R.id.tv_remainingOnIkama);
//        descriptionLayout = activity.findViewById(R.id.description_layout_container);

        messageSize = message.getTextSize();
        descriptionSize = description.getTextSize();
        remainingOnIkamaSize = remainingOnIkama.getTextSize();

        safeRunOnUi(() -> messageMargin = getMargin(message));
//        descriptionSize = activity.getResources().getDimension(R.dimen.hadithBetweenAdhaanAndIkama);
        ResetViews();
    }

    public void viewMessage(AnnouncementMessage announcement) {
        this.announcement = announcement;
        safeRunOnUi(() -> {
            updateFunerals();
            ResetViews();
            switch (announcement.type) {
                case AZAN:
                    loadAzan();
                    break;
                case BETWEEN_AZAN_AND_IKAMA:
                    loadBetweenAzanAndIkama();
                    break;
                case MINUTE_BEFORE_IKAMA:
                    loadMinuteBeforeIkama();
                    break;
                case PRAY:
                    loadPray();
                    break;
            }
        });
    }

    private void updateFunerals() {
        this.eventFuneralMessages = getFuneralMessage();
    }

    public EventAlrabeeaTimes getFuneralMessage() {
        if (!Hawk.get(IS_ENABLE_FUNERAL_MESSAGES_KEY, IS_ENABLE_FUNERAL_MESSAGES_DEFAULT))
            return null;
        EventAlrabeeaTimes message = Utils.getInstanceOfRealm().where(EventAlrabeeaTimes.class)
                .equalTo("type", ConstantsOfApp.FUNERAL_MESSAGES_KEY)
                .equalTo("isActive", true)
                .lessThanOrEqualTo("timeEventByMilliseconds", System.currentTimeMillis())
                .findFirst();
        if (message != null && !message.getPrayerName().isEmpty()) {
            int year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR));
            int month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH));
            int day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY));
            if (Objects.equals(message.getStartDate(), day + "-" + month + "-" + year))
                return message;
        }
        return null;
    }

    public String getFuneralInNextPrayer() {
        try {
            if (eventFuneralMessages != null) {
                Log.i("AnnouncementManager", "funeral prayer key : " + eventFuneralMessages.getTagOfPray());

                if (Objects.equals(eventFuneralMessages.getTagOfPray(), this.announcement.prayer.getKEY())) {
                    return eventFuneralMessages.getMessageEvent();
                }
            }
            return null;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
            return null;
        }
    }
    //1.14
    //12.54


    private void loadPray() {
//        boolean displayAllInMessage = false;
//        int themeAppKey = Hawk.get(APP_THEME_KEY, APP_THEME_DEFAULT_KEY);
//        switch (themeAppKey){
//            case ARG_APP_THEME_BLUE_NEW:
//            case ARG_APP_THEME_BROWN_NEW:
//            case ARG_APP_THEME_Dark_Green:
//                displayAllInMessage = true;
//        }
        setVisibility(message, View.VISIBLE);
        if (!activity.isLandscape)
            setVisibility(description, View.INVISIBLE);
        setVisibility(descriptionLayout, View.GONE);

        setText(message, announcement.prayer.getFullName());
        //default size 18
//        float size
        setTextSize(message, activity.getResources().getDimension(com.intuit.sdp.R.dimen._28sdp));
        setMarginForThemes();
        if (announcement.prayer == PrayerType.Duha) {
            setVisibility(remainingOnIkama, View.VISIBLE);
            //adding line to move the text to down
            String remain = !activity.isLandscape ? "\n" : "";
            remain += Utils.getString(R.string.enter_time);
            setText(remainingOnIkama, remain);
            setTextSize(remainingOnIkama, activity.getResources().getDimension(com.intuit.sdp.R.dimen._20sdp));
        } else {
            eventFuneralMessages = getFuneralMessage();
//            eventFuneralMessages.getPrayerName();
            if (eventFuneralMessages != null) {
                if (Objects.equals(eventFuneralMessages.getTagOfPray(), this.announcement.prayer.getKEY())) {
                    setVisibility(descriptionLayout, View.VISIBLE);
                    setVisibility(description, View.VISIBLE);
//                    setBackground(description, R.drawable.shape_without_corners_12_stroke_gray_1_transparent);
                    setTextSize(description, activity.getResources().getDimension(R.dimen.funeralMessages));
                    setText(description, getString(R.string.followed)
                            + " " + getString(R.string.funeral_prayer)
                            + "\n" + eventFuneralMessages.getNameEvent());
                }
            }
            if (!isNowPlaying() && HawkSettings.getAudioIkamaEnabled())
                playSound(IkamaAudio.values()[HawkSettings.getCurrentIkama()]);
        }
    }

    private void setMarginForThemes() {
        switch (HawkSettings.getCurrentTheme()) {
            case BROWN_NEW:
                if (activity.isLandscape) {
                    safeRunOnUi(() -> {
                        Margin m = new Margin();
                        m.t = activity.getResources().getDimensionPixelOffset(com.intuit.sdp.R.dimen._minus15sdp);
                        m.b = activity.getResources().getDimensionPixelOffset(com.intuit.sdp.R.dimen._minus10sdp);
                        setMargins(message, m);
                    });

                }
                break;
        }
    }

    private void setBackground(TextView tv, int res) {
        if (tv == null)
            return;
        safeRunOnUi(() -> tv.setBackgroundResource(res));
    }

    private void loadMinuteBeforeIkama() {
        setVisibility(message, View.VISIBLE);
        setVisibility(description, View.VISIBLE);
        setVisibility(remainingOnIkama, View.VISIBLE);

        setVisibility(layout_progress_remaining, View.VISIBLE);
        setVisibility(silentGif, View.VISIBLE);
        try {
            updateRemainingTime();
//            if(!updateRemainingProgress.isAlive())
//                updateRemainingProgress.start();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        setText(remainingOnIkama, getString(R.string.remaining_on_ikama));
        setText(message, announcement.prayer.getFullName());
        setText(description, getString(R.string.hadeth_after_prayerselint));
    }

    private void loadBetweenAzanAndIkama() {
        setVisibility(message, View.VISIBLE);
        setVisibility(description, View.VISIBLE);
        setVisibility(remainingOnIkama, View.VISIBLE);
        if (announcement.prayer == PrayerType.Dhuhr && announcement.prayer.prayerTime.isJomaa()) {

            setText(remainingOnIkama, getString(R.string.now));
            setTextSize(remainingOnIkama, activity.getResources().getDimension(com.intuit.sdp.R.dimen._16sdp));
            setTextSize(message, activity.getResources().getDimension(com.intuit.sdp.R.dimen._24sdp));
            setTextSize(description, activity.getResources().getDimension(com.intuit.sdp.R.dimen._16sdp));
            if (Hawk.get("titlejomaaenable", false) && !Hawk.get(ConstantsOfApp.TITLE_JOMAA, "").isEmpty())
                setText(message, "خطبة الجمعة" + "\n{ " + Hawk.get(ConstantsOfApp.TITLE_JOMAA, "") + " }");
            else
                setText(message, "خطبة الجمعة");
            setText(description, getString(R.string.hadeth_after_prayersejomaa));
        } else {
            setVisibility(layout_progress_remaining, View.VISIBLE);
            setText(remainingOnIkama, getString(R.string.remaining_on_ikama));
            setText(message, announcement.prayer.getFullName());
            setText(description, getString(R.string.hadeth_after_prayer));
            try {
                updateRemainingTime();
//                if(!updateRemainingProgress.isAlive())
//                    updateRemainingProgress.start();
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
            }

        }
    }

    public void updateRemainingTime() {
        String[] diff = Utils.getDifferenceTimeForIkama(announcement.prayer.prayerTime);
        setText(remainingNumber, diff[0]+"-"+diff[1]);
        setText(remainingText, "دقائق");
        float remainTime = announcement.prayer.prayerTime.getIkamaTime() - System.currentTimeMillis();
        float fullDuration = announcement.prayer.prayerTime.getIkamaAnnouncementDuration(); // 60 000  1257542
        //safe division by zero
        float progress = fullDuration != 0 ? (remainTime / fullDuration) * 100 : 0;

        handler.post(updateRemainingProgressRunnable);
        //reverse progress from 100 to 0 .. from 90 to 10
        progress = Math.abs(progress - 100);
        setProgress(progress);
    }

    private long getCurrentTimePlusMinute() {
        long cur = System.currentTimeMillis();
        long secondsLeft = cur % MINUTES_MILLI_SECOND;
        return cur - secondsLeft + MINUTES_MILLI_SECOND;
    }

    private void setProgress(float progress) {
        if (circularProgressBar != null)
            safeRunOnUi(() -> circularProgressBar.setProgress(progress));
    }

    public void ResetViews() {
        stopPlayer();
        setVisibility(message, View.GONE);
        setVisibility(description, View.GONE);
        setVisibility(silentGif, View.GONE);
        setVisibility(remainingOnIkama, View.GONE);
        setVisibility(layout_progress_remaining, View.GONE);
        setVisibility(descriptionLayout, View.VISIBLE);
        setBackground(description, 0);
        setMargins(message, messageMargin);
        safeRunOnUi(() -> {
            setTextSize(message, messageSize);
            setTextSize(description, descriptionSize);
            setTextSize(remainingOnIkama, remainingOnIkamaSize);

            setTypeface(description, description.getTypeface(), Typeface.NORMAL);
        });

    }

    private void loadAzan() {
        setVisibility(message, View.VISIBLE);
        setVisibility(description, View.VISIBLE);
        setVisibility(remainingOnIkama, View.VISIBLE);
        setTextSize(remainingOnIkama, activity.getResources().getDimension(com.intuit.sdp.R.dimen._16sdp));
        setVisibility(message, View.VISIBLE);
        setText(remainingOnIkama, getString(R.string.now_enter_time));
        setTextSize(message, activity.getResources().getDimension(com.intuit.sdp.R.dimen._28sdp));
        setMarginForThemes();
        setText(message, announcement.prayer.getFullName());
        setText(description, getString(R.string.hadeth_on_prayer));
        if (announcement.prayer != PrayerType.Duha && !isNowPlaying() && HawkSettings.getAudioAzanEnabled())
            playSound(AzanAudio.values()[HawkSettings.getCurrentAzan()]);
//            safeRunOnUi(() -> Sound(announcement.prayer.KEY));
    }

    public void playSound(IAudio audio) {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release(); // تأكد من تحرير السابق
                mediaPlayer = null;
            }

            if (audio.isOffline()) {
                if (audio.getOfflineResource() == 0)
                    mediaPlayer = MediaPlayer.create(activity.getBaseContext(), R.raw.a);
                else
                    mediaPlayer = MediaPlayer.create(activity.getBaseContext(), audio.getOfflineResource());
            } else {
                mediaPlayer = MediaPlayer.create(activity.getBaseContext(), audio.getUri());
            }

            if (mediaPlayer != null) {
                float vol = HawkSettings.getAudioLevel();
                mediaPlayer.setVolume(vol, vol);
                mediaPlayer.start();
            } else {
                log("MediaPlayer is null for audio: " + audio);
            }

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }


    private void setVisibility(View view, int visibility) {
        if (view == null)
            return;
        try {
            safeRunOnUi(() -> view.setVisibility(visibility));
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void setText(TextView tv, String text) {
        if (tv == null)
            return;
        safeRunOnUi(() -> tv.setText(text));
    }

    private void setTextSize(TextView tv, float size) {
        if (tv != null) {
            safeRunOnUi(() -> tv.setTextSize(size));
        }
    }

    private void setTypeface(TextView tv, Typeface tf, int style) {
        if (tv != null) {
            safeRunOnUi(() -> tv.setTypeface(tf, style));
        }
    }

    public void onStop() {
        stopPlayer();
        try {
            if (mediaPlayer != null)
                mediaPlayer.release();
            mediaPlayer = null;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
//        safeRun(()->updateRemainingProgress.interrupt());
    }

    public boolean isNowPlaying() {
        return (mediaPlayer != null && mediaPlayer.isPlaying());
    }

    public void stopPlayer() {
        try {
            if (isNowPlaying())
                mediaPlayer.stop();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }

    private void safeRunOnUi(Runnable job) {
        activity.runOnUiThread(job);
    }

    private void safeRun(Runnable job) {
        try {
            job.run();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public void setMargins(View v, Margin m) {
        if (v == null)
            return;
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(m.l, m.t, m.r, m.b);
            v.requestLayout();
        }
    }

    public Margin getMargin(@NonNull View v) {
        Margin m = new Margin();
        if (v.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams p = (ViewGroup.MarginLayoutParams) v.getLayoutParams();
            p.setMargins(m.l, m.t, m.r, m.b);
            m.t = p.topMargin;
            m.b = p.bottomMargin;
            m.r = p.rightMargin;
            m.l = p.leftMargin;
        }
        return m;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true"
    tools:context=".deleted.ContactUsFragment"
    tools:layoutDirection="rtl">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <EditText
            android:id="@+id/fullName_EditText_ContactUsFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="start|center_vertical"
            android:hint="@string/full_name"
            android:includeFontPadding="false"
            android:inputType="textPersonName"
            android:paddingStart="16dp"
            android:paddingTop="8dp"
            android:paddingEnd="16dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:textAlignment="viewStart"
            android:textColor="@android:color/black"
            android:textColorHint="#474747"
            android:textSize="@dimen/contactUsView"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/email_EditText_ContactUsFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="start|center_vertical"
            android:hint="@string/email"
            android:includeFontPadding="false"
            android:inputType="textWebEmailAddress"
            android:paddingStart="16dp"
            android:paddingTop="8dp"
            android:paddingEnd="16dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:textAlignment="viewStart"
            android:textColor="@android:color/black"
            android:textColorHint="#474747"
            android:textSize="@dimen/contactUsView"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fullName_EditText_ContactUsFragment" />

        <EditText
            android:id="@+id/mobileNumber_EditText_ContactUsFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="start|center_vertical"
            android:hint="@string/mobile_number"
            android:includeFontPadding="false"
            android:inputType="phone"
            android:paddingStart="16dp"
            android:paddingTop="8dp"
            android:paddingEnd="16dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:textAlignment="viewStart"
            android:textColor="@android:color/black"
            android:textColorHint="#474747"
            android:textSize="@dimen/contactUsView"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/email_EditText_ContactUsFragment" />

        <EditText
            android:id="@+id/subject_EditText_ContactUsFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="start|center_vertical"
            android:hint="@string/subject"
            android:includeFontPadding="false"
            android:inputType="text"
            android:paddingStart="16dp"
            android:paddingTop="8dp"
            android:paddingEnd="16dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:textAlignment="viewStart"
            android:textColor="@android:color/black"
            android:textColorHint="#474747"
            android:textSize="@dimen/contactUsView"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mobileNumber_EditText_ContactUsFragment" />


        <EditText
            android:id="@+id/bodyOfMessage_EditText_ContactUsFragment"
            android:layout_width="0dp"
            android:layout_height="250dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/without_corners_bottom_20_background_gray"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="start|top"
            android:hint="@string/body_of_message"
            android:includeFontPadding="false"
            android:inputType="textMultiLine"
            android:paddingStart="16dp"
            android:paddingTop="8dp"
            android:paddingEnd="16dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:textAlignment="viewStart"
            android:textColor="@android:color/black"
            android:textColorHint="#474747"
            android:textSize="@dimen/contactUsView"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/subject_EditText_ContactUsFragment" />


        <Button
            android:id="@+id/send_Button_ContactUsFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:paddingStart="8dp"
            android:paddingTop="4dp"
            android:paddingEnd="8dp"
            android:paddingBottom="4dp"
            android:text="@string/send"
            android:textColor="@android:color/white"
            android:textSize="@dimen/contactUsView"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/bodyOfMessage_EditText_ContactUsFragment" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import org.jetbrains.annotations.NotNull;

import java.util.Calendar;
import java.util.Date;

import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class MiladiDate {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: a, reason: collision with root package name and from kotlin metadata */
    private int day;

    /* renamed from: b, reason: from kotlin metadata */
    private int month;

    /* renamed from: c, reason: from kotlin metadata */
    private int year;

    /* renamed from: d, reason: from kotlin metadata */
    private int current_year;

    public MiladiDate(int day, int month, int year) {
        this.day = day;
        this.month = month;
        this.year = year;
    }

    @NotNull
    public final Calendar ToCalendar() {
        Calendar cal = Calendar.getInstance();
        cal.set(1, this.year);
        cal.set(2, this.month - 1);
        cal.set(5, this.day);
        Intrinsics.checkNotNullExpressionValue(cal, "cal");
        return cal;
    }

    @NotNull
    public final Date ToDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(1, this.year);
        calendar.set(2, this.month - 1);
        calendar.set(5, this.day);
        Date time = calendar.getTime();
        Intrinsics.checkNotNullExpressionValue(time, "cal.time");
        return time;
    }

    public final int getCurrent_year() {
        return this.current_year;
    }

    public final void setCurrent_year(int i) {
        this.current_year = i;
    }

    public final int getDay() {
        return this.day;
    }

    public final void setDay(int i) {
        this.day = i;
    }

    public final int getMonth() {
        return this.month;
    }

    public final void setMonth(int i) {
        this.month = i;
    }

    public final int getYear() {
        return this.year;
    }

    public final void setYear(int i) {
        this.year = i;
    }

    public final void setYearMinus(int myval) {
        this.year -= myval;
    }

    @NotNull
    public final String toString4Sqlite(boolean is_ignore_year) {
        String str = "" + this.month;
        String str2 = "" + this.day;
        if (str.length() == 1) {
            str = "0".concat(str);
        }
        if (str2.length() == 1) {
            str2 = "0".concat(str2);
        }
        String str3 = this.year + '/' + str + '/' + str2;
        if (is_ignore_year) {
            return "/" + str + '/' + str2;
        }
        return str3;
    }

    /* compiled from: MiladiDate.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006¨\u0006\u0007"}, d2 = {"Lcom/alawail/prayertimes/moazen/MiladiDate$Companion;", "", "()V", "getInstanse", "Lcom/alawail/prayertimes/moazen/MiladiDate;", "date", "Ljava/util/Date;", "prayer_time_mobileRelease"}, k = 1, mv = {1, 7, 1}, xi = 48)
    /* loaded from: classes.dex */
    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        @NotNull
        public final MiladiDate getInstanse(@NotNull Date date) {
            Intrinsics.checkNotNullParameter(date, "date");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            return new MiladiDate(calendar.get(5), calendar.get(2) + 1, calendar.get(1));
        }
    }
}
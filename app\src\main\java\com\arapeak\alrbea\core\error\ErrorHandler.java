//package com.arapeak.alrbea.core.error;
//
//import android.content.Context;
//import android.util.Log;
//
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//
//import java.io.IOException;
//import java.net.SocketTimeoutException;
//import java.net.UnknownHostException;
//import java.sql.SQLException;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.atomic.AtomicInteger;
//
///**
// * Centralized error handler for the application
// * Provides comprehensive error handling, logging, and recovery mechanisms
// */
//public class ErrorHandler {
//
//    private static final String TAG = "ErrorHandler";
//
//    // Singleton instance
//    private static volatile ErrorHandler instance;
//
//    private final Context context;
//    private final ConcurrentHashMap<String, AtomicInteger> errorCounts = new ConcurrentHashMap<>();
//    private final ConcurrentHashMap<String, Long> lastErrorTimes = new ConcurrentHashMap<>();
//
//    // Error thresholds
//    private static final int MAX_ERROR_COUNT = 5;
//    private static final long ERROR_RESET_INTERVAL = 5 * 60 * 1000; // 5 minutes
//    private static final long MIN_ERROR_INTERVAL = 1000; // 1 second
//
//    private ErrorHandler(Context context) {
//        this.context = context.getApplicationContext();
//    }
//
//    /**
//     * Get singleton instance
//     */
//    public static ErrorHandler getInstance(Context context) {
//        if (instance == null) {
//            synchronized (ErrorHandler.class) {
//                if (instance == null) {
//                    instance = new ErrorHandler(context);
//                }
//            }
//        }
//        return instance;
//    }
//
//    /**
//     * Handle any exception with automatic error type detection
//     */
//    public ErrorState handleError(String message, Exception exception) {
//        try {
//            Log.e(TAG, "Handling error: " + message, exception);
//
//            // Detect error type
//            ErrorState.ErrorType errorType = detectErrorType(exception);
//
//            // Check if error should be throttled
//            if (shouldThrottleError(message, exception)) {
//                Log.w(TAG, "Error throttled: " + message);
//                return null;
//            }
//
//            // Log error count
//            incrementErrorCount(message);
//
//            // Log to Crashlytics
//            logToCrashlytics(message, exception, errorType);
//
//            // Create appropriate error state
//            ErrorState errorState = createErrorState(message, exception, errorType);
//
//            // Attempt automatic recovery if possible
//            attemptRecovery(errorState);
//
//            return errorState;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error in error handler", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return ErrorState.unknown("Error handler failed", e);
//        }
//    }
//
//    /**
//     * Handle network errors specifically
//     */
//    public ErrorState handleNetworkError(String message, Exception exception) {
//        try {
//            Log.e(TAG, "Handling network error: " + message, exception);
//
//            if (shouldThrottleError(message, exception)) {
//                return null;
//            }
//
//            incrementErrorCount(message);
//            logToCrashlytics(message, exception, ErrorState.ErrorType.NETWORK);
//
//            String userMessage = "Network connection error. Please check your internet connection and try again.";
//            ErrorState errorState = new ErrorState(message, exception, ErrorState.ErrorType.NETWORK, true, userMessage);
//
//            // Attempt network recovery
//            attemptNetworkRecovery();
//
//            return errorState;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error handling network error", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return ErrorState.unknown("Network error handler failed", e);
//        }
//    }
//
//    /**
//     * Handle database errors specifically
//     */
//    public ErrorState handleDatabaseError(String message, Exception exception) {
//        try {
//            Log.e(TAG, "Handling database error: " + message, exception);
//
//            if (shouldThrottleError(message, exception)) {
//                return null;
//            }
//
//            incrementErrorCount(message);
//            logToCrashlytics(message, exception, ErrorState.ErrorType.DATABASE);
//
//            String userMessage = "Database error occurred. The app will try to recover automatically.";
//            ErrorState errorState = new ErrorState(message, exception, ErrorState.ErrorType.DATABASE, true, userMessage);
//
//            // Attempt database recovery
//            attemptDatabaseRecovery();
//
//            return errorState;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error handling database error", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return ErrorState.unknown("Database error handler failed", e);
//        }
//    }
//
//    /**
//     * Handle prayer calculation errors specifically
//     */
//    public ErrorState handlePrayerCalculationError(String message, Exception exception) {
//        try {
//            Log.e(TAG, "Handling prayer calculation error: " + message, exception);
//
//            if (shouldThrottleError(message, exception)) {
//                return null;
//            }
//
//            incrementErrorCount(message);
//            logToCrashlytics(message, exception, ErrorState.ErrorType.PRAYER_CALCULATION);
//
//            String userMessage = "Error calculating prayer times. Using default values.";
//            ErrorState errorState = new ErrorState(message, exception, ErrorState.ErrorType.PRAYER_CALCULATION, true, userMessage);
//
//            // Attempt prayer calculation recovery
//            attemptPrayerCalculationRecovery();
//
//            return errorState;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error handling prayer calculation error", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return ErrorState.unknown("Prayer calculation error handler failed", e);
//        }
//    }
//
//    /**
//     * Handle file system errors specifically
//     */
//    public ErrorState handleFileSystemError(String message, Exception exception) {
//        try {
//            Log.e(TAG, "Handling file system error: " + message, exception);
//
//            if (shouldThrottleError(message, exception)) {
//                return null;
//            }
//
//            incrementErrorCount(message);
//            logToCrashlytics(message, exception, ErrorState.ErrorType.FILE_SYSTEM);
//
//            String userMessage = "File access error. Please check storage permissions.";
//            ErrorState errorState = new ErrorState(message, exception, ErrorState.ErrorType.FILE_SYSTEM, true, userMessage);
//
//            // Attempt file system recovery
//            attemptFileSystemRecovery();
//
//            return errorState;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error handling file system error", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return ErrorState.unknown("File system error handler failed", e);
//        }
//    }
//
//    /**
//     * Handle permission errors specifically
//     */
//    public ErrorState handlePermissionError(String message, Exception exception) {
//        try {
//            Log.e(TAG, "Handling permission error: " + message, exception);
//
//            if (shouldThrottleError(message, exception)) {
//                return null;
//            }
//
//            incrementErrorCount(message);
//            logToCrashlytics(message, exception, ErrorState.ErrorType.PERMISSION);
//
//            String userMessage = "Permission required. Please grant the necessary permissions to continue.";
//            ErrorState errorState = new ErrorState(message, exception, ErrorState.ErrorType.PERMISSION, false, userMessage);
//
//            // Permission errors usually require user action
//            return errorState;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error handling permission error", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//            return ErrorState.unknown("Permission error handler failed", e);
//        }
//    }
//
//    /**
//     * Detect error type based on exception
//     */
//    private ErrorState.ErrorType detectErrorType(Exception exception) {
//        try {
//            if (exception instanceof UnknownHostException ||
//                exception instanceof SocketTimeoutException ||
//                exception instanceof IOException) {
//                return ErrorState.ErrorType.NETWORK;
//            }
//
//            if (exception instanceof SQLException ||
//                exception.getMessage() != null && exception.getMessage().contains("database")) {
//                return ErrorState.ErrorType.DATABASE;
//            }
//
//            if (exception instanceof SecurityException ||
//                exception.getMessage() != null && exception.getMessage().contains("permission")) {
//                return ErrorState.ErrorType.PERMISSION;
//            }
//
//            if (exception instanceof IOException ||
//                exception.getMessage() != null && exception.getMessage().contains("file")) {
//                return ErrorState.ErrorType.FILE_SYSTEM;
//            }
//
//            if (exception.getMessage() != null &&
//                (exception.getMessage().contains("prayer") ||
//                 exception.getMessage().contains("timing") ||
//                 exception.getMessage().contains("calculation"))) {
//                return ErrorState.ErrorType.PRAYER_CALCULATION;
//            }
//
//            return ErrorState.ErrorType.UNKNOWN;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error detecting error type", e);
//            return ErrorState.ErrorType.UNKNOWN;
//        }
//    }
//
//    /**
//     * Check if error should be throttled to prevent spam
//     */
//    private boolean shouldThrottleError(String message, Exception exception) {
//        try {
//            String errorKey = message + ":" + exception.getClass().getSimpleName();
//
//            // Check error count
//            AtomicInteger count = errorCounts.get(errorKey);
//            if (count != null && count.get() >= MAX_ERROR_COUNT) {
//                return true;
//            }
//
//            // Check time interval
//            Long lastTime = lastErrorTimes.get(errorKey);
//            long currentTime = System.currentTimeMillis();
//
//            if (lastTime != null && (currentTime - lastTime) < MIN_ERROR_INTERVAL) {
//                return true;
//            }
//
//            lastErrorTimes.put(errorKey, currentTime);
//            return false;
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error checking throttle", e);
//            return false;
//        }
//    }
//
//    /**
//     * Increment error count for tracking
//     */
//    private void incrementErrorCount(String message) {
//        try {
//            String errorKey = message;
//            AtomicInteger count = errorCounts.computeIfAbsent(errorKey, k -> new AtomicInteger(0));
//            count.incrementAndGet();
//
//            // Reset count if enough time has passed
//            Long lastTime = lastErrorTimes.get(errorKey);
//            if (lastTime != null &&
//                (System.currentTimeMillis() - lastTime) > ERROR_RESET_INTERVAL) {
//                count.set(0);
//            }
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error incrementing error count", e);
//        }
//    }
//
//    /**
//     * Log error to Crashlytics with additional context
//     */
//    private void logToCrashlytics(String message, Exception exception, ErrorState.ErrorType errorType) {
//        try {
//            CrashlyticsUtils.INSTANCE.setCustomKey("error_type", errorType.name());
//            CrashlyticsUtils.INSTANCE.setCustomKey("error_message", message);
//            CrashlyticsUtils.INSTANCE.setCustomKey("timestamp", System.currentTimeMillis());
//            CrashlyticsUtils.INSTANCE.logException(exception);
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error logging to Crashlytics", e);
//        }
//    }
//
//    /**
//     * Create appropriate error state based on type
//     */
//    private ErrorState createErrorState(String message, Exception exception, ErrorState.ErrorType errorType) {
//        try {
//            switch (errorType) {
//                case NETWORK:
//                    return ErrorState.network(message, exception);
//                case DATABASE:
//                    return ErrorState.database(message, exception);
//                case PRAYER_CALCULATION:
//                    return ErrorState.prayerCalculation(message, exception);
//                case FILE_SYSTEM:
//                    return ErrorState.fileSystem(message, exception);
//                case PERMISSION:
//                    return ErrorState.permission(message, exception);
//                case CONFIGURATION:
//                    return ErrorState.configuration(message, exception);
//                default:
//                    return ErrorState.unknown(message, exception);
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error creating error state", e);
//            return ErrorState.unknown(message, exception);
//        }
//    }
//
//    /**
//     * Attempt automatic recovery based on error type
//     */
//    private void attemptRecovery(ErrorState errorState) {
//        try {
//            if (!errorState.isRecoverable()) {
//                return;
//            }
//
//            switch (errorState.getErrorType()) {
//                case NETWORK:
//                    attemptNetworkRecovery();
//                    break;
//                case DATABASE:
//                    attemptDatabaseRecovery();
//                    break;
//                case PRAYER_CALCULATION:
//                    attemptPrayerCalculationRecovery();
//                    break;
//                case FILE_SYSTEM:
//                    attemptFileSystemRecovery();
//                    break;
//                case CONFIGURATION:
//                    attemptConfigurationRecovery();
//                    break;
//            }
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error during recovery attempt", e);
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Attempt network recovery
//     */
//    private void attemptNetworkRecovery() {
//        try {
//            Log.d(TAG, "Attempting network recovery");
//            // Implementation for network recovery
//            // e.g., retry with exponential backoff, check connectivity, etc.
//        } catch (Exception e) {
//            Log.e(TAG, "Error in network recovery", e);
//        }
//    }
//
//    /**
//     * Attempt database recovery
//     */
//    private void attemptDatabaseRecovery() {
//        try {
//            Log.d(TAG, "Attempting database recovery");
//            // Implementation for database recovery
//            // e.g., close and reopen connections, clear cache, etc.
//        } catch (Exception e) {
//            Log.e(TAG, "Error in database recovery", e);
//        }
//    }
//
//    /**
//     * Attempt prayer calculation recovery
//     */
//    private void attemptPrayerCalculationRecovery() {
//        try {
//            Log.d(TAG, "Attempting prayer calculation recovery");
//            // Implementation for prayer calculation recovery
//            // e.g., use fallback calculation method, default values, etc.
//        } catch (Exception e) {
//            Log.e(TAG, "Error in prayer calculation recovery", e);
//        }
//    }
//
//    /**
//     * Attempt file system recovery
//     */
//    private void attemptFileSystemRecovery() {
//        try {
//            Log.d(TAG, "Attempting file system recovery");
//            // Implementation for file system recovery
//            // e.g., check permissions, create directories, etc.
//        } catch (Exception e) {
//            Log.e(TAG, "Error in file system recovery", e);
//        }
//    }
//
//    /**
//     * Attempt configuration recovery
//     */
//    private void attemptConfigurationRecovery() {
//        try {
//            Log.d(TAG, "Attempting configuration recovery");
//            // Implementation for configuration recovery
//            // e.g., reset to defaults, reload configuration, etc.
//        } catch (Exception e) {
//            Log.e(TAG, "Error in configuration recovery", e);
//        }
//    }
//
//    /**
//     * Clear error counts (useful for testing or manual reset)
//     */
//    public void clearErrorCounts() {
//        try {
//            errorCounts.clear();
//            lastErrorTimes.clear();
//            Log.d(TAG, "Error counts cleared");
//        } catch (Exception e) {
//            Log.e(TAG, "Error clearing error counts", e);
//        }
//    }
//
//    /**
//     * Get error statistics
//     */
//    public String getErrorStatistics() {
//        try {
//            StringBuilder stats = new StringBuilder();
//            stats.append("Error Statistics:\n");
//
//            for (String key : errorCounts.keySet()) {
//                AtomicInteger count = errorCounts.get(key);
//                if (count != null) {
//                    stats.append(key).append(": ").append(count.get()).append("\n");
//                }
//            }
//
//            return stats.toString();
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error getting error statistics", e);
//            return "Error getting statistics";
//        }
//    }
//}

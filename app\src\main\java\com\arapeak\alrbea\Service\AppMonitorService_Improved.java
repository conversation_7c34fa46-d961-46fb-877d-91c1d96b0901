package com.arapeak.alrbea.Service;

import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.IBinder;
import android.os.SystemClock;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.arapeak.alrbea.BroadcastReceiver.AlarmReceiver;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Improved AppMonitorService with better error handling and lifecycle
 * management
 */
public class AppMonitorService_Improved extends Service {
    private static final String TAG = "AppMonitorService_Improved";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "AppMonitorChannel";
    private static final long CHECK_INTERVAL = 10 * 60 * 1000; // 10 minutes for 24/7 monitoring
    private static final long MIN_RESTART_INTERVAL = 30 * 1000; // 30 seconds minimum between restarts
    private static final long HEARTBEAT_INTERVAL = 2 * 60 * 1000; // 2 minutes heartbeat check

    private AlarmManager alarmManager;
    private PendingIntent alarmPendingIntent;
    private NotificationManager notificationManager;
    private final AtomicBoolean isServiceRunning = new AtomicBoolean(false);
    private final AtomicBoolean isShuttingDown = new AtomicBoolean(false);
    private long lastRestartTime = 0;
    private long lastHeartbeat = 0;

    // 24/7 monitoring components
    private Handler monitoringHandler;
    private Runnable heartbeatRunnable;
    private Runnable appCheckRunnable;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "AppMonitorService_Improved created");

        try {
            if (isServiceRunning.compareAndSet(false, true)) {
                initializeService();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            handleServiceError(e);
        }
    }

    private void initializeService() {
        try {
            // Create notification channel
            createNotificationChannel();

            // Start foreground service
            startForegroundSafely();

            // Initialize 24/7 monitoring components
            initializeMonitoring();

            // Set up monitoring alarm
            setupMonitoringAlarm();

            Log.d(TAG, "Service initialized successfully with 24/7 monitoring");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing service", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            throw e; // Re-throw to trigger error handling
        }
    }

    /**
     * Initialize 24/7 monitoring components
     */
    private void initializeMonitoring() {
        try {
            Log.d(TAG, "Initializing 24/7 monitoring components");

            // Initialize monitoring handler
            monitoringHandler = new Handler(Looper.getMainLooper());
            lastHeartbeat = System.currentTimeMillis();

            // Initialize heartbeat runnable
            heartbeatRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        performHeartbeat();

                        // Schedule next heartbeat
                        if (!isShuttingDown.get() && monitoringHandler != null) {
                            monitoringHandler.postDelayed(this, HEARTBEAT_INTERVAL);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in heartbeat", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                }
            };

            // Initialize app check runnable
            appCheckRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        performAppCheck();

                        // Schedule next app check
                        if (!isShuttingDown.get() && monitoringHandler != null) {
                            monitoringHandler.postDelayed(this, CHECK_INTERVAL);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in app check", e);
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                }
            };

            // Start monitoring
            startMonitoring();

            Log.d(TAG, "24/7 monitoring components initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing monitoring", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Start the monitoring processes
     */
    private void startMonitoring() {
        try {
            if (monitoringHandler != null) {
                // Start heartbeat monitoring
                monitoringHandler.post(heartbeatRunnable);

                // Start app checking (delayed start)
                monitoringHandler.postDelayed(appCheckRunnable, 30000); // Start after 30 seconds

                Log.d(TAG, "24/7 monitoring started");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting monitoring", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void startForegroundSafely() {
        try {
            Notification notification = createNotification();

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // API 34
                startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_SYSTEM_EXEMPTED);
            } else {
                startForeground(NOTIFICATION_ID, notification);
            }

            Log.d(TAG, "Foreground service started successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error starting foreground service", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Try without foreground service type on older versions
            try {
                startForeground(NOTIFICATION_ID, createNotification());
                Log.d(TAG, "Foreground service started without service type");
            } catch (Exception fallbackError) {
                Log.e(TAG, "Failed to start foreground service even with fallback", fallbackError);
                throw fallbackError;
            }
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "AppMonitorService_Improved onStartCommand called");

        try {
            if (!isServiceRunning.get() && !isShuttingDown.get()) {
                initializeService();
            }

            // Return START_STICKY to restart if killed, but with better handling
            return START_STICKY;

        } catch (Exception e) {
            Log.e(TAG, "Error in onStartCommand", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            handleServiceError(e);
            return START_NOT_STICKY; // Don't restart if there's a critical error
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null; // This is not a bound service
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "AppMonitorService_Improved onDestroy called");

        try {
            isShuttingDown.set(true);
            cleanup();

            // Only restart if not explicitly shutting down and enough time has passed
            if (shouldRestartService()) {
                scheduleServiceRestart();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        } finally {
            super.onDestroy();
        }
    }

    private boolean shouldRestartService() {
        long currentTime = System.currentTimeMillis();
        boolean shouldRestart = !isShuttingDown.get() &&
                (currentTime - lastRestartTime) > MIN_RESTART_INTERVAL;

        if (shouldRestart) {
            lastRestartTime = currentTime;
        }

        Log.d(TAG, "Should restart service: " + shouldRestart);
        return shouldRestart;
    }

    private void scheduleServiceRestart() {
        try {
            Intent restartServiceIntent = new Intent(getApplicationContext(), AppMonitorService_Improved.class);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getApplicationContext().startForegroundService(restartServiceIntent);
            } else {
                getApplicationContext().startService(restartServiceIntent);
            }

            Log.d(TAG, "Service restart scheduled");

        } catch (Exception e) {
            Log.e(TAG, "Error scheduling service restart", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void cleanup() {
        try {
            isServiceRunning.set(false);

            // Cancel the monitoring alarm
            if (alarmManager != null && alarmPendingIntent != null) {
                alarmManager.cancel(alarmPendingIntent);
                alarmPendingIntent = null;
            }

            // Clear references
            alarmManager = null;
            notificationManager = null;

            Log.d(TAG, "Service cleanup completed");

        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                NotificationChannel channel = new NotificationChannel(
                        CHANNEL_ID,
                        "App Monitor Service",
                        NotificationManager.IMPORTANCE_LOW);
                channel.setDescription("Keeps the prayer times app running in the background");
                channel.setShowBadge(false);
                channel.setSound(null, null);
                channel.enableVibration(false);

                notificationManager = getSystemService(NotificationManager.class);
                if (notificationManager != null) {
                    notificationManager.createNotificationChannel(channel);
                    Log.d(TAG, "Notification channel created");
                } else {
                    Log.w(TAG, "NotificationManager is null");
                }

            } catch (Exception e) {
                Log.e(TAG, "Error creating notification channel", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        }
    }

    private Notification createNotification() {
        try {
            Intent notificationIntent = new Intent(this, MainActivity.class);
            notificationIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }

            PendingIntent pendingIntent = PendingIntent.getActivity(
                    this, 0, notificationIntent, flags);

            NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("Prayer Times Monitor")
                    .setContentText("Keeping prayer times app active")
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setContentIntent(pendingIntent)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setOngoing(true)
                    .setAutoCancel(false)
                    .setShowWhen(false);

            return builder.build();

        } catch (Exception e) {
            Log.e(TAG, "Error creating notification", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Return a minimal notification as fallback
            return new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("Prayer Times")
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .build();
        }
    }

    private void setupMonitoringAlarm() {
        try {
            alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
            if (alarmManager == null) {
                Log.e(TAG, "AlarmManager is null, cannot setup monitoring");
                return;
            }

            Intent alarmIntent = new Intent(this, AlarmReceiver.class);
            int flags = PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= PendingIntent.FLAG_IMMUTABLE;
            }

            alarmPendingIntent = PendingIntent.getBroadcast(this, 0, alarmIntent, flags);

            // Use different alarm types based on Android version
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // For Android 6.0+ use setExactAndAllowWhileIdle for better reliability
                alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                        alarmPendingIntent);
            } else {
                // For older versions use setRepeating
                alarmManager.setRepeating(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + CHECK_INTERVAL,
                        CHECK_INTERVAL,
                        alarmPendingIntent);
            }

            Log.d(TAG, "Monitoring alarm set successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up monitoring alarm", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void handleServiceError(Exception e) {
        try {
            Log.e(TAG, "Handling service error", e);

            // Try to recover from error
            if (isServiceRunning.get()) {
                cleanup();
            }

            // Stop the service if error is critical
            if (isCriticalError(e)) {
                Log.e(TAG, "Critical error detected, stopping service");
                stopSelf();
            }

        } catch (Exception handlingError) {
            Log.e(TAG, "Error while handling service error", handlingError);
        }
    }

    private boolean isCriticalError(Exception e) {
        // Define what constitutes a critical error
        return e instanceof SecurityException ||
                e instanceof RuntimeException;
    }

    private boolean isCriticalError(Throwable t) {
        // Define what constitutes a critical error for any Throwable
        return t instanceof SecurityException ||
                t instanceof OutOfMemoryError ||
                t instanceof NoClassDefFoundError ||
                t instanceof RuntimeException;
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        Log.d(TAG, "Task removed, but service should continue running");

        try {
            // Restart the main activity if needed
            Intent restartActivityIntent = new Intent(getApplicationContext(), MainActivity.class);
            restartActivityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(restartActivityIntent);

        } catch (Exception e) {
            Log.e(TAG, "Error restarting activity after task removal", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Log.w(TAG, "Low memory warning in service");

        try {
            // Perform memory cleanup
            System.gc();

        } catch (Exception e) {
            Log.e(TAG, "Error handling low memory", e);
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Log.w(TAG, "Memory trim requested in service, level: " + level);

        try {
            if (level >= TRIM_MEMORY_RUNNING_CRITICAL) {
                // Critical memory situation
                System.gc();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error handling memory trim", e);
        }
    }
}
package com.arapeak.alrbea.Model;


import static com.arapeak.alrbea.hawk.HawkConstants.AR_LANGUAGE;

import com.arapeak.alrbea.hawk.HawkSettings;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class MonthAlrabeeaTimes {
    @Expose
    @SerializedName("ar")
    private String ar;
    @Expose
    @SerializedName("en")
    private String en;
    @Expose
    @SerializedName("number")
    private int number;

    public String getAr() {
        return ar;
    }

    public void setAr(String ar) {
        this.ar = ar;
    }

    public String getEn() {
        return en;
    }

    public void setEn(String en) {
        this.en = en;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getDefault() {
        if (HawkSettings.getLocaleLanguage().equalsIgnoreCase(AR_LANGUAGE))
            return getAr();
        else
            return getEn();
    }
}

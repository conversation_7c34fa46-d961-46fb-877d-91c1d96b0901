package com.arapeak.alrbea.BroadcastReceiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.UI.Activity.SplashScreen;

public class BootUpReceiver extends BroadcastReceiver {
    private static final String TAG = "BootUpReceiver";

    @Override
    public void onReceive(final Context context, Intent intent) {
        if (intent != null && intent.getAction() != null &&
            (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED) ||
             intent.getAction().equals("android.intent.action.QUICKBOOT_POWERON") ||
             intent.getAction().equals("android.intent.action.MY_PACKAGE_REPLACED"))) {

            Log.d(TAG, "Boot completed received, starting service and app");

            // Start the app monitor service
            Intent serviceIntent = new Intent(context, AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }

            // Also launch the main activity
            Intent launchIntent = new Intent(context, MainActivity.class);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(launchIntent);
        }
    }
}
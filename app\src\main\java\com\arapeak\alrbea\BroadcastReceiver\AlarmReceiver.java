package com.arapeak.alrbea.BroadcastReceiver;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.List;

public class AlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "AlarmReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String alarmType = intent.getStringExtra("alarm_type");
        if (alarmType == null)
            alarmType = "main_check"; // Default to main check

        Log.i(TAG, "24/7 Monitor alarm received - Type: " + alarmType + " - performing app health check");

        try {
            boolean isServiceRunning = isAppMonitorServiceRunning(context);
            boolean isMainActivityRunning = isMainActivityRunning(context);
            boolean isAppProcessRunning = isAppProcessRunning(context);
            boolean isAppInForeground = isAppInForeground(context);

            Log.d(TAG, "App status check [" + alarmType + "] - Service: " + isServiceRunning +
                    ", MainActivity: " + isMainActivityRunning +
                    ", Process: " + isAppProcessRunning +
                    ", Foreground: " + isAppInForeground);

            // Update service notification if service is running
            if (isServiceRunning) {
                updateServiceNotification(context, "Health check completed (" + alarmType + ")");
            }

            // Aggressive monitoring logic based on alarm type
            if ("quick_check".equals(alarmType)) {
                handleQuickCheck(context, isServiceRunning, isMainActivityRunning, isAppProcessRunning,
                        isAppInForeground);
            } else {
                handleMainCheck(context, isServiceRunning, isMainActivityRunning, isAppProcessRunning,
                        isAppInForeground);
            }

            // Schedule next alarm for newer Android versions
            scheduleNextAlarm(context, alarmType);

        } catch (Exception e) {
            Log.e(TAG, "Error in 24/7 monitor alarm (" + alarmType + ")", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Try to restart on error
            try {
                Log.w(TAG, "Emergency restart due to monitoring error");
                restartAppAndService(context);
            } catch (Exception restartError) {
                Log.e(TAG, "Failed to restart on error", restartError);
                CrashlyticsUtils.INSTANCE.logException(restartError);
            }
        }
    }

    /**
     * Handle quick check (30-second smart monitoring) - Only restart when truly
     * needed
     */
    private void handleQuickCheck(Context context, boolean isServiceRunning, boolean isMainActivityRunning,
            boolean isAppProcessRunning, boolean isAppInForeground) {
        try {
            // SMART MODE: Only restart when app is truly dead or in background
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "QUICK CHECK: App or service not running, performing immediate restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning || !isAppInForeground) {
                // Check if user is actively using another app (don't interrupt user)
                if (isUserActivelyUsingAnotherApp(context)) {
                    Log.d(TAG,
                            "QUICK CHECK: User is actively using another app, will bring app to foreground when appropriate");
                    // Schedule a gentle bring-to-foreground after user activity settles
                    scheduleGentleForegroundBring(context);
                } else {
                    Log.w(TAG, "QUICK CHECK: App not in foreground and no user activity, bringing to front");
                    bringAppToForeground(context);
                }
            } else {
                // App is in foreground and user is using it - DO NOT RESTART
                if (isUserActivelyUsingApp(context)) {
                    Log.v(TAG, "QUICK CHECK: User is actively using the app, monitoring only (no restart)");
                    // Just monitor, don't restart when user is actively using the app
                    performLightHealthCheck(context);
                } else {
                    // App is in foreground but user might not be actively using it
                    Log.d(TAG, "QUICK CHECK: App in foreground but user not active, performing health check");
                    performSmartHealthCheck(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in quick check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // Only restart on error if app is not being actively used
            if (!isUserActivelyUsingApp(context)) {
                restartAppAndService(context);
            }
        }
    }

    /**
     * ALWAYS LIVE verification - ensures app is truly functional even when in
     * foreground
     */
    private void performAlwaysLiveCheck(Context context) {
        try {
            Log.v(TAG, "ALWAYS LIVE: Performing deep health verification");

            // Check 1: Verify app is truly responsive (not frozen)
            boolean isAppResponsive = isAppResponsive(context);

            // Check 2: Verify MainActivity is actually the top activity
            boolean isMainActivityTop = isMainActivityTopActivity(context);

            // Check 3: Verify app has been in foreground for reasonable time
            boolean hasBeenForegroundLongEnough = hasAppBeenForegroundLongEnough(context);

            Log.d(TAG, "ALWAYS LIVE checks - Responsive: " + isAppResponsive +
                    ", TopActivity: " + isMainActivityTop +
                    ", ForegroundTime: " + hasBeenForegroundLongEnough);

            // ALWAYS LIVE: If any check fails, restart to ensure perfect operation
            if (!isAppResponsive || !isMainActivityTop || !hasBeenForegroundLongEnough) {
                Log.w(TAG, "ALWAYS LIVE: App health verification failed, restarting for optimal performance");
                restartAppAndService(context);
            } else {
                // Even when all checks pass, occasionally restart for freshness
                if (shouldPerformFreshnessRestart(context)) {
                    Log.i(TAG, "ALWAYS LIVE: Performing freshness restart to maintain optimal performance");
                    restartAppAndService(context);
                } else {
                    Log.v(TAG, "ALWAYS LIVE: App is perfectly healthy and responsive");
                    // Still bring to front to ensure maximum visibility
                    bringAppToForeground(context);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in ALWAYS LIVE check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // On any error, restart to ensure ALWAYS LIVE
            restartAppAndService(context);
        }
    }

    /**
     * Check if app is truly responsive (not frozen or hanging)
     */
    private boolean isAppResponsive(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check if app process is in a good state
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                if (packageName.equals(processInfo.processName)) {
                    // Check if process importance indicates healthy state
                    boolean isHealthy = processInfo.importance <= ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE;
                    Log.v(TAG, "App process importance: " + processInfo.importance + ", healthy: " + isHealthy);
                    return isHealthy;
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking app responsiveness", e);
            return false;
        }
    }

    /**
     * Verify MainActivity is actually the top activity (not just in foreground)
     */
    private boolean isMainActivityTopActivity(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(1);
            if (runningTasks != null && !runningTasks.isEmpty()) {
                ActivityManager.RunningTaskInfo topTask = runningTasks.get(0);
                if (topTask.topActivity != null) {
                    String topActivityName = topTask.topActivity.getClassName();
                    boolean isMainActivityTop = topActivityName.contains("MainActivity");
                    Log.v(TAG, "Top activity: " + topActivityName + ", isMainActivity: " + isMainActivityTop);
                    return isMainActivityTop;
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking top activity", e);
            return false;
        }
    }

    /**
     * Check if app has been in foreground for a reasonable time (not just switched)
     */
    private boolean hasAppBeenForegroundLongEnough(Context context) {
        try {
            // For ALWAYS LIVE mode, we want the app to be stable in foreground
            // This prevents constant restarts when user is actively using the app

            // Get last foreground time from preferences
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastForegroundTime = prefs.getLong("last_foreground_time", 0);
            long currentTime = System.currentTimeMillis();

            // Update last foreground time
            prefs.edit().putLong("last_foreground_time", currentTime).apply();

            // If this is first check or app has been foreground for at least 10 seconds,
            // it's stable
            long foregroundDuration = currentTime - lastForegroundTime;
            boolean isStable = lastForegroundTime == 0 || foregroundDuration > 10000; // 10 seconds

            Log.v(TAG, "Foreground duration: " + foregroundDuration + "ms, stable: " + isStable);
            return isStable;

        } catch (Exception e) {
            Log.e(TAG, "Error checking foreground duration", e);
            return true; // Default to stable to avoid unnecessary restarts
        }
    }

    /**
     * Check if user is actively using another app (don't interrupt user)
     */
    private boolean isUserActivelyUsingAnotherApp(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check what app is currently in foreground
            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(1);
            if (runningTasks != null && !runningTasks.isEmpty()) {
                ActivityManager.RunningTaskInfo topTask = runningTasks.get(0);
                if (topTask.topActivity != null) {
                    String topPackage = topTask.topActivity.getPackageName();

                    // If another app is in foreground, user is actively using it
                    if (!packageName.equals(topPackage)) {
                        Log.v(TAG, "User is actively using: " + topPackage);
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking user activity", e);
            return false;
        }
    }

    /**
     * Check if user is actively using our app (don't restart when user is using it)
     */
    private boolean isUserActivelyUsingApp(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check if our app is in foreground and recently active
            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(1);
            if (runningTasks != null && !runningTasks.isEmpty()) {
                ActivityManager.RunningTaskInfo topTask = runningTasks.get(0);
                if (topTask.topActivity != null && packageName.equals(topTask.topActivity.getPackageName())) {

                    // Check if user has been actively using the app recently
                    android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                            Context.MODE_PRIVATE);
                    long lastUserActivity = prefs.getLong("last_user_activity", 0);
                    long currentTime = System.currentTimeMillis();

                    // Update user activity timestamp
                    prefs.edit().putLong("last_user_activity", currentTime).apply();

                    // Consider user active if app has been in foreground for less than 2 minutes
                    long timeSinceActivity = currentTime - lastUserActivity;
                    boolean isActive = timeSinceActivity < (2 * 60 * 1000); // 2 minutes

                    Log.v(TAG, "User activity check - time since last: " + (timeSinceActivity / 1000) + "s, active: "
                            + isActive);
                    return isActive;
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking user app activity", e);
            return false;
        }
    }

    /**
     * Schedule gentle bring-to-foreground when user activity settles
     */
    private void scheduleGentleForegroundBring(Context context) {
        try {
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastScheduled = prefs.getLong("last_gentle_schedule", 0);
            long currentTime = System.currentTimeMillis();

            // Only schedule if we haven't scheduled recently (avoid spam)
            if (currentTime - lastScheduled > (60 * 1000)) { // 1 minute
                prefs.edit().putLong("last_gentle_schedule", currentTime).apply();

                // Schedule a gentle check in 30 seconds
                android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
                handler.postDelayed(() -> {
                    try {
                        // Only bring to foreground if user is no longer actively using another app
                        if (!isUserActivelyUsingAnotherApp(context)) {
                            Log.d(TAG, "Gentle foreground bring - user activity settled");
                            bringAppToForeground(context);
                        } else {
                            Log.v(TAG, "Gentle foreground bring - user still active, will try later");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error in gentle foreground bring", e);
                    }
                }, 30000); // 30 seconds

                Log.v(TAG, "Scheduled gentle foreground bring in 30 seconds");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling gentle foreground bring", e);
        }
    }

    /**
     * Light health check - minimal monitoring when user is actively using app
     */
    private void performLightHealthCheck(Context context) {
        try {
            Log.v(TAG, "Performing light health check - user is actively using app");

            // Only check critical issues, don't restart for minor problems
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return;

            String packageName = context.getPackageName();

            // Check if app process is in critical state (only restart if critical)
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                if (packageName.equals(processInfo.processName)) {
                    // Only restart if process is in critical state
                    if (processInfo.importance > ActivityManager.RunningAppProcessInfo.IMPORTANCE_CACHED) {
                        Log.w(TAG, "Light health check: App process in critical state, restart needed");
                        restartAppAndService(context);
                    } else {
                        Log.v(TAG, "Light health check: App process healthy, no action needed");
                    }
                    return;
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in light health check", e);
        }
    }

    /**
     * Smart health check - moderate monitoring when app is in foreground but user
     * not active
     */
    private void performSmartHealthCheck(Context context) {
        try {
            Log.v(TAG, "Performing smart health check - app in foreground but user not active");

            // Check if app is truly responsive
            boolean isAppResponsive = isAppResponsive(context);

            // Check if MainActivity is the top activity
            boolean isMainActivityTop = isMainActivityTopActivity(context);

            Log.d(TAG, "Smart health check - Responsive: " + isAppResponsive + ", TopActivity: " + isMainActivityTop);

            // Only restart if there are real issues
            if (!isAppResponsive) {
                Log.w(TAG, "Smart health check: App not responsive, restarting");
                restartAppAndService(context);
            } else if (!isMainActivityTop) {
                Log.w(TAG, "Smart health check: MainActivity not top, bringing to foreground");
                bringAppToForeground(context);
            } else {
                // Check for very long-term freshness restart (only when user not active)
                if (shouldPerformLongTermFreshnessRestart(context)) {
                    Log.i(TAG, "Smart health check: Performing long-term freshness restart");
                    restartAppAndService(context);
                } else {
                    Log.v(TAG, "Smart health check: App is healthy");
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in smart health check", e);
        }
    }

    /**
     * Long-term freshness restart (only when user not actively using app)
     */
    private boolean shouldPerformLongTermFreshnessRestart(Context context) {
        try {
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastFreshnessRestart = prefs.getLong("last_freshness_restart", 0);
            long currentTime = System.currentTimeMillis();

            // Perform freshness restart every 6 hours (much less aggressive)
            long timeSinceLastRestart = currentTime - lastFreshnessRestart;
            boolean shouldRestart = timeSinceLastRestart > (6 * 60 * 60 * 1000); // 6 hours

            if (shouldRestart) {
                prefs.edit().putLong("last_freshness_restart", currentTime).apply();
                Log.d(TAG, "Long-term freshness restart needed - last restart was " + (timeSinceLastRestart / 60000)
                        + " minutes ago");
            }

            return shouldRestart;

        } catch (Exception e) {
            Log.e(TAG, "Error checking long-term freshness restart", e);
            return false;
        }
    }

    /**
     * Handle main check (10-minute smart comprehensive monitoring)
     */
    private void handleMainCheck(Context context, boolean isServiceRunning, boolean isMainActivityRunning,
            boolean isAppProcessRunning, boolean isAppInForeground) {
        try {
            // Main check is comprehensive but respects user activity
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "MAIN CHECK: App or service not running, performing restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning) {
                // Check if user is using another app before bringing to foreground
                if (isUserActivelyUsingAnotherApp(context)) {
                    Log.d(TAG,
                            "MAIN CHECK: MainActivity not running but user using another app, scheduling gentle bring");
                    scheduleGentleForegroundBring(context);
                } else {
                    Log.d(TAG, "MAIN CHECK: MainActivity not running, bringing app to foreground");
                    bringAppToForeground(context);
                }
            } else if (!isAppInForeground) {
                // Check if user is using another app before bringing to foreground
                if (isUserActivelyUsingAnotherApp(context)) {
                    Log.d(TAG, "MAIN CHECK: App in background but user using another app, scheduling gentle bring");
                    scheduleGentleForegroundBring(context);
                } else {
                    Log.d(TAG, "MAIN CHECK: App in background, bringing to foreground");
                    bringAppToForeground(context);
                }
            } else {
                // App appears healthy - check user activity before performing health checks
                if (isUserActivelyUsingApp(context)) {
                    Log.v(TAG, "MAIN CHECK: User actively using app, light monitoring only");
                    performLightHealthCheck(context);
                } else {
                    Log.d(TAG, "MAIN CHECK: App healthy but user not active, performing smart health check");
                    performMainCheckSmartHealth(context);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in main check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // Only restart on error if user is not actively using the app
            if (!isUserActivelyUsingApp(context)) {
                restartAppAndService(context);
            }
        }
    }

    /**
     * Smart health check for main check (respects user activity)
     */
    private void performMainCheckSmartHealth(Context context) {
        try {
            Log.v(TAG, "MAIN CHECK SMART HEALTH: Performing comprehensive health verification");

            // Check 1: Verify app is truly responsive
            boolean isAppResponsive = isAppResponsive(context);

            // Check 2: Verify MainActivity is the top activity
            boolean isMainActivityTop = isMainActivityTopActivity(context);

            // Check 3: Check if app needs a very long-term health restart (only when user
            // not active)
            boolean needsLongTermRestart = shouldPerformVeryLongTermRestart(context);

            Log.d(TAG, "MAIN CHECK SMART HEALTH - Responsive: " + isAppResponsive +
                    ", TopActivity: " + isMainActivityTop +
                    ", NeedsLongTermRestart: " + needsLongTermRestart);

            // Only restart for serious issues or very long-term maintenance
            if (!isAppResponsive) {
                Log.w(TAG, "MAIN CHECK SMART HEALTH: App not responsive, restarting");
                restartAppAndService(context);
            } else if (!isMainActivityTop) {
                Log.w(TAG, "MAIN CHECK SMART HEALTH: MainActivity not top, bringing to foreground");
                bringAppToForeground(context);
            } else if (needsLongTermRestart) {
                Log.i(TAG, "MAIN CHECK SMART HEALTH: Performing very long-term maintenance restart");
                restartAppAndService(context);
            } else {
                Log.d(TAG, "MAIN CHECK SMART HEALTH: App is healthy - 24/7 monitoring active");
                // Just ensure app stays visible, no unnecessary restarts
                bringAppToForeground(context);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in main check smart health", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // Only restart on error if it's a serious issue
            if (!isAppResponsive(context)) {
                restartAppAndService(context);
            }
        }
    }

    /**
     * Very long-term restart (only for maintenance, very infrequent)
     */
    private boolean shouldPerformVeryLongTermRestart(Context context) {
        try {
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastVeryLongTermRestart = prefs.getLong("last_very_long_term_restart", 0);
            long currentTime = System.currentTimeMillis();

            // Perform very long-term restart every 12 hours (very conservative)
            long timeSinceLastRestart = currentTime - lastVeryLongTermRestart;
            boolean shouldRestart = timeSinceLastRestart > (12 * 60 * 60 * 1000); // 12 hours

            if (shouldRestart) {
                prefs.edit().putLong("last_very_long_term_restart", currentTime).apply();
                Log.d(TAG, "Very long-term restart needed - last restart was " + (timeSinceLastRestart / 60000)
                        + " minutes ago");
            }

            return shouldRestart;

        } catch (Exception e) {
            Log.e(TAG, "Error checking very long-term restart", e);
            return false;
        }
    }

    /**
     * Determine if we should perform a health restart (every 30 minutes for main
     * check)
     */
    private boolean shouldPerformHealthRestart(Context context) {
        try {
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastHealthRestart = prefs.getLong("last_health_restart", 0);
            long currentTime = System.currentTimeMillis();

            // Perform health restart every 30 minutes for main check (less aggressive than
            // freshness restart)
            long timeSinceLastRestart = currentTime - lastHealthRestart;
            boolean shouldRestart = timeSinceLastRestart > (30 * 60 * 1000); // 30 minutes

            if (shouldRestart) {
                prefs.edit().putLong("last_health_restart", currentTime).apply();
                Log.d(TAG,
                        "Health restart needed - last restart was " + (timeSinceLastRestart / 60000) + " minutes ago");
            }

            return shouldRestart;

        } catch (Exception e) {
            Log.e(TAG, "Error checking health restart", e);
            return false;
        }
    }

    /**
     * Check if AppMonitorService is running
     */
    private boolean isAppMonitorServiceRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                        service.service.getPackageName().equals(packageName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking service status", e);
            return false;
        }
    }

    /**
     * Check if MainActivity is running
     */
    private boolean isMainActivityRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(10);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                if (taskInfo.topActivity != null &&
                        packageName.equals(taskInfo.topActivity.getPackageName()) &&
                        taskInfo.topActivity.getClassName().contains("MainActivity")) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking MainActivity status", e);
            return false;
        }
    }

    /**
     * Restart both app and service
     */
    private void restartAppAndService(Context context) {
        try {
            Log.i(TAG, "Restarting app and service for 24/7 operation");

            // Start the service first
            Intent serviceIntent = new Intent(context, AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }

            // Start the main activity
            Intent launchIntent = new Intent(context, MainActivity.class);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_CLEAR_TOP |
                    Intent.FLAG_ACTIVITY_SINGLE_TOP);
            launchIntent.putExtra("restarted_by_monitor", true);
            context.startActivity(launchIntent);

            Log.i(TAG, "App and service restart initiated");

        } catch (Exception e) {
            Log.e(TAG, "Error restarting app and service", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Bring app to foreground with enhanced methods
     */
    private void bringAppToForeground(Context context) {
        try {
            Log.d(TAG, "Attempting to bring app to foreground");

            // Method 1: Use launch intent
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(launchIntent);
                Log.d(TAG, "Brought app to foreground using launch intent");
            }

            // Method 2: Direct MainActivity launch as backup
            try {
                Intent mainActivityIntent = new Intent(context, MainActivity.class);
                mainActivityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(mainActivityIntent);
                Log.d(TAG, "Brought app to foreground using MainActivity intent");
            } catch (Exception e) {
                Log.w(TAG, "Could not start MainActivity directly", e);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error bringing app to foreground", e);
        }
    }

    /**
     * Check if app is running in foreground (aggressive check)
     */
    private boolean isAppInForeground(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check running tasks first (most reliable)
            try {
                List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(3);
                if (runningTasks != null && !runningTasks.isEmpty()) {
                    for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                        if (taskInfo.topActivity != null &&
                                packageName.equals(taskInfo.topActivity.getPackageName())) {
                            Log.v(TAG, "App found in foreground tasks");
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "Could not check running tasks", e);
            }

            // Check app process importance as backup
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            if (runningProcesses != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    if (packageName.equals(processInfo.processName)) {
                        boolean isForeground = processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
                                ||
                                processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE;
                        Log.v(TAG, "App process importance: " + processInfo.importance + ", isForeground: "
                                + isForeground);
                        return isForeground;
                    }
                }
            }

            Log.v(TAG, "App not found in foreground");
            return false;

        } catch (Exception e) {
            Log.e(TAG, "Error checking foreground status", e);
            return false;
        }
    }

    /**
     * Update service notification (if possible)
     */
    private void updateServiceNotification(Context context, String status) {
        try {
            // Send broadcast to service to update notification
            Intent updateIntent = new Intent("com.arapeak.alrbea.UPDATE_NOTIFICATION");
            updateIntent.putExtra("status", status);
            context.sendBroadcast(updateIntent);
        } catch (Exception e) {
            Log.e(TAG, "Error updating service notification", e);
        }
    }

    /**
     * Schedule next alarm for newer Android versions that don't support repeating
     * alarms
     */
    private void scheduleNextAlarm(Context context, String alarmType) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                android.app.AlarmManager alarmManager = (android.app.AlarmManager) context
                        .getSystemService(Context.ALARM_SERVICE);
                if (alarmManager == null)
                    return;

                // Schedule based on alarm type
                if ("quick_check".equals(alarmType)) {
                    scheduleQuickCheckAlarm(context, alarmManager);
                } else {
                    scheduleMainCheckAlarm(context, alarmManager);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling next alarm", e);
        }
    }

    /**
     * Schedule next main check alarm (10 minutes)
     */
    private void scheduleMainCheckAlarm(Context context, android.app.AlarmManager alarmManager) {
        try {
            Intent alarmIntent = new Intent(context, AlarmReceiver.class);
            alarmIntent.putExtra("alarm_type", "main_check");

            int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
            }

            android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 1001,
                    alarmIntent, flags);

            // Schedule next main check in 10 minutes
            long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (10 * 60 * 1000);
            alarmManager.setExactAndAllowWhileIdle(
                    android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    nextAlarmTime,
                    pendingIntent);

            Log.v(TAG, "Next main check alarm scheduled in 10 minutes");
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling main check alarm", e);
        }
    }

    /**
     * Schedule next quick check alarm (30 seconds)
     */
    private void scheduleQuickCheckAlarm(Context context, android.app.AlarmManager alarmManager) {
        try {
            Intent quickAlarmIntent = new Intent(context, AlarmReceiver.class);
            quickAlarmIntent.putExtra("alarm_type", "quick_check");

            int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
            }

            android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 1002,
                    quickAlarmIntent, flags);

            // Schedule next quick check in 30 seconds
            long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (30 * 1000);
            alarmManager.setExactAndAllowWhileIdle(
                    android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    nextAlarmTime,
                    pendingIntent);

            Log.v(TAG, "Next quick check alarm scheduled in 30 seconds");
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling quick check alarm", e);
        }
    }

    private boolean isAppProcessRunning(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();

        // Check if our service is running
        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                    service.service.getPackageName().equals(packageName)) {
                return true;
            }
        }

        // Check if our app process is running
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();
        if (processes != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : processes) {
                if (processInfo.processName.equals(packageName)) {
                    return true;
                }
            }
        }

        return false;
    }
}

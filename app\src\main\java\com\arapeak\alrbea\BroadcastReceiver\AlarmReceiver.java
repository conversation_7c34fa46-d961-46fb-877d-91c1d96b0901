package com.arapeak.alrbea.BroadcastReceiver;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.List;

public class AlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "AlarmReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "24/7 Monitor alarm received - performing app health check");

        try {
            boolean isServiceRunning = isAppMonitorServiceRunning(context);
            boolean isMainActivityRunning = isMainActivityRunning(context);
            boolean isAppProcessRunning = isAppProcessRunning(context);

            Log.d(TAG, "App status check - Service: " + isServiceRunning +
                    ", MainActivity: " + isMainActivityRunning +
                    ", Process: " + isAppProcessRunning);

            // Update service notification if service is running
            if (isServiceRunning) {
                updateServiceNotification(context, "Health check completed");
            }

            // Restart if needed
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "App or service not running, performing restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning) {
                Log.d(TAG, "MainActivity not running, bringing app to foreground");
                bringAppToForeground(context);
            } else {
                Log.d(TAG, "App is running normally - 24/7 monitoring active");
            }

            // Schedule next alarm for newer Android versions
            scheduleNextAlarm(context);

        } catch (Exception e) {
            Log.e(TAG, "Error in 24/7 monitor alarm", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Try to restart on error
            try {
                restartAppAndService(context);
            } catch (Exception restartError) {
                Log.e(TAG, "Failed to restart on error", restartError);
                CrashlyticsUtils.INSTANCE.logException(restartError);
            }
        }
    }

    /**
     * Check if AppMonitorService is running
     */
    private boolean isAppMonitorServiceRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                        service.service.getPackageName().equals(packageName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking service status", e);
            return false;
        }
    }

    /**
     * Check if MainActivity is running
     */
    private boolean isMainActivityRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(10);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                if (taskInfo.topActivity != null &&
                        packageName.equals(taskInfo.topActivity.getPackageName()) &&
                        taskInfo.topActivity.getClassName().contains("MainActivity")) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking MainActivity status", e);
            return false;
        }
    }

    /**
     * Restart both app and service
     */
    private void restartAppAndService(Context context) {
        try {
            Log.i(TAG, "Restarting app and service for 24/7 operation");

            // Start the service first
            Intent serviceIntent = new Intent(context, AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }

            // Start the main activity
            Intent launchIntent = new Intent(context, MainActivity.class);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_CLEAR_TOP |
                    Intent.FLAG_ACTIVITY_SINGLE_TOP);
            launchIntent.putExtra("restarted_by_monitor", true);
            context.startActivity(launchIntent);

            Log.i(TAG, "App and service restart initiated");

        } catch (Exception e) {
            Log.e(TAG, "Error restarting app and service", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Bring app to foreground
     */
    private void bringAppToForeground(Context context) {
        try {
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                context.startActivity(launchIntent);
                Log.d(TAG, "Brought app to foreground");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error bringing app to foreground", e);
        }
    }

    /**
     * Update service notification (if possible)
     */
    private void updateServiceNotification(Context context, String status) {
        try {
            // Send broadcast to service to update notification
            Intent updateIntent = new Intent("com.arapeak.alrbea.UPDATE_NOTIFICATION");
            updateIntent.putExtra("status", status);
            context.sendBroadcast(updateIntent);
        } catch (Exception e) {
            Log.e(TAG, "Error updating service notification", e);
        }
    }

    /**
     * Schedule next alarm for newer Android versions that don't support repeating
     * alarms
     */
    private void scheduleNextAlarm(Context context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // For newer versions, we need to reschedule the alarm
                android.app.AlarmManager alarmManager = (android.app.AlarmManager) context
                        .getSystemService(Context.ALARM_SERVICE);
                Intent alarmIntent = new Intent(context, AlarmReceiver.class);

                int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
                }

                android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 0,
                        alarmIntent, flags);

                // Schedule next check in 10 minutes
                long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (10 * 60 * 1000);
                alarmManager.setExactAndAllowWhileIdle(
                        android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        nextAlarmTime,
                        pendingIntent);

                Log.v(TAG, "Next 24/7 monitor alarm scheduled");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling next alarm", e);
        }
    }

    private boolean isAppProcessRunning(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();

        // Check if our service is running
        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                    service.service.getPackageName().equals(packageName)) {
                return true;
            }
        }

        // Check if our app process is running
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();
        if (processes != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : processes) {
                if (processInfo.processName.equals(packageName)) {
                    return true;
                }
            }
        }

        return false;
    }
}

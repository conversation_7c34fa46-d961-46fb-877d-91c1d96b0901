package com.arapeak.alrbea.BroadcastReceiver;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.List;

public class AlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "AlarmReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String alarmType = intent.getStringExtra("alarm_type");
        if (alarmType == null)
            alarmType = "main_check"; // Default to main check

        Log.i(TAG, "24/7 Monitor alarm received - Type: " + alarmType + " - performing app health check");

        try {
            boolean isServiceRunning = isAppMonitorServiceRunning(context);
            boolean isMainActivityRunning = isMainActivityRunning(context);
            boolean isAppProcessRunning = isAppProcessRunning(context);
            boolean isAppInForeground = isAppInForeground(context);

            Log.d(TAG, "App status check [" + alarmType + "] - Service: " + isServiceRunning +
                    ", MainActivity: " + isMainActivityRunning +
                    ", Process: " + isAppProcessRunning +
                    ", Foreground: " + isAppInForeground);

            // Update service notification if service is running
            if (isServiceRunning) {
                updateServiceNotification(context, "Health check completed (" + alarmType + ")");
            }

            // Aggressive monitoring logic based on alarm type
            if ("quick_check".equals(alarmType)) {
                handleQuickCheck(context, isServiceRunning, isMainActivityRunning, isAppProcessRunning,
                        isAppInForeground);
            } else {
                handleMainCheck(context, isServiceRunning, isMainActivityRunning, isAppProcessRunning,
                        isAppInForeground);
            }

            // Schedule next alarm for newer Android versions
            scheduleNextAlarm(context, alarmType);

        } catch (Exception e) {
            Log.e(TAG, "Error in 24/7 monitor alarm (" + alarmType + ")", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Try to restart on error
            try {
                Log.w(TAG, "Emergency restart due to monitoring error");
                restartAppAndService(context);
            } catch (Exception restartError) {
                Log.e(TAG, "Failed to restart on error", restartError);
                CrashlyticsUtils.INSTANCE.logException(restartError);
            }
        }
    }

    /**
     * Handle quick check (30-second aggressive monitoring) - ALWAYS LIVE MODE
     */
    private void handleQuickCheck(Context context, boolean isServiceRunning, boolean isMainActivityRunning,
            boolean isAppProcessRunning, boolean isAppInForeground) {
        try {
            // ALWAYS LIVE MODE: Even if app appears to be running, verify it's truly
            // functional
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "QUICK CHECK: App or service not running, performing immediate restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning || !isAppInForeground) {
                Log.w(TAG, "QUICK CHECK: App not in foreground, bringing to front immediately");
                bringAppToForeground(context);

                // If still not in foreground after 5 seconds, restart
                android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
                handler.postDelayed(() -> {
                    if (!isAppInForeground(context)) {
                        Log.w(TAG, "QUICK CHECK: App still not in foreground, forcing restart");
                        restartAppAndService(context);
                    }
                }, 5000);
            } else {
                // ALWAYS LIVE: Even when app appears fine, perform additional health checks
                Log.d(TAG, "QUICK CHECK: App appears to be in foreground, performing ALWAYS LIVE verification");
                performAlwaysLiveCheck(context);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in quick check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // On any error, restart to ensure ALWAYS LIVE
            restartAppAndService(context);
        }
    }

    /**
     * ALWAYS LIVE verification - ensures app is truly functional even when in
     * foreground
     */
    private void performAlwaysLiveCheck(Context context) {
        try {
            Log.v(TAG, "ALWAYS LIVE: Performing deep health verification");

            // Check 1: Verify app is truly responsive (not frozen)
            boolean isAppResponsive = isAppResponsive(context);

            // Check 2: Verify MainActivity is actually the top activity
            boolean isMainActivityTop = isMainActivityTopActivity(context);

            // Check 3: Verify app has been in foreground for reasonable time
            boolean hasBeenForegroundLongEnough = hasAppBeenForegroundLongEnough(context);

            Log.d(TAG, "ALWAYS LIVE checks - Responsive: " + isAppResponsive +
                    ", TopActivity: " + isMainActivityTop +
                    ", ForegroundTime: " + hasBeenForegroundLongEnough);

            // ALWAYS LIVE: If any check fails, restart to ensure perfect operation
            if (!isAppResponsive || !isMainActivityTop || !hasBeenForegroundLongEnough) {
                Log.w(TAG, "ALWAYS LIVE: App health verification failed, restarting for optimal performance");
                restartAppAndService(context);
            } else {
                // Even when all checks pass, occasionally restart for freshness
                if (shouldPerformFreshnessRestart(context)) {
                    Log.i(TAG, "ALWAYS LIVE: Performing freshness restart to maintain optimal performance");
                    restartAppAndService(context);
                } else {
                    Log.v(TAG, "ALWAYS LIVE: App is perfectly healthy and responsive");
                    // Still bring to front to ensure maximum visibility
                    bringAppToForeground(context);
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in ALWAYS LIVE check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // On any error, restart to ensure ALWAYS LIVE
            restartAppAndService(context);
        }
    }

    /**
     * Check if app is truly responsive (not frozen or hanging)
     */
    private boolean isAppResponsive(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check if app process is in a good state
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                if (packageName.equals(processInfo.processName)) {
                    // Check if process importance indicates healthy state
                    boolean isHealthy = processInfo.importance <= ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE;
                    Log.v(TAG, "App process importance: " + processInfo.importance + ", healthy: " + isHealthy);
                    return isHealthy;
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking app responsiveness", e);
            return false;
        }
    }

    /**
     * Verify MainActivity is actually the top activity (not just in foreground)
     */
    private boolean isMainActivityTopActivity(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(1);
            if (runningTasks != null && !runningTasks.isEmpty()) {
                ActivityManager.RunningTaskInfo topTask = runningTasks.get(0);
                if (topTask.topActivity != null) {
                    String topActivityName = topTask.topActivity.getClassName();
                    boolean isMainActivityTop = topActivityName.contains("MainActivity");
                    Log.v(TAG, "Top activity: " + topActivityName + ", isMainActivity: " + isMainActivityTop);
                    return isMainActivityTop;
                }
            }

            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking top activity", e);
            return false;
        }
    }

    /**
     * Check if app has been in foreground for a reasonable time (not just switched)
     */
    private boolean hasAppBeenForegroundLongEnough(Context context) {
        try {
            // For ALWAYS LIVE mode, we want the app to be stable in foreground
            // This prevents constant restarts when user is actively using the app

            // Get last foreground time from preferences
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastForegroundTime = prefs.getLong("last_foreground_time", 0);
            long currentTime = System.currentTimeMillis();

            // Update last foreground time
            prefs.edit().putLong("last_foreground_time", currentTime).apply();

            // If this is first check or app has been foreground for at least 10 seconds,
            // it's stable
            long foregroundDuration = currentTime - lastForegroundTime;
            boolean isStable = lastForegroundTime == 0 || foregroundDuration > 10000; // 10 seconds

            Log.v(TAG, "Foreground duration: " + foregroundDuration + "ms, stable: " + isStable);
            return isStable;

        } catch (Exception e) {
            Log.e(TAG, "Error checking foreground duration", e);
            return true; // Default to stable to avoid unnecessary restarts
        }
    }

    /**
     * Determine if we should perform a freshness restart for optimal performance
     */
    private boolean shouldPerformFreshnessRestart(Context context) {
        try {
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastFreshnessRestart = prefs.getLong("last_freshness_restart", 0);
            long currentTime = System.currentTimeMillis();

            // Perform freshness restart every 2 hours for optimal performance
            long timeSinceLastRestart = currentTime - lastFreshnessRestart;
            boolean shouldRestart = timeSinceLastRestart > (2 * 60 * 60 * 1000); // 2 hours

            if (shouldRestart) {
                prefs.edit().putLong("last_freshness_restart", currentTime).apply();
                Log.d(TAG, "Freshness restart needed - last restart was " + (timeSinceLastRestart / 60000)
                        + " minutes ago");
            }

            return shouldRestart;

        } catch (Exception e) {
            Log.e(TAG, "Error checking freshness restart", e);
            return false;
        }
    }

    /**
     * Handle main check (10-minute comprehensive monitoring) - ALWAYS LIVE MODE
     */
    private void handleMainCheck(Context context, boolean isServiceRunning, boolean isMainActivityRunning,
            boolean isAppProcessRunning, boolean isAppInForeground) {
        try {
            // Main check is comprehensive and includes ALWAYS LIVE verification
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "MAIN CHECK: App or service not running, performing restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning) {
                Log.d(TAG, "MAIN CHECK: MainActivity not running, bringing app to foreground");
                bringAppToForeground(context);
            } else if (!isAppInForeground) {
                Log.d(TAG, "MAIN CHECK: App in background, bringing to foreground");
                bringAppToForeground(context);
            } else {
                // ALWAYS LIVE: Even in main check, verify app health when it appears fine
                Log.d(TAG, "MAIN CHECK: App appears healthy, performing ALWAYS LIVE verification");
                performMainCheckAlwaysLive(context);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in main check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // On any error, restart to ensure ALWAYS LIVE
            restartAppAndService(context);
        }
    }

    /**
     * ALWAYS LIVE verification for main check (less aggressive than quick check)
     */
    private void performMainCheckAlwaysLive(Context context) {
        try {
            Log.v(TAG, "MAIN CHECK ALWAYS LIVE: Performing comprehensive health verification");

            // Check 1: Verify app is truly responsive
            boolean isAppResponsive = isAppResponsive(context);

            // Check 2: Verify MainActivity is the top activity
            boolean isMainActivityTop = isMainActivityTopActivity(context);

            // Check 3: Check if app needs a health restart (less frequent than quick check)
            boolean needsHealthRestart = shouldPerformHealthRestart(context);

            Log.d(TAG, "MAIN CHECK ALWAYS LIVE - Responsive: " + isAppResponsive +
                    ", TopActivity: " + isMainActivityTop +
                    ", NeedsHealthRestart: " + needsHealthRestart);

            // ALWAYS LIVE: Restart if health checks fail or health restart is needed
            if (!isAppResponsive || !isMainActivityTop) {
                Log.w(TAG, "MAIN CHECK ALWAYS LIVE: App health verification failed, restarting");
                restartAppAndService(context);
            } else if (needsHealthRestart) {
                Log.i(TAG, "MAIN CHECK ALWAYS LIVE: Performing scheduled health restart");
                restartAppAndService(context);
            } else {
                Log.d(TAG, "MAIN CHECK ALWAYS LIVE: App is healthy - 24/7 monitoring active");
                // Ensure app stays in foreground
                bringAppToForeground(context);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in main check ALWAYS LIVE", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            // On any error, restart to ensure ALWAYS LIVE
            restartAppAndService(context);
        }
    }

    /**
     * Determine if we should perform a health restart (every 30 minutes for main
     * check)
     */
    private boolean shouldPerformHealthRestart(Context context) {
        try {
            android.content.SharedPreferences prefs = context.getSharedPreferences("AppMonitorPrefs",
                    Context.MODE_PRIVATE);
            long lastHealthRestart = prefs.getLong("last_health_restart", 0);
            long currentTime = System.currentTimeMillis();

            // Perform health restart every 30 minutes for main check (less aggressive than
            // freshness restart)
            long timeSinceLastRestart = currentTime - lastHealthRestart;
            boolean shouldRestart = timeSinceLastRestart > (30 * 60 * 1000); // 30 minutes

            if (shouldRestart) {
                prefs.edit().putLong("last_health_restart", currentTime).apply();
                Log.d(TAG,
                        "Health restart needed - last restart was " + (timeSinceLastRestart / 60000) + " minutes ago");
            }

            return shouldRestart;

        } catch (Exception e) {
            Log.e(TAG, "Error checking health restart", e);
            return false;
        }
    }

    /**
     * Check if AppMonitorService is running
     */
    private boolean isAppMonitorServiceRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                        service.service.getPackageName().equals(packageName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking service status", e);
            return false;
        }
    }

    /**
     * Check if MainActivity is running
     */
    private boolean isMainActivityRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(10);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                if (taskInfo.topActivity != null &&
                        packageName.equals(taskInfo.topActivity.getPackageName()) &&
                        taskInfo.topActivity.getClassName().contains("MainActivity")) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking MainActivity status", e);
            return false;
        }
    }

    /**
     * Restart both app and service
     */
    private void restartAppAndService(Context context) {
        try {
            Log.i(TAG, "Restarting app and service for 24/7 operation");

            // Start the service first
            Intent serviceIntent = new Intent(context, AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }

            // Start the main activity
            Intent launchIntent = new Intent(context, MainActivity.class);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_CLEAR_TOP |
                    Intent.FLAG_ACTIVITY_SINGLE_TOP);
            launchIntent.putExtra("restarted_by_monitor", true);
            context.startActivity(launchIntent);

            Log.i(TAG, "App and service restart initiated");

        } catch (Exception e) {
            Log.e(TAG, "Error restarting app and service", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Bring app to foreground with enhanced methods
     */
    private void bringAppToForeground(Context context) {
        try {
            Log.d(TAG, "Attempting to bring app to foreground");

            // Method 1: Use launch intent
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(launchIntent);
                Log.d(TAG, "Brought app to foreground using launch intent");
            }

            // Method 2: Direct MainActivity launch as backup
            try {
                Intent mainActivityIntent = new Intent(context, MainActivity.class);
                mainActivityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(mainActivityIntent);
                Log.d(TAG, "Brought app to foreground using MainActivity intent");
            } catch (Exception e) {
                Log.w(TAG, "Could not start MainActivity directly", e);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error bringing app to foreground", e);
        }
    }

    /**
     * Check if app is running in foreground (aggressive check)
     */
    private boolean isAppInForeground(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check running tasks first (most reliable)
            try {
                List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(3);
                if (runningTasks != null && !runningTasks.isEmpty()) {
                    for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                        if (taskInfo.topActivity != null &&
                                packageName.equals(taskInfo.topActivity.getPackageName())) {
                            Log.v(TAG, "App found in foreground tasks");
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "Could not check running tasks", e);
            }

            // Check app process importance as backup
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            if (runningProcesses != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    if (packageName.equals(processInfo.processName)) {
                        boolean isForeground = processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
                                ||
                                processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE;
                        Log.v(TAG, "App process importance: " + processInfo.importance + ", isForeground: "
                                + isForeground);
                        return isForeground;
                    }
                }
            }

            Log.v(TAG, "App not found in foreground");
            return false;

        } catch (Exception e) {
            Log.e(TAG, "Error checking foreground status", e);
            return false;
        }
    }

    /**
     * Update service notification (if possible)
     */
    private void updateServiceNotification(Context context, String status) {
        try {
            // Send broadcast to service to update notification
            Intent updateIntent = new Intent("com.arapeak.alrbea.UPDATE_NOTIFICATION");
            updateIntent.putExtra("status", status);
            context.sendBroadcast(updateIntent);
        } catch (Exception e) {
            Log.e(TAG, "Error updating service notification", e);
        }
    }

    /**
     * Schedule next alarm for newer Android versions that don't support repeating
     * alarms
     */
    private void scheduleNextAlarm(Context context, String alarmType) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                android.app.AlarmManager alarmManager = (android.app.AlarmManager) context
                        .getSystemService(Context.ALARM_SERVICE);
                if (alarmManager == null)
                    return;

                // Schedule based on alarm type
                if ("quick_check".equals(alarmType)) {
                    scheduleQuickCheckAlarm(context, alarmManager);
                } else {
                    scheduleMainCheckAlarm(context, alarmManager);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling next alarm", e);
        }
    }

    /**
     * Schedule next main check alarm (10 minutes)
     */
    private void scheduleMainCheckAlarm(Context context, android.app.AlarmManager alarmManager) {
        try {
            Intent alarmIntent = new Intent(context, AlarmReceiver.class);
            alarmIntent.putExtra("alarm_type", "main_check");

            int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
            }

            android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 1001,
                    alarmIntent, flags);

            // Schedule next main check in 10 minutes
            long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (10 * 60 * 1000);
            alarmManager.setExactAndAllowWhileIdle(
                    android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    nextAlarmTime,
                    pendingIntent);

            Log.v(TAG, "Next main check alarm scheduled in 10 minutes");
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling main check alarm", e);
        }
    }

    /**
     * Schedule next quick check alarm (30 seconds)
     */
    private void scheduleQuickCheckAlarm(Context context, android.app.AlarmManager alarmManager) {
        try {
            Intent quickAlarmIntent = new Intent(context, AlarmReceiver.class);
            quickAlarmIntent.putExtra("alarm_type", "quick_check");

            int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
            }

            android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 1002,
                    quickAlarmIntent, flags);

            // Schedule next quick check in 30 seconds
            long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (30 * 1000);
            alarmManager.setExactAndAllowWhileIdle(
                    android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    nextAlarmTime,
                    pendingIntent);

            Log.v(TAG, "Next quick check alarm scheduled in 30 seconds");
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling quick check alarm", e);
        }
    }

    private boolean isAppProcessRunning(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();

        // Check if our service is running
        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                    service.service.getPackageName().equals(packageName)) {
                return true;
            }
        }

        // Check if our app process is running
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();
        if (processes != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : processes) {
                if (processInfo.processName.equals(packageName)) {
                    return true;
                }
            }
        }

        return false;
    }
}

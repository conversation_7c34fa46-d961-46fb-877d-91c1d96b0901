package com.arapeak.alrbea.BroadcastReceiver;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.List;

public class AlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "AlarmReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        String alarmType = intent.getStringExtra("alarm_type");
        if (alarmType == null)
            alarmType = "main_check"; // Default to main check

        Log.i(TAG, "24/7 Monitor alarm received - Type: " + alarmType + " - performing app health check");

        try {
            boolean isServiceRunning = isAppMonitorServiceRunning(context);
            boolean isMainActivityRunning = isMainActivityRunning(context);
            boolean isAppProcessRunning = isAppProcessRunning(context);
            boolean isAppInForeground = isAppInForeground(context);

            Log.d(TAG, "App status check [" + alarmType + "] - Service: " + isServiceRunning +
                    ", MainActivity: " + isMainActivityRunning +
                    ", Process: " + isAppProcessRunning +
                    ", Foreground: " + isAppInForeground);

            // Update service notification if service is running
            if (isServiceRunning) {
                updateServiceNotification(context, "Health check completed (" + alarmType + ")");
            }

            // Aggressive monitoring logic based on alarm type
            if ("quick_check".equals(alarmType)) {
                handleQuickCheck(context, isServiceRunning, isMainActivityRunning, isAppProcessRunning,
                        isAppInForeground);
            } else {
                handleMainCheck(context, isServiceRunning, isMainActivityRunning, isAppProcessRunning,
                        isAppInForeground);
            }

            // Schedule next alarm for newer Android versions
            scheduleNextAlarm(context, alarmType);

        } catch (Exception e) {
            Log.e(TAG, "Error in 24/7 monitor alarm (" + alarmType + ")", e);
            CrashlyticsUtils.INSTANCE.logException(e);

            // Try to restart on error
            try {
                Log.w(TAG, "Emergency restart due to monitoring error");
                restartAppAndService(context);
            } catch (Exception restartError) {
                Log.e(TAG, "Failed to restart on error", restartError);
                CrashlyticsUtils.INSTANCE.logException(restartError);
            }
        }
    }

    /**
     * Handle quick check (30-second aggressive monitoring)
     */
    private void handleQuickCheck(Context context, boolean isServiceRunning, boolean isMainActivityRunning,
            boolean isAppProcessRunning, boolean isAppInForeground) {
        try {
            // Quick check is more aggressive - restart immediately if app is not visible
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "QUICK CHECK: App or service not running, performing immediate restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning || !isAppInForeground) {
                Log.w(TAG, "QUICK CHECK: App not in foreground, bringing to front immediately");
                bringAppToForeground(context);

                // If still not in foreground after 5 seconds, restart
                android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
                handler.postDelayed(() -> {
                    if (!isAppInForeground(context)) {
                        Log.w(TAG, "QUICK CHECK: App still not in foreground, forcing restart");
                        restartAppAndService(context);
                    }
                }, 5000);
            } else {
                Log.v(TAG, "QUICK CHECK: App is running normally in foreground");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in quick check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Handle main check (10-minute comprehensive monitoring)
     */
    private void handleMainCheck(Context context, boolean isServiceRunning, boolean isMainActivityRunning,
            boolean isAppProcessRunning, boolean isAppInForeground) {
        try {
            // Main check is comprehensive but less aggressive
            if (!isAppProcessRunning || !isServiceRunning) {
                Log.w(TAG, "MAIN CHECK: App or service not running, performing restart");
                restartAppAndService(context);
            } else if (!isMainActivityRunning) {
                Log.d(TAG, "MAIN CHECK: MainActivity not running, bringing app to foreground");
                bringAppToForeground(context);
            } else if (!isAppInForeground) {
                Log.d(TAG, "MAIN CHECK: App in background, bringing to foreground");
                bringAppToForeground(context);
            } else {
                Log.d(TAG, "MAIN CHECK: App is running normally - 24/7 monitoring active");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in main check", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Check if AppMonitorService is running
     */
    private boolean isAppMonitorServiceRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                        service.service.getPackageName().equals(packageName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking service status", e);
            return false;
        }
    }

    /**
     * Check if MainActivity is running
     */
    private boolean isMainActivityRunning(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(10);
            String packageName = context.getPackageName();

            for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                if (taskInfo.topActivity != null &&
                        packageName.equals(taskInfo.topActivity.getPackageName()) &&
                        taskInfo.topActivity.getClassName().contains("MainActivity")) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking MainActivity status", e);
            return false;
        }
    }

    /**
     * Restart both app and service
     */
    private void restartAppAndService(Context context) {
        try {
            Log.i(TAG, "Restarting app and service for 24/7 operation");

            // Start the service first
            Intent serviceIntent = new Intent(context, AppMonitorService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }

            // Start the main activity
            Intent launchIntent = new Intent(context, MainActivity.class);
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                    Intent.FLAG_ACTIVITY_CLEAR_TOP |
                    Intent.FLAG_ACTIVITY_SINGLE_TOP);
            launchIntent.putExtra("restarted_by_monitor", true);
            context.startActivity(launchIntent);

            Log.i(TAG, "App and service restart initiated");

        } catch (Exception e) {
            Log.e(TAG, "Error restarting app and service", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Bring app to foreground with enhanced methods
     */
    private void bringAppToForeground(Context context) {
        try {
            Log.d(TAG, "Attempting to bring app to foreground");

            // Method 1: Use launch intent
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(launchIntent);
                Log.d(TAG, "Brought app to foreground using launch intent");
            }

            // Method 2: Direct MainActivity launch as backup
            try {
                Intent mainActivityIntent = new Intent(context, MainActivity.class);
                mainActivityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                        Intent.FLAG_ACTIVITY_REORDER_TO_FRONT |
                        Intent.FLAG_ACTIVITY_SINGLE_TOP);
                context.startActivity(mainActivityIntent);
                Log.d(TAG, "Brought app to foreground using MainActivity intent");
            } catch (Exception e) {
                Log.w(TAG, "Could not start MainActivity directly", e);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error bringing app to foreground", e);
        }
    }

    /**
     * Check if app is running in foreground (aggressive check)
     */
    private boolean isAppInForeground(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager == null)
                return false;

            String packageName = context.getPackageName();

            // Check running tasks first (most reliable)
            try {
                List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(3);
                if (runningTasks != null && !runningTasks.isEmpty()) {
                    for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                        if (taskInfo.topActivity != null &&
                                packageName.equals(taskInfo.topActivity.getPackageName())) {
                            Log.v(TAG, "App found in foreground tasks");
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "Could not check running tasks", e);
            }

            // Check app process importance as backup
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
            if (runningProcesses != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    if (packageName.equals(processInfo.processName)) {
                        boolean isForeground = processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
                                ||
                                processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE;
                        Log.v(TAG, "App process importance: " + processInfo.importance + ", isForeground: "
                                + isForeground);
                        return isForeground;
                    }
                }
            }

            Log.v(TAG, "App not found in foreground");
            return false;

        } catch (Exception e) {
            Log.e(TAG, "Error checking foreground status", e);
            return false;
        }
    }

    /**
     * Update service notification (if possible)
     */
    private void updateServiceNotification(Context context, String status) {
        try {
            // Send broadcast to service to update notification
            Intent updateIntent = new Intent("com.arapeak.alrbea.UPDATE_NOTIFICATION");
            updateIntent.putExtra("status", status);
            context.sendBroadcast(updateIntent);
        } catch (Exception e) {
            Log.e(TAG, "Error updating service notification", e);
        }
    }

    /**
     * Schedule next alarm for newer Android versions that don't support repeating
     * alarms
     */
    private void scheduleNextAlarm(Context context, String alarmType) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                android.app.AlarmManager alarmManager = (android.app.AlarmManager) context
                        .getSystemService(Context.ALARM_SERVICE);
                if (alarmManager == null)
                    return;

                // Schedule based on alarm type
                if ("quick_check".equals(alarmType)) {
                    scheduleQuickCheckAlarm(context, alarmManager);
                } else {
                    scheduleMainCheckAlarm(context, alarmManager);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling next alarm", e);
        }
    }

    /**
     * Schedule next main check alarm (10 minutes)
     */
    private void scheduleMainCheckAlarm(Context context, android.app.AlarmManager alarmManager) {
        try {
            Intent alarmIntent = new Intent(context, AlarmReceiver.class);
            alarmIntent.putExtra("alarm_type", "main_check");

            int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
            }

            android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 1001,
                    alarmIntent, flags);

            // Schedule next main check in 10 minutes
            long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (10 * 60 * 1000);
            alarmManager.setExactAndAllowWhileIdle(
                    android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    nextAlarmTime,
                    pendingIntent);

            Log.v(TAG, "Next main check alarm scheduled in 10 minutes");
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling main check alarm", e);
        }
    }

    /**
     * Schedule next quick check alarm (30 seconds)
     */
    private void scheduleQuickCheckAlarm(Context context, android.app.AlarmManager alarmManager) {
        try {
            Intent quickAlarmIntent = new Intent(context, AlarmReceiver.class);
            quickAlarmIntent.putExtra("alarm_type", "quick_check");

            int flags = android.app.PendingIntent.FLAG_UPDATE_CURRENT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                flags |= android.app.PendingIntent.FLAG_IMMUTABLE;
            }

            android.app.PendingIntent pendingIntent = android.app.PendingIntent.getBroadcast(context, 1002,
                    quickAlarmIntent, flags);

            // Schedule next quick check in 30 seconds
            long nextAlarmTime = android.os.SystemClock.elapsedRealtime() + (30 * 1000);
            alarmManager.setExactAndAllowWhileIdle(
                    android.app.AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    nextAlarmTime,
                    pendingIntent);

            Log.v(TAG, "Next quick check alarm scheduled in 30 seconds");
        } catch (Exception e) {
            Log.e(TAG, "Error scheduling quick check alarm", e);
        }
    }

    private boolean isAppProcessRunning(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();

        // Check if our service is running
        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (AppMonitorService.class.getName().equals(service.service.getClassName()) &&
                    service.service.getPackageName().equals(packageName)) {
                return true;
            }
        }

        // Check if our app process is running
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();
        if (processes != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : processes) {
                if (processInfo.processName.equals(packageName)) {
                    return true;
                }
            }
        }

        return false;
    }
}

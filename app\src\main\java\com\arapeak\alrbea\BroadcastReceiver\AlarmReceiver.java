package com.arapeak.alrbea.BroadcastReceiver;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import com.arapeak.alrbea.Service.AppMonitorService;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.List;

public class AlarmReceiver extends BroadcastReceiver {
    private static final String TAG = "AlarmReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Alarm received, checking if app is running");
        
        try {
            // Check if our app is running
            if (!isAppProcessRunning(context)) {
                Log.d(TAG, "App is not running, restarting it");
                
                // Start the service
                Intent serviceIntent = new Intent(context, AppMonitorService.class);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(serviceIntent);
                } else {
                    context.startService(serviceIntent);
                }
                
                // Start the main activity
                Intent launchIntent = new Intent(context, MainActivity.class);
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(launchIntent);
            } else {
                Log.d(TAG, "App is already running");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in AlarmReceiver", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
    
    private boolean isAppProcessRunning(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        String packageName = context.getPackageName();
        
        // Check if our service is running
        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (AppMonitorService.class.getName().equals(service.service.getClassName()) && 
                service.service.getPackageName().equals(packageName)) {
                return true;
            }
        }
        
        // Check if our app process is running
        List<ActivityManager.RunningAppProcessInfo> processes = activityManager.getRunningAppProcesses();
        if (processes != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : processes) {
                if (processInfo.processName.equals(packageName)) {
                    return true;
                }
            }
        }
        
        return false;
    }
}

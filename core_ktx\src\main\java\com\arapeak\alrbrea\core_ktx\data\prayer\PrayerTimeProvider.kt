package com.arapeak.alrbrea.core_ktx.data.prayer

import android.location.Location
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.batoulapps.adhan2.Madhab
import java.util.Calendar

abstract class PrayerTimeProvider {

    abstract fun getPrayerTime(
        location: Location,
        calculationMethod: CalculationMethod,
        madhab: Madhab,
        date: Calendar
    ): DayPrayers
}
package com.arapeak.alrbea.hawk;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_CREATE_PHOTO_GALLERY_IKAMA;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_CREATE_PHOTO_GALLERY_IKAMA_JOMAA;
import static com.arapeak.alrbea.AppController.baseContext;
import static com.arapeak.alrbea.Utils.getValueWithoutNull;
import static com.arapeak.alrbea.Utils.setLocaleLanguage;
import static com.arapeak.alrbea.hawk.HawkConstants.APP_AZKAR_THEME_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.APP_FIREBASE_THEME_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.APP_UITHEME_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.AR_LANGUAGE;
import static com.arapeak.alrbea.hawk.HawkConstants.CURRENT_AZAN;
import static com.arapeak.alrbea.hawk.HawkConstants.CURRENT_IKAMA;
import static com.arapeak.alrbea.hawk.HawkConstants.CURRENT_PRAYER_METHOD;
import static com.arapeak.alrbea.hawk.HawkConstants.DEFAULT_GALLERY_SELECTED_ID;
import static com.arapeak.alrbea.hawk.HawkConstants.ENABLE_SHOW_IKAMA_DELAY;
import static com.arapeak.alrbea.hawk.HawkConstants.ENABLE_TIME_ATHKAR;
import static com.arapeak.alrbea.hawk.HawkConstants.ENABLE_TIME_GALLERY;
import static com.arapeak.alrbea.hawk.HawkConstants.IS_AM_PM_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.IS_ENABLE_CREATE_PHOTO_GALLERY;
import static com.arapeak.alrbea.hawk.HawkConstants.IS_ENABLE_CREATE_PHOTO_GALLERY_DEFAULT;
import static com.arapeak.alrbea.hawk.HawkConstants.IS_LANDSCAPE_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.PRAYER_TIMES_UPDATED;
import static com.arapeak.alrbea.hawk.HawkConstants.PRAYER_TIME_KEY;
import static com.arapeak.alrbea.hawk.HawkConstants.SELECTED_GALLERY_ID;

import android.content.Context;
import android.util.Log;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.Enum.AthkarType;
import com.arapeak.alrbea.Enum.AzkarTheme;
import com.arapeak.alrbea.Enum.News;
import com.arapeak.alrbea.Enum.PrayerMethod;
import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.Model.CalcFunction;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.PremiumUserModel;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.OmanCity;
import com.orhanobut.hawk.Hawk;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.Locale;

public class HawkSettings {

    public static String getLocaleLanguage() {
        return Hawk.get(ConstantsOfApp.LOCALE_LANGUAGE_KEY, ConstantsOfApp.DEFAULT_LANGUAGE);
    }


    public static void changeLocaleLanguage(Context mContext, String localeLanguage) {

        if (getValueWithoutNull(localeLanguage).isEmpty()) {
            return;
        }
        Hawk.put(ConstantsOfApp.LOCALE_LANGUAGE_KEY, localeLanguage);
        setLocaleLanguage(mContext, localeLanguage);
    }


    public static void putLatLong(double lat, double lng) {
        Hawk.put("latitude", lat);
        Hawk.put("longitude", lng);
        resetPrayerTimes();
    }


    public static void putOmanLatLong(OmanCity city) {
        putLatLong(city.lat1, city.long1);
        Hawk.put("latitude2", city.lat2);
        Hawk.put("longitude2", city.long2);
        resetPrayerTimes();
    }


    public static double getLatitude() {
        return Hawk.get("latitude", 0.0);
    }

    public static double getLongitude() {
        return Hawk.get("longitude", 0.0);
    }

    public static double getLatitude2() {
        return Hawk.get("latitude2", 0.0);
    }

    public static double getLongitude2() {
        return Hawk.get("longitude2", 0.0);
    }


    public static String getCustomLogo() {
        return Hawk.get("custom_logo_alrabea", "");
    }

    public static void setCustomLogo(String path) {
        Hawk.put("custom_logo_alrabea", path);
    }


    public static News getCurrentNews() {
        return Hawk.get("_News", new News());
    }

    @NotNull
    public static void setCurrentNews(News news) {
        Hawk.put("_News", news);
    }


    public static PremiumUserModel getPremiumUser() {
        return Hawk.get("PremiumUserModel", null);
    }

    public static void setPremiumUser(PremiumUserModel user) {
        Hawk.put("PremiumUserModel", user);
    }

    public static void deletePremiumUserCustomIcon() {
        File file = getPremiumUserCustomIcon();
        if (file != null) file.delete();
        Hawk.delete("PremiumUserCustomIcon");
    }

    public static File getPremiumUserCustomIcon() {
        String fileName = Hawk.get("PremiumUserCustomIcon", "");
        if (fileName == null || fileName.isEmpty()) return null;
        File file = new File(baseContext.getApplicationInfo().dataDir, fileName);
        if (file.exists()) return file;
        return null;
    }

    public static void setPremiumUserCustomIcon(String fileName) {
        Hawk.put("PremiumUserCustomIcon", fileName);
    }

    public static String getCountryCode() {
        return Hawk.get("CountryCode", "SA").toUpperCase(Locale.ENGLISH);
    }

    public static void setCountryCode(String countryCode) {
        Hawk.put("CountryCode", countryCode);
    }

    public static void resetPrayerTimes() {
        for (int i = 2020; i <= 2030; i++) {
            Hawk.delete(PRAYER_TIME_KEY + i);
            //      putPrayerTimesForYear(null,i+"");
        }
        Hawk.put(PRAYER_TIMES_UPDATED, false);
    }

    public static PrayerApi getPrayerTimesForYear(String year) {
        return Hawk.get(PRAYER_TIME_KEY + year, null);
    }

    public static String getTypeNumber() {
        return Hawk.get(ConstantsOfApp.IS_ARABIC_NUMBER, ConstantsOfApp.EN_LANGUAGE);
    }


    public static void setTypeNumber(String typeNumber) {
        Hawk.put(ConstantsOfApp.IS_ARABIC_NUMBER, typeNumber);
    }

    public static String getTypeAM() {
        return Hawk.get(IS_AM_PM_KEY, ConstantsOfApp.EN_LANGUAGE);
    }

    public static void setTypeAM(String am) {
        Hawk.put(IS_AM_PM_KEY, am);
    }

    public static int getAppOrientation() {
        return Hawk.get(IS_LANDSCAPE_KEY, -1);
    }

    public static void setAppOrientation(int orientation) {
        Hawk.put(IS_LANDSCAPE_KEY, orientation);
        AppController.screenOrientationEnforcer.update();

//        if (orientation == 0){
//            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
//        }
//        if (orientation==2){
//            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
//        }
//        if (orientation == 1){
//            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
//        }

    }

    public static boolean isUpdateLocation() {
        return Hawk.get(ConstantsOfApp.UPDATE_LOCATION_KEY, false);
    }

    public static void setUpdateLocation(boolean isUpdateLocation) {
        Hawk.put(ConstantsOfApp.UPDATE_LOCATION_KEY, isUpdateLocation);
    }

    public static boolean isPhotoGalleryEnabled() {
        return Hawk.get(IS_ENABLE_CREATE_PHOTO_GALLERY, IS_ENABLE_CREATE_PHOTO_GALLERY_DEFAULT);
    }

    public static long getSelectedPhotoGalleryId() {
        return Hawk.get(SELECTED_GALLERY_ID, DEFAULT_GALLERY_SELECTED_ID);
    }

    public static boolean getEnableTimeWithAzkar() {
        return Hawk.get(ENABLE_TIME_ATHKAR, true);
    }

    public static void setEnableTimeWithAzkar(boolean b) {
        Hawk.put(ENABLE_TIME_ATHKAR, b);
    }

    public static boolean getEnableTimeWithGallery() {
        return Hawk.get(ENABLE_TIME_GALLERY, false);
    }

    public static void setEnableTimeWithGallery(boolean b) {
        Hawk.put(ENABLE_TIME_GALLERY, b);
    }

    public static boolean getEnableShowIkamaDelay() {
        return Hawk.get(ENABLE_SHOW_IKAMA_DELAY, false);
    }

    public static void setEnableShowIkamaDelay(boolean b) {
        Hawk.put(ENABLE_SHOW_IKAMA_DELAY, b);
    }

    public static UITheme getCurrentTheme() {
        return Hawk.get(APP_UITHEME_KEY, UITheme.BLUE);
    }

    public static void setCurrentTheme(UITheme theme) {
        Hawk.put(APP_UITHEME_KEY, theme);
    }

    public static int getCurrentFirebaseThemeIndex() {
        return Hawk.get(APP_FIREBASE_THEME_KEY, -1);
    }

    public static void setCurrentFirebaseThemeIndex(int index) {
        Hawk.put(APP_FIREBASE_THEME_KEY, index);
    }

    public static int getCurrentAzan() {
        return Hawk.get(CURRENT_AZAN, 0);
    }

    public static void setCurrentAzan(int position) {
        Hawk.put(CURRENT_AZAN, position);
    }

    public static int getCurrentIkama() {
        return Hawk.get(CURRENT_IKAMA, 0);
    }

    public static void setCurrentIkama(int position) {
        Hawk.put(CURRENT_IKAMA, position);
    }

    public static AzkarTheme getCurrentAzkarTheme() {
        return Hawk.get(APP_AZKAR_THEME_KEY, AzkarTheme.FIFTH);
    }

    public static void setCurrentAzkarTheme(AzkarTheme theme) {
        Hawk.put(APP_AZKAR_THEME_KEY, theme);
    }

    public static float getAudioLevel() {
        return Hawk.get(ConstantsOfApp.DARAGT_SOUND, 1f);
    }

    public static void setAudioLevel(float level) {
        Hawk.put(ConstantsOfApp.DARAGT_SOUND, level);
    }

    public static boolean getAudioAzanEnabled() {
        return Hawk.get(ConstantsOfApp.SOUND_AZAN_ENABLE, false);
    }

    public static void setAudioAzanEnabled(boolean isEnabled) {
        Hawk.put(ConstantsOfApp.SOUND_AZAN_ENABLE, isEnabled);
    }

    public static boolean getAudioIkamaEnabled() {
        return Hawk.get(ConstantsOfApp.SOUND_IKAMA_ENABLE, false);
    }

    public static void setAudioIkamaEnabled(boolean isEnabled) {
        Hawk.put(ConstantsOfApp.SOUND_IKAMA_ENABLE, isEnabled);
    }

    public static boolean getEventsEnabled() {
        return Hawk.get(ConstantsOfApp.EVENT_ENABLE, true);
    }

    public static void setEventsEnabled(boolean isEnabled) {
        Hawk.put(ConstantsOfApp.EVENT_ENABLE, isEnabled);
    }

    public static PrayerMethod getCurrentPrayerMethod() {
        return Hawk.get(CURRENT_PRAYER_METHOD, PrayerMethod.automatic);
    }

    public static void setCurrentPrayerMethod(PrayerMethod prayerMethod) {
        Hawk.put(CURRENT_PRAYER_METHOD, prayerMethod);
    }

    public static PrayerMethod getCurrentPrayerMethod(boolean withNull) {
        return Hawk.get(CURRENT_PRAYER_METHOD, null);
    }

    public static int getSupportApksVersions() {
        return Hawk.get(ConstantsOfApp.SUPPORT_APKS_VERSION, 0);
    }

    public static void setSupportApksVersions(int version) {
        Hawk.put(ConstantsOfApp.SUPPORT_APKS_VERSION, version);
    }

    public static CalcFunction getCurrentCalcFunction() {
        return Hawk.get("currentCalcFunction", CalcFunction.Saudi);
    }

    public static void setCurrentCalcFunction(CalcFunction calcFunction) {
        Hawk.put("currentCalcFunction", calcFunction);
    }

    public static int getShowDuhaSetting() {
        Log.e("Duha", "get " + Hawk.get("duhaSetting", 0));
        return Hawk.get("duhaSetting", 0);
//        0- > duha
//        1- > sunrise
//        2- > hide
//        3- > alternate

    }

    public static void setShowDuhaSetting(int duha) {
//        0-> duha
//        1-> sunrise
//        2-> hide
//        3- > alternate
        Log.e("Duha", "set " + duha);

        Hawk.put("duhaSetting", duha);
    }

    public static boolean getShowEventsOnAllScreens() {
        return Hawk.get("ShowEventsOnAllScreens", true);
    }

    public static void setShowEventsOnAllScreens(boolean show) {
        Hawk.put("ShowEventsOnAllScreens", show);
    }

    public static int getAzkarTimeType(AthkarType type) {
        switch (type) {
            case MorningAthkar:
                return Hawk.get(ConstantsOfApp.MORNING_TIME_KEY, 1);
            case EveningAthkar:
                return Hawk.get(ConstantsOfApp.EVENING_TIME_KEY, 1);
        }
        return 0;
    }

    public static void setAzkarTimeType(AthkarType type, int index) {
        switch (type) {
            case MorningAthkar:
                Hawk.put(ConstantsOfApp.MORNING_TIME_KEY, index);
            case EveningAthkar:
                Hawk.put(ConstantsOfApp.EVENING_TIME_KEY, index);
        }
    }

    public static int getAzkarTimeDuration(AthkarType type) {
        switch (type) {
            case MorningAthkar:
                return Hawk.get(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT);
            case EveningAthkar:
                return Hawk.get(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, ConstantsOfApp.ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT);
        }
        return 0;
    }

    public static void setAzkarTimeDuration(AthkarType type, int duration) {
        switch (type) {
            case MorningAthkar:
                Hawk.put(ConstantsOfApp.CITATION_FOR_MORNING_TIME_KEY, duration);
            case EveningAthkar:
                Hawk.put(ConstantsOfApp.CITATION_FOR_EVENING_TIME_KEY, duration);
        }
    }

    public static Locale getLocale() {
        return new Locale(getLocaleLanguage());
    }

    public static boolean isArabic() {
//        String localeLanguage = Hawk.get(ConstantsOfApp.LOCALE_LANGUAGE_KEY, DEFAULT_LANGUAGE);
        String localeLanguage = HawkSettings.getLocaleLanguage();
        return localeLanguage.equalsIgnoreCase(AR_LANGUAGE);

        /*if (!localeLanguage.isEmpty()) {
            return localeLanguage.equalsIgnoreCase("ar")
                    || localeLanguage.equalsIgnoreCase("arabic")
                    || localeLanguage.equalsIgnoreCase("العربية")
                    || localeLanguage.equalsIgnoreCase("عربية");
        } else {
            return Locale.getDefault().getLanguage().equalsIgnoreCase("ar")
                    || Locale.getDefault().getLanguage().equalsIgnoreCase("arabic")
                    || Locale.getDefault().getLanguage().equalsIgnoreCase("العربية")
                    || Locale.getDefault().getLanguage().equalsIgnoreCase("عربية");
        }*/
    }

    public static boolean isPhotoGalleryManagerEnabled() {
        return Hawk.get(IS_ENABLE_CREATE_PHOTO_GALLERY, IS_ENABLE_CREATE_PHOTO_GALLERY_DEFAULT);
    }

    public static boolean isPhotoGalleryManagerEnabledBetweenAzanAndIkama() {
        if (Utils.isJomaa())
            return Hawk.get(IS_ENABLE_CREATE_PHOTO_GALLERY_IKAMA_JOMAA, false);
        else
            return Hawk.get(IS_ENABLE_CREATE_PHOTO_GALLERY_IKAMA, false);
    }


}

<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:contentPadding="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="@dimen/_48sdp"
            android:layout_height="@dimen/_48sdp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:scaleType="fitCenter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/anydesk" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="TeamViewer" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:textSize="@dimen/_9sdp"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            tools:text="15.5.5" />


        <LinearLayout
            android:id="@+id/ll_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:animateLayoutChanges="true"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_8sdp"
            android:paddingVertical="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_icon"
            app:layout_constraintTop_toBottomOf="@+id/tv_state">

            <Button
                android:id="@+id/btn_open"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/_8sdp"
                android:background="@drawable/button_gray_without_corners_shape"
                android:drawableStart="@drawable/ic_open"
                android:drawablePadding="8dp"
                android:backgroundTint="@color/colorGreen"
                android:drawableTint="@color/white"
                android:textColor="@color/white"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:minWidth="@dimen/_54sdp"
                android:minHeight="@dimen/_36sdp"
                android:padding="@dimen/_8sdp"
                android:text="@string/open"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/btn_install"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/_8sdp"
                android:background="@drawable/button_gray_without_corners_shape"
                android:drawableStart="@drawable/ic_install"
                android:drawablePadding="8dp"
                android:drawableTint="@color/bluelett"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:minWidth="@dimen/_42sdp"
                android:minHeight="@dimen/_36sdp"
                android:padding="@dimen/_8sdp"
                android:text="@string/install"
                android:textColor="@color/bluelett"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/btn_update"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/_8sdp"
                android:background="@drawable/button_gray_without_corners_shape"
                android:drawableStart="@drawable/ic_update"
                android:drawablePadding="8dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:backgroundTint="@color/colorBlueMain"
                android:drawableTint="@color/white"
                android:textColor="@color/white"
                android:gravity="center"
                android:minWidth="@dimen/_42sdp"
                android:minHeight="@dimen/_36sdp"
                android:padding="@dimen/_8sdp"
                android:text="@string/update"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold" />

            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                android:id="@+id/progressBar"
                android:layout_width="@dimen/_36sdp"
                android:layout_height="@dimen/_36sdp"
                android:layout_marginHorizontal="@dimen/_8sdp"
                android:textColor="@color/colorBlueMain"
                android:visibility="visible"
                app:cpb_background_progressbar_color="#7B918F8F"
                app:cpb_background_progressbar_width="16dp"
                app:cpb_indeterminate_mode="true"
                app:cpb_progress="50"

                app:cpb_progress_max="100"
                app:cpb_progressbar_color="@color/colorBlueMain"
                app:cpb_progressbar_width="16dp"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_8sdp"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:textSize="@dimen/_12sdp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_version"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/tv_version"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            tools:text="this app is installed" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
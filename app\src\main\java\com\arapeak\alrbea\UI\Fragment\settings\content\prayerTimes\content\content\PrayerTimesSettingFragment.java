package com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.content.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ASR_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.DHUHR_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.DUHA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.FAJR_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.ISHA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_ANNOUNCEMENT_SHOW_TIME_IKAMA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_JOMAA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_LOCK_DURING_PRAYER_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_PRAYER_TIME_ANNOUNCEMENT_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_TIME_OF_IKAMA_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.JOMAA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.LOCK_DURING_PRAYER_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MAGHRIB_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_DUHA_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_TO_DUHA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.POST_OR_PRE_TO_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.PRAYER_TIME_ANNOUNCEMENT_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_POST_OR_PRE_DUHA_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_POST_OR_PRE_PRAY_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_POST_OR_PRE_TO_DUHA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TIME_OF_IKAMA_KEY;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class PrayerTimesSettingFragment extends AlrabeeaTimesFragment implements SettingsAdapterCallback {

    public static final String NAME_OF_PRAYER_ARG = "nameOfPrayer";
    public static final String TYPE_OF_PRAYER_ARG = "typeOfPrayer";
    public static final String IS_RAMADAN_SETTINGS_ARG = "isRamadanSettings";
    public static final String IS_JOMAA_ARG = "isJomaa";
    private static final String TAG = "PrayerTimesSettingF";
    private View prayerTimesSettingView;
    private RecyclerView settingItemRecyclerView;

    private MainSettingsAdapter mainSettingsAdapter;
    private String nameOfPrayer, nameOfPrayerKey, postOrPreToPrayKey, isEnablePostOrPrePrayKey, isEnableTimeBetweenAdanAndIkamaKey, isEnableTimeOfIkamaKey, timeOfIkamaDefault;
    private int timeBetweenAdanAndIkamaDefault;
    private PrayerType prayerType;
    private PrayerTime prayerTime;
    private boolean isRamadanSettings, isJomaa;

    public PrayerTimesSettingFragment() {

    }

    public static PrayerTimesSettingFragment newInstance(PrayerType prayerType, String nameOfPrayer
            , boolean isRamadanSettings
            , boolean isJomaa) {

        Bundle args = new Bundle();

        PrayerTimesSettingFragment fragment = new PrayerTimesSettingFragment();
        args.putInt(TYPE_OF_PRAYER_ARG, prayerType.ordinal());
        args.putString(NAME_OF_PRAYER_ARG, nameOfPrayer);
        args.putBoolean(IS_RAMADAN_SETTINGS_ARG, isRamadanSettings);
        args.putBoolean(IS_JOMAA_ARG, isJomaa);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            int prayerTypeOrd = getArguments().getInt(TYPE_OF_PRAYER_ARG, PrayerType.Fajr.ordinal());
            prayerType = PrayerType.values()[prayerTypeOrd];
            prayerTime = prayerType.prayerTime;
            nameOfPrayer = getArguments().getString(NAME_OF_PRAYER_ARG, "");
            isRamadanSettings = getArguments().getBoolean(IS_RAMADAN_SETTINGS_ARG, false);
            isJomaa = getArguments().getBoolean(IS_JOMAA_ARG, false);
            if (isJomaa) PrayerTime.EnableJomaaForTesting = true;
        }
    }

    @Override
    public void onDestroy() {
        PrayerTime.EnableJomaaForTesting = false;
        super.onDestroy();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        prayerTimesSettingView = inflater.inflate(R.layout.fragment_prayer_times_setting, container, false);

        initView();
        SetParameter();
        SetAction();

        return prayerTimesSettingView;
    }

    private void initView() {
        settingItemRecyclerView = prayerTimesSettingView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingFragment);

        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<>(), this);
        TextView titleView = prayerTimesSettingView.findViewById(R.id.tv_prayer_time_title);
        titleView.setText(prayerType.FullString_R_ID);
    }

    private void SetParameter() {
        settingItemRecyclerView.setAdapter(mainSettingsAdapter);

        Object[] objects = Utils.putPrayKey(getContext(), nameOfPrayer, isRamadanSettings);
        nameOfPrayerKey = (String) objects[0];
        postOrPreToPrayKey = (String) objects[1];
        timeBetweenAdanAndIkamaDefault = (int) objects[2];
        isEnablePostOrPrePrayKey = (String) objects[3];
        isEnableTimeBetweenAdanAndIkamaKey = (String) objects[4];
        isEnableTimeOfIkamaKey = (String) objects[5];
        timeOfIkamaDefault = (String) objects[6];

        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.prayer_time_announcement)
                , ViewsAlrabeeaTimes.TEXT_VIEW
                , getString(R.string.show_prayer_time_announcement_for_four_minutes));
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(getRamadanKey() + IS_ENABLE_KEY + PRAYER_TIME_ANNOUNCEMENT_KEY + nameOfPrayerKey
                , IS_ENABLE_PRAYER_TIME_ANNOUNCEMENT_DEFAULT));
        subSettingAlrabeeaTimes.setisShowSpace(true);
        subSettingAlrabeeaTimes.setEnabled(true);
        subSettingAlrabeeaTimes.setisPray(true);
        subSettingAlrabeeaTimes.setTag(SettingItems.PrayerTimeAnnouncement);


        mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.edit_pray_time) + " " + getNameOfPrayer()
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , getString(R.string.edit_pray_time) + " " + getNameOfPrayer()
                , Hawk.get(postOrPreToPrayKey, isRamadanSettings ? RAMADAN_POST_OR_PRE_PRAY_DEFAULT : POST_OR_PRE_PRAY_DEFAULT));
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setisShowSpace(true);
        subSettingAlrabeeaTimes.setEnabled(false);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(isEnablePostOrPrePrayKey, IS_ENABLE_SETTINGS_DEFAULT));
        subSettingAlrabeeaTimes.setisPray(true);
        subSettingAlrabeeaTimes.setTag(SettingItems.AdjustPrayerTime);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        if (isJomaa) {

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.time_ofjomaaa), ViewsAlrabeeaTimes.TIME_PICKER, ""
                    , Hawk.get(getRamadanKey() + TIME_OF_IKAMA_KEY + nameOfPrayerKey, timeOfIkamaDefault));
            subSettingAlrabeeaTimes.setShowEnableButton(true);

            subSettingAlrabeeaTimes.setEnable(Hawk.get(isEnableTimeOfIkamaKey, isJomaa
                    ? IS_ENABLE_JOMAA_DEFAULT
                    : IS_ENABLE_TIME_OF_IKAMA_DEFAULT));
            subSettingAlrabeeaTimes.setEnabled(true);
            subSettingAlrabeeaTimes.setTag(SettingItems.FixedIkamaTime);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.time_between_adan_and_jomaa)
                    , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER, ""
                    , Hawk.get(getRamadanKey() + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + nameOfPrayerKey
                    , Utils.getAnnouncementShowTimeDefault(getAppCompatActivity(), nameOfPrayer, isRamadanSettings)), false);
            subSettingAlrabeeaTimes.setShowEnableButton(true);
//      subSettingAlrabeeaTimes.setEnable(Hawk.get(getRamadanKey() + IS_ENABLE_KEY +ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + nameOfPrayerKey
//              , isJomaa ? IS_ENABLE_JOMAA_DEFAULT : IS_ENABLE_ANNOUNCEMENT_SHOW_TIME_IKAMA_DEFAULT));
            subSettingAlrabeeaTimes.setEnable(prayerTime.isAllowedToAnnounceIkama());
            subSettingAlrabeeaTimes.setEnabled(true);
            subSettingAlrabeeaTimes.setisPray(true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            subSettingAlrabeeaTimes.setTag(SettingItems.IkamaAnnouncementDuration);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        } else {
            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.time_between_adan_and_ikama)
                    , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                    , ""
                    , Hawk.get(getRamadanKey() + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + nameOfPrayerKey, timeBetweenAdanAndIkamaDefault)
                    , false);
            subSettingAlrabeeaTimes.setShowEnableButton(true);
            subSettingAlrabeeaTimes.setisShowSpace(true);
            subSettingAlrabeeaTimes.setEnabled(true);
            // subSettingAlrabeeaTimes.setEnable(true);
//    subSettingAlrabeeaTimes.setisPray(true);
            subSettingAlrabeeaTimes.setEnable(Hawk.get(isEnableTimeBetweenAdanAndIkamaKey, isJomaa
                    ? IS_ENABLE_JOMAA_DEFAULT
                    : IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT));
            subSettingAlrabeeaTimes.setTag(SettingItems.TimeBetweenAzanAndIkama);

            mainSettingsAdapter.add(subSettingAlrabeeaTimes);


            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.time_of_ikama), ViewsAlrabeeaTimes.TIME_PICKER, ""
                    , Hawk.get(getRamadanKey() + TIME_OF_IKAMA_KEY + nameOfPrayerKey, timeOfIkamaDefault));
            subSettingAlrabeeaTimes.setShowEnableButton(true);
            subSettingAlrabeeaTimes.setEnable(Hawk.get(isEnableTimeOfIkamaKey, isJomaa
                    ? IS_ENABLE_JOMAA_DEFAULT
                    : IS_ENABLE_TIME_OF_IKAMA_DEFAULT));
            subSettingAlrabeeaTimes.setEnabled(true);
            subSettingAlrabeeaTimes.setisPray(true);
            subSettingAlrabeeaTimes.setTag(SettingItems.FixedIkamaTime);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.announcement_show_time), ViewsAlrabeeaTimes.ADD_MINUS_NUMBER, ""
                    , Hawk.get(getRamadanKey() + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + nameOfPrayerKey
                    , Utils.getAnnouncementShowTimeDefault(getAppCompatActivity(), nameOfPrayer, isRamadanSettings)), false);
            subSettingAlrabeeaTimes.setShowEnableButton(true);
            subSettingAlrabeeaTimes.setEnable(Hawk.get(getRamadanKey()
                            + IS_ENABLE_KEY
                            + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY
                            + nameOfPrayerKey
                    , isJomaa ? IS_ENABLE_JOMAA_DEFAULT
                            : IS_ENABLE_ANNOUNCEMENT_SHOW_TIME_IKAMA_DEFAULT));
            subSettingAlrabeeaTimes.setEnabled(true);
            subSettingAlrabeeaTimes.setisPray(true);
            subSettingAlrabeeaTimes.setisShowSpace(true);

            subSettingAlrabeeaTimes.setTag(SettingItems.IkamaAnnouncementDuration);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);
        }
        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.time_to_finish_the_prayer)
                , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                , getString(isJomaa ? R.string.lock_during_friday_sermon_and_prayer : R.string.lock_during_prayer)
                , Hawk.get(getRamadanKey()
                        + LOCK_DURING_PRAYER_KEY
                        + nameOfPrayerKey
                , Utils.getTimeToFinishThePrayer(getAppCompatActivity(), nameOfPrayer, isRamadanSettings))
                , false);
        subSettingAlrabeeaTimes.setisPray(true);
        subSettingAlrabeeaTimes.setShowEnableButton(true);
        subSettingAlrabeeaTimes.setisShowSpace(true);
        subSettingAlrabeeaTimes.setEnable(Hawk.get(getRamadanKey()
                        + IS_ENABLE_KEY
                        + LOCK_DURING_PRAYER_KEY
                        + nameOfPrayerKey
                , isJomaa ? IS_ENABLE_JOMAA_DEFAULT
                        : IS_ENABLE_LOCK_DURING_PRAYER_DEFAULT));
        subSettingAlrabeeaTimes.setisShowSpace(true);
        subSettingAlrabeeaTimes.setEnabled(true);
        subSettingAlrabeeaTimes.setTag(SettingItems.PrayingTime);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        if (nameOfPrayer.equalsIgnoreCase(FAJR_KEY)) {

            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.time_to_finish_the_fajer_prayer_on_jomaa)
                    , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                    , getString(R.string.lock_during_prayer)
                    , Hawk.get(getRamadanKey()
                            + LOCK_DURING_PRAYER_KEY
                            + nameOfPrayer + JOMAA_KEY
                    , Utils.getTimeToFinishThePrayer(getAppCompatActivity(), JOMAA_KEY, isRamadanSettings))
                    , false);
            subSettingAlrabeeaTimes.setisPray(true);
            subSettingAlrabeeaTimes.setShowEnableButton(true);
            subSettingAlrabeeaTimes.setEnable(Hawk.get(getRamadanKey()
                            + IS_ENABLE_KEY
                            + LOCK_DURING_PRAYER_KEY
                            + nameOfPrayer + JOMAA_KEY
                    , IS_ENABLE_LOCK_DURING_PRAYER_DEFAULT));
            subSettingAlrabeeaTimes.setisShowSpace(true);
            subSettingAlrabeeaTimes.setEnabled(true);
            subSettingAlrabeeaTimes.setTag(SettingItems.FajrPrayingTimeDuringJomaa);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);


            subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.prayer_time_announcement_al_duha_prayer)
                    , ViewsAlrabeeaTimes.ADD_MINUS_NUMBER
                    , getString(R.string.announcement_of_the_time_of_the_al_duha_prayers_after_the_sunrise_fifteen_minutes_for_5_minutes)
                    , Hawk.get(isRamadanSettings
                    ? RAMADAN_POST_OR_PRE_TO_DUHA_KEY
                    : POST_OR_PRE_TO_DUHA_KEY, isRamadanSettings
                    ? RAMADAN_POST_OR_PRE_DUHA_PRAY_DEFAULT
                    : POST_OR_PRE_DUHA_PRAY_DEFAULT));
            subSettingAlrabeeaTimes.setisPray(true);
            subSettingAlrabeeaTimes.setShowEnableButton(true);
            subSettingAlrabeeaTimes.setEnable(Hawk.get(getRamadanKey() + IS_ENABLE_KEY
                            + PRAYER_TIME_ANNOUNCEMENT_KEY
                            + DUHA_KEY
                    , IS_ENABLE_PRAYER_TIME_ANNOUNCEMENT_DEFAULT));
            subSettingAlrabeeaTimes.setisShowSpace(true);
            subSettingAlrabeeaTimes.setEnabled(true);
            subSettingAlrabeeaTimes.setTag(SettingItems.DuhaPrayerAnnouncement);
            mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        }


    }

    private String getNameOfPrayer() {
        if (nameOfPrayer.equalsIgnoreCase(FAJR_KEY)) {
            return getString(R.string.fajr);
        } else if (nameOfPrayer.equalsIgnoreCase(DHUHR_KEY)) {
            return getString(R.string.dhuhr);
        } else if (nameOfPrayer.equalsIgnoreCase(ASR_KEY)) {
            return getString(R.string.asr);
        } else if (nameOfPrayer.equalsIgnoreCase(MAGHRIB_KEY)) {
            return getString(R.string.maghrib);
        } else if (nameOfPrayer.equalsIgnoreCase(ISHA_KEY)) {
            return getString(R.string.isha);
        } else if (nameOfPrayer.equalsIgnoreCase(JOMAA_KEY)) {
            return getString(R.string.jomaa);
        }
        return "";
    }

    private void SetAction() {

    }

    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {

        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = mainSettingsAdapter.getItem(position);
        boolean isEnable = subPosition == 1;
        SubSettingAlrabeeaTimes temp;

        switch ((SettingItems) subSettingAlrabeeaTimes.getTag()) {

            case PrayerTimeAnnouncement:
                Hawk.put(getRamadanKey() + IS_ENABLE_KEY
                                + PRAYER_TIME_ANNOUNCEMENT_KEY
                                + nameOfPrayerKey
                        , isEnable);
                break;

            case AdjustPrayerTime:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(isEnablePostOrPrePrayKey, isEnable);

                    if (!isEnable) {
                        Hawk.put(getRamadanKey() + POST_OR_PRE_TO_KEY + nameOfPrayerKey, 0);
                    } else {
                        int positionDefaultValue = isRamadanSettings
                                ? RAMADAN_POST_OR_PRE_PRAY_DEFAULT
                                : POST_OR_PRE_PRAY_DEFAULT;
                        Hawk.put(postOrPreToPrayKey, positionDefaultValue);
                        subSettingAlrabeeaTimes.setPositionDefaultValue(positionDefaultValue);
                    }
                    subSettingAlrabeeaTimes.setEnable(isEnable);
                    mainSettingsAdapter.notifyItemChanged(position);
                    return;
                }
                Hawk.put(getRamadanKey() + POST_OR_PRE_TO_KEY + nameOfPrayerKey, subPosition);
                break;

            case TimeBetweenAzanAndIkama:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {

                    //if enable then disable FixedIkamaTime
                    if (isEnable) {
                        temp = mainSettingsAdapter.getByTag(SettingItems.FixedIkamaTime);
                        temp.setEnable(false);
                        Hawk.put(isEnableTimeOfIkamaKey, false);
                        mainSettingsAdapter.notifyItemChangedByTag(SettingItems.FixedIkamaTime);
                    }
                    subSettingAlrabeeaTimes.setEnable(isEnable);
                    Hawk.put(isEnableTimeBetweenAdanAndIkamaKey, isEnable);
                    mainSettingsAdapter.notifyItemChanged(position);


          /*if (!isEnable) {
            subSettingAlrabeeaTimes.setEnable(false);
            mainSettingsAdapter.setItem(position, subSettingAlrabeeaTimes);
            if (mainSettingsAdapter.getItem(position + 1) != null
                    && !mainSettingsAdapter.getItem(position + 1).isEnable()) {
              SubSettingAlrabeeaTimes subSettingAlrabeeaTimesLastItem = mainSettingsAdapter.getItem(position + 2);
              subSettingAlrabeeaTimesLastItem.setEnable(false);
              mainSettingsAdapter.setItem(position + 2, subSettingAlrabeeaTimesLastItem);
            }

            Hawk.put(getRamadanKey() + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + nameOfPrayerKey, 0);
            Hawk.put(getRamadanKey() + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + nameOfPrayerKey, 0);

            Hawk.put(getRamadanKey() + IS_ENABLE_KEY
                    + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY
                    + nameOfPrayerKey, isEnable);

          } else {
            SubSettingAlrabeeaTimes subSettingAlrabeeaTimesLastItem = mainSettingsAdapter.getItem(position + 1);
            if (subSettingAlrabeeaTimesLastItem != null
                    && subSettingAlrabeeaTimesLastItem.isEnable()) {
              subSettingAlrabeeaTimesLastItem.setEnable(false);
              mainSettingsAdapter.setItem(position + 1, subSettingAlrabeeaTimesLastItem);

              Hawk.put(isEnableTimeOfIkamaKey, false);
              Hawk.put(getRamadanKey() + TIME_OF_IKAMA_KEY + nameOfPrayerKey, "");
            }

            Hawk.put(getRamadanKey()
                    + TIME_BETWEEN_ADAN_AND_IKAMA_KEY
                    + nameOfPrayerKey, timeBetweenAdanAndIkamaDefault);
            subSettingAlrabeeaTimes.setPositionDefaultValue(timeBetweenAdanAndIkamaDefault);
          }
          mainSettingsAdapter.notifyItemChanged(position);*/

                    return;
                }
                Hawk.put(getRamadanKey() + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + nameOfPrayerKey, subPosition);

                break;

            case FixedIkamaTime:

                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(isEnableTimeOfIkamaKey, isEnable);
                    subSettingAlrabeeaTimes.setEnable(isEnable);
                    mainSettingsAdapter.notifyItemChanged(position);
                    if (isEnable && !isJomaa) {
                        //disable time between ikama and prayer
                        mainSettingsAdapter.getByTag(SettingItems.TimeBetweenAzanAndIkama).setEnable(false);
                        Hawk.put(isEnableTimeBetweenAdanAndIkamaKey, false);
                        mainSettingsAdapter.notifyItemChangedByTag(SettingItems.TimeBetweenAzanAndIkama);
                    }
                    return;
                }

                Hawk.put(getRamadanKey() + TIME_OF_IKAMA_KEY + nameOfPrayerKey, subSettingAlrabeeaTimes.getPositionDefaultValueString());

                break;

            case IkamaAnnouncementDuration:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(getRamadanKey() + IS_ENABLE_KEY + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + nameOfPrayerKey, isEnable);
                    if (isJomaa)
                        Hawk.put(getRamadanKey() + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + nameOfPrayerKey, isEnable);
                    subSettingAlrabeeaTimes.setEnable(isEnable);
                    mainSettingsAdapter.notifyItemChanged(position);
                    return;
                }

                Hawk.put(getRamadanKey() + ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY + nameOfPrayerKey, subPosition);
                if (isJomaa)
                    Hawk.put(getRamadanKey() + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + nameOfPrayerKey, subPosition);

                break;

            case PrayingTime: {
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {

                    Hawk.put(getRamadanKey()
                            + IS_ENABLE_KEY
                            + LOCK_DURING_PRAYER_KEY
                            + nameOfPrayerKey, isEnable);

                    if (!isEnable) {
                        Hawk.put(getRamadanKey()
                                + LOCK_DURING_PRAYER_KEY
                                + nameOfPrayerKey, 0);

                    } else {
                        int positionDefaultValue = Utils.getTimeToFinishThePrayer(getAppCompatActivity(), nameOfPrayerKey, isRamadanSettings);
                        Hawk.put(getRamadanKey()
                                        + LOCK_DURING_PRAYER_KEY
                                        + nameOfPrayerKey
                                , Utils.getTimeToFinishThePrayer(getAppCompatActivity(), nameOfPrayerKey, isRamadanSettings));
                        subSettingAlrabeeaTimes.setPositionDefaultValue(positionDefaultValue);


                    }
                    subSettingAlrabeeaTimes.setEnable(isEnable);
                    mainSettingsAdapter.notifyItemChanged(position);

                    return;
                }

                Hawk.put(getRamadanKey()
                        + LOCK_DURING_PRAYER_KEY
                        + nameOfPrayerKey, subPosition);
                break;
            }
            case FajrPrayingTimeDuringJomaa:
                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {

                    Hawk.put(getRamadanKey() + IS_ENABLE_KEY + LOCK_DURING_PRAYER_KEY + nameOfPrayer + JOMAA_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.put(getRamadanKey() + LOCK_DURING_PRAYER_KEY + nameOfPrayer + JOMAA_KEY, 0);
                    } else {
                        int positionDefaultValue = Utils.getTimeToFinishThePrayer(getAppCompatActivity(), nameOfPrayerKey, isRamadanSettings);
                        Hawk.put(getRamadanKey()
                                        + LOCK_DURING_PRAYER_KEY
                                        + nameOfPrayer + JOMAA_KEY
                                , positionDefaultValue);
                        subSettingAlrabeeaTimes.setPositionDefaultValue(positionDefaultValue);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }
                    return;
                }

                Hawk.put(getRamadanKey()
                        + LOCK_DURING_PRAYER_KEY
                        + nameOfPrayer + JOMAA_KEY, subPosition);
                break;

            case DuhaPrayerAnnouncement:
                String duhaKey = isRamadanSettings
                        ? RAMADAN_POST_OR_PRE_TO_DUHA_KEY
                        : POST_OR_PRE_TO_DUHA_KEY;
                int positionDefaultValueDuha = isRamadanSettings
                        ? RAMADAN_POST_OR_PRE_DUHA_PRAY_DEFAULT
                        : POST_OR_PRE_DUHA_PRAY_DEFAULT;

                if (viewsAlrabeeaTimes == ViewsAlrabeeaTimes.ENABLE_BUTTON) {
                    Hawk.put(getRamadanKey() + IS_ENABLE_KEY
                            + PRAYER_TIME_ANNOUNCEMENT_KEY
                            + DUHA_KEY, isEnable);

                    if (!isEnable) {
                        Hawk.put(duhaKey, 0);
                    } else {
                        Hawk.put(duhaKey, positionDefaultValueDuha);
                        subSettingAlrabeeaTimes.setPositionDefaultValue(positionDefaultValueDuha);
                        mainSettingsAdapter.notifyItemChanged(position);
                    }
                    return;
                }
                Hawk.put(duhaKey, subPosition);
                break;
        }
    }

    public String getRamadanKey() {
        return isRamadanSettings ? RAMADAN_KEY : "";
    }

    private enum SettingItems {
        PrayerTimeAnnouncement,
        AdjustPrayerTime,
        TimeBetweenAzanAndIkama,
        FixedIkamaTime,
        IkamaAnnouncementDuration,
        PrayingTime,
        FajrPrayingTimeDuringJomaa,
        DuhaPrayerAnnouncement,
        KhutbaDuration,
        KhutbaFixedTime
    }

}

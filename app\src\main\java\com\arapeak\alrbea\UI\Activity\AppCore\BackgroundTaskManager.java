//package com.arapeak.alrbea.UI.Activity.AppCore;
//
//
//import android.app.Activity;
//import android.content.Context;
//import android.os.Handler;
//import android.os.HandlerThread;
//import android.util.Log;
//
//import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
//import com.arapeak.alrbea.APIs.ConstantsOfApp;
//import com.arapeak.alrbea.Enum.AthkarType;
//import com.arapeak.alrbea.Enum.PrayerType;
//import com.arapeak.alrbea.Interface.OnCompleteListener;
//import com.arapeak.alrbea.Interface.PrayerTime;
//import com.arapeak.alrbea.Model.Event;
//import com.arapeak.alrbea.Model.PrayerApi;
//import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
//import com.arapeak.alrbea.PrayerUtils;
//import com.arapeak.alrbea.R;
//import com.arapeak.alrbea.UI.Activity.HomeUi.CallbackInterfaces;
//import com.arapeak.alrbea.UI.Activity.MainActivity;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbea.database.Repositories;
//import com.arapeak.alrbea.hawk.HawkSettings;
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//import com.arapeak.alrbrea.core_ktx.ui.screensaver.ScreensaverScheduler;
//import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;
//
//import java.util.ArrayList;
//import java.util.Calendar;
//import java.util.GregorianCalendar;
//import java.util.HashMap;
//import java.util.List;
//import java.util.concurrent.atomic.AtomicBoolean;
//
//import static com.arapeak.alrbea.APIs.ConstantsOfApp.DAYS_MILLI_SECOND;
//import static com.arapeak.alrbea.APIs.ConstantsOfApp.HOURS_MILLI_SECOND;
//import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
//import static com.arapeak.alrbrea.core_ktx.ui.utils.ContextExtKt.isActivityInForeground;
//
//public class BackgroundTaskManager {
//
//    private static final String TAG = "BackgroundTaskManager";
//
//    private Context context;
//    private AppLifecycleManager appLifecycleManager;
//    private ScreensaverScheduler screensaver;
//
//    private CallbackInterfaces.OnPrayerTimeUpdateListener prayerTimeUpdateListener;
//    private CallbackInterfaces.OnContentDisplayListener contentDisplayListener;
//    private CallbackInterfaces.OnEventMessageListener eventMessageListener;
//
//    // --- Core Logic Variables ---
//    public static int year, month, day;
//    public static TimingsAlrabeeaTimes timingsAlrabeeaTimes;
//    public static long LastMaghrib = 0;
//    private AtomicBoolean isPrayerListUpdating = new AtomicBoolean(false);
//    private UmmalquraCalendar hijriCalendar = Utils.getUmmalquraCalendar();
//    private GregorianCalendar gregorianCalendar = new GregorianCalendar();
//    private long sleepTimeUntilNextRefresh = 0;
//    private int lastAthkarIndex = 0;
//    private boolean isJomaa;
//    private boolean longTextInRemainForPrayer;
//    private boolean showDuha = false;
//
//    private String ssMiladiDate = "";
//    private String ssHijriDate = "";
//    private String ssDayName = "";
//
//    // --- HandlerThreads and Handlers ---
//    private HandlerThread mainLogicHandlerThread;
//    private Handler mainLogicHandler;
//    private Runnable mainLogicRunnable;
//    private volatile boolean isMainLogicRunning = false;
//
//    private HandlerThread eventsHandlerThread;
//    private Handler eventsHandler;
//    private Runnable eventsRunnable;
//    private volatile boolean isEventsRunning = false;
//
//    private HandlerThread athkarUpdaterHandlerThread;
//    private Handler athkarUpdaterHandler;
//    private Runnable athkarUpdaterRunnable;
//    private volatile boolean isAthkarUpdaterRunning = false;
//
//    private HandlerThread screenSaverHandlerThread;
//    private Handler screenSaverHandler;
//    private Runnable screenSaverRunnable;
//    private volatile boolean isScreenSaverRunning = false;
//
//    private HandlerThread timeUpdaterHandlerThread;
//    private Handler timeUpdaterHandler;
//    private Runnable timeUpdaterRunnable;
//    private volatile boolean isTimeUpdaterRunning = false;
//
//    private HandlerThread duhaSunriseUpdaterHandlerThread;
//    private Handler duhaSunriseUpdaterHandler;
//    private Runnable duhaSunriseUpdaterRunnable;
//    private volatile boolean isDuhaSunriseUpdaterRunning = false;
//
//    private HandlerThread maintenanceHandlerThread;
//    private Handler maintenanceHandler;
//    private Runnable maintenanceRunnable;
//    private volatile boolean isMaintenanceRunning = false;
//
//    private HandlerThread textUpdateHandlerThread; // For executeEvery
//    private Handler textUpdateHandler;
//    private Runnable currentTextUpdateRunnable;
//
//
//    public BackgroundTaskManager(Context context) {
//        this.context = context.getApplicationContext();
//        this.appLifecycleManager = new AppLifecycleManager(context, null); // MainActivity will handle restart
//        this.screensaver = new ScreensaverScheduler(context);
//        this.longTextInRemainForPrayer = true; // Default, can be updated by UI logic
//    }
//
//    public void setListeners(CallbackInterfaces.OnPrayerTimeUpdateListener prayerTimeUpdateListener,
//                             CallbackInterfaces.OnContentDisplayListener contentDisplayListener,
//                             CallbackInterfaces.OnEventMessageListener eventMessageListener) {
//        this.prayerTimeUpdateListener = prayerTimeUpdateListener;
//        this.contentDisplayListener = contentDisplayListener;
//        this.eventMessageListener = eventMessageListener;
//    }
//
//    public void initializeManagedThreads() {
//        // Main Logic Thread
//        mainLogicHandlerThread = new HandlerThread("MainLogicThread");
//        mainLogicHandlerThread.start();
//        mainLogicHandler = new Handler(mainLogicHandlerThread.getLooper());
//        mainLogicRunnable = () -> {
//            while (isMainLogicRunning && !Thread.currentThread().isInterrupted()) {
//                try {
//                    Log.i(TAG, "MainLogicRunnable is alive");
//                    while (isPrayerListUpdating.get()) {
//                        if (!isMainLogicRunning || Thread.currentThread().isInterrupted()) return;
//                        Thread.sleep(1000);
//                    }
//
//                    // Perform core logic
//                    long tempTime = System.currentTimeMillis();
//                    year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR, tempTime));
//                    month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH, tempTime));
//                    day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY, tempTime));
//                    gregorianCalendar = new GregorianCalendar();
//                    hijriCalendar = Utils.getUmmalquraCalendar();
//
//                    if (timingsAlrabeeaTimes == null || timingsAlrabeeaTimes.getIntDay() != day || timingsAlrabeeaTimes.getIntMonth() != month)
//                        timingsAlrabeeaTimes = PrayerUtils.getTiming(year, month, day);
//
//                    if (timingsAlrabeeaTimes == null) {
//                        getPrayerTimesThisYear(); // This updates isPrayerListUpdating
//                    } else {
//                        // This logic needs to be called on a prayer data update or on a schedule
//                        selectNextPrayer();
//                    }
//
//                    long timeToSleep = sleepTimeUntilNextRefresh - System.currentTimeMillis();
//                    if (timeToSleep <= 0) timeToSleep = 1000; // Minimum 1 second sleep to prevent tight loop
//
//                    Log.e(TAG, "MainLogicRunnable will sleep for " + timeToSleep / 1000 + " seconds.");
//                    Thread.sleep(timeToSleep);
//
//                } catch (InterruptedException e) {
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                    Thread.currentThread().interrupt(); // Preserve interrupt status
//                    Log.i(TAG, "MainLogicRunnable interrupted.");
//                    break;
//                } catch (Exception e) {
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                    try {
//                        Log.e(TAG, "Exception in MainLogicRunnable, sleeping for 1s: " + e.getMessage());
//                        Thread.sleep(1000); // Sleep on other exceptions before retrying
//                    } catch (InterruptedException ie) {
//                        Thread.currentThread().interrupt();
//                        break;
//                    }
//                }
//            }
//            Log.i(TAG, "MainLogicRunnable ended.");
//        };
//
//        // Events Thread
//        eventsHandlerThread = new HandlerThread("EventsThread");
//        eventsHandlerThread.start();
//        eventsHandler = new Handler(eventsHandlerThread.getLooper());
//        eventsRunnable = () -> {
//            try {
//                Thread.sleep(3000); // Initial delay
//                while (isEventsRunning && !Thread.currentThread().isInterrupted()) {
//                    List<Event> events = new ArrayList<>();
//                    for (Event event : Repositories.getEventDb().getAllEnabled()) {
//                        if (eventDayArrived(event)) events.add(event);
//                    }
//
//                    long nextHour = System.currentTimeMillis() - (System.currentTimeMillis() % ConstantsOfApp.HOURS_MILLI_SECOND) + ConstantsOfApp.HOURS_MILLI_SECOND;
//
//                    while (isEventsRunning && !Thread.currentThread().isInterrupted() && nextHour > System.currentTimeMillis()) {
//                        while (!isAllowedToShowEvent()) {
//                            if (!isEventsRunning || Thread.currentThread().isInterrupted()) return;
//                            Thread.sleep(10000);
//                        }
//
//                        if (!events.isEmpty() && eventMessageListener != null) {
//                            StringBuilder eventText = new StringBuilder();
//                            for (Event event : events)
//                                eventText.append("                ").append(event.text).append("                ");
//                            eventMessageListener.onShowEvent(eventText.toString());
//                        }
//                        if (!isEventsRunning || Thread.currentThread().isInterrupted()) return;
//                        Thread.sleep(10 * ConstantsOfApp.MINUTES_MILLI_SECOND);
//                    }
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                Log.i(TAG, "eventsRunnable interrupted, exiting thread normally.");
//            } catch (Exception e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//            }
//            Log.i(TAG, "EventsRunnable finished.");
//        };
//
//        // Athkar Updater Thread
//        athkarUpdaterHandlerThread = new HandlerThread("AthkarUpdaterThread");
//        athkarUpdaterHandlerThread.start();
//        athkarUpdaterHandler = new Handler(athkarUpdaterHandlerThread.getLooper());
//        athkarUpdaterRunnable = () -> {
//            try {
//                String[] athkars = context.getResources().getStringArray(R.array.athkars);
//                while (isAthkarUpdaterRunning && athkars.length > 0 && !Thread.currentThread().isInterrupted()) {
//                    if (lastAthkarIndex >= athkars.length)
//                        lastAthkarIndex = 0;
//                    // Directly updating athkarTextView from here is not ideal as it's a UI view.
//                    // This should be a callback to MainActivity -> ContentDisplayManager
//                    if (contentDisplayListener != null) {
//                        contentDisplayListener.onDisplayAthkar(AthkarType.AfterPrayer, null); // Simplified, original had prayer type
//                        // Need a way to pass the specific athkar text. Adding to ContentDisplayManager's method
//                        // Or pass text directly to a new method like `updateAthkarText(String text)`
//                    }
//                    Thread.sleep(45000);
//                    lastAthkarIndex++;
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                CrashlyticsUtils.INSTANCE.logException(e);
//            } catch (Exception e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//            }
//            Log.i(TAG, "AthkarUpdaterRunnable finished.");
//        };
//
//        // ScreenSaver Thread
//        screenSaverHandlerThread = new HandlerThread("ScreenSaverThread");
//        screenSaverHandlerThread.start();
//        screenSaverHandler = new Handler(screenSaverHandlerThread.getLooper());
//        screenSaverRunnable = () -> {
//            try {
//                while (isScreenSaverRunning && !Thread.currentThread().isInterrupted()) {
//                    Log.e(TAG, "Screensaver thread sleep for " + ScreensaverScheduler.CheckFrequency);
//                    Thread.sleep(ScreensaverScheduler.CheckFrequency * 1000);
//                    final Activity activity = (Activity) context; // Cast context to Activity for foreground check
//                    if (activity != null) { // Ensure activity is not null or destroyed
//                        activity.runOnUiThread(() -> { // UI updates on main thread
//                            HashMap<String, String> prayers = new HashMap<>();
//                            boolean isCurrentlyPraying = false;
//                            boolean isBetweenAzanIkama = false;
//                            boolean isCurrentlyAthkar = false;
//
//                            for (PrayerType prayerType : PrayerType.values()) {
//                                PrayerTime prayerTime = prayerType.prayerTime;
//                                prayers.put(prayerType.getKEY(), prayerTime.getTime());
//                                Log.i(TAG, "isCurrentlyPraying " + (prayerTime.isDuringPrayer()) + "  " + prayerType.getName());
//                                if (prayerType.isFard()) {
//                                    isCurrentlyPraying = (prayerTime.isDuringPrayer()) || isCurrentlyPraying;
//                                    isCurrentlyAthkar = (prayerTime.isNowLockingDuringAthkar()) || isCurrentlyAthkar;
//                                    isBetweenAzanIkama = (prayerTime.isBetweenAzanAndIkama()) || isBetweenAzanIkama;
//                                }
//                            }
//                            screensaver.setPrayers(prayers);
//                            screensaver.setIsPrayingTime(isCurrentlyPraying);
//                            screensaver.setIsBetweenPrayerIkama(isBetweenAzanIkama);
//                            screensaver.isShowingAthkar(isCurrentlyAthkar);
//                            screensaver.checkAndStart(activity);
//                            if (screensaver.isEnabled() && contentDisplayListener != null) {
//                                contentDisplayListener.onHideTextDesign(); // Hide custom text design when screensaver is on
//                            }
//                        });
//                    }
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                CrashlyticsUtils.INSTANCE.logException(e);
//            } catch (Exception e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//            }
//            Log.i(TAG, "ScreenSaverRunnable finished.");
//        };
//
//        // Time Updater Thread
//        timeUpdaterHandlerThread = new HandlerThread("TimeUpdaterThread");
//        timeUpdaterHandlerThread.start();
//        timeUpdaterHandler = new Handler(timeUpdaterHandlerThread.getLooper());
//        timeUpdaterRunnable = () -> {
//            try {
//                while (isTimeUpdaterRunning && !Thread.currentThread().isInterrupted()) {
//                    appLifecycleManager.checkAndRestartAppIfMidnight();
//
//                    String time = Utils.getTimeNow(), timeType = Utils.getTypeTimeNow();
//
//                    // UI updates should go through callback to main thread
//                    if (prayerTimeUpdateListener != null) {
//                        ((Activity)context).runOnUiThread(() -> { // Ensure UI updates on main thread
//                            // Update timeNowTextView, timeNowTypeTextView
//                            // Update athkarTimeTextView etc.
//                            // These should be managed by PrayerTimeUIManager
//                            // Need a new method in PrayerTimeUIManager to update live time and date
//                            prayerTimeUpdateListener.onRefreshDateUI(); // To update date elements that rotate
//                        });
//                    }
//
//                    // Screensaver update
//                    if (screensaver.isEnabled()) {
//                        ((Activity)context).runOnUiThread(() ->
//                                screensaver.updateData(
//                                        time,
//                                        timeType,
//                                        ssMiladiDate,
//                                        ssHijriDate,
//                                        ssDayName,
//                                        null // Assuming announcementManager.getFuneralInNextPrayer() is not directly accessible here
//                                ));
//                    }
//                    Thread.sleep(1000);
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                CrashlyticsUtils.INSTANCE.logException(e);
//            } catch (Exception e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//            }
//            Log.i(TAG, "TimeUpdaterRunnable finished.");
//        };
//
//        // Duha Sunrise Updater Thread
//        duhaSunriseUpdaterHandlerThread = new HandlerThread("DuhaSunriseUpdaterThread");
//        duhaSunriseUpdaterHandlerThread.start();
//        duhaSunriseUpdaterHandler = new Handler(duhaSunriseUpdaterHandlerThread.getLooper());
//        duhaSunriseUpdaterRunnable = () -> {
//            try {
//                while (isDuhaSunriseUpdaterRunning && !Thread.currentThread().isInterrupted()) {
//                    showDuha = !showDuha;
//                    Log.e("DUHA", "Show duha " + showDuha);
//                    if (prayerTimeUpdateListener != null) {
//                        ((Activity)context).runOnUiThread(() -> prayerTimeUpdateListener.onUpdateSunriseDuhaNames());
//                    }
//                    Thread.sleep(10000);
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                CrashlyticsUtils.INSTANCE.logException(e);
//            } catch (Exception e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//            }
//            Log.i(TAG, "DuhaSunriseUpdaterRunnable finished.");
//        };
//
//        // Maintenance Thread
//        maintenanceHandlerThread = new HandlerThread("MaintenanceThread");
//        maintenanceHandlerThread.start();
//        maintenanceHandler = new Handler(maintenanceHandlerThread.getLooper());
//        maintenanceRunnable = () -> {
//            try {
//                while (isMaintenanceRunning && !Thread.currentThread().isInterrupted()) {
//                    Thread.sleep(60 * 1000);
//                    if (isActivityInForeground((Activity) context, MainActivity.class) && !screensaver.isEnabled()) {
//                        // Re-trigger startManagedThreads, ensuring it only posts if not already running
//                        ((Activity)context).runOnUiThread(this::startManagedThreads);
//                    }
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                CrashlyticsUtils.INSTANCE.logException(e);
//            } catch (Exception e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//            }
//            Log.i(TAG, "MaintenanceRunnable finished.");
//        };
//
//        // Text Update HandlerThread (for executeEvery)
//        textUpdateHandlerThread = new HandlerThread("TextUpdateHandlerThread");
//        textUpdateHandlerThread.start();
//        textUpdateHandler = new Handler(textUpdateHandlerThread.getLooper());
//    }
//
//    public void startManagedThreads() {
//        if (mainLogicHandler != null && mainLogicRunnable != null && !isMainLogicRunning) {
//            isMainLogicRunning = true;
//            mainLogicHandler.post(mainLogicRunnable);
//        }
//
//        if (timeUpdaterHandler != null && timeUpdaterRunnable != null && !isTimeUpdaterRunning) {
//            isTimeUpdaterRunning = true;
//            timeUpdaterHandler.post(timeUpdaterRunnable);
//        }
//
//        if (screensaver != null && screenSaverHandler != null && screenSaverRunnable != null && !isScreenSaverRunning) {
//            isScreenSaverRunning = true;
//            screenSaverHandler.post(screenSaverRunnable);
//        }
//
//        if (HawkSettings.getEventsEnabled()) {
//            if (eventsHandler != null && eventsRunnable != null && !isEventsRunning) {
//                isEventsRunning = true;
//                eventsHandler.post(eventsRunnable);
//            }
//        }
//
//        if (HawkSettings.getCurrentTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) {
//            if (athkarUpdaterHandler != null && athkarUpdaterRunnable != null && !isAthkarUpdaterRunning) {
//                isAthkarUpdaterRunning = true;
//                athkarUpdaterHandler.post(athkarUpdaterRunnable);
//            }
//        }
//
//        if (HawkSettings.getShowDuhaSetting() == 3) {
//            if (duhaSunriseUpdaterHandler != null && duhaSunriseUpdaterRunnable != null && !isDuhaSunriseUpdaterRunning) {
//                isDuhaSunriseUpdaterRunning = true;
//                duhaSunriseUpdaterHandler.post(duhaSunriseUpdaterRunnable);
//            }
//        }
//
//        if (maintenanceHandler != null && maintenanceRunnable != null && !isMaintenanceRunning) {
//            isMaintenanceRunning = true;
//            maintenanceHandler.post(maintenanceRunnable);
//        }
//    }
//
//    public void stopManagedThreads() {
//        isMainLogicRunning = false;
//        if (mainLogicHandler != null) mainLogicHandler.removeCallbacks(mainLogicRunnable);
//
//        isTimeUpdaterRunning = false;
//        if (timeUpdaterHandler != null) timeUpdaterHandler.removeCallbacks(timeUpdaterRunnable);
//
//        if (textUpdateHandler != null && currentTextUpdateRunnable != null) {
//            textUpdateHandler.removeCallbacks(currentTextUpdateRunnable);
//            currentTextUpdateRunnable = null;
//        }
//
//        isScreenSaverRunning = false;
//        if (screenSaverHandler != null) screenSaverHandler.removeCallbacks(screenSaverRunnable);
//
//        isEventsRunning = false;
//        if (eventsHandler != null) eventsHandler.removeCallbacks(eventsRunnable);
//
//        isAthkarUpdaterRunning = false;
//        if (athkarUpdaterHandler != null) athkarUpdaterHandler.removeCallbacks(athkarUpdaterRunnable);
//
//        isDuhaSunriseUpdaterRunning = false;
//        if (duhaSunriseUpdaterHandler != null) duhaSunriseUpdaterHandler.removeCallbacks(duhaSunriseUpdaterRunnable);
//
//        isMaintenanceRunning = false;
//        if (maintenanceHandler != null) maintenanceHandler.removeCallbacks(maintenanceRunnable);
//    }
//
//    public void quitAllHandlerThreads() {
//        quitHandlerThread(mainLogicHandlerThread);
//        quitHandlerThread(timeUpdaterHandlerThread);
//        quitHandlerThread(textUpdateHandlerThread);
//        quitHandlerThread(screenSaverHandlerThread);
//        quitHandlerThread(eventsHandlerThread);
//        quitHandlerThread(athkarUpdaterHandlerThread);
//        quitHandlerThread(duhaSunriseUpdaterHandlerThread);
//        quitHandlerThread(maintenanceHandlerThread);
//    }
//
//    private void quitHandlerThread(HandlerThread thread) {
//        if (thread != null) {
//            thread.quitSafely();
//            try {
//                thread.join(1000); // Wait for the thread to finish
//            } catch (InterruptedException e) {
//                CrashlyticsUtils.INSTANCE.logException(e);
//                Thread.currentThread().interrupt();
//            }
//        }
//    }
//
//    // --- Core Logic Methods (Moved from MainActivity) ---
//
//    private void getPrayerTimesThisYear() {
//        isPrayerListUpdating.set(true);
//        // showLoadingDialog() should be a callback to MainActivity
//        AlrabeeaTimesRequests.getPrayerTimesWithDefault("" + year, ConstantsOfApp.BASE_URL_ALADHAN,
//                new OnCompleteListener<PrayerApi, String>() {
//                    @Override
//                    public void onSuccess(PrayerApi prayers) {
//                        if (prayers != null) {
//                            Utils.putPrayerTimesForYear(prayers);
//                        }
//                        // hideLoadingDialog() should be a callback to MainActivity
//                        isPrayerListUpdating.set(false);
//                    }
//
//                    @Override
//                    public void onFail(String object) {
//                        // hideLoadingDialog() should be a callback to MainActivity
//                        isPrayerListUpdating.set(false);
//                    }
//                });
//    }
//
//    public void selectNextPrayer() throws InterruptedException {
//        // Notify UI to display prayer times layout first
//        if (contentDisplayListener != null) {
//            contentDisplayListener.onDisplayPrayerTimes();
//            contentDisplayListener.onShowTextDesign(); // Re-show text design
//        }
//
//        boolean settled = false;
//        for (PrayerType prayerType : PrayerType.values()) {
//            PrayerTime prayerTime = prayerType.prayerTime;
//
//            if (prayerTime.isNowLockingDuringAzan()) {
//                Log.e(TAG, "isNowLockingDuringAzan " + prayerTime.prayerType.toString());
//                if (contentDisplayListener != null) {
//                    contentDisplayListener.onDisplayAnnouncement(new com.arapeak.alrbea.AnnouncementMessage(com.arapeak.alrbea.Enum.AnnouncementType.AZAN, prayerType));
//                }
//                sleepTimeUntilNextRefresh = prayerTime.getTimeUntilUnlockAzan();
//                Thread.sleep(sleepTimeUntilNextRefresh - System.currentTimeMillis()); // Wait till unlock azan
//                settled = true;
//            } else if (prayerTime.isNowAnnouncingForIkama()) {
//                Log.e(TAG, "isNowAnnouncingForIkama " + prayerTime.prayerType.toString());
//                long ikama = prayerTime.getIkamaTime();
//                boolean isLessThanMinute = (ikama - System.currentTimeMillis()) <= ConstantsOfApp.MINUTES_MILLI_SECOND;
//
//                com.arapeak.alrbea.AnnouncementMessage message = new com.arapeak.alrbea.AnnouncementMessage();
//                message.prayer = prayerType;
//
//                if (isLessThanMinute) {
//                    sleepTimeUntilNextRefresh = ikama;
//                    message.type = com.arapeak.alrbea.Enum.AnnouncementType.MINUTE_BEFORE_IKAMA;
//                } else {
//                    if (isJomaa && prayerType == PrayerType.Dhuhr) {
//                        // Special handling for Khutba might involve a different announcement type or message
//                    }
//                    sleepTimeUntilNextRefresh = ikama - ConstantsOfApp.MINUTES_MILLI_SECOND;
//                    message.type = com.arapeak.alrbea.Enum.AnnouncementType.BETWEEN_AZAN_AND_IKAMA;
//                }
//
//                if (contentDisplayListener != null) {
//                    contentDisplayListener.onDisplayAnnouncement(message);
//                }
//                Thread.sleep(sleepTimeUntilNextRefresh - System.currentTimeMillis());
//                settled = true;
//            } else if (prayerTime.isNowLockDuringPrayer()) {
//                Log.e(TAG, "isNowLockDuringPrayer " + prayerTime.prayerType.toString());
//                sleepTimeUntilNextRefresh = prayerTime.getTimeUntilUnlockPrayer();
//                if (contentDisplayListener != null) {
//                    contentDisplayListener.onDisplayAnnouncement(new com.arapeak.alrbea.AnnouncementMessage(com.arapeak.alrbea.Enum.AnnouncementType.PRAY, prayerType));
//                }
//                Thread.sleep(sleepTimeUntilNextRefresh - System.currentTimeMillis());
//                settled = true;
//            } else if (prayerTime.isNextAzan()) {
//                Log.e(TAG, "isNextAzan " + prayerType.toString());
//                sleepTimeUntilNextRefresh = getCurrentTimePlusMinute();
//                String timeRemainingNextPrayer = prayerTime.getTimeRemainForAzan(longTextInRemainForPrayer);
//                if (prayerTimeUpdateListener != null) {
//                    prayerTimeUpdateListener.onNextPrayerTimeUpdate(prayerType, timeRemainingNextPrayer, longTextInRemainForPrayer);
//                }
//                settled = true;
//            } else if (prayerTime.isNextIkama()) {
//                Log.e(TAG, "isNextIkama " + prayerType.toString());
//                sleepTimeUntilNextRefresh = getCurrentTimePlusMinute();
//                String timeRemainingForIkama = prayerTime.getTimeRemainForIkama();
//                if (prayerTimeUpdateListener != null) {
//                    prayerTimeUpdateListener.onNextPrayerTimeUpdate(prayerType, timeRemainingForIkama, longTextInRemainForPrayer);
//                }
//                settled = true;
//            } else if (prayerTime.isNowLockingDuringAthkar()) {
//                Log.e(TAG, "isNowLockingDuringAthkar ");
//                sleepTimeUntilNextRefresh = prayerTime.getTimeUntilUnlockAthkar();
//                if (contentDisplayListener != null) {
//                    contentDisplayListener.onDisplayAthkar(AthkarType.AfterPrayer, prayerType);
//                }
//                settled = true;
//            } else if (PrayerTime.isNowLockingDuringAthkar(AthkarType.MorningAthkar)) {
//                Log.e(TAG, "isNowLockingDuringAthkar (Morning)");
//                sleepTimeUntilNextRefresh = PrayerTime.getTimeUntilUnlockAthkar(AthkarType.MorningAthkar);
//                if (contentDisplayListener != null) {
//                    contentDisplayListener.onDisplayAthkar(AthkarType.MorningAthkar, null);
//                }
//                settled = true;
//            } else if (PrayerTime.isNowLockingDuringAthkar(AthkarType.EveningAthkar)) {
//                Log.e(TAG, "isNowLockingDuringAthkar (Evening)");
//                sleepTimeUntilNextRefresh = PrayerTime.getTimeUntilUnlockAthkar(AthkarType.EveningAthkar);
//                if (contentDisplayListener != null) {
//                    contentDisplayListener.onDisplayAthkar(AthkarType.EveningAthkar, null);
//                }
//                settled = true;
//            }
//            if (settled) break;
//        }
//
//        if (!settled) {
//            LastMaghrib = PrayerType.Maghrib.prayerTime.getAzanTime();
//            // Default sleep if no prayer found to be active/next (e.g., after Isha till Fajr)
//            // sleepTimeUntilNextRefresh = calculateSleepUntilFajrOrNextDay();
//            sleepTimeUntilNextRefresh = System.currentTimeMillis() + DAYS_MILLI_SECOND; // Sleep until next day or some interval
//            Log.e(TAG, "No prayer active/next, sleeping until next day or determined time.");
//        }
//
//        // Photo Gallery logic (runs after prayer logic)
//        if (HawkSettings.isPhotoGalleryEnabled()) {
//            long selectedId = HawkSettings.getSelectedPhotoGalleryId();
//            if (selectedId >= 0) {
//                com.arapeak.alrbea.Model.PhotoGallery gallery = Repositories.getPhotoGalleryRepository(context).get(selectedId);
//                if (gallery != null && gallery.images != null && !gallery.images.isEmpty()) {
//                    // This is where you'd decide when to show the gallery, currently only after prayer times
//                    // if (currentDisplayType == DisplayTypes.PrayerTimes && gallery.isEnabledOnPrayerTimesScreen())
//                    try {
//                        Thread.sleep(gallery.getPrayerTimesDuration().getTime()); // Wait before showing gallery
//                        if (contentDisplayListener != null) {
//                            contentDisplayListener.onDisplayPhotoGallery(gallery);
//                        }
//                        Thread.sleep(gallery.getSingleImageDuration().getTime()); // Display single image for this duration
//                        sleepTimeUntilNextRefresh = 0; // Reset sleep time, let main loop recalculate
//                    } catch (InterruptedException i) {
//                        throw i;
//                    } catch (Exception e) {
//                        CrashlyticsUtils.INSTANCE.logException(e);
//                    }
//                }
//            }
//        }
//    }
//
//
//    private long getCurrentTimePlusMinute() {
//        long current = System.currentTimeMillis();
//        long secondRemain = current % ConstantsOfApp.MINUTES_MILLI_SECOND;
//        return current - secondRemain + ConstantsOfApp.MINUTES_MILLI_SECOND;
//    }
//
//    private boolean isAllowedToShowEvent() {
//        // This requires knowing the current display type. Pass currentDisplayType to BackgroundTaskManager
//        // Or BackgroundTaskManager should be the source of truth for display type and notify UI
//        // For simplicity, let's assume `lastDisplayed` is managed by MainActiviy and passed back to BTM
//        // This is a common challenge when separating logic; dependencies need careful management.
//        // For now, let's assume MainActivity sends `lastDisplayed` to this manager.
//        return true; // Placeholder
//    }
//
//    private boolean eventDayArrived(Event event) {
//        UmmalquraCalendar calendar = Utils.getUmmalquraCalendar();
//        return (calendar.after(event.sCal) && calendar.before(event.eCal));
//    }
//
//    // Callbacks from UI, e.g., when theme changes, to update internal flags
//    public void updateLongTextInRemainForPrayer(boolean value) {
//        this.longTextInRemainForPrayer = value;
//    }
//
//    public void updateJomaaStatus(boolean isJomaa) {
//        this.isJomaa = isJomaa;
//    }
//
//    public boolean getShowDuhaStatus() {
//        return showDuha;
//    }
//
//    public ScreensaverScheduler getScreensaverScheduler() {
//        return screensaver;
//    }
//
//
//    // This method will now be called by MainActivity based on theme settings
//    // The actual update logic for TextUpdateThread has to be passed here from MainActivity
//    public void executeEvery(Runnable exec, int seconds) {
//        if (textUpdateHandler == null || textUpdateHandlerThread == null || !textUpdateHandlerThread.isAlive()) {
//            if (textUpdateHandlerThread == null) { // Check if thread object itself is null
//                textUpdateHandlerThread = new HandlerThread("TextUpdateHandlerThread");
//                textUpdateHandlerThread.start();
//                textUpdateHandler = new Handler(textUpdateHandlerThread.getLooper());
//                Log.w(TAG, "executeEvery: Re-initialized textUpdateHandlerThread.");
//            } else if (!textUpdateHandlerThread.isAlive()) { // Check if thread is not alive but object exists
//                // Attempt to restart if it's dead but not null
//                textUpdateHandlerThread.quitSafely(); // Ensure it's cleanly shut down if somehow stuck
//                try {
//                    textUpdateHandlerThread.join(500); // Give it a moment to finish
//                } catch (InterruptedException ignored) { Thread.currentThread().interrupt(); }
//                textUpdateHandlerThread = new HandlerThread("TextUpdateHandlerThread");
//                textUpdateHandlerThread.start();
//                textUpdateHandler = new Handler(textUpdateHandlerThread.getLooper());
//                Log.w(TAG, "executeEvery: Restarted textUpdateHandlerThread.");
//            }
//            else {
//                // If it's alive but handler is null (shouldn't happen if thread is properly started)
//                if (textUpdateHandler == null) {
//                    textUpdateHandler = new Handler(textUpdateHandlerThread.getLooper());
//                    Log.w(TAG, "executeEvery: Re-obtained handler for active textUpdateHandlerThread.");
//                }
//            }
//
//            if (textUpdateHandler == null) {
//                Log.e(TAG, "executeEvery: textUpdateHandler is still null after attempting re-initialization.");
//                return;
//            }
//        }
//
//        if (currentTextUpdateRunnable != null) {
//            textUpdateHandler.removeCallbacks(currentTextUpdateRunnable);
//        }
//
//        currentTextUpdateRunnable = new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    exec.run();
//                    if (textUpdateHandler != null && textUpdateHandlerThread != null &&
//                            textUpdateHandlerThread.isAlive() && !Thread.currentThread().isInterrupted()) {
//                        textUpdateHandler.postDelayed(this, seconds * 1000L);
//                    } else {
//                        Log.i(TAG, "executeEvery's repeating task will not reschedule.");
//                    }
//                } catch (Exception e) {
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                    Log.e(TAG, "Exception in executeEvery's runnable, task will not reschedule.", e);
//                }
//            }
//        };
//        textUpdateHandler.post(currentTextUpdateRunnable);
//    }
//
//    // Getters for current date info (used by UI for display)
//    public UmmalquraCalendar getHijriCalendar() { return hijriCalendar; }
//    public GregorianCalendar getGregorianCalendar() { return gregorianCalendar; }
//    public String getGYear() { return String.valueOf(gregorianCalendar.get(Calendar.YEAR)); }
//    public String getGMonthAr() { return gregorianCalendar.getDisplayName(Calendar.MONTH, Calendar.LONG, new java.util.Locale("ar")); }
//    public String getGMonthEn() { return gregorianCalendar.getDisplayName(Calendar.MONTH, Calendar.LONG, new java.util.Locale("en")); }
//    public String getGMonthNum() { return String.valueOf(gregorianCalendar.get(Calendar.MONTH) + 1); }
//    public String getGDayNum() { return String.valueOf(gregorianCalendar.get(Calendar.DAY_OF_MONTH)); }
//    public String getGDayNameAr() { return gregorianCalendar.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, new java.util.Locale("ar")); }
//    public String getGDayNameEn() { return gregorianCalendar.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, new java.util.Locale("en")); }
//    public String getHYear() { return String.valueOf(hijriCalendar.get(Calendar.YEAR)); }
//    public String getHMonthAr() { return hijriCalendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, new java.util.Locale("ar")); }
//    public String getHMonthEn() { return context.getResources().getStringArray(R.array.hijri_months_en)[hijriCalendar.get(Calendar.MONTH)]; }
//    public String getHDayNum() { return String.valueOf(hijriCalendar.get(Calendar.DAY_OF_MONTH)); }
//    public boolean isJomaa() { return isJomaa; }
//
//    public void setSsMiladiDate(String ssMiladiDate) { this.ssMiladiDate = ssMiladiDate; }
//    public void setSsHijriDate(String ssHijriDate) { this.ssHijriDate = ssHijriDate; }
//    public void setSsDayName(String ssDayName) { this.ssDayName = ssDayName; }
//
//    public ScreensaverScheduler getScreensaver() {
//        return screensaver;
//    }
//}
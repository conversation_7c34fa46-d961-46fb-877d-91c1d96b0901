apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'realm-android'
apply plugin: 'com.google.firebase.crashlytics'

// 86659
android {

    defaultConfig {
        applicationId "com.alrbea.prayer.client"
        minSdkVersion 23
        compileSdkVersion 35
        targetSdkVersion 35
        versionCode 30937
        versionName "4.0.9"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        vectorDrawables.useSupportLibrary = true

//        viewBinding {
//            enabled = true
//        }
    }

//    buildFeatures {
//        viewBinding true
//        dataBinding true
//    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    signingConfigs {
        release {
            storeFile file('keystore.jks')
            storePassword ALREBEA_KEY_PASSWORD
            keyAlias ALREBEA_KEY_ALIAS
            keyPassword ALREBEA_KEY_PASSWORD
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            debuggable true
        }
    }


    splits {
        abi {
            enable true
            reset()
            include 'armeabi-v7a', 'x86_64', 'arm64-v8a'
            universalApk true
        }
    }


} packagingOptions {
    exclude 'META-INF/proguard/androidx-annotations.pro'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':core_ktx')
    implementation project(':libraries:RootCommands')
    implementation 'androidx.activity:activity:1.10.1'
    implementation 'androidx.lifecycle:lifecycle-service:2.9.0'
//    implementation 'com.android.identity:identity-jvm:202408.1'

    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.annotation:annotation:1.3.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.palette:palette:1.0.0'
    implementation 'com.intuit.sdp:sdp-android:1.1.1'


//    implementation 'com.google.android.play:core:1.5.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'com.google.android.gms:play-services-location:16.0.0'
    implementation 'com.google.android.gms:play-services-gcm:16.1.0'
//    implementation 'com.google.android.gms:play-services-maps:18.2.0'


    implementation platform('com.google.firebase:firebase-bom:33.1.2')
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-functions'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-database'
    implementation 'com.google.firebase:firebase-config'
    implementation 'com.google.firebase:firebase-storage'


    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    androidTestImplementation 'androidx.test.uiautomator:uiautomator:2.2.0'


    implementation 'com.github.msarhan:ummalqura-calendar:2.0.2'
    implementation 'com.batoulapps.adhan:adhan2:0.0.4'


    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.19'
    implementation 'com.nostra13.universalimageloader:universal-image-loader:1.9.4'
    implementation 'com.squareup.picasso:picasso:2.8'


    implementation "com.github.skydoves:colorpickerview:2.3.0"

    implementation 'com.wdullaer:materialdatetimepicker:3.6.3'
    implementation "com.orhanobut:hawk:2.0.1"
    implementation 'com.mindorks.android:prdownloader:0.6.0'
    implementation 'com.android.volley:volley:1.2.1'
    implementation 'com.amitshekhar.android:jackson-android-networking:1.0.2'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.github.tapadoo:alerter:7.2.4'
    implementation 'com.github.hedzr:android-file-chooser:v1.1.14'
    implementation 'pub.devrel:easypermissions:3.0.0'
    implementation 'com.budiyev.android:circular-progress-bar:1.2.2'
    implementation 'com.mikhaellopez:circularprogressbar:2.0.0'
    implementation 'com.intuit.sdp:sdp-android:1.1.1'
    implementation 'com.intuit.ssp:ssp-android:1.1.1'

    implementation("com.github.tonyofrancis.Fetch:fetch2:3.3.0")

}

android {
    compileOptions {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
    }
    namespace 'com.arapeak.alrbea'
    defaultConfig {
        minSdkVersion 23
    }

}

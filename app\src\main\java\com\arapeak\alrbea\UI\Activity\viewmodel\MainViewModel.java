package com.arapeak.alrbea.UI.Activity.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.PrayerUtils;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.Repositories;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ViewModel for MainActivity following MVVM architecture
 * Handles all business logic and data management
 */
public class MainViewModel extends AndroidViewModel {

    private static final String TAG = "MainViewModel";

    // LiveData for UI state
    private final MutableLiveData<String> currentTime = new MutableLiveData<>();
    private final MutableLiveData<String> currentTimeType = new MutableLiveData<>();
    private final MutableLiveData<String> currentDate = new MutableLiveData<>();
    private final MutableLiveData<String> hijriDate = new MutableLiveData<>();
    private final MutableLiveData<String> gregorianDate = new MutableLiveData<>();
    private final MutableLiveData<TimingsAlrabeeaTimes> prayerTimes = new MutableLiveData<>();
    private final MutableLiveData<PrayerType> nextPrayer = new MutableLiveData<>();
    private final MutableLiveData<String> remainingTime = new MutableLiveData<>();
    private final MutableLiveData<String> currentAthkar = new MutableLiveData<>();
    private final MutableLiveData<List<Event>> activeEvents = new MutableLiveData<>();
    private final MutableLiveData<PhotoGallery> currentPhotoGallery = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<UITheme> currentTheme = new MutableLiveData<>();

    // Thread management
    private ThreadManager threadManager;
    private final AtomicBoolean isPrayerListUpdating = new AtomicBoolean(false);

    // Data fields
    private TimingsAlrabeeaTimes timingsAlrabeeaTimes;
    private int year, month, day;
    private int lastAthkarIndex = 0;
    private long tempTime = 0;
    private GregorianCalendar gregorianCalendar = new GregorianCalendar();

    public MainViewModel(@NonNull Application application) {
        super(application);
        initializeViewModel();
    }

    private void initializeViewModel() {
        try {
            Log.d(TAG, "Initializing MainViewModel");

            // Initialize thread manager
            threadManager = new ThreadManager();

            // Initialize current theme
            currentTheme.setValue(HawkSettings.getCurrentTheme());

            // Initialize time
            tempTime = System.currentTimeMillis();
            updateCurrentDateTime();

            // Start background tasks
            startTimeUpdateTask();
            startPrayerUpdateTask();
            startAthkarUpdateTask();
            startEventsUpdateTask();

            Log.d(TAG, "MainViewModel initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing MainViewModel", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            errorMessage.setValue("Failed to initialize app: " + e.getMessage());
        }
    }

    // Public getters for LiveData
    public LiveData<String> getCurrentTime() {
        return currentTime;
    }

    public LiveData<String> getCurrentTimeType() {
        return currentTimeType;
    }

    public LiveData<String> getCurrentDate() {
        return currentDate;
    }

    public LiveData<String> getHijriDate() {
        return hijriDate;
    }

    public LiveData<String> getGregorianDate() {
        return gregorianDate;
    }

    public LiveData<TimingsAlrabeeaTimes> getPrayerTimes() {
        return prayerTimes;
    }

    public LiveData<PrayerType> getNextPrayer() {
        return nextPrayer;
    }

    public LiveData<String> getRemainingTime() {
        return remainingTime;
    }

    public LiveData<String> getCurrentAthkar() {
        return currentAthkar;
    }

    public LiveData<List<Event>> getActiveEvents() {
        return activeEvents;
    }

    public LiveData<PhotoGallery> getCurrentPhotoGallery() {
        return currentPhotoGallery;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<UITheme> getCurrentTheme() {
        return currentTheme;
    }

    /**
     * Update current date and time
     */
    private void updateCurrentDateTime() {
        try {
            String time = Utils.getTimeNow();
            String timeType = Utils.getTypeTimeNow();

            currentTime.postValue(time);
            currentTimeType.postValue(timeType);

            // Update date information
            updateDateInformation();

        } catch (Exception e) {
            Log.e(TAG, "Error updating current date time", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update date information (Hijri and Gregorian)
     */
    private void updateDateInformation() {
        try {
            year = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.YEAR, tempTime));
            month = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.MONTH, tempTime));
            day = Integer.parseInt(Utils.getEnglishDateTime(ConstantsOfApp.DAY, tempTime));

            gregorianCalendar = new GregorianCalendar();

            // Update prayer times if needed
            if (timingsAlrabeeaTimes == null ||
                    timingsAlrabeeaTimes.getIntDay() != day ||
                    timingsAlrabeeaTimes.getIntMonth() != month) {
                updatePrayerTimes();
            }

            // Update date displays
            updateDateDisplays();

        } catch (Exception e) {
            Log.e(TAG, "Error updating date information", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update prayer times for current date
     */
    private void updatePrayerTimes() {
        if (isPrayerListUpdating.get()) {
            return;
        }

        try {
            isPrayerListUpdating.set(true);

            TimingsAlrabeeaTimes newTiming = PrayerUtils.getTiming(year, month, day);
            if (newTiming != null) {
                timingsAlrabeeaTimes = newTiming;
                prayerTimes.postValue(timingsAlrabeeaTimes);

                // Update next prayer
                updateNextPrayer();
            } else {
                // Handle case where prayer times are not available
                errorMessage.postValue("Prayer times not available for current date");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error updating prayer times", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            errorMessage.postValue("Failed to update prayer times: " + e.getMessage());
        } finally {
            isPrayerListUpdating.set(false);
        }
    }

    /**
     * Update next prayer and remaining time
     */
    private void updateNextPrayer() {
        try {
            if (timingsAlrabeeaTimes == null) {
                return;
            }

            // Logic to determine next prayer
            PrayerType next = determineNextPrayer();
            nextPrayer.postValue(next);

            // Calculate remaining time
            String remaining = calculateRemainingTime(next);
            remainingTime.postValue(remaining);

        } catch (Exception e) {
            Log.e(TAG, "Error updating next prayer", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Determine the next prayer based on current time
     */
    private PrayerType determineNextPrayer() {
        // Implementation to determine next prayer
        // This is a simplified version - you may need to adapt based on your existing
        // logic
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);
        int currentMinute = now.get(Calendar.MINUTE);

        // Simple logic - you should replace with your existing prayer time logic
        if (currentHour < 5)
            return PrayerType.Fajr;
        if (currentHour < 12)
            return PrayerType.Dhuhr;
        if (currentHour < 15)
            return PrayerType.Asr;
        if (currentHour < 18)
            return PrayerType.Maghrib;
        return PrayerType.Isha;
    }

    /**
     * Calculate remaining time to next prayer
     */
    private String calculateRemainingTime(PrayerType nextPrayerType) {
        // Implementation to calculate remaining time
        // This should use your existing time calculation logic
        return "00:00"; // Placeholder
    }

    /**
     * Update date displays
     */
    private void updateDateDisplays() {
        try {
            // Update current date
            String dateStr = Utils.getDateNow();
            currentDate.postValue(dateStr);

            // Update Hijri date
            String hijriStr = Utils.getHijriDateNow();
            hijriDate.postValue(hijriStr);

            // Update Gregorian date
            String gregorianStr = Utils.getGregorianDateNow();
            gregorianDate.postValue(gregorianStr);

        } catch (Exception e) {
            Log.e(TAG, "Error updating date displays", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Start time update background task
     */
    private void startTimeUpdateTask() {
        threadManager.scheduleRepeatingTask("TimeUpdate", new ThreadManager.ScheduledTask() {
            @Override
            public void execute() {
                updateCurrentDateTime();

                // Check for midnight to restart app
                if (isMidnight()) {
                    // Handle midnight restart logic
                    handleMidnightRestart();
                }
            }

            @Override
            public long getNextDelay() {
                return 1000; // Update every second
            }
        }, 1000);
    }

    /**
     * Start prayer times update task
     */
    private void startPrayerUpdateTask() {
        threadManager.scheduleRepeatingTask("PrayerUpdate", new ThreadManager.ScheduledTask() {
            @Override
            public void execute() {
                if (!isPrayerListUpdating.get()) {
                    updateDateInformation();
                }
            }

            @Override
            public long getNextDelay() {
                return 60000; // Update every minute
            }
        }, 5000); // Start after 5 seconds
    }

    /**
     * Start Athkar update task
     */
    private void startAthkarUpdateTask() {
        if (HawkSettings.getCurrentTheme() == UITheme.BROWN_NEW_3) {
            threadManager.scheduleRepeatingTask("AthkarUpdate", new ThreadManager.ScheduledTask() {
                @Override
                public void execute() {
                    updateAthkar();
                }

                @Override
                public long getNextDelay() {
                    return 45000; // Update every 45 seconds
                }
            }, 10000); // Start after 10 seconds
        }
    }

    /**
     * Start events update task
     */
    private void startEventsUpdateTask() {
        if (HawkSettings.getEventsEnabled()) {
            threadManager.scheduleRepeatingTask("EventsUpdate", new ThreadManager.ScheduledTask() {
                @Override
                public void execute() {
                    updateActiveEvents();
                }

                @Override
                public long getNextDelay() {
                    return 600000; // Update every 10 minutes
                }
            }, 15000); // Start after 15 seconds
        }
    }

    /**
     * Update current Athkar
     */
    private void updateAthkar() {
        try {
            // This should be moved to a repository class
            String[] athkars = getApplication().getResources().getStringArray(com.arapeak.alrbea.R.array.athkars);
            if (athkars.length > 0) {
                if (lastAthkarIndex >= athkars.length) {
                    lastAthkarIndex = 0;
                }
                currentAthkar.postValue(athkars[lastAthkarIndex]);
                lastAthkarIndex++;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating athkar", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Update active events
     */
    private void updateActiveEvents() {
        try {
            List<Event> events = new ArrayList<>();
            for (Event event : Repositories.getEventDb().getAllEnabled()) {
                if (isEventActive(event)) {
                    events.add(event);
                }
            }
            activeEvents.postValue(events);
        } catch (Exception e) {
            Log.e(TAG, "Error updating active events", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    /**
     * Check if event is currently active
     */
    private boolean isEventActive(Event event) {
        // Implementation to check if event should be displayed
        // This should use your existing event logic
        return false; // Placeholder
    }

    /**
     * Check if it's midnight
     */
    private boolean isMidnight() {
        Calendar now = Calendar.getInstance();
        return now.get(Calendar.HOUR_OF_DAY) == 0 && now.get(Calendar.MINUTE) == 0;
    }

    /**
     * Handle midnight restart logic
     */
    private void handleMidnightRestart() {
        // Implementation for midnight restart
        // This should trigger app restart logic
    }

    /**
     * Refresh all data
     */
    public void refreshData() {
        try {
            isLoading.setValue(true);
            tempTime = System.currentTimeMillis();
            updateCurrentDateTime();
            updatePrayerTimes();
        } catch (Exception e) {
            Log.e(TAG, "Error refreshing data", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            errorMessage.setValue("Failed to refresh data: " + e.getMessage());
        } finally {
            isLoading.setValue(false);
        }
    }

    /**
     * Update theme
     */
    public void updateTheme(UITheme newTheme) {
        try {
            currentTheme.setValue(newTheme);
            HawkSettings.setCurrentTheme(newTheme);

            // Restart Athkar task if needed
            if (newTheme == UITheme.BROWN_NEW_3) {
                startAthkarUpdateTask();
            } else {
                threadManager.stopTask("AthkarUpdate");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating theme", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            errorMessage.setValue("Failed to update theme: " + e.getMessage());
        }
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        try {
            Log.d(TAG, "Cleaning up MainViewModel");

            // Stop all background tasks
            if (threadManager != null) {
                threadManager.shutdown();
                threadManager = null;
            }

            // Clear references
            timingsAlrabeeaTimes = null;
            gregorianCalendar = null;

            Log.d(TAG, "MainViewModel cleaned up successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up MainViewModel", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}
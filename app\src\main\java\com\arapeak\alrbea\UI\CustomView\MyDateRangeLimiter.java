package com.arapeak.alrbea.UI.CustomView;

import android.os.Parcel;
import android.os.Parcelable;

import com.wdullaer.materialdatetimepicker.date.DateRangeLimiter;

import java.util.Calendar;

public class MyDateRangeLimiter implements DateRangeLimiter {


    public static final Parcelable.Creator<MyDateRangeLimiter> CREATOR
            = new Parcelable.Creator<MyDateRangeLimiter>() {
        public MyDateRangeLimiter createFromParcel(Parcel in) {
            return new MyDateRangeLimiter(in);
        }

        public MyDateRangeLimiter[] newArray(int size) {
            return new MyDateRangeLimiter[size];
        }
    };

    public MyDateRangeLimiter(Parcel in) {

    }

    @Override
    public int getMinYear() {
        return 1900;
    }

    @Override
    public int getMaxYear() {
        return 2100;
    }

    @Override
    public Calendar getStartDate() {
        Calendar output = Calendar.getInstance();
        output.set(Calendar.YEAR, 1900);
        output.set(Calendar.DAY_OF_MONTH, 1);
        output.set(Calendar.MONTH, Calendar.JANUARY);
        return output;
    }

    @Override
    public Calendar getEndDate() {
        Calendar output = Calendar.getInstance();
        output.set(Calendar.YEAR, 2100);
        output.set(Calendar.DAY_OF_MONTH, 1);
        output.set(Calendar.MONTH, Calendar.JANUARY);
        return output;
    }

    @Override
    public boolean isOutOfRange(int year, int month, int day) {
        return false;
    }

   /* @Override
    public void writeToParcel(Parcel out) {

    }*/
/*
    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

    }*/

    @Override
    public Calendar setToNearestDate(Calendar day) {
        return day;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

    }
}
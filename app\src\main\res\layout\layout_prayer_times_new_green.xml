<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:orientation="vertical"
    tools:background="@color/colorblack">


    <LinearLayout
        android:id="@+id/content_LinearLayout_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            style="@style/LinearLayoutPrayerTimeRow.blue_lett"
            android:layout_marginBottom="@dimen/_5sdp"
            android:background="@android:color/transparent">

            <TextView
                style="@style/PrayerTimeLayout.blue_lett.Right"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_30sdp"
                android:layout_marginBottom="0dp"
                android:background="@drawable/background_prays_head_new_green"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="@string/adhaan"
                android:textColor="@color/white"
                android:textSize="@dimen/_18sdp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                style="@style/PrayerTimeLayout.blue_lett.center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_30sdp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:background="@drawable/background_prays_head_new_green"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="@string/prayer"
                android:textColor="@color/white"
                android:textSize="@dimen/_18sdp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                style="@style/PrayerTimeLayout.blue_lett.Left"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_30sdp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:background="@drawable/background_prays_head_new_green"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="@string/ikama"
                android:textColor="@color/white"
                android:textSize="@dimen/_18sdp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentFajr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.new_green"
            android:layout_marginTop="0dp"
            android:background="@drawable/background_prays_new_green_now">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/fajrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/fajrTime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time"
                        android:textColor="@color/new_green_2" />

                    <TextView
                        android:id="@+id/fajrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType"
                        android:textColor="@color/new_green_2" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.new_green.center">

                <TextView
                    android:id="@+id/fajr_TextView_MainActivity"
                    style="@style/TimeTextView.new_green.TimeNameAR"
                    android:text="@string/fajr" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_fajr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/fajrATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/fajrATime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time"
                        android:textColor="@color/new_green_2" />

                    <TextView
                        android:id="@+id/fajrATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType"
                        android:textColor="@color/new_green_2" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.new_green">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/dhuhrTime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.new_green.center">

                <TextView
                    android:id="@+id/dhuhr_TextView_MainActivity"
                    style="@style/TimeTextView.new_green.TimeNameAR"
                    android:text="@string/dhuhr" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_dhur"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/dhuhrATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/dhuhrATime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/dhuhrATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentAsr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.new_green">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/asrTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/asrTime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/asrTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.new_green.center">

                <TextView
                    android:id="@+id/asr_TextView_MainActivity"
                    style="@style/TimeTextView.new_green.TimeNameAR"
                    android:text="@string/asr" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_asr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/asrATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/asrATime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/asrATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.new_green">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/maghribTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/maghribTime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/maghribTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.new_green.center">

                <TextView
                    android:id="@+id/maghrib_TextView_MainActivity"
                    style="@style/TimeTextView.new_green.TimeNameAR"
                    android:text="@string/maghrib" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_maghrib"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/maghribATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/maghribATime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/maghribATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentIsha_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow.new_green">

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Right">

                <LinearLayout
                    android:id="@+id/ishaTime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/ishaTime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/ishaTimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.new_green.center">

                <TextView
                    android:id="@+id/isha_TextView_MainActivity"
                    style="@style/TimeTextView.new_green.TimeNameAR"
                    android:text="@string/isha" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_isha"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.blue_lett.Left">

                <LinearLayout
                    android:id="@+id/ishaATime_LinearLayout_MainActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/ishaATime_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.Time" />

                    <TextView
                        android:id="@+id/ishaATimeType_TextView_MainActivity"
                        style="@style/TimeTextView.new_green.TimeType" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_20sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/sunrise_TextView_MainActivity"
                style="@style/TimeTextView.new_green.TimeNameAR"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:text="@string/duha"
                android:textColor="@color/white" />

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp">

                <TextView
                    android:id="@+id/sunriseTime_TextView_MainActivity"
                    style="@style/TimeTextView.new_green.Time"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp" />


                <TextView
                    android:id="@+id/sunriseTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_lett.TimeType"
                    android:textSize="@dimen/_12sdp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
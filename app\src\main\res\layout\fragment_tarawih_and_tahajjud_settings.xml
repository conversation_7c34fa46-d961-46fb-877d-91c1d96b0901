<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:orientation="vertical"
    tools:context=".UI.Fragment.settings.content.prayerTimes.content.content.TarawihAndTahajjudSettingsFragment"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="20dp">

        <TextView
            android:id="@+id/tv_prayer_time_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_14sdp"
            android:paddingStart="14dp"
            android:paddingEnd="14dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="الإعدادات الرئيسية">

        </TextView>



        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/isEnable_CheckBox_TarawihAndTahajjudSettingsFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:text="@string/active"
            android:textColor="@android:color/black"
            android:textSize="14sp"
            app:buttonTint="@color/colorPrimary" />

        <RadioGroup
            android:id="@+id/optionOfTarawihAndTahajjud_RadioGroup_TarawihAndTahajjudSettingsFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="4dp"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/activationOfTheDeclarationOfPrayer_AppCompatRadioButton_TarawihAndTahajjudSettingsFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textAlignment="viewStart"
                android:textColor="#686060"
                android:textSize="14sp"
                app:buttonTint="@color/colorPrimary"
                tools:text="@string/activation_of_the_declaration_of_tarawih_prayer" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/enableAndStopGallery_AppCompatRadioButton_TarawihAndTahajjudSettingsFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textAlignment="viewStart"
                android:textColor="#686060"
                android:textSize="14sp"
                app:buttonTint="@color/colorPrimary"
                tools:text="@string/enable_and_stop_gallery" />
        </RadioGroup>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/settingItem_RecyclerView_TarawihAndTahajjudSettingsFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"

            android:layout_marginTop="20dp"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:reverseLayout="false"
            tools:listitem="@layout/layout_list_item_option_choose" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView>
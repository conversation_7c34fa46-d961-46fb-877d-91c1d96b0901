# Immediate Stability Fixes Plan - Android TV App
## Critical Issues Resolution (Week 1 Implementation)

### Overview
This plan addresses the most critical stability issues in MainActivity.java (3,031 lines) that are causing crashes, memory leaks, and performance problems during 24-hour operation.

## 🚨 Critical Issues Identified

### 1. Threading System Problems
- **7 separate HandlerThreads** running simultaneously
- **34+ Handler.postDelayed() calls** without proper cleanup
- **No centralized thread management**
- **Threads not properly terminated** in onDestroy()

### 2. Memory Leak Sources
- **Static references** holding activity context
- **<PERSON><PERSON> references** not cleared on destroy
- **Thread references** not nullified
- **No proper cleanup** in lifecycle methods

### 3. Exception Handling Issues
- **Already improved** in MyExceptionHandler.java and AppController.java
- **Need to integrate** with new threading system

## 📋 Implementation Plan (5 Days)

### Day 1: Threading System Replacement
**Goal**: Replace 7 HandlerThreads with centralized ThreadManager

#### Current Problematic Code:
```java
// 7 separate HandlerThreads
private HandlerThread mainLogicHandlerThread;
private Handler mainLogicHandler;
private HandlerThread eventsHandlerThread;
private Handler eventsHandler;
// ... 5 more similar patterns

// 34+ postDelayed calls like:
mainLogicHandler.postDelayed(this::scheduleMainLogic, delay);
eventsHandler.postDelayed(this::scheduleEvents, delay);
```

#### Replacement Strategy:
```java
// Single ThreadManager instance
private ThreadManager threadManager;

// Replace all postDelayed with:
threadManager.scheduleRepeatingTask("MainLogic", new ScheduledTask() {
    @Override
    public void execute() { /* main logic */ }
    @Override
    public long getNextDelay() { return calculatedDelay; }
}, initialDelay);
```

#### Files to Modify:
- `MainActivity.java` - Replace threading system
- Integration with existing `ThreadManager.java`

### Day 2: Memory Leak Fixes
**Goal**: Remove static references and improve cleanup

#### Static References to Fix:
```java
// Current memory leaks:
public static TimingsAlrabeeaTimes timingsAlrabeeaTimes;
public static boolean isShowAthkarsAfterPrayer = false;
public static int timeOfAthkarsAfterPrayer = 0;
public static boolean isHijri;
public static long LastMaghrib = 0;

// Replace with instance variables:
private TimingsAlrabeeaTimes timingsAlrabeeaTimes;
private boolean isShowAthkarsAfterPrayer = false;
private int timeOfAthkarsAfterPrayer = 0;
private boolean isHijri;
private long lastMaghrib = 0;
```

#### Cleanup Implementation:
```java
@Override
protected void onDestroy() {
    super.onDestroy();
    
    // Stop all threads
    if (threadManager != null) {
        threadManager.shutdown();
        threadManager = null;
    }
    
    // Clear all references
    timingsAlrabeeaTimes = null;
    announcementManager = null;
    // ... clear other references
}
```

### Day 3: Handler Cleanup and Lifecycle Management
**Goal**: Ensure proper Handler cleanup and lifecycle management

#### Current Issues:
- Handlers created with `new Handler(Looper.getMainLooper())`
- No cleanup in onDestroy()
- executeEvery() method creates new HandlerThreads

#### Fixes:
```java
// Replace direct Handler creation:
new Handler(Looper.getMainLooper()).postDelayed(() -> {
    // UI updates
}, delay);

// With ThreadManager UI thread execution:
threadManager.executeOnUiThread(() -> {
    // UI updates
});
```

### Day 4: Integration Testing and Validation
**Goal**: Test all changes and ensure stability

#### Testing Checklist:
- [ ] App starts without crashes
- [ ] No memory leaks after 1 hour operation
- [ ] Thread count remains stable
- [ ] All features working correctly
- [ ] No ANRs reported

#### Monitoring Setup:
```java
// Add memory monitoring
private void logMemoryUsage() {
    Runtime runtime = Runtime.getRuntime();
    long usedMemory = runtime.totalMemory() - runtime.freeMemory();
    Log.d(TAG, "Memory usage: " + (usedMemory / 1024 / 1024) + " MB");
}

// Add thread monitoring
private void logThreadCount() {
    ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
    int threadCount = rootGroup.activeCount();
    Log.d(TAG, "Active threads: " + threadCount);
}
```

### Day 5: Final Validation and Documentation
**Goal**: 24-hour stress test and documentation

#### Stress Test Protocol:
1. **Continuous operation test** - 24 hours
2. **Memory monitoring** - every hour
3. **Thread count tracking** - every 30 minutes
4. **Feature functionality** - every 4 hours
5. **Crash monitoring** - Firebase Crashlytics

## 🔧 Specific Code Changes

### 1. MainActivity Threading Replacement

#### Remove These Declarations:
```java
// Remove all HandlerThread declarations (lines 144-182)
private HandlerThread mainLogicHandlerThread;
private Handler mainLogicHandler;
private HandlerThread eventsHandlerThread;
private Handler eventsHandler;
// ... remove all 7 HandlerThread/Handler pairs
```

#### Add ThreadManager:
```java
// Add single ThreadManager
private ThreadManager threadManager;

// Initialize in onCreate()
private void initializeThreadManager() {
    threadManager = new ThreadManager();
}
```

### 2. Replace All postDelayed Calls

#### Pattern Replacement:
```java
// OLD PATTERN (34 instances to replace):
someHandler.postDelayed(this::someMethod, delay);

// NEW PATTERN:
threadManager.scheduleRepeatingTask("ThreadName", new ScheduledTask() {
    @Override
    public void execute() {
        // Original method logic here
    }
    
    @Override
    public long getNextDelay() {
        return calculatedDelay; // or -1 to stop
    }
}, initialDelay);
```

### 3. Static Reference Cleanup

#### Update All Static References:
```java
// Change all static fields to instance fields
// Update all references throughout the codebase
// Ensure proper initialization in onCreate()
```

### 4. Lifecycle Management

#### Enhanced onDestroy():
```java
@Override
protected void onDestroy() {
    super.onDestroy();
    
    // Stop ThreadManager
    if (threadManager != null) {
        threadManager.shutdown();
        threadManager = null;
    }
    
    // Clear all object references
    clearAllReferences();
    
    // Force garbage collection
    System.gc();
}

private void clearAllReferences() {
    timingsAlrabeeaTimes = null;
    announcementManager = null;
    uiTheme = null;
    hijriCalendar = null;
    gregorianCalendar = null;
    azkarTheme = null;
    // ... clear all other references
}
```

## 📊 Success Metrics

### Before Fixes (Current State):
- ❌ Crashes after 12-24 hours
- ❌ Memory usage grows over time
- ❌ 7+ background threads
- ❌ ANRs from threading issues
- ❌ Static memory leaks

### After Fixes (Expected):
- ✅ Stable 24/7 operation
- ✅ Consistent memory usage (~150-200MB)
- ✅ 3-5 managed threads maximum
- ✅ No ANRs
- ✅ No memory leaks

## 🚨 Risk Mitigation

### Backup Strategy:
```bash
# Create backup before changes
git checkout -b backup-before-stability-fixes
git add .
git commit -m "Backup before stability fixes"
git checkout main
```

### Rollback Plan:
```bash
# If issues occur:
git checkout backup-before-stability-fixes
git checkout main
git reset --hard backup-before-stability-fixes
```

### Testing Protocol:
1. **Unit testing** - Each component individually
2. **Integration testing** - Full app functionality
3. **Stress testing** - 24-hour continuous operation
4. **Memory testing** - LeakCanary integration
5. **Performance testing** - CPU and battery usage

## 📝 Implementation Notes

### Critical Files to Modify:
1. **MainActivity.java** - Main threading and memory fixes
2. **ThreadManager.java** - Already exists, may need enhancements
3. **MyExceptionHandler.java** - Already improved
4. **AppController.java** - Already improved

### Dependencies:
- Existing ThreadManager.java implementation
- Firebase Crashlytics for monitoring
- LeakCanary for memory leak detection (debug builds)

### Testing Environment:
- Android TV device for 24-hour testing
- Development device for rapid iteration
- Firebase console for crash monitoring

## 🎯 Next Steps After Week 1

Once immediate stability is achieved:

### Week 2-3: Code Organization
- Extract manager classes from MainActivity
- Implement proper separation of concerns
- Create focused, single-responsibility classes

### Week 4: Service Architecture
- Move background tasks to proper services
- Implement proper service lifecycle management
- Add comprehensive error handling

### Week 5-6: Modern Architecture
- Implement MVVM pattern
- Add Repository pattern for data access
- Integrate dependency injection

---

**Priority**: 🔴 **CRITICAL** - Implement immediately
**Timeline**: 5 days
**Expected Impact**: 80-90% reduction in crashes and memory issues
**Risk Level**: Low (incremental changes with rollback capability)
package com.arapeak.alrbea.UI.Activity.repository;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.arapeak.alrbea.APIs.AlrabeeaTimesRequests;
import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.PrayerUtils;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.Calendar;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Repository for handling prayer times data
 * Follows Repository pattern for clean architecture
 */
public class PrayerRepository {

    private static final String TAG = "PrayerRepository";
    private static PrayerRepository instance;

    private final ExecutorService executorService;
    private final MutableLiveData<TimingsAlrabeeaTimes> prayerTimesLiveData = new MutableLiveData<>();
    private final MutableLiveData<PrayerType> nextPrayerLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> remainingTimeLiveData = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoadingLiveData = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorLiveData = new MutableLiveData<>();

    private TimingsAlrabeeaTimes cachedPrayerTimes;
    private int cachedYear = -1, cachedMonth = -1, cachedDay = -1;

    private PrayerRepository() {
        executorService = Executors.newFixedThreadPool(2);
    }

    public static synchronized PrayerRepository getInstance() {
        if (instance == null) {
            instance = new PrayerRepository();
        }
        return instance;
    }

    // LiveData getters
    public LiveData<TimingsAlrabeeaTimes> getPrayerTimesLiveData() {
        return prayerTimesLiveData;
    }

    public LiveData<PrayerType> getNextPrayerLiveData() {
        return nextPrayerLiveData;
    }

    public LiveData<String> getRemainingTimeLiveData() {
        return remainingTimeLiveData;
    }

    public LiveData<Boolean> getIsLoadingLiveData() {
        return isLoadingLiveData;
    }

    public LiveData<String> getErrorLiveData() {
        return errorLiveData;
    }

    /**
     * Get prayer times for specific date
     */
    public void getPrayerTimes(int year, int month, int day) {
        // Check if we already have cached data for this date
        if (cachedPrayerTimes != null &&
                cachedYear == year &&
                cachedMonth == month &&
                cachedDay == day) {

            prayerTimesLiveData.postValue(cachedPrayerTimes);
            updateNextPrayer();
            return;
        }

        executorService.execute(() -> {
            try {
                isLoadingLiveData.postValue(true);

                // Try to get from local database first
                TimingsAlrabeeaTimes localTiming = PrayerUtils.getTiming(year, month, day);

                if (localTiming != null) {
                    // Cache the result
                    cachedPrayerTimes = localTiming;
                    cachedYear = year;
                    cachedMonth = month;
                    cachedDay = day;

                    prayerTimesLiveData.postValue(localTiming);
                    updateNextPrayer();
                    errorLiveData.postValue(null);
                } else {
                    // Fetch from API if not available locally
                    fetchPrayerTimesFromApi(year);
                }

            } catch (Exception e) {
                Log.e(TAG, "Error getting prayer times", e);
                CrashlyticsUtils.INSTANCE.logException(e);
                errorLiveData.postValue("Failed to get prayer times: " + e.getMessage());
            } finally {
                isLoadingLiveData.postValue(false);
            }
        });
    }

    /**
     * Fetch prayer times from API for the entire year
     */
    private void fetchPrayerTimesFromApi(int year) {
        try {
            AlrabeeaTimesRequests.getPrayerTimesThisYear(
                    year,
                    new OnCompleteListener<PrayerApi, String>() {
                        @Override
                        public void onSuccess(PrayerApi prayerApi) {
                            try {
                                // Save to local database
                                PrayerUtils.savePrayerTimesToDatabase(prayerApi);

                                // Get the specific day we need
                                TimingsAlrabeeaTimes timing = PrayerUtils.getTiming(cachedYear, cachedMonth, cachedDay);
                                if (timing != null) {
                                    cachedPrayerTimes = timing;
                                    prayerTimesLiveData.postValue(timing);
                                    updateNextPrayer();
                                    errorLiveData.postValue(null);
                                } else {
                                    errorLiveData.postValue("Prayer times not available for selected date");
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error processing API response", e);
                                CrashlyticsUtils.INSTANCE.logException(e);
                                errorLiveData.postValue("Failed to process prayer times: " + e.getMessage());
                            }
                        }

                        @Override
                        public void onFail(String object) {

                        }

                        @Override
                        public void onFailure(String error) {
                            Log.e(TAG, "API request failed: " + error);
                            errorLiveData.postValue("Failed to fetch prayer times: " + error);
                        }
                    });
        } catch (Exception e) {
            Log.e(TAG, "Error fetching from API", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            errorLiveData.postValue("Failed to fetch prayer times: " + e.getMessage());
        }
    }

    /**
     * Update next prayer and remaining time
     */
    private void updateNextPrayer() {
        if (cachedPrayerTimes == null) {
            return;
        }

        executorService.execute(() -> {
            try {
                PrayerType nextPrayer = determineNextPrayer();
                nextPrayerLiveData.postValue(nextPrayer);

                String remainingTime = calculateRemainingTime(nextPrayer);
                remainingTimeLiveData.postValue(remainingTime);

            } catch (Exception e) {
                Log.e(TAG, "Error updating next prayer", e);
                CrashlyticsUtils.INSTANCE.logException(e);
            }
        });
    }

    /**
     * Determine the next prayer based on current time
     */
    private PrayerType determineNextPrayer() {
        if (cachedPrayerTimes == null) {
            return PrayerType.Fajr;
        }

        try {
            Calendar now = Calendar.getInstance();
            long currentTimeMillis = now.getTimeInMillis();

            // Get prayer times for today
            for (PrayerType prayerType : PrayerType.values()) {
                if (!prayerType.isFard()) {
                    continue; // Skip non-obligatory prayers
                }

                PrayerTime prayerTime = prayerType.prayerTime;
                if (prayerTime != null) {
                    long prayerTimeMillis = prayerTime.getTimeInMillis();

                    // If current time is before this prayer time, this is the next prayer
                    if (currentTimeMillis < prayerTimeMillis) {
                        return prayerType;
                    }
                }
            }

            // If we're past all prayers for today, next prayer is Fajr tomorrow
            return PrayerType.Fajr;

        } catch (Exception e) {
            Log.e(TAG, "Error determining next prayer", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return PrayerType.Fajr;
        }
    }

    /**
     * Calculate remaining time to next prayer
     */
    private String calculateRemainingTime(PrayerType nextPrayerType) {
        if (cachedPrayerTimes == null || nextPrayerType == null) {
            return "00:00";
        }

        try {
            Calendar now = Calendar.getInstance();
            long currentTimeMillis = now.getTimeInMillis();

            PrayerTime prayerTime = nextPrayerType.prayerTime;
            if (prayerTime == null) {
                return "00:00";
            }

            long prayerTimeMillis = prayerTime.getTimeInMillis();

            // If prayer time is tomorrow (for Fajr case)
            if (prayerTimeMillis < currentTimeMillis) {
                prayerTimeMillis += 24 * 60 * 60 * 1000; // Add 24 hours
            }

            long remainingMillis = prayerTimeMillis - currentTimeMillis;

            if (remainingMillis <= 0) {
                return "00:00";
            }

            // Convert to hours and minutes
            long hours = remainingMillis / (60 * 60 * 1000);
            long minutes = (remainingMillis % (60 * 60 * 1000)) / (60 * 1000);

            return String.format("%02d:%02d", hours, minutes);

        } catch (Exception e) {
            Log.e(TAG, "Error calculating remaining time", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return "00:00";
        }
    }

    /**
     * Get current prayer times (cached)
     */
    public TimingsAlrabeeaTimes getCurrentPrayerTimes() {
        return cachedPrayerTimes;
    }

    /**
     * Check if currently in prayer time
     */
    public boolean isCurrentlyPrayerTime() {
        if (cachedPrayerTimes == null) {
            return false;
        }

        try {
            for (PrayerType prayerType : PrayerType.values()) {
                if (prayerType.isFard()) {
                    PrayerTime prayerTime = prayerType.prayerTime;
                    if (prayerTime != null && prayerTime.isDuringPrayer()) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking prayer time", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    /**
     * Check if currently between Azan and Ikama
     */
    public boolean isBetweenAzanAndIkama() {
        if (cachedPrayerTimes == null) {
            return false;
        }

        try {
            for (PrayerType prayerType : PrayerType.values()) {
                if (prayerType.isFard()) {
                    PrayerTime prayerTime = prayerType.prayerTime;
                    if (prayerTime != null && prayerTime.isBetweenAzanAndIkama()) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking Azan-Ikama time", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            return false;
        }
    }

    /**
     * Refresh prayer times for current date
     */
    public void refreshPrayerTimes() {
        try {
            String currentYear = Utils.getEnglishDateTime(ConstantsOfApp.YEAR, System.currentTimeMillis());
            String currentMonth = Utils.getEnglishDateTime(ConstantsOfApp.MONTH, System.currentTimeMillis());
            String currentDay = Utils.getEnglishDateTime(ConstantsOfApp.DAY, System.currentTimeMillis());

            int year = Integer.parseInt(currentYear);
            int month = Integer.parseInt(currentMonth);
            int day = Integer.parseInt(currentDay);

            // Clear cache to force refresh
            cachedPrayerTimes = null;
            cachedYear = -1;
            cachedMonth = -1;
            cachedDay = -1;

            getPrayerTimes(year, month, day);

        } catch (Exception e) {
            Log.e(TAG, "Error refreshing prayer times", e);
            CrashlyticsUtils.INSTANCE.logException(e);
            errorLiveData.postValue("Failed to refresh prayer times: " + e.getMessage());
        }
    }

    /**
     * Clean up resources
     */
    public void cleanup() {
        try {
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
            }

            cachedPrayerTimes = null;
            cachedYear = -1;
            cachedMonth = -1;
            cachedDay = -1;

        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="4dp"
    android:paddingTop="20dp"
    android:paddingEnd="4dp"
    android:paddingBottom="20dp"
    tools:context=".UI.Fragment.settings.content.prayerTimes.content.content.PrayerTimesSettingFragment"
    tools:layoutDirection="rtl">

    <TextView
        android:id="@+id/tv_prayer_time_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_14sdp"
        android:paddingStart="14dp"
        android:paddingEnd="14dp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="الإعدادات الرئيسية">

    </TextView>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/settingItem_RecyclerView_PrayerTimesSettingFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:overScrollMode="never"
        android:paddingTop="20dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_prayer_time_title"
        app:reverseLayout="false"
        tools:listitem="@layout/layout_list_item_option_choose" />


</androidx.constraintlayout.widget.ConstraintLayout>
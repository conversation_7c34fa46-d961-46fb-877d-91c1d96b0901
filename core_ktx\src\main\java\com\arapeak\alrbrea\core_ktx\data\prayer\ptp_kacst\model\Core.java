package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model;


class Core {
    public static int[] DeltaTTable = {1240, 1150, 1060, 980, 910, 850, 790, 740, 700, 650, 620, 580, 550, 530, 500, 480, 460, 440, 420, 370, 350, 330, 310, 280, 260, 240,
            220, 200, 180, 160, 140, 130, 120, 110, 100, 90, 90, 90, 90, 90, 90, 90, 90, 100, 100, 100, 100, 100, 110, 110, 110, 110, 110, 110,
            110, 110, 120, 120, 120, 120, 120, 130, 130, 130, 130, 140, 140, 140, 150, 150, 150, 150, 160, 160, 160, 160, 160, 170, 170, 170,
            170, 170, 170, 170, 170, 160, 160, 150, 140, 137, 131, 127, 125, 125, 125, 125, 125, 125, 123, 120, 114, 106, 96, 86, 75, 66,
            60, 57, 56, 57, 59, 62, 65, 68, 71, 73, 75, 77, 78, 79, 75, 64, 54, 29, 16, -10, -27, -36, -47, -54, -52, -55, -56, -58,
            -59, -62, -64, -61, -47, -27, 0, 26, 54, 77, 105, 134, 160, 182, 202, 212, 224, 235, 239, 243, 240, 239, 239, 237, 240,
            243, 253, 262, 273, 282, 291, 300, 307, 314, 322, 331, 340, 350, 365, 383, 402, 422, 445, 465, 485, 505, 522, 538,
            549, 558, 569, 580};

    Core() {
    }

    public static double DegToRad(double d) {
        return d * 0.017453292519943295d;
    }

    public static double HCalendarToJD(int i, int i2, int i3) {
        return (((((i - 1.0d) * 354.367068d) + ((i2 - 1.0d) * 29.530589d)) + i3) - 1.0d) + 1948439.0d;
    }

    private static double Interpol3(double d, double d2, double d3, double d4) {
        double d5 = d2 - d;
        double d6 = d3 - d2;
        return d2 + ((d4 * ((d5 + d6) + ((d6 - d5) * d4))) / 2.0d);
    }

    public static double MJDToT(double d) {
        return (d - 2451545.0d) / 36525.0d;
    }

    public static double RadToDeg(double d) {
        return d * 57.29577951308232d;
    }

    public static double iprt(double d) {
        return (int) d;
    }

    public static PlanetParams SunParam(int i, int i2, int i3, double d, double d2, double d3) {
        Coordinates coordinates = new Coordinates(new tagTSVECTOR(0.0d, 0.0d, 0.0d));
        double d4 = d3 / 24.0d;
        double GrCalendarToJD = ((GrCalendarToJD(i, i2, i3) + d4) - 2451545.0d) / 36525.0d;
        double ApproxDeltaT = ApproxDeltaT(GrCalendarToJD);
        double Obliquity = Obliquity(GrCalendarToJD);
        double floor = ((Math.floor(((GrCalendarToJD * 36525.0d) - 0.5d) - d4) + 0.5d) + d4) / 36525.0d;
        double d5 = (3.168808781402895E-10d * ApproxDeltaT) + floor;
        coordinates.SEarth = PlPosHi(d5 - 2.7378507871321012E-5d);
        coordinates.SGeo = HelioToGeo(coordinates.SHelio, coordinates.SEarth);
        Equatorial EclToEqu = EclToEqu(coordinates.SGeo.L, coordinates.SGeo.B, Obliquity);
        coordinates.SEarth = PlPosHi(d5);
        coordinates.SGeo = HelioToGeo(coordinates.SHelio, coordinates.SEarth);
        Equatorial EclToEqu2 = EclToEqu(coordinates.SGeo.L, coordinates.SGeo.B, Obliquity);
        coordinates.SEarth = PlPosHi(d5 + 2.7378507871321012E-5d);
        coordinates.SGeo = HelioToGeo(coordinates.SHelio, coordinates.SEarth);
        Equatorial EclToEqu3 = EclToEqu(coordinates.SGeo.L, coordinates.SGeo.B, Obliquity);
        PlanetParams RiseSet = RiseSet(floor, ApproxDeltaT, EclToEqu.RA, EclToEqu.Decl, EclToEqu2.RA, EclToEqu2.Decl, EclToEqu3.RA, EclToEqu3.Decl, -0.014543828656868749d, d, d2);
        RiseSet.Rise *= 3.819718634205488d;
        RiseSet.Set *= 3.819718634205488d;
        RiseSet.Transit *= 3.819718634205488d;
        RiseSet.Eqatorial = EclToEqu3;
        return RiseSet;
    }

    public static PlanetParams RiseSet(double d, double d2, double d3, double d4, double d5, double d6, double d7, double d8, double d9, double d10, double d11) {
        int i;
        int i2;
        int i3;
        double Interpol3;
        double cos;
        double[] dArr = new double[3];
        PlanetParams planetParams = new PlanetParams();
        planetParams.FlagRS = 0;
        double modpi = d5 + modpi(d3 - d5);
        double modpi2 = d5 + modpi(d7 - d5);
        double modpi22 = modpi2(((d * ((d * (3.87933E-4d - (d / 3.871E7d))) + 1.3185000770053742E7d)) + 280.46061837d) * 0.017453292519943295d);
        double sin = (Math.sin(d9) - (Math.sin(d11) * Math.sin(d6))) / (Math.cos(d11) * Math.cos(d6));
        dArr[0] = ((d5 + d10) - modpi22) / 6.283185307179586d;
        if (dArr[0] < 0.0d) {
            dArr[0] = dArr[0] + 1.0d;
        }
        int i4 = 2;
        if (Math.abs(sin) <= 1.0d) {
            double acos = Math.acos(sin) / 6.283185307179586d;
            dArr[1] = dArr[0] - acos;
            dArr[2] = dArr[0] + acos;
        } else if (sin < -1.0d) {
            dArr[1] = dArr[0] - 0.5d;
            dArr[2] = dArr[0] + 0.5d;
            planetParams.FlagRS = 16;
        } else {
            dArr[1] = dArr[0];
            dArr[2] = dArr[0];
            planetParams.FlagRS = 32;
        }
        if (dArr[1] < 0.0d) {
            dArr[1] = dArr[1] + 1.0d;
        }
        if (dArr[2] > 1.0d) {
            dArr[2] = dArr[2] - 1.0d;
        }
        int i5 = planetParams.FlagRS == 0 ? 3 : 1;
        int i6 = 0;
        double d12 = 0.0d;
        while (i6 < i5) {
            double d13 = d12;
            while (true) {
                double d14 = modpi22 + (dArr[i6] * 6.300388092591991d);
                double d15 = dArr[i6] + (d2 / 86400.0d);
                i = i6;
                i2 = i4;
                i3 = i5;
                double Interpol32 = Interpol3(modpi, d5, modpi2, d15);
                Interpol3 = i != 0 ? Interpol3(d4, d6, d8, d15) : d13;
                double modpi3 = modpi((d14 - d10) - Interpol32);
                if (i == 0) {
                    cos = (-modpi3) / 6.283185307179586d;
                } else {
                    double asin = Math.asin((Math.sin(d11) * Math.sin(Interpol3)) + (Math.cos(d11) * Math.cos(Interpol3) * Math.cos(modpi3)));
                    cos = (Math.cos(asin) * (asin - d9)) / (((Math.cos(Interpol3) * 6.283185307179586d) * Math.cos(d11)) * Math.sin(modpi3));
                }
                dArr[i] = dArr[i] + cos;
                if (Math.abs(cos) <= 2.0E-4d) {
                    break;
                }
                d13 = Interpol3;
                i4 = i2;
                i6 = i;
                i5 = i3;
            }
            i6 = i + 1;
            d12 = Interpol3;
            i4 = i2;
            i5 = i3;
        }
        int i7 = i4;
        if (planetParams.FlagRS == 0) {
            for (int i8 = 1; i8 <= i7; i8++) {
                if (dArr[i8] < 0.0d) {
                    dArr[i8] = dArr[i8] + 1.0d;
                } else if (dArr[i8] > 1.0d) {
                    dArr[i8] = dArr[i8] - 1.0d;
                }
            }
        }
        planetParams.Rise = dArr[1] * 6.283185307179586d;
        planetParams.Transit = dArr[0] * 6.283185307179586d;
        planetParams.Set = dArr[i7] * 6.283185307179586d;
        return planetParams;
    }

    public static double GCalendarToJD(int i, int i2, double d) {
        if (i2 <= 2) {
            i--;
            i2 += 12;
        }
        int i3 = i / 100;
        return (((iprt((i + 4716) * 365.25d) + iprt((i2 + 1) * 30.6001d)) + d) + ((2 - i3) + (i3 / 4))) - 1524.5d;
    }

//    public static CustomDate JDToGCalendar(double d) {
//        double d2 = d + 0.5d;
//        double floor = (long) Math.floor(d2);
//        double d3 = d2 - floor;
//        int i = (int) ((floor - 1867216.25d) / 36524.25d);
//        double d4 = (((r0 + 1) + i) - (i / 4)) + 1524.0d;
//        int i2 = (int) ((d4 - 122.1d) / 365.25d);
//        double d5 = d4 - ((long) (i2 * 365.25d));
//        int i3 = (int) (d5 / 30.6001d);
//        int floor2 = (int) ((d5 - Math.floor(i3 * 30.6001d)) + d3);
//        int i4 = i3 < 14 ? i3 - 1 : i3 - 13;
//        return new CustomDate(i4 > 2 ? i2 - 4716 : i2 - 4715, i4, floor2);
//    }

    private static double GrCalendarToJD(int i, int i2, double d) {
        if (i2 <= 2) {
            i--;
            i2 += 12;
        }
        int i3 = i / 100;
        return (((iprt((i + 4716) * 365.25d) + iprt((i2 + 1) * 30.6001d)) + d) + ((2 - i3) + (i3 / 4))) - 1524.5d;
    }

    private static tagTSVECTOR HelioToGeo(tagTSVECTOR tagtsvector, tagTSVECTOR tagtsvector2) {
        double[] dArr = new double[3];
        double[] dArr2 = new double[3];
        double[] SphToRect = SphToRect(tagtsvector);
        double[] SphToRect2 = SphToRect(tagtsvector2);
        for (int i = 0; i < 3; i++) {
            SphToRect[i] = SphToRect[i] - SphToRect2[i];
        }
        return RectToSph(SphToRect);
    }

    private static double[] SphToRect(tagTSVECTOR tagtsvector) {
        return new double[]{tagtsvector.R * Math.cos(tagtsvector.L) * Math.cos(tagtsvector.B), tagtsvector.R * Math.sin(tagtsvector.L) * Math.cos(tagtsvector.B), tagtsvector.R * Math.sin(tagtsvector.B)};
    }

    private static tagTSVECTOR RectToSph(double[] dArr) {
        tagTSVECTOR tagtsvector = new tagTSVECTOR();
        tagtsvector.L = Math.atan2(dArr[1], dArr[0]);
        if (tagtsvector.L < 0.0d) {
            tagtsvector.L += 6.283185307179586d;
        }
        tagtsvector.R = Math.sqrt((dArr[0] * dArr[0]) + (dArr[1] * dArr[1]) + (dArr[2] * dArr[2]));
        tagtsvector.B = Math.asin(dArr[2] / tagtsvector.R);
        return tagtsvector;
    }

    public static Equatorial EclToEqu(double d, double d2, double d3) {
        Equatorial equatorial = new Equatorial();
        double sin = Math.sin(d3);
        double cos = Math.cos(d3);
        double sin2 = Math.sin(d);
        double cos2 = Math.cos(d);
        double sin3 = Math.sin(d2);
        double cos3 = Math.cos(d2);
        equatorial.RA = Math.atan2(((cos3 * sin2) * cos) - (sin3 * sin), cos2 * cos3);
        if (equatorial.RA < 0.0d) {
            equatorial.RA += 6.283185307179586d;
        }
        equatorial.Decl = Math.asin((sin3 * cos) + (cos3 * sin * sin2));
        return equatorial;
    }

    public static double ApproxDeltaT(double d) {
        double d2 = (100.0d * d) + 2000.0d;
        if (d2 > 2000.0d) {
            return (d * ((32.5d * d) + 123.5d)) + 102.3d;
        }
        if (d2 < 1620.0d) {
            return d2 < 948.0d ? (d * ((46.5d * d) + 573.36d)) + 2715.6d : (d * ((22.5d * d) + 67.5d)) + 50.6d;
        }
        int i = (int) ((d2 - 1620.0d) / 2.0d);
        if (i > 185) {
            i = 185;
        }
        return (DeltaTTable[i] + ((DeltaTTable[i + 1] - DeltaTTable[i]) * (((d2 / 2.0d) - i) - 810.0d))) / 10.0d;
    }

    public static double Obliquity(double d) {
        return EvalPoly(new double[]{84381.448d, -4680.93d, -1.55d, 1999.25d, -51.38d, -249.67d, -39.05d, 7.12d, 27.87d, 5.79d, 2.45d}, d / 100.0d) * 4.84813681109536E-6d;
    }

    public static tagTSVECTOR PlPosHi(double d) {
        double[] dArr = new double[3];
        double d2 = d / 10.0d;
        double[] dArr2 = Data.Term3;
        int[] iArr = Data.Index3;
        int i = 0;
        int i2 = 0;
        int i3 = 0;
        while (i < 3) {
            double d3 = 0.0d;
            dArr[i] = 0.0d;
            double d4 = 1.0d;
            int i4 = i2;
            int i5 = 0;
            while (i5 < 6) {
                int i6 = i3 + 1;
                int i7 = iArr[i3];
                double d5 = d3;
                int i8 = i4;
                int i9 = 0;
                while (i9 < i7) {
                    int i10 = i8 + 1;
                    double d6 = dArr2[i8];
                    int i11 = i10 + 1;
                    d5 += d6 * Math.cos(dArr2[i10] + (dArr2[i11] * d2));
                    i9++;
                    i8 = i11 + 1;
                }
                dArr[i] = dArr[i] + (d5 * d4);
                d4 *= d2;
                i5++;
                i3 = i6;
                i4 = i8;
                d3 = 0.0d;
            }
            i++;
            i2 = i4;
        }
        tagTSVECTOR tagtsvector = new tagTSVECTOR(modpi2(dArr[0]), dArr[1], dArr[2]);
        double d7 = tagtsvector.L - ((d2 * ((3.1E-4d * d2) + 1.397d)) * 0.017453292519943295d);
        tagtsvector.L += (((Math.tan(tagtsvector.B) * 0.03916d) * (Math.cos(d7) + Math.sin(d7))) - 0.09033d) * 4.84813681109536E-6d;
        tagtsvector.B += (Math.cos(d7) - Math.sin(d7)) * 0.03916d * 4.84813681109536E-6d;
        return tagtsvector;
    }

    private static double modpi(double d) {
        return d - (Math.floor((d / 6.283185307179586d) + 0.5d) * 6.283185307179586d);
    }

    public static double modpi2(double d) {
        return d - (Math.floor(d / 6.283185307179586d) * 6.283185307179586d);
    }

    public static double EvalPoly(double[] dArr, double d) {
        double d2 = dArr[dArr.length - 1];
        for (int length = dArr.length - 2; length >= 0; length--) {
            d2 = (d2 * d) + dArr[length];
        }
        return d2;
    }
}
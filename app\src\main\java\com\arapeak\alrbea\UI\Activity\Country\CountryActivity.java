package com.arapeak.alrbea.UI.Activity.Country;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Model.City;
import com.arapeak.alrbea.Model.Country;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.OmanCitiesDb;
import com.arapeak.alrbea.database.OmanCity;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

import io.realm.Case;
import io.realm.RealmObject;
import io.realm.Sort;


public class CountryActivity extends AppCompatActivity implements AdapterCallback {

    public static final String MESSAGE_AFTER_FINISH_RESOURCES_ID_KEY = "messageAfterFinishResourcesId";
    private static final String TAG = "CountryActivity";
    ImageView addlocaton;
    private LinearLayout searchLinearLayout;
    private EditText searchEditText;
    private RecyclerView optionChooseRecyclerView;
    private CountryAdapter countryAdapter;
    private ConstraintLayout contentLayout;
    private Country countrySelected;
    private boolean isFinish;
    private String messageAfterFinish;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Utils.initActivity(CountryActivity.this);
        setContentView(R.layout.activity_country);

        initView();
        SetParameter();
        SetAction();
    }

    @Override
    protected void onSaveInstanceState(Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            savedInstanceState.putAll(getIntent().getExtras());
        }
        super.onSaveInstanceState(savedInstanceState);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Utils.initActivity(CountryActivity.this);
        Utils.setScreenBrightnessMax(CountryActivity.this);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

    }

    @Override
    public void onBackPressed() {
        if (countrySelected != null && !isFinish) {
            /*if (countrySelected != null) {*/
            countrySelected = null;
            getAllCountries();
            return;
        }
        super.onBackPressed();
    }

    private void initView() {

        contentLayout = findViewById(R.id.content_ConstraintLayout_CountryActivity);
        searchLinearLayout = findViewById(R.id.search_LinearLayout_CountryActivity);
        searchEditText = findViewById(R.id.search_EditText_CountryActivity);
        optionChooseRecyclerView = findViewById(R.id.optionChoose_RecyclerView_CountryActivity);
        addlocaton = findViewById(R.id.addlocaton);
        countryAdapter = new CountryAdapter(CountryActivity.this, new ArrayList<RealmObject>(), this);


        isFinish = false;

        if (getIntent() != null && getIntent().getExtras() != null
                && getIntent().getExtras()
                .getInt(MESSAGE_AFTER_FINISH_RESOURCES_ID_KEY) > 0) {
            try {
                messageAfterFinish = getString(getIntent().getExtras().getInt(MESSAGE_AFTER_FINISH_RESOURCES_ID_KEY));
            } catch (Exception e) {
                Log.e(TAG, "Error: " + e.getMessage());
                CrashlyticsUtils.INSTANCE.logException(e);
            } finally {
                messageAfterFinish = Utils.getValueWithoutNull(messageAfterFinish);
            }
        }
    }

    private void SetParameter() {
        if (HawkSettings.isArabic()) {
            ViewCompat.setLayoutDirection(contentLayout, ViewCompat.LAYOUT_DIRECTION_RTL);
        } else {
            ViewCompat.setLayoutDirection(contentLayout, ViewCompat.LAYOUT_DIRECTION_LTR);
        }
        addlocaton.setOnClickListener(view -> Utils.inputLocationDialog(CountryActivity.this
                , "أضافة يدوي "
                , getString(R.string.alert)

                , true

                , new OnSuccessful() {
                    @Override
                    public void onSuccessful(boolean isSuccessful) {
                        if (isSuccessful) {
                            isFinish = true;
                            finish();
                        }
                    }
                }));
        optionChooseRecyclerView.setAdapter(countryAdapter);
        getAllCountries();

    }

    private void SetAction() {
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                searchForCityCountry(searchEditText.getText().toString().trim());
            }
        });
    }

    @Override
    public void onItemClick(int position, String tag) {
        if (countrySelected != null) {
            if (countryAdapter.getItem(position) == null || (countryAdapter.getItem(position) instanceof Country))
                return;

            String code = countrySelected.getId();
            if (code.equals("om")) {
                OmanCity city = (OmanCity) countryAdapter.getItem(position);
                HawkSettings.putOmanLatLong(city);
            } else {
                City city = (City) countryAdapter.getItem(position);
                Log.d("BeshrCity", city.toString());
                HawkSettings.putLatLong(city.getLatitude(), city.getLongitude());
            }
            HawkSettings.setCountryCode(code);
            Hawk.put(ConstantsOfApp.IS_INITIAL_SETUP_KEY, false);
            Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, false);
            Hawk.put(ConstantsOfApp.IS_UPDATE_PRAY_TIME, true);
            HawkSettings.setUpdateLocation(false);

            isFinish = true;

            if (!messageAfterFinish.isEmpty()) {
                Toast.makeText(this, messageAfterFinish, Toast.LENGTH_SHORT).show();
                Utils.showSuccessAlert(CountryActivity.this, messageAfterFinish);
            }

            finish();
        } else {
            if (countryAdapter.getItem(position) == null || !(countryAdapter.getItem(position) instanceof Country)) {
                return;
            }

            countrySelected = (Country) countryAdapter.getItem(position);

            getAllCities();
        }
    }

    private void getAllCountries() {
        countryAdapter.clear();
        searchEditText.setText("");
        Log.i("CountryAdapter", "loading countries count:" + Utils.getInstanceOfRealm().where(Country.class).count());
        try {
            for (Country c : Utils.getInstanceOfRealm().where(Country.class).findAll())
                Log.i("CountryAdapter", "id:" + c.getId() + ",name:" + c.getName_ar());
            countryAdapter.addAllCountries(Utils.getInstanceOfRealm().where(Country.class).sort(HawkSettings.isArabic() ? "name_ar" : "name_en", Sort.ASCENDING).findAll());

            Log.d(TAG, "CountryAdapter.getItemCount: " + countryAdapter.getItemCount());
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void getAllCities() {
        if (countrySelected == null)
            return;

        countryAdapter.clear();
        searchEditText.setText("");
        try {
            if (countrySelected.getId().equals("om"))
                countryAdapter.addAllOmanCities(OmanCitiesDb.getAllCities());
            else
                countryAdapter.addAllCties(Utils.getInstanceOfRealm().where(City.class)
                        .equalTo("country_id", countrySelected.getId(), Case.INSENSITIVE)
                        .sort("city_name", Sort.ASCENDING)
                        .findAll());
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void searchForCityCountry(String text) {
        if (text.trim().isEmpty()) {
            return;

        }
        countryAdapter.clear();

        if (countrySelected != null) {
            if (countrySelected.getId().equals("om"))
                countryAdapter.addAllOmanCities(OmanCitiesDb.getAllCities(text));
            else
                countryAdapter.addAllCties(Utils.getInstanceOfRealm().where(City.class)
                        .equalTo("country_id", countrySelected.getId(), Case.INSENSITIVE)
                        .contains("city_name", text, Case.INSENSITIVE)
                        .sort("city_name", Sort.ASCENDING).findAll());
        } else {
            if (Utils.isArabicString(text)) {
                countryAdapter.addAllCountries(Utils.getInstanceOfRealm().where(Country.class)
                        .contains("name_ar", text, Case.INSENSITIVE)
                        .sort("name_ar", Sort.ASCENDING).findAll());
            } else {
                countryAdapter.addAllCountries(Utils.getInstanceOfRealm().where(Country.class)
                        .contains("name_en", text, Case.INSENSITIVE)
                        .sort("name_en", Sort.ASCENDING).findAll());
            }
        }
    }
}

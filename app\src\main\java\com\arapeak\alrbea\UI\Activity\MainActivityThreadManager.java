//package com.arapeak.alrbea.UI.Activity;
//
//import android.app.Activity;
//import android.util.Log;
//
//import com.arapeak.alrbea.APIs.ConstantsOfApp;
//import com.arapeak.alrbea.Enum.PrayerType;
//import com.arapeak.alrbea.Interface.PrayerTime;
//import com.arapeak.alrbea.Model.Event;
//import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
//import com.arapeak.alrbea.PrayerUtils;
//import com.arapeak.alrbea.R;
//import com.arapeak.alrbea.UI.Activity.managers.ThreadManager;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbea.database.Repositories;
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//import com.arapeak.alrbrea.core_ktx.ui.screensaver.ScreensaverScheduler;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.concurrent.atomic.AtomicBoolean;
//
//import static com.arapeak.alrbea.APIs.ConstantsOfApp.HOURS_MILLI_SECOND;
//import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
//
///**
// * Helper class for MainActivity Thread Management
// * Handles all thread scheduling, execution, and lifecycle management
// */
//public class MainActivityThreadManager {
//    private static final String TAG = "MainActivityThreadManager";
//    private final MainActivity activity;
//    private final ThreadManager threadManager;
//
//    // Thread state tracking
//    private volatile boolean isMainLogicRunning = false;
//    private volatile boolean isEventsRunning = false;
//    private volatile boolean isAthkarUpdaterRunning = false;
//    private volatile boolean isScreenSaverRunning = false;
//    private volatile boolean isTimeUpdaterRunning = false;
//    private volatile boolean isDuhaSunriseUpdaterRunning = false;
//    private volatile boolean isMaintenanceRunning = false;
//
//    public MainActivityThreadManager(MainActivity activity) {
//        this.activity = activity;
//        this.threadManager = new ThreadManager();
//        Log.d(TAG, "ThreadManager initialized successfully");
//    }
//
//    /**
//     * Initialize and start all managed threads
//     */
//    public void startAllManagedThreads() {
//        try {
//            Log.d(TAG, "Starting all managed threads...");
//
//            // Main Logic
//            if (!isMainLogicRunning) {
//                isMainLogicRunning = true;
//                scheduleMainLogic();
//            }
//
//            // Time Updater
//            if (!isTimeUpdaterRunning) {
//                isTimeUpdaterRunning = true;
//                scheduleTimeUpdate();
//            }
//
//            // ScreenSaver
//            if (!isScreenSaverRunning) {
//                isScreenSaverRunning = true;
//                scheduleScreenSaverCheck();
//            }
//
//            // Events (if enabled)
//            if (activity.isEventsEnabled()) {
//                if (!isEventsRunning) {
//                    isEventsRunning = true;
//                    scheduleEvents();
//                }
//            }
//
//            // Athkar Updater (for specific theme)
//            if (activity.shouldRunAthkarUpdater()) {
//                if (!isAthkarUpdaterRunning) {
//                    isAthkarUpdaterRunning = true;
//                    scheduleAthkarUpdate();
//                }
//            }
//
//            // Duha Sunrise Updater (if enabled)
//            if (activity.shouldRunDuhaSunriseUpdater()) {
//                if (!isDuhaSunriseUpdaterRunning) {
//                    isDuhaSunriseUpdaterRunning = true;
//                    scheduleDuhaSunriseUpdate();
//                }
//            }
//
//            // Maintenance Thread
//            if (!isMaintenanceRunning) {
//                isMaintenanceRunning = true;
//                scheduleMaintenance();
//            }
//
//            Log.d(TAG, "All managed threads started successfully");
//        } catch (Exception e) {
//            Log.e(TAG, "Error starting managed threads: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Stop all managed threads
//     */
//    public void stopAllManagedThreads() {
//        try {
//            Log.d(TAG, "Stopping all managed threads...");
//
//            isMainLogicRunning = false;
//            isEventsRunning = false;
//            isAthkarUpdaterRunning = false;
//            isScreenSaverRunning = false;
//            isTimeUpdaterRunning = false;
//            isDuhaSunriseUpdaterRunning = false;
//            isMaintenanceRunning = false;
//
//            Log.d(TAG, "All managed threads stopped");
//        } catch (Exception e) {
//            Log.e(TAG, "Error stopping managed threads: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Schedule main logic thread
//     */
//    private void scheduleMainLogic() {
//        if (!isMainLogicRunning) {
//            activity.log("MainLogicRunnable: isMainLogicRunning is false, not scheduling.");
//            return;
//        }
//
//        threadManager.scheduleRepeatingTask("MainLogic", new ThreadManager.ScheduledTask() {
//            @Override
//            public void execute() {
//                try {
//                    if (activity.isPrayerListUpdating()) {
//                        activity.log("MainLogicRunnable: isPrayerListUpdating is true, will retry in 1s.");
//                        return;
//                    }
//
//                    activity.log("MainLogicRunnable is alive and not updating prayer list");
//                    activity.updateDateComponents();
//                    activity.updateCalendars();
//
//                    // Handle prayer times using prayer manager
//                    activity.handlePrayerTimesUpdate();
//
//                    if (activity.getTimingsAlrabeeaTimes() == null) {
//                        activity.getPrayerTimesThisYear();
//                    } else {
//                        threadManager.executeOnUiThread(() -> {
//                            try {
//                                activity.selectNextPrayerAndDisplay();
//                            } catch (Exception e) {
//                                activity.log("MainLogicRunnable UI update error: " + e.getMessage());
//                                CrashlyticsUtils.INSTANCE.logException(e);
//                            }
//                        });
//                    }
//
//                } catch (Exception e) {
//                    activity.log("MainLogicRunnable caught exception: " + e.getMessage());
//                    CrashlyticsUtils.INSTANCE.logException(e);
//                }
//            }
//
//            @Override
//            public long getNextDelay() {
//                if (!isMainLogicRunning) {
//                    return -1;
//                }
//
//                try {
//                    if (activity.isPrayerListUpdating()) {
//                        return 1000;
//                    }
//
//                    if (activity.getTimingsAlrabeeaTimes() == null) {
//                        return 5000;
//                    }
//
//                    long currentTime = System.currentTimeMillis();
//                    long nextRunTimestamp = activity.selectNextPrayerAndDisplay();
//
//                    if (nextRunTimestamp > currentTime) {
//                        long delay = nextRunTimestamp - currentTime;
//                        delay = Math.min(delay, 60000);
//                        activity.log("MainLogicRunnable will reschedule in " + delay + " ms for timestamp "
//                                + nextRunTimestamp);
//                        return delay;
//                    } else {
//                        return 100;
//                    }
//                } catch (Exception e) {
//                    activity.log("MainLogicRunnable getNextDelay error: " + e.getMessage());
//                    return 1000;
//                }
//            }
//        }, 1000);
//    }
//
//    /**
//     * Resume all threads
//     */
//    public void resumeAll() {
//        try {
//            if (threadManager != null) {
//                threadManager.resumeAll();
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error resuming threads: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Pause all threads
//     */
//    public void pauseAll() {
//        try {
//            if (threadManager != null) {
//                threadManager.pauseAll();
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error pausing threads: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Shutdown thread manager
//     */
//    public void shutdown() {
//        try {
//            stopAllManagedThreads();
//            if (threadManager != null) {
//                threadManager.shutdown();
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error shutting down thread manager: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Execute on UI thread
//     */
//    public void executeOnUiThread(Runnable runnable) {
//        try {
//            if (threadManager != null) {
//                threadManager.executeOnUiThread(runnable);
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error executing on UI thread: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    /**
//     * Execute on background thread
//     */
//    public void executeOnThread(String taskName, Runnable runnable) {
//        try {
//            if (threadManager != null) {
//                threadManager.executeOnThread(taskName, runnable);
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error executing on background thread: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//}
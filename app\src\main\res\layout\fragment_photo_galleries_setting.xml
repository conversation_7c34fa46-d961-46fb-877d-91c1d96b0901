<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".UI.Fragment.settings.content.photoGallery.PhotoGalleriesSettingFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_10sdp"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/enable_gallery_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:text="@string/enable_photo_gallery"
            android:textSize="@dimen/_14sdp" />

        <LinearLayout
            android:id="@+id/linear_layout_galleries_setting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/uploadPhoto_LinearLayout_PhotoGalleryFragment"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_30sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/_25sdp"
                    android:layout_height="@dimen/_25sdp"
                    app:srcCompat="@drawable/ic_picture"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_4sdp"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:includeFontPadding="false"
                    android:text="@string/create_a_photo_gallery"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_14sdp"
                    android:textStyle="bold" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/photo_RecyclerView_PhotoGalleryFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:overScrollMode="never"

                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:reverseLayout="false"
                app:spanCount="1"
                tools:itemCount="2"
                tools:listitem="@layout/galleries_layout" />
        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
package com.arapeak.alrbea.UI.Fragment.settings.content.athkar;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.athkar.content.AthkarsAfterPrayerFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.athkar.content.SettingsAlAhadethFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.athkar.content.SettingsAthkarsForMorningAndEveningFragment;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.List;

public class SettingsAthkarsFragment extends AlrabeeaTimesFragment implements AdapterCallback {

    private static final String TAG = "SettingsAthkarsFragment";

    private View settingsAthkarsView;
    private RecyclerView settingItemRecyclerView;

    private SettingsAdapter settingsAdapter;

    public SettingsAthkarsFragment() {

    }

    public static SettingsAthkarsFragment newInstance() {
        return new SettingsAthkarsFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        settingsAthkarsView = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return settingsAthkarsView;
    }

    private void initView() {

        settingItemRecyclerView = settingsAthkarsView.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));
        settingsAdapter = new SettingsAdapter(getContext(), setupAthkarsSettings(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()) {
//            SettingsLandscapeActivity.setTextTite(getString(R.string.athkars_ahadeth_settings));
//        } else {
//            SettingsActivity.setTextTite(getString(R.string.athkars_ahadeth_settings));
//        }
        SettingsActivity.setTextTite(getString(R.string.athkars_ahadeth_settings));
        settingItemRecyclerView.setAdapter(settingsAdapter);
    }

    private void SetAction() {

    }

    private List<SettingAlrabeeaTimes> setupAthkarsSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] athkarsSettingsTitleArray;
        athkarsSettingsTitleArray = getResources().getStringArray(R.array.athkars_title);


        List<Integer> settingsIconArray = setupAthkarsSettingsIconArray(athkarsSettingsTitleArray.length);


        for (int i = 0; i < athkarsSettingsTitleArray.length; ++i) {
            settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(athkarsSettingsTitleArray[i]
                    , ""
                    , settingsIconArray.get(i)));
        }

        return settingAlrabeeaTimes;
    }

    private List<Integer> setupAthkarsSettingsIconArray(int length) {
        int[] settingsIconArray = new int[length];
        List<Integer> settingsList = new ArrayList<>();

        settingsList.add(R.drawable.ackernight);

        settingsList.add(R.drawable.ackersleh);

        // settingsList.add(R.drawable.acker);

        return settingsList;
    }

    @Override
    public void onItemClick(int position, String tag) {
        switch (position) {
            case 0:
                Utils.loadFragment(SettingsAthkarsForMorningAndEveningFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 1:
                Utils.loadFragment(AthkarsAfterPrayerFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 2:
                Utils.loadFragment(SettingsAlAhadethFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
        }
    }
}

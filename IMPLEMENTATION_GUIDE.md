# Implementation Guide - Android TV App Fixes

## Quick Start Implementation

### Step 1: Backup Current Code
```bash
# Create a backup branch
git checkout -b backup-original-code
git add .
git commit -m "Backup original code before fixes"
git checkout main
```

### Step 2: Apply Critical Fixes (Priority Order)

#### 2.1 Replace Exception Handler (IMMEDIATE)
- ✅ **File**: `MyExceptionHandler.java` - Already updated
- **Impact**: Prevents infinite restart loops
- **Test**: Force an exception and verify restart behavior

#### 2.2 Update Application Controller (IMMEDIATE)
- ✅ **File**: `AppController.java` - Already updated
- **Impact**: Better memory management and initialization
- **Test**: Monitor memory usage during app startup

#### 2.3 Implement Thread Manager (HIGH PRIORITY)
- ✅ **File**: `ThreadManager.java` - Already created
- **Usage**: Replace all direct Handler usage with ThreadManager
- **Test**: Monitor thread count and lifecycle

### Step 3: Update AndroidManifest.xml

```xml
<!-- Replace the current service declaration -->
<service
    android:name=".Service.AppMonitorService_Improved"
    android:enabled="true"
    android:exported="false"
    android:foregroundServiceType="systemExempted" />

<!-- Optional: Update MainActivity reference when ready to switch -->
<!-- 
<activity
    android:name=".UI.Activity.MainActivity_Improved"
    android:clearTaskOnLaunch="true"
    android:hardwareAccelerated="true"
    android:launchMode="singleTask"
    android:stateNotNeeded="true"
    android:theme="@style/SplashTheme"
    android:exported="true">
</activity>
-->
```

### Step 4: Gradual Migration Strategy

#### Phase 1: Core Stability (Week 1)
1. **Deploy updated exception handler and app controller**
2. **Monitor crash reports for 3-5 days**
3. **Verify memory usage improvements**

#### Phase 2: Service Improvements (Week 2)
1. **Replace AppMonitorService with improved version**
2. **Update AlarmReceiver to use improved service**
3. **Test 24-hour continuous operation**

#### Phase 3: Threading Fixes (Week 3-4)
1. **Integrate ThreadManager into existing MainActivity**
2. **Replace Handler.postDelayed calls gradually**
3. **Monitor thread count and performance**

## Code Integration Examples

### Example 1: Replace Handler Usage in Existing Code

**Before:**
```java
Handler handler = new Handler();
handler.postDelayed(new Runnable() {
    @Override
    public void run() {
        // Some background task
        updateUI();
    }
}, 5000);
```

**After:**
```java
ThreadManager threadManager = new ThreadManager();
threadManager.scheduleRepeatingTask("BackgroundTask", new ThreadManager.ScheduledTask() {
    @Override
    public void execute() {
        // Some background task
        threadManager.executeOnUiThread(() -> updateUI());
    }
    
    @Override
    public long getNextDelay() {
        return 5000; // or -1 to stop
    }
}, 5000);
```

### Example 2: Update Service Reference

**In any class that starts the service:**
```java
// Replace
Intent serviceIntent = new Intent(context, AppMonitorService.class);

// With
Intent serviceIntent = new Intent(context, AppMonitorService_Improved.class);
```

### Example 3: Add Proper Cleanup

**In Activity onDestroy():**
```java
@Override
protected void onDestroy() {
    super.onDestroy();
    
    // Add this cleanup
    if (threadManager != null) {
        threadManager.shutdown();
        threadManager = null;
    }
    
    // Clear other references
    cleanupUIReferences();
}
```

## Testing Checklist

### ✅ Immediate Testing (After Each Phase)
- [ ] App starts without crashes
- [ ] No infinite restart loops
- [ ] Memory usage stable after 1 hour
- [ ] UI remains responsive
- [ ] Background services working

### ✅ Extended Testing (24-48 hours)
- [ ] No memory leaks detected
- [ ] Thread count remains stable
- [ ] No ANRs reported
- [ ] Battery usage acceptable
- [ ] All features working correctly

### ✅ Stress Testing (1 week)
- [ ] Continuous 24/7 operation
- [ ] Memory usage under 200MB consistently
- [ ] No crashes in Firebase Crashlytics
- [ ] Performance metrics stable

## Monitoring Setup

### Add to build.gradle (app level):
```gradle
dependencies {
    // For memory leak detection (debug builds only)
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
    
    // For performance monitoring
    implementation 'com.google.firebase:firebase-perf'
}
```

### Monitor These Metrics:
1. **Memory Usage**: Should stay under 200MB
2. **Thread Count**: Should not exceed 10-15 active threads
3. **Crash Rate**: Should be < 0.1%
4. **ANR Rate**: Should be 0%

## Rollback Plan

If issues occur after deployment:

### Quick Rollback:
```bash
# Revert to backup
git checkout backup-original-code
git checkout main
git reset --hard backup-original-code
```

### Partial Rollback:
```bash
# Revert specific files
git checkout HEAD~1 -- app/src/main/java/com/arapeak/alrbea/MyExceptionHandler.java
```

## Performance Optimization Tips

### Memory Management:
```java
// Add to Application class
@Override
public void onTrimMemory(int level) {
    super.onTrimMemory(level);
    if (level >= TRIM_MEMORY_MODERATE) {
        // Clear caches
        ImageLoader.getInstance().clearMemoryCache();
        System.gc();
    }
}
```

### Thread Optimization:
```java
// Limit concurrent threads
ThreadManager threadManager = new ThreadManager();
// Use thread pools for similar tasks
ExecutorService executor = Executors.newFixedThreadPool(3);
```

## Common Issues and Solutions

### Issue 1: App Still Crashing
**Solution**: Check Firebase Crashlytics for new crash patterns, may need additional exception handling

### Issue 2: High Memory Usage
**Solution**: Add more aggressive memory cleanup, reduce image cache size

### Issue 3: UI Freezing
**Solution**: Move more operations to background threads using ThreadManager

### Issue 4: Service Not Starting
**Solution**: Check Android version compatibility, update manifest permissions

## Support and Debugging

### Enable Debug Logging:
```java
// Add to AppController
private static final boolean DEBUG = BuildConfig.DEBUG;

public static void log(String tag, String message) {
    if (DEBUG) {
        Log.d(tag, message);
    }
}
```

### Monitor Thread Health:
```java
// Add periodic thread monitoring
threadManager.logThreadStatus(); // Call every 5 minutes
```

### Memory Monitoring:
```java
// Add to maintenance thread
Runtime runtime = Runtime.getRuntime();
long usedMemory = runtime.totalMemory() - runtime.freeMemory();
Log.d(TAG, "Memory usage: " + (usedMemory / 1024 / 1024) + " MB");
```

## Final Notes

1. **Test thoroughly** in development before production
2. **Monitor closely** for the first week after deployment
3. **Keep backup** of working version
4. **Document any issues** encountered during implementation
5. **Update this guide** based on real-world experience

## Contact for Issues

If you encounter problems during implementation:
1. Check Firebase Crashlytics for crash details
2. Review device logs for threading issues
3. Monitor memory usage patterns
4. Test on different Android TV devices if possible

---

**Remember**: These fixes address the core stability issues, but thorough testing is essential before production deployment.
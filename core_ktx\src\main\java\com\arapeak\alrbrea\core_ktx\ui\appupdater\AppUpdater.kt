package com.arapeak.alrbrea.core_ktx.ui.appupdater

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.core.content.FileProvider
import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.data.network.model.LatestVersionResponse
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppCheckListener
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppCheckState
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppUpdateState
import com.arapeak.alrbrea.core_ktx.model.appupdater.AppUpdaterListener
import com.arapeak.alrbrea.core_ktx.repo.AppUpdateRepo
import com.arapeak.alrbrea.core_ktx.repo.utils.CallResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File

class AppUpdater {
    private val repo: AppUpdateRepo = AppUpdateRepo()

    private var state: AppUpdateState = AppUpdateState.Init

    private val TAG = "AppUpdater"

    private val apkName = "update.apk"

    private var downloader: Downloader? = null

    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())


    @Suppress("t")
    fun checkUpdate(context: Context, listener: AppUpdaterListener, optional: Boolean) {
        coroutineScope.launch {

            updateState(listener, AppUpdateState.Checking)

            withContext(Dispatchers.IO) {
                val res = repo.getLatestVersion()
                Log.i(TAG, res.toString())

                withContext(Dispatchers.Main) {

                    if (res is CallResult.Failure) {
                        updateState(listener, AppUpdateState.Failed(context.getString(R.string.failed_check_update)))
                        return@withContext
                    }

                    val installedVersion = getInstalledVersion(context)
                    val latestVersion = (res as? CallResult.Success)?.value?.version ?: 0

                    if (shouldUpdate(installedVersion, latestVersion).not()) {
                        updateState(listener, AppUpdateState.UpToDate)
                        return@withContext
                    }

                    val resS = (res as CallResult.Success).value


                    if (optional) {
                        UpdaterDialogs().initConfirmDialog(
                            context,
                            0,
                            context.getString(R.string.alert),
                            context.getString(R.string.there_is_a_new_version)
                                    + " \"" + resS?.name + "\""
                                    + " " + context.getString(R.string.do_you_want_to_download_it),
                            true,
                            true
                        ) { isSuccessful ->
                            when (isSuccessful) {
                                true -> downloadUpdate(context, listener, resS)
                                false -> updateState(listener, AppUpdateState.Refused)
                            }

                        }
                    } else {
                        downloadUpdate(context, listener, resS)
                    }


                }   // return from error
                Log.i(TAG, "return from errors")

            }


        }

    }

    fun checkUpdateSync(context: Context, listener: AppUpdaterListener) {
        updateState(listener, AppUpdateState.Checking)
        var res: CallResult<LatestVersionResponse?, Exception>? = null
        runBlocking {
            res = repo.getLatestVersion()
            Log.i(TAG, res.toString())
        }
        if (res is CallResult.Failure) {
            updateState(listener, AppUpdateState.Failed(context.getString(R.string.failed_check_update)))
            return
        }
        val installedVersion = getInstalledVersion(context)
        val latestVersion = (res as? CallResult.Success)?.value?.version ?: 0

        if (shouldUpdate(installedVersion, latestVersion).not()) {
            updateState(listener, AppUpdateState.UpToDate)
            return
        }

        val resS = (res as CallResult.Success).value

        downloadUpdate(context, listener, resS)

    }

    fun checkForUpdate(context: Context, listener: AppCheckListener): LatestVersionResponse? {
        updateState(listener, AppCheckState.Checking)
        var res: CallResult<LatestVersionResponse?, Exception>? = null
        runBlocking {
            res = repo.getLatestVersion()
            Log.i(TAG, res.toString())
        }
        if (res is CallResult.Failure) {
            updateState(listener, AppCheckState.Failed(context.getString(R.string.failed_check_update)))
            return null
        }
        val installedVersion = getInstalledVersion(context)
        val latestVersion = (res as? CallResult.Success)?.value?.version ?: 0

        if (shouldUpdate(installedVersion, latestVersion).not()) {
            updateState(listener, AppCheckState.UpToDate)
            return null
        }


        val resS = (res as CallResult.Success).value
        updateState(listener, AppCheckState.Outdated(resS?.link ?: ""))


        return resS
    }


    private fun downloadUpdate(context: Context, listener: AppUpdaterListener, resS: LatestVersionResponse?) {
        downloader = Downloader(context)

        downloader?.downloadUpdate(resS?.link ?: "", apkName,
            onLoading = {
                updateState(listener, AppUpdateState.Downloading(it))
            },
            onSuccess = {
                installApk(listener, context, it)
                updateState(listener, AppUpdateState.Downloaded(it))
            },
            onError = {
                updateState(listener, AppUpdateState.Failed(it))
            }
        )
    }


    private fun installApk(listener: AppUpdaterListener, context: Context, file: String) {
        try {
            val uri = FileProvider.getUriForFile(
                context,
                context.packageName + ".fileprovider",
                File(file)
            )

            val install = Intent(Intent.ACTION_VIEW)
            install.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            install.setDataAndType(uri, "application/vnd.android.package-archive")
            context.startActivity(install)

        } catch (e: Exception) {
            logException(e)
            Log.i(TAG, "installApk error ${e.message}")
            updateState(listener, AppUpdateState.Failed(context.getString(R.string.failed_install_update)))
        }
    }

    private fun updateState(listener: AppUpdaterListener, stat: AppUpdateState) {
        this.state = stat
        listener.onUpdateState(state)

    }

    private fun updateState(listener: AppCheckListener, state: AppCheckState) {
        listener.onUpdateState(state)
    }

    private fun getInstalledVersion(context: Context): Int {
        val pInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        val versionName = pInfo.versionName
        val versionCode = pInfo.versionCode

        Log.i(TAG, "installed version $versionName $versionCode")

        return versionCode
    }

    private fun shouldUpdate(installedVersion: Int, latestVersion: Int): Boolean {
        return latestVersion > installedVersion
    }

    fun close() {
        downloader?.close()
        coroutineScope.cancel()
    }
}

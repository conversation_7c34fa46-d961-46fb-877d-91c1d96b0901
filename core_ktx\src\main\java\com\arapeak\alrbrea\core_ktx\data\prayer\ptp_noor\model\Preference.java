package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import android.content.Context;

import java.util.GregorianCalendar;

/* loaded from: classes.dex */
public class Preference {
    public static final double DEFAULT_PRESSURE = 1010.0d;
    public static final double DEFAULT_SEA_LEVEL = 0.0d;
    public static final double DEFAULT_TEMPERATURE = 10.0d;
    public static String DEFAULT_CITY_ACTUAL_DATE = "2017-03-08";
    public static String DEFAULT_CITY_ID = "2238312";
    public static String DEFAULT_CITY_NAME = "Makkah";
    public static String DEFAULT_COUNTRY_ID = "sa";
    public static String DEFAULT_COUNTRY_NAME = "Saudi Arabia";
    public static int NumDayHijri = 3;
    public static String is_demo_version_Default__app = "--";
    public static Integer DEFAULT_TIMEZONE = 3;
    public static String DEFAULT_LATITUDE = "21.4300003051758";
    public static String DEFAULT_LONGITUDE = "39.8199996948242";
    public static String DEFAULT_CALC_METHOD = "UmmAlQuraUniv";
    public static String DEFAULT_MAZHAB = "Default";
    public static String DEFAULT_SEASON = "Summer";
    public static String SUMMER_SEASON = "Summer";
    public static String WINTER_SEASON = "Winter";
    public static String DEFAULT_HOUR_12_24 = "hour12";
    public static Integer DEFAULT_SEASON_WINTER_MONTH = 11;
    public static Integer DEFAULT_SEASON_SUMMER_MONTH = 4;
    public static Integer DEFAULT_SEASON_WINTER_DAY = 1;
    public static Integer DEFAULT_SEASON_SUMMER_DAY = 1;
    public static Integer DEFAULT_SILENT_DURATION = 2;
    public static Integer DEFAULT_SILENT_DURATION_5 = 30;
    public static Integer DEFAULT_SILENT_DURATION_30 = 40;
    public static Integer DEFAULT_SILENT_START = 3;
    public String UDP_IP;
    public int UDP_PORT;
    public boolean fullScreen_Activity;
    public int powerSaveFajr;
    public int powerSaveIsha;
    public String url_ScreenFullTextURL = "/tv/pages/?t=13";
    public String url_ScreenFullVideoURL = "/tv/pages/?t=17";
    public String url_ScreenVideoURL = "/tv/pages/?t=16";
    public String url_ScreenFatawaURL = "/tv/pages/?t=15";
    public String url_ScreenNewsURL = "/tv/pages/news.php";
    public String url_ScreenLectureURL = "/tv/pages/?t=14";
    //    public IkamaAudio ikamaAudio = new IkamaAudio();
//    public RefreshWidgetMobile refreshWidgetMobile = new RefreshWidgetMobile();
//    public GPS_Travel GPS_travel = new GPS_Travel();
    public City city = null;
    public City city_2 = null;
    protected Context context;

    /* loaded from: classes.dex */
//    public class GPS_Travel {
//        public GPS_Travel() {
//        }
//
//        public String getGPS_Travel_Duration() {
//            return PreferenceManager.getDefaultSharedPreferences(Preference.this.context).getString("GPS_Travel_Duration", "60");
//        }
//
//        public boolean getGPS_Travel_Settings() {
//            return PreferenceManager.getDefaultSharedPreferences(Preference.this.context).getBoolean("GPS_Travel_Settings", false);
//        }
//    }
//
//    /* loaded from: classes.dex */
//    public class IkamaAudio {
//        public IkamaAudio() {
//        }
//
//        public String getIkama_Asr() {
//            return Preference.this.b().getString("ikama_asr", MyTool.azan_none);
//        }
//
//        public String getIkama_AsrAudio() {
//            return Preference.this.b().getString("ikama_asr_audio", MyTool.OtherAzan);
//        }
//
//        public String getIkama_Dhuhr() {
//            return Preference.this.b().getString("ikama_dhuhr", MyTool.azan_none);
//        }
//
//        public String getIkama_DhuhrAudio() {
//            return Preference.this.b().getString("ikama_dhuhr_audio", MyTool.OtherAzan);
//        }
//
//        public String getIkama_Fajr() {
//            return Preference.this.b().getString("ikama_fajr", MyTool.azan_none);
//        }
//
//        public String getIkama_FajrAudio() {
//            return Preference.this.b().getString("ikama_fajr_audio", MyTool.FajrAzan);
//        }
//
//        public String getIkama_Isha() {
//            return Preference.this.b().getString("ikama_isha", MyTool.azan_none);
//        }
//
//        public String getIkama_IshaAudio() {
//            return Preference.this.b().getString("ikama_isha_audio", MyTool.OtherAzan);
//        }
//
//        public String getIkama_Jomo3a() {
//            return Preference.this.b().getString("ikama_jomo3a", MyTool.azan_none);
//        }
//
//        public String getIkama_Jomo3aAudio() {
//            return Preference.this.b().getString("ikama_jomo3a_audio", MyTool.OtherAzan);
//        }
//
//        public String getIkama_Maghrib() {
//            return Preference.this.b().getString("ikama_maghrib", MyTool.azan_none);
//        }
//
//        public String getIkama_MaghribAudio() {
//            return Preference.this.b().getString("ikama_maghrib_audio", MyTool.OtherAzan);
//        }
//
//        public String getIkama_Shuruq() {
//            return Preference.this.b().getString("ikama_shuruq", MyTool.azan_none);
//        }
//
//        public String getIkama_ShuruqAudio() {
//            return Preference.this.b().getString("ikama_shuruq_audio", MyTool.FajrAzan);
//        }
//    }
//
//    /* loaded from: classes.dex */
//    public class RefreshWidgetMobile {
//        public RefreshWidgetMobile() {
//        }
//
//        public boolean getIsScreenRound() {
//            return PreferenceManager.getDefaultSharedPreferences(Preference.this.context).getBoolean("IsScreenRound", false);
//        }
//
//        public boolean getIs_delete_DigitalClockWidget_3() {
//            return Preference.this.b().getBoolean("is_delete_DigitalClockWidget_3", false);
//        }
//
//        public String getRefreshWidget() {
//            return Preference.this.b().getString("RefreshWidget", "2");
//        }
//
//        public boolean getRefreshWidgetOnScreenOn() {
//            return Preference.this.b().getBoolean("RefreshWidgetOnScreenOn", false);
//        }
//
//        public boolean getRefreshWidgetPreviousAndroid() {
//            return Preference.this.b().getBoolean("RefreshWidgetPreviousAndroid", false);
//        }
//
//        public boolean getRefreshWidgetWithoutAlarmIcon() {
//            return Preference.this.b().getBoolean("RefreshWidgetWithoutAlarmIcon", false);
//        }
//
//        public void setIsScreenRound(boolean z) {
//            androidx.appcompat.graphics.drawable.b.d(Preference.this.b(), "IsScreenRound", z);
//        }
//
//        public void setIs_delete_DigitalClockWidget_3(boolean z) {
//            androidx.appcompat.graphics.drawable.b.d(Preference.this.b(), "is_delete_DigitalClockWidget_3", z);
//        }
//
//        public void setRefreshWidget(String str) {
//            d.b(Preference.this.b(), "RefreshWidget", str);
//        }
//
//        public void setRefreshWidgetOnScreenOn(boolean z) {
//            androidx.appcompat.graphics.drawable.b.d(Preference.this.b(), "RefreshWidgetOnScreenOn", z);
//        }
//
//        public void setRefreshWidgetPreviousAndroid(boolean z) {
//            androidx.appcompat.graphics.drawable.b.d(Preference.this.b(), "RefreshWidgetPreviousAndroid", z);
//        }
//
//        public void setRefreshWidgetWithoutAlarmIcon(boolean z) {
//            androidx.appcompat.graphics.drawable.b.d(Preference.this.b(), "RefreshWidgetWithoutAlarmIcon", z);
//        }
//    }

    public Preference(Context context) {
        this.context = context;
    }

//    public SharedPreferences b() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context);
//    }
//
//    public void fetchCurrentPreferences() {
//        SharedPreferences b = b();
//        this.city = new City();
//        this.city_2 = new City_2();
//        this.city.setAllValue();
//        this.city_2.setAllValue();
//        this.powerSaveIsha = b.getInt("powerSaveIsha_preferen", 0);
//        this.powerSaveFajr = b.getInt("powerSaveFajr_preferen", 0);
//        this.UDP_PORT = MyTool.strToInt(getStrPreferenceVal("" + MyTool.UDP_PORT_DEFAULT, "UDP_PORT"), MyTool.UDP_PORT_DEFAULT);
//        this.UDP_IP = getStrPreferenceVal("UDP_IP_DEFAULT_VAL", "UDP_IP");
//    }

//    public String getAL_MoakitaVisiblePrayerInfo() {
//        return b().getString("AL_MoakitaVisiblePrayerInfo", "prayer_info_fasting");
//    }

//    public String getAl_Muaqita_font() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("al_muaqita_font", "4");
//    }
//
//    public String getAlarmSound() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("notSound", "Disable");
//    }
//
//    public String getAlarmTxtContent_AlarmID() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("AlarmTxtContent_AlarmID", "no_id");
//    }
//
//    public int getAlarmTxtContent_Time4Stop() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("AlarmTxtContent_Time4Stop", 2);
//    }
//
//    public String getAlarmTxtContent_Title() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("AlarmTxtContent_Title", "AlarmTxtContent_Title");
//    }
//
//    public String getAlarmTxtContent_Txt() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("AlarmTxtContent_Txt", "AlarmTxtContent_Txt");
//    }
//
//    public String getAlarmTxtContent_WindowTitle() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("AlarmTxtContent_WindowTitle", "AlarmTxtContent_WindowTitle");
//    }
//
//    public boolean getAlarmTxtContent_isTxtContentCustom() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("AlarmTxtContent_isTxtContentCustom", true);
//    }

//    public Boolean getAlarm_azkar_active() {
//        return Boolean.valueOf(b().getBoolean("alarm_azkar_active", true));
//    }

//    public Map<String, ?> getAll() {
//        return b().getAll();
//    }

//    public int getAnalogClockShape() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("AnalogClockShape", 1);
//    }

//    public String getApkFileName() {
//        return b().getString("apkFileName", "-111");
//    }

//    public float[] getApt() {
//        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(this.context);
//        return new float[]{defaultSharedPreferences.getFloat("location_altitude", BitmapDescriptorFactory.HUE_RED), defaultSharedPreferences.getFloat("location_pressure", 1010.0f), defaultSharedPreferences.getFloat("location_temperature", 10.0f)};
//    }

//    public int getAudioManagerStreamMode() {
//        return b().getInt("AudioManagerStreamMode", 0);
//    }
//
//    public boolean getAutoRestart4GPS() {
//        return b().getBoolean("AutoRestart4GPS", true);
//    }

//    public boolean getAutoTurnOnWhenErroe() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("AutoTurnOnWhenErroe", true);
//    }

//    public String getAzan_Asr() {
//        return b().getString("azan_asr", MyTool.azan_none);
//    }
//
//    public String getAzan_AsrAudio() {
//        return b().getString("azan_asr_audio", MyTool.OtherAzan);
//    }
//
//    public String getAzan_Dhuhr() {
//        return b().getString("azan_dhuhr", MyTool.azan_none);
//    }
//
//    public String getAzan_DhuhrAudio() {
//        return b().getString("azan_dhuhr_audio", MyTool.OtherAzan);
//    }
//
//    public String getAzan_Fajr() {
//        return b().getString("azan_fajr", MyTool.azan_none);
//    }
//
//    public String getAzan_FajrAudio() {
//        return b().getString("azan_fajr_audio", MyTool.FajrAzan);
//    }
//
//    public String getAzan_Isha() {
//        return b().getString("azan_isha", MyTool.azan_none);
//    }
//
//    public String getAzan_IshaAudio() {
//        return b().getString("azan_isha_audio", MyTool.OtherAzan);
//    }
//
//    public String getAzan_Jomo3a() {
//        return b().getString("azan_jomo3a", MyTool.azan_none);
//    }
//
//    public String getAzan_Jomo3aAudio() {
//        return b().getString("azan_jomo3a_audio", MyTool.OtherAzan);
//    }
//
//    public boolean getAzan_Jomo3aAudio_Check() {
//        return b().getBoolean("azan_jomo3a_audio_check", false);
//    }
//
//    public String getAzan_Maghrib() {
//        return b().getString("azan_maghrib", MyTool.azan_none);
//    }
//
//    public String getAzan_MaghribAudio() {
//        return b().getString("azan_maghrib_audio", MyTool.OtherAzan);
//    }
//
//    public String getAzan_Shuruq() {
//        return b().getString("azan_shuruq", MyTool.azan_none);
//    }
//
//    public String getAzan_ShuruqAudio() {
//        return b().getString("azan_shuruq_audio", MyTool.FajrAzan);
//    }
//
//    public Boolean getAzan_before_notification_Settings() {
//        return Boolean.valueOf(b().getBoolean("azan_before_notification_Settings", false));
//    }
//
//    public Boolean getAzan_notification_Settings() {
//        return Boolean.valueOf(b().getBoolean("azan_notification_Settings", false));
//    }
//
//    public int getAzkarDurationAsr() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationAsr", Integer.toString(10)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public int getAzkarDurationDhuhr() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationDhuhr", Integer.toString(10)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public int getAzkarDurationFajr() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationFajr", Integer.toString(15)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 15;
//        }
//    }
//
//    public int getAzkarDurationIsha() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationIsha", Integer.toString(15)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 15;
//        }
//    }
//
//    public int getAzkarDurationJomo3a() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationJomo3a", Integer.toString(15)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 15;
//        }
//    }
//
//    public int getAzkarDurationMaghrib() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationMaghrib", Integer.toString(10)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public int getAzkarDurationShuruq() {
//        try {
//            return Integer.parseInt(b().getString("azkarDurationShuruq", Integer.toString(15)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 15;
//        }
//    }
//
//    public int getAzkarStartAsr() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartAsr", Integer.toString(7)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 7;
//        }
//    }
//
//    public int getAzkarStartDhuhr() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartDhuhr", Integer.toString(7)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 7;
//        }
//    }
//
//    public int getAzkarStartFajr() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartFajr", Integer.toString(10)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public int getAzkarStartIsha() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartIsha", Integer.toString(10)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public int getAzkarStartJomo3a() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartJomo3a", Integer.toString(65)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public int getAzkarStartMaghrib() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartMaghrib", Integer.toString(7)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 7;
//        }
//    }
//
//    public int getAzkarStartShuruq() {
//        try {
//            return Integer.parseInt(b().getString("azkarStartShuruq", Integer.toString(10)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 10;
//        }
//    }
//
//    public Boolean getAzkar_notification_Settings() {
//        return Boolean.valueOf(b().getBoolean("azkar_notification_Settings", false));
//    }
//
//    public Boolean getCase_Jom3a_Duhur() {
//        return Boolean.valueOf(b().getBoolean("Case_Jom3a_Duhur", false));
//    }
//
//    public boolean getChangLanApp() {
//        return b().getBoolean("ChangLanApp", false);
//    }
//
//    public boolean getChangeSeasonPrayerNotShowAgainMsg() {
//        return b().getBoolean("changeSeasonPrayerNotShowAgainMsg", false);
//    }
//
////    public boolean getChangeSeasonSummerWinterDLG_visible() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("changeSeasonSummerWinterDLG_visible", false);
////    }
//
//    public int getChangedVolume() {
//        return b().getInt("changedVolume", -1);
//    }
//
//    public Boolean getCheck20180126() {
//        return Boolean.valueOf(b().getBoolean("Check20180126", false));
//    }
//
//    public boolean getCheckPermissionAndroid6() {
//        return b().getBoolean("CheckPermissionAndroid6", false);
//    }
//
//    public boolean getCity_Selected() {
//        return b().getBoolean("City_Selected", false);
//    }
//
////    public int getColorSelectPrayerTime() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("ColorSelectPrayerTime", Color.parseColor("#7DB2D0"));
////    }
////
////    public int getColorSelectPrayerTimeScreen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("ColorSelectPrayerTimeScreen", Color.parseColor("#3a5e53"));
////    }
//
//    public String getCurrentAlarmID() {
//        return b().getString("CurrentAlarmID", "CurrentAlarmID");
//    }
//
//    public int getCurrentVolume() {
//        return b().getInt("currentVolume", -1);
//    }
//
//    public int getCurrent_Prayer_Today() {
//        return b().getInt("Current_Prayer_Today", 0);
//    }
//
////    public int getDateTypeSelected() {
////        String string = PreferenceManager.getDefaultSharedPreferences(this.context).getString("date_type_selected", "gregorian");
////        if (string.equals("gregorian")) {
////            return 1;
////        }
////        if (string.equals("hijri")) {
////            return 2;
////        }
////        return 3;
////    }
////
////    public String getDateTypeSelectedStr() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("date_type_selected", "gregorian");
////    }
//
//    public String getDefaultDesignTV() {
//        return b().getString("DefaultDesignTV", "ar");
//    }
//
//    public String getDesignTV_Font() {
//        return b().getString("designTV_Font", "Hacen-Fonts.ttf");
//    }
//
//    public MyTool.MainActivityType getDeviceType() {
//        MyTool.MainActivityType mainActivityType = MyTool.MainActivityType.NONE;
//        String string = b().getString("devicetype", "NONE");
//        if (string.equals("MOBILE")) {
//            return MyTool.MainActivityType.MOBILE;
//        }
//        if (string.equals("TV")) {
//            return MyTool.MainActivityType.TV;
//        }
//        return mainActivityType;
//    }
//
////    public String getDigitalDateFormat() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("DigitalDateFormat", "dd-mm-yyyy");
////    }
////
////    public String getDigitalDateSep() {
////        return b().getString("DigitalDateSep", this.context.getString(R.string.DigitalDateSep_default));
////    }
//
//    public boolean getDigitalDate_zero_day_month() {
//        return b().getBoolean("DigitalDate_zero_day_month", false);
//    }
//
////    public boolean getDo_batteryOptimizationNot() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Do_batteryOptimizationNot", true);
////    }
////
////    public String getDuration_hide_notification() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("duration_hide_notification", "30");
////    }
//
//    public String getDynamic_design_Landscape_File() {
//        return b().getString("dynamic_design_Landscape_File", "Sample123.json");
//    }
//
//    public String getDynamic_design_Portrait_File() {
//        return b().getString("dynamic_design_Portrait_File", "Sample123.json");
//    }
//
//    public boolean getEnableAzkarAsr() {
//        return b().getBoolean("EnableAzkarAsr", false);
//    }
//
//    public boolean getEnableAzkarDhuhr() {
//        return b().getBoolean("EnableAzkarDhuhr", false);
//    }
//
//    public boolean getEnableAzkarFajr() {
//        return b().getBoolean("EnableAzkarFajr", false);
//    }
//
//    public boolean getEnableAzkarIsha() {
//        return b().getBoolean("EnableAzkarIsha", false);
//    }
//
//    public boolean getEnableAzkarJomo3a() {
//        return b().getBoolean("EnableAzkarJomo3a", false);
//    }
//
//    public boolean getEnableAzkarMaghrib() {
//        return b().getBoolean("EnableAzkarMaghrib", false);
//    }
//
//    public boolean getEnableAzkarShuruq() {
//        return b().getBoolean("EnableAzkarShuruq", false);
//    }
//
//    public boolean getEnableScreenAsr() {
//        return b().getBoolean("EnableScreenAsr", false);
//    }
//
//    public boolean getEnableScreenDhuhr() {
//        return b().getBoolean("EnableScreenDhuhr", false);
//    }
//
//    public boolean getEnableScreenFajr() {
//        return b().getBoolean("EnableScreenFajr", false);
//    }
//
//    public boolean getEnableScreenIsha() {
//        return b().getBoolean("EnableScreenIsha", false);
//    }
//
//    public boolean getEnableScreenJomo3a() {
//        return b().getBoolean("EnableScreenJomo3a", false);
//    }
//
//    public boolean getEnableScreenMaghrib() {
//        return b().getBoolean("EnableScreenMaghrib", false);
//    }
//
//    public boolean getEnableScreenShuruq() {
//        return b().getBoolean("EnableScreenShuruq", false);
//    }
//
//    public boolean getEnableSilentAsr() {
//        return b().getBoolean("EnableSilentAsr", false);
//    }
//
//    public boolean getEnableSilentAtFirst() {
//        return b().getBoolean("EnableSilentAtFirst", false);
//    }
//
//    public boolean getEnableSilentDhuhr() {
//        return b().getBoolean("EnableSilentDhuhr", false);
//    }
//
//    public boolean getEnableSilentFajr() {
//        return b().getBoolean("EnableSilentFajr", false);
//    }
//
//    public boolean getEnableSilentIsha() {
//        return b().getBoolean("EnableSilentIsha", false);
//    }
//
//    public boolean getEnableSilentJomo3a() {
//        return b().getBoolean("EnableSilentJomo3a", false);
//    }
//
//    public boolean getEnableSilentMaghrib() {
//        return b().getBoolean("EnableSilentMaghrib", false);
//    }
//
//    public boolean getEnableSilentSound() {
//        return b().getBoolean("EnableSilentSound", false);
//    }
//
//    public boolean getEnableSilentTarawih() {
//        return b().getBoolean("EnableSilentTarawih", false);
//    }
//
//    public boolean getEnableSilentVibration() {
//        return b().getBoolean("EnableSilentVibration", true);
//    }
//
////    public String getFatawaFilePath() {
////        String string = b().getString("fatawaFilePath", "-1");
////        if (string.equals("-1")) {
////            return MyTool.getFirstFatawaFile();
////        }
////        return string;
////    }
////
////    public boolean getFullScreen_Activity() {
////        boolean z = PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("FullScreen_Activity", true);
////        this.fullScreen_Activity = z;
////        return z;
////    }
//
//    public boolean getFull_name_day_month_en() {
//        return b().getBoolean("Full_name_day_month_en", false);
//    }

    public double getGMTOffset() {
        GregorianCalendar gregorianCalendar = new GregorianCalendar();
        return gregorianCalendar.getTimeZone().getOffset(gregorianCalendar.getTimeInMillis()) / 3600000;
    }

//    public boolean getGoToMain() {
//        return b().getBoolean("GoToMain", false);
//    }
//
////    public boolean getHide_notification_Settings() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("hide_notification_Settings", true);
////    }
////
////    public HijriDate getHijriDate(int i, int i2, int i3, int i4, String str) {
////        Calendar calendar = Calendar.getInstance();
////        calendar.set(5, i);
////        calendar.set(2, i2 - 1);
////        calendar.set(1, i3);
////        calendar.add(5, i4);
////        HijriCalendar hijriCalendar = new HijriCalendar(calendar.get(1), calendar.get(2) + 1, calendar.get(5));
////        String DateToStr = MyTool.DateToStr(i3, i2, i);
////        int hijriYear = hijriCalendar.getHijriYear();
////        int hijriMonth = hijriCalendar.getHijriMonth();
////        int hijriDay = hijriCalendar.getHijriDay();
////        if (DateToStr.equals(str)) {
////            hijriDay = 30;
////        }
////        return new HijriDate(hijriYear, hijriMonth, hijriDay);
////    }
//
//    public int getHijriDatePreference() {
//        return b().getInt("HijriDatePreference", 0);
//    }
//
////    public String getHijriDatePreference_30() {
////        return b().getString("HijriDatePreference_30", HelpFormatter.DEFAULT_OPT_PREFIX);
////    }
////
////    public Hour getHour12_24() {
////        return new Hour(PreferenceManager.getDefaultSharedPreferences(this.context).getString("hour", DEFAULT_HOUR_12_24));
////    }
////
////    public String getHour12_24Pref() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("hour", DEFAULT_HOUR_12_24);
////    }
////
////    public String getIP1_screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("IP1_screen", "192");
////    }
////
////    public String getIP2_screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("IP2_screen", "168");
////    }
////
////    public String getIP3_screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("IP3_screen", "43");
////    }
////
////    public String getIP4_screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("IP4_screen", SettingsActivity_2.CityType_1);
////    }
//
//    public Boolean getIcon_alarm_notification_Settings() {
//        return Boolean.valueOf(b().getBoolean("icon_alarm_notification_Settings", true));
//    }
//
//    /* JADX WARN: Unreachable blocks removed: 2, instructions: 2 */
////    public int getIkamaPrayerVal(int i, String str) {
////        String str2 = "0";
////        try {
////            String string = PreferenceManager.getDefaultSharedPreferences(this.context).getString(str, this.context.getString(i));
////            if (string != "") {
////                str2 = string;
////            }
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
////        String num = Integer.toString(MyTool.strToInt(str2));
////        int i2 = 0;
////        try {
////            i2 = Integer.parseInt(this.context.getString(i));
////            return Integer.parseInt(num);
////        } catch (NumberFormatException e2) {
////            e2.printStackTrace();
////            return i2;
////        }
////    }
//
//    public boolean getIkama_Activity_Visible() {
//        return b().getBoolean("Ikama_Activity_Visible", true);
//    }
//
//    public Boolean getIkama_notification_Settings() {
//        return Boolean.valueOf(b().getBoolean("ikama_notification_Settings", false));
//    }
//
//    public Boolean getImask_Time() {
//        return Boolean.valueOf(b().getBoolean("Imask_Time", false));
//    }
//
////    public String getImsak_before_fajr_value_minutes() {
////        return b().getString("Imsak_before_fajr_value_minutes", this.context.getString(R.string.Imsak_before_fajr_value_minutes));
////    }
////
////    public int getIntPreferenceVal(int i, String str) {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt(str, i);
////    }
//
//    public boolean getIsDownloadDesignTV() {
//        return b().getBoolean("IsDownloadDesignTV", true);
//    }
//
//    public Boolean getIsEnabledChangeVolume() {
//        return Boolean.valueOf(b().getBoolean("IsEnabledChangeVolume", false));
//    }
//
////    public boolean getIsPressHome_ScreenActivity() {
////        boolean z = PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("IsPressHome_ScreenActivity", false);
////        MyTool.MyLog("startScreenActivity", "111" + z);
////        return z;
////    }
////
////    public boolean getIsRINGER_MODE_NORMAL() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("IsRINGER_MODE_NORMAL", false);
////    }
//
//    public Boolean getIsScreenOn() {
//        return Boolean.valueOf(b().getBoolean("IsScreenOn", true));
//    }
//
////    public Boolean getIs_hour_24_pref() {
////        return Boolean.valueOf(PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("is_hour_24_pref", false));
////    }
////
////    public String getIsha_Magrib_Between_value_minutes() {
////        return "" + MyTool.strToInt(b().getString("Isha_Magrib_Between_value_minutes", this.context.getString(R.string.Isha_Magrib_Between_value_minutes)), MyTool.strToInt(this.context.getString(R.string.Isha_Magrib_Between_value_minutes), 90));
////    }
//
//    public boolean getItems_Form_Properties_Edit() {
//        return b().getBoolean("Items_Form_Properties_Edit", false);
//    }
//
////    public String getLanguageLocale() {
////        return b().getString("LanguageLocale", MyTool.getCurrentLang());
////    }
////
////    public String getLecturesFilePath() {
////        String string = b().getString("lecturesFilePath", "-1");
////        if (string.equals("-1")) {
////            return MyTool.getFirstLecturesFile();
////        }
////        return string;
////    }
////
////    public boolean getLoad_Default_Dynamic_Screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Load_Default_Dynamic_Screen", true);
////    }
////
////    public float[] getLocation() {
////        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(this.context);
////        return new float[]{defaultSharedPreferences.getFloat("location_latitude", 21.4225f), defaultSharedPreferences.getFloat("location_longitude", 39.8261f)};
////    }
//
//    public String getLongMonthNames() {
//        return b().getString("long_month_names", "NameWithNumber");
//    }
//
//    public int getLongMonthNamesDecreaseFontSize() {
//        return b().getInt("long_month_names_Decrease_Font_Size", 75);
//    }
//
////    public int getMiladi_month() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("miladi_month", "month_yanayer").equals("month_yanayer") ? 1 : 0;
////    }
////
////    public String getMiladi_month_yanayer() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("miladi_month", "month_yanayer");
////    }
////
////    public String getMobileBackgroundType() {
////        return b().getString("MobileBackgroundType", MobileBackgrounds.background_Al_Masjid_an_Nabawi.getType());
////    }
////
////    public String getMobileDesignType() {
////        return b().getString("MobileDesignType", MobileDesigns.design_clock.getType());
////    }
//
//    public String getMode_orientation() {
//        return b().getString("mode_orientation", "portrait");
//    }
//
//    public String getMode_orientation_new() {
//        return b().getString("orientation_screen_preference_new", "orientation_none");
//    }
//
//    public int getMove_value() {
//        return b().getInt("Move_value", 50);
//    }
//
//    public boolean getMust_request_random() {
//        return b().getBoolean("must_request_random", false);
//    }
//
//    public boolean getMy_City_Selected() {
//        return b().getBoolean("My_City_Selected", false);
//    }
//
//    public boolean getNew_Mobile_Design() {
//        return b().getBoolean("New_Mobile_Design", true);
//    }
//
////    public String getNewsFilePath() {
////        String string = b().getString("newsFilePath", "-1");
////        if (string.equals("-1")) {
////            return MyTool.getFirstNewsFile();
////        }
////        return string;
////    }
//
//    public String getNotiContentText() {
//        return b().getString("NotiContentText", "NotiContentText");
//    }
//
//    public String getNotiContentTitle() {
//        return b().getString("NotiContentTitle", "NotiContentTitle");
//    }
//
//    public int getNotiID() {
//        return b().getInt("NotiID", 123);
//    }
//
//    public long getNotiWhen() {
//        return b().getLong("NotiWhen", System.currentTimeMillis());
//    }
//
//    public boolean getOpenAppOnAzan() {
//        return b().getBoolean("open_app_on_azan", false);
//    }
//
//    public String getOrientaionTV() {
//        return b().getString("orientaionTV", "LANDSCAPE");
//    }
//
//    public String getParamT4RandomRequest() {
//        return b().getString("ParamT4RandomRequest", "null");
//    }
//
//    public Integer getPauseVideoPosition() {
//        return Integer.valueOf(b().getInt("pauseVideoPosition", -111));
//    }
//
//    public Integer getPause_productsList_index() {
//        return Integer.valueOf(b().getInt("pause_productsList_index", -111));
//    }
//
//    public Boolean getPause_resume_update_data() {
//        return Boolean.valueOf(b().getBoolean("pause_resume_update_data", false));
//    }
//
//    public boolean getPlayAzanSound() {
//        return b().getBoolean("play_azan_sound", false);
//    }
//
//    public Boolean getPlayInSilentMode() {
//        boolean z = true;
//        if (getAudioManagerStreamMode() != 1) {
//            z = false;
//        }
//        return Boolean.valueOf(z);
//    }
//
//    public Boolean getPlayInSilentMode1() {
//        return Boolean.valueOf(b().getBoolean("PlayInSilentMode", false));
//    }
//
//    public boolean getPlayQuranSound() {
//        return b().getBoolean("play_quran_sound", false);
//    }
//
//    public boolean getPlaySoundOnSilentBegin() {
//        return b().getBoolean("PlaySoundOnSilentBegin", false);
//    }
//
////    public int getPowerSaveAutoTurnOff() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("powerSaveAutoTurnOff_preferen", 2);
////    }
////
////    public int getPowerSaveFajr() {
////        int i = PreferenceManager.getDefaultSharedPreferences(this.context).getInt("powerSaveFajr_preferen", 0);
////        this.powerSaveFajr = i;
////        return i;
////    }
////
////    public int getPowerSaveIsha() {
////        int i = PreferenceManager.getDefaultSharedPreferences(this.context).getInt("powerSaveIsha_preferen", 0);
////        this.powerSaveIsha = i;
////        return i;
////    }
////
////    public boolean getPrayerWidgetNotificationSettings() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("prayer_widget_notification_Settings", true);
////    }
////
////    public boolean getPrayer_News() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Prayer_News", false);
////    }
////
////    public boolean getPrayer_News_From_Screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Prayer_News_From_Screen", false);
////    }
////
////    public boolean getPrayer_News_Old_Method() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Prayer_News_Old_Method", true);
////    }
////
////    public String getPrayer_News_txt() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("Prayer_News_Txt", "");
////    }
////
////    public String getPreference_Property_name() {
////        return b().getString("Preference_Property_name", "-111");
////    }
//
//    public Boolean getPrivateCodeDesignTV() {
//        return Boolean.valueOf(b().getBoolean("PrivateCodeDesignTV", false));
//    }
//
//    public int getQuran_audioOnly() {
//        return b().getInt("quran_audioOnly", 0);
//    }
//
////    public String getQuran_date_bagin() {
////        return b().getString("quran_date_bagin", Alarm.NONE_STR);
////    }
//
//    public int getQuran_parts() {
//        return b().getInt("quran_parts", 0);
//    }
//
//    public int getQuran_quarters() {
//        return b().getInt("quran_quarters", 0);
//    }
//
//    public int getQuran_showTafseer() {
//        return b().getInt("quran_showTafseer", 1);
//    }
//
////    public String getQuran_time() {
////        return b().getString("quran_time", Alarm.NONE_STR);
////    }
////
////    public String getQuran_time_prayer() {
////        return b().getString("quran_time_prayer", Alarm.NONE_STR);
////    }
////
////    public String getQuran_times() {
////        return b().getString("quran_times", Alarm.NONE_STR);
////    }
//
//    public String getRandom_Date4Request() {
//        return b().getString("Random_Date4Request", "null");
//    }
//
//    public String getRandom_NextDate4Request() {
//        return b().getString("Random_NextDate4Request", "null");
//    }
//
//    public String getRandom_days4Request() {
//        return b().getString("Random_days4Request", "7");
//    }
//
//    public String getRandom_number4Request() {
//        return b().getString("Random_number4Request", "325590");
//    }
//
////    public String getRestartAppMSG() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("RestartAppMSG", "NONE");
////    }
////
////    public boolean getRun_Full_Image() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Full_Image", false);
////    }
////
////    public boolean getRun_Full_PDF() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Full_PDF", false);
////    }
////
////    public boolean getRun_Full_Video() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Full_Video", false);
////    }
////
////    public boolean getRun_Screen() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Run_Screen", false);
////    }
////
////    public boolean getRun_Screen_Alwayes() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Run_Screen_Alwayes", false);
////    }
////
////    public boolean getRun_Screen_Alwayes_with_events() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Run_Screen_Alwayes_with_events", false);
////    }
//
//    public String getScreenBackground() {
//        return b().getString("ScreenBackground", "/xxx/xx.xxx");
//    }
//
//    public int getScreenDurationAsr() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationAsr", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public int getScreenDurationDhuhr() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationDhuhr", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public int getScreenDurationFajr() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationFajr", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public int getScreenDurationIsha() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationIsha", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public int getScreenDurationJomo3a() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationJomo3a", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public int getScreenDurationMaghrib() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationMaghrib", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public int getScreenDurationShuruq() {
//        try {
//            return Integer.parseInt(b().getString("screenDurationShuruq", Integer.toString(25)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return 25;
//        }
//    }
//
//    public String getScreenFatawaURL() {
//        return b().getString("ScreenFatawaURL", getScreenURL() + this.url_ScreenFatawaURL);
//    }
//
//    public String getScreenFatawaURL_net() {
//        return b().getString("ScreenFatawaURL_net", getScreenURL_net() + this.url_ScreenFatawaURL);
//    }
//
//    public String getScreenFullTextURL() {
//        return b().getString("ScreenFullTextURL", getScreenURL() + this.url_ScreenFullTextURL);
//    }
//
//    public String getScreenFullTextURL_net() {
//        return b().getString("ScreenFullTextURL_net", getScreenURL_net() + this.url_ScreenFullTextURL);
//    }
//
//    public String getScreenFullVideoURL() {
//        return b().getString("ScreenFullVideoURL", getScreenURL() + this.url_ScreenFullVideoURL);
//    }
//
//    public String getScreenFullVideoURL_net() {
//        return b().getString("ScreenFullVideoURL_net", getScreenURL_net() + this.url_ScreenFullVideoURL);
//    }
//
//    public String getScreenLectureURL() {
//        return b().getString("ScreenLectureURL", getScreenURL() + this.url_ScreenLectureURL);
//    }
//
//    public String getScreenLectureURL_net() {
//        return b().getString("ScreenLectureURL_net", getScreenURL_net() + this.url_ScreenLectureURL);
//    }
//
//    public MyTool.ScreenManagementType getScreenManagementType() {
//        MyTool.ScreenManagementType screenManagementType = MyTool.ScreenManagementType.REMOTE;
//        String string = b().getString("screenManagementType", "REMOTE");
//        if (string.equals("LOCAL")) {
//            return MyTool.ScreenManagementType.LOCAL;
//        }
//        if (!string.equals("REMOTE") && string.equals("REMOTE_NET")) {
//            return MyTool.ScreenManagementType.REMOTE_NET;
//        }
//        return screenManagementType;
//    }
//
//    public String getScreenNewsURL() {
//        return b().getString("ScreenNewsURL", getScreenURL() + this.url_ScreenNewsURL);
//    }
//
//    public String getScreenNewsURL_net() {
//        return b().getString("ScreenNewsURL_net", getScreenURL_net() + this.url_ScreenNewsURL);
//    }
//
////    public boolean getScreenOnAlwayes() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Screen_On_Alwayes", false);
////    }
//
//    public String getScreenOrientation() {
//        return b().getString("ScreenOrientation", "0");
//    }
//
//    public int getScreenStartAsr() {
//        try {
//            return Integer.parseInt(b().getString("screenStartAsr", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
//    public int getScreenStartDhuhr() {
//        try {
//            return Integer.parseInt(b().getString("screenStartDhuhr", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
//    public int getScreenStartFajr() {
//        try {
//            return Integer.parseInt(b().getString("screenStartFajr", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
//    public int getScreenStartIsha() {
//        try {
//            return Integer.parseInt(b().getString("screenStartIsha", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
//    public int getScreenStartJomo3a() {
//        try {
//            return Integer.parseInt(b().getString("screenStartJomo3a", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
//    public int getScreenStartMaghrib() {
//        try {
//            return Integer.parseInt(b().getString("screenStartMaghrib", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
//    public int getScreenStartShuruq() {
//        try {
//            return Integer.parseInt(b().getString("screenStartShuruq", Integer.toString(-30)));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return -30;
//        }
//    }
//
////    public boolean getScreenTV_Alwayes() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Screen_TV_Alwayes", false);
////    }
//
//    public String getScreenType() {
//        return b().getString("ScreenType", "landscape");
//    }
//
//    public String getScreenURL() {
//        return b().getString("ScreenURL", "http://localhost:8080");
//    }
//
//    public String getScreenURL_2() {
//        return b().getString("ScreenURL_2", "http://localhost:8080");
//    }
//
//    public String getScreenURL_net() {
//        return b().getString("ScreenURL_net", "http://awailalsham.com");
//    }
//
//    public String getScreenVideoURL() {
//        return b().getString("ScreenVideoURL", getScreenURL() + this.url_ScreenVideoURL);
//    }
//
//    public String getScreenVideoURL_net() {
//        return b().getString("ScreenVideoURL_net", getScreenURL_net() + this.url_ScreenVideoURL);
//    }
//
////    public boolean getScreen_Font_Increase() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Screen_Font_Increase", true);
////    }
////
////    public boolean getScreen_Font_Increase_Tab() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("Screen_Font_Increase_Tab", false);
////    }
//
//    public String getSeason() {
//        return b().getString("season", DEFAULT_SEASON);
//    }
//
//    public boolean getSeasonPrayerNotChange() {
//        return b().getBoolean("SeasonPrayerNotChange", false);
//    }

//    public String getSerialVersion__app() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("is_demo_version", is_demo_version_Default__app);
//    }
//
//    public String getSerialVersion__screen() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("is_demo_version__screen", is_demo_version_Default__app);
//    }

//    public String getServerURL() {
//        if (getScreenManagementType() == MyTool.ScreenManagementType.REMOTE) {
//            return getScreenURL();
//        }
//        if (getScreenManagementType() == MyTool.ScreenManagementType.REMOTE_NET) {
//            return getScreenURL_net();
//        }
//        return "";
//    }
//
//    public String getServerURLNet() {
//        return getScreenURL();
//    }
//
//    public boolean getShortCutMainScreen() {
//        return b().getBoolean("ShortCutMainScreen", false);
//    }

//    public boolean getShowBlackWhenTurnOff() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("showBlackWhenTurnOff", true);
//    }

//    public boolean getShowIkamaRemainOnWidget() {
//        return b().getBoolean("show_ikama_remain_on_widget", false);
//    }

//    public boolean getShowPowerManagerSettings_not_visible_again() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("ShowPowerManagerSettings_not_visible_again", false);
//    }
//
//    public boolean getShowPowerManagerSettings_other_DLG_visible() {
//        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("ShowPowerManagerSettings_other_DLG_visible", false);
//    }

//    public boolean getShowShurouqRemain() {
//        return b().getBoolean("show_shurouq_remain", false);
//    }
//
//    public Boolean getShow_azkar_notification_Window() {
//        return Boolean.valueOf(b().getBoolean("show_azkar_notification_Window", false));
//    }
//
//    public String getSilentBeginAudio() {
//        return b().getString("silent_begin_audio", MyTool.takbeerAzan);
//    }
//
//    public int getSilentDuration() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDuration", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationAsr() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationAsr", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationDhuhr() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationDhuhr", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationFajr() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_30.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationFajr", Integer.toString(DEFAULT_SILENT_DURATION_30.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationIsha() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationIsha", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationJomo3a() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_30.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationJomo3a", Integer.toString(DEFAULT_SILENT_DURATION_30.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationMaghrib() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationMaghrib", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentDurationTarawih() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_DURATION_30.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentDurationTarawih", Integer.toString(DEFAULT_SILENT_DURATION_30.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public String getSilentDuration_Str() {
//        return b().getString("silentDuration", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue()));
//    }
//
//    public int getSilentStart() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStart", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartAsr() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartAsr", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartDhuhr() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartDhuhr", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartFajr() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartFajr", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartIsha() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartIsha", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartJomo3a() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartJomo3a", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartMaghrib() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartMaghrib", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public int getSilentStartTarawih() {
//        SharedPreferences b = b();
//        int intValue = DEFAULT_SILENT_START.intValue();
//        try {
//            return Integer.parseInt(b.getString("silentStartTarawih", Integer.toString(DEFAULT_SILENT_START.intValue())));
//        } catch (NumberFormatException e) {
//            e.printStackTrace();
//            return intValue;
//        }
//    }
//
//    public String getSilentStart_Str() {
//        return b().getString("silentStart", Integer.toString(DEFAULT_SILENT_START.intValue()));
//    }
//
//    public Boolean getSilent_notification_Settings() {
//        return Boolean.valueOf(b().getBoolean("silent_notification_Settings", false));
//    }
//
//    public Boolean getStartAlMokittaFromTCP() {
//        return Boolean.valueOf(b().getBoolean("StartAlMokittaFromTCP", false));
//    }
//
//    public boolean getStartOnBoot() {
//        return b().getBoolean("start_on_boot", false);
//    }
//
//    public Boolean getStartURLDownloadJustNews() {
//        return Boolean.valueOf(b().getBoolean("StartURLDownloadJustNews", false));
//    }
//
//    public Boolean getStartURLDownloadVideo() {
//        return Boolean.valueOf(b().getBoolean("StartURLDownloadVideo", false));
//    }
//
//    public Boolean getStart_ForegroundService() {
//        return Boolean.valueOf(b().getBoolean("Start_ForegroundService", false));
//    }
//
//    public boolean getStartingSilentVibration() {
//        return b().getBoolean("startingSilentVibration", false);
//    }
//
//    public boolean getStopSoundDialog() {
//        return b().getBoolean("stop_sound_dialog", false);
//    }
//
//    public boolean getStopSoundIfMovment() {
//        return b().getBoolean("stop_sound_if_movment", true);
//    }
//
//    public boolean getStopSoundIfPressVolumeKey() {
//        return b().getBoolean("stop_sound_if_press_volume_key", false);
//    }
//
//    public Boolean getStop_play_on_call() {
//        return Boolean.valueOf(b().getBoolean("stop_play_on_call", false));
//    }
//
////    public String getStrPreferenceVal(String str, String str2) {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString(str2, str);
////    }
//
//    public Boolean getTCPServerService_preferences() {
//        return Boolean.valueOf(b().getBoolean("TCPServerService_preferences", false));
//    }
//
////    public String getTime_minute_start_app() {
////        return b().getString("time_minute_start_app", this.context.getString(R.string.time_minute_start_app));
////    }
////
////    public String getToday() {
////        return b().getString("today", HelpFormatter.DEFAULT_LONG_OPT_PREFIX);
////    }
////
////    public String getUDP_IP() {
////        return b().getString("UDP_IP", this.context.getString(R.string.UDP_IP_DEFAULT_VAL));
////    }
////
////    public String getUDP_PORT() {
////        return b().getString("UDP_PORT", this.context.getString(R.string.UDP_PORT_DEFAULT_VAL));
////    }
//
//    public Boolean getURLCacheVideoP() {
//        return Boolean.valueOf(b().getBoolean("URLCacheVideo", false));
//    }
//
//    public Boolean getURLDownloadVideo() {
//        return Boolean.valueOf(b().getBoolean("URLDownloadVideo", false));
//    }
//
////    public boolean getUseDynamicScreenTV() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("UseDynamicScreenTV", true);
////    }
//
//    public int getVibrationMode() {
//        return b().getInt("vibrationMode", 0);
//    }
//
//    public Boolean getVibration_on_azan() {
//        return Boolean.valueOf(b().getBoolean("vibration_on_azan", true));
//    }
//
//    public Boolean getVibration_on_ikama() {
//        return Boolean.valueOf(b().getBoolean("vibration_on_ikama", false));
//    }
//
//    public Boolean getVibration_on_silent() {
//        return Boolean.valueOf(b().getBoolean("vibration_on_silent", true));
//    }
//
////    public String getVideoFilePath() {
////        String string = b().getString("videoFilePath", "-1");
////        if (string.equals("-1")) {
////            return MyTool.getFirstVideoFile();
////        }
////        return string;
////    }
//
//    public String getVideoList_Screen() {
//        return b().getString("VideoList_Screen", null);
//    }
//
////    public String getWidget_color_transparent() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("widget_color_transparent", "4");
////    }
////
////    public String getWidget_font() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("widget_font", "4");
////    }
//
//    public int get__end_ayah() {
//        return b().getInt("_end_ayah", 0);
//    }
//
//    public int get__end_sura() {
//        return b().getInt("_end_sura", 0);
//    }
//
//    public int get__quran_ok_read() {
//        return b().getInt("_quran_ok_read", 0);
//    }
//
//    public int get__start_ayah() {
//        return b().getInt("_start_ayah", 0);
//    }
//
//    public int get__start_sura() {
//        return b().getInt("_start_sura", 0);
//    }
//
//    public boolean get_city_widget_lock_visible() {
//        return b().getBoolean("city_widget_lock_visible", false);
//    }
//
//    public boolean get_city_widget_visible() {
//        return b().getBoolean("city_widget_visible", true);
//    }
//
//    public boolean get_clock_widget_visible() {
//        return b().getBoolean("clock_widget_visible", false);
//    }
//
//    public String get_daysToAdd() {
//        return b().getString("daysToAdd", "0");
//    }
//
//    public String get_design_tv_background() {
//        return b().getString("design_tv_background", "");
//    }
//
//    public int get_design_tv_dynamic() {
//        return b().getInt("design_tv_dynamic", 0);
//    }
//
//    public String get_design_tv_dynamic_LanguageDescrList() {
//        return b().getString("design_tv_dynamic_LanguageDescrList", "all_langs");
//    }
//
//    public String get_design_tv_dynamic_LanguageIDList() {
//        return b().getString("design_tv_dynamic_LanguageIDList", "all_langs");
//    }
//
////    public String get_design_tv_dynamic_code() {
////        return b().getString("design_tv_dynamic_code", MainActivityControlsBasic.INSTANCE.getCurrent_design_tv_dynamic_code_default());
////    }
//
//    public String get_design_tv_dynamic_langs_descr() {
//        return b().getString("design_tv_dynamic_langs_descr", "AR");
//    }
//
//    public String get_design_tv_dynamic_langs_id() {
//        return b().getString("design_tv_dynamic_langs_id", "59");
//    }
//
//    public String get_design_tv_dynamic_langs_index() {
//        return b().getString("design_tv_dynamic_langs_index", "0");
//    }
//
//    public int get_design_tv_dynamic_type() {
//        return b().getInt("design_tv_dynamic_type", 1);
//    }
//
//    public int get_design_tv_screen_dynamic_type() {
//        return b().getInt("design_tv_screen_dynamic_type", 1);
//    }
//
//    public String get_distance_nearest_mosques() {
//        return b().getString("distance_nearest_mosques", "0.5");
//    }
//
//    public boolean get_fasting_widget_lock_visible() {
//        return b().getBoolean("fasting_widget_lock_visible", false);
//    }
//
//    public boolean get_fasting_widget_visible() {
//        return b().getBoolean("fasting_widget_visible", true);
//    }
//
//    public boolean get_hijri_date_widget_lock_visible() {
//        return b().getBoolean("hijri_date_widget_lock_visible", true);
//    }
//
//    public boolean get_hijri_date_widget_visible() {
//        return b().getBoolean("hijri_date_widget_visible", false);
//    }
//
//    public boolean get_is_azan_only() {
//        return b().getBoolean("is_azan_only", false);
//    }
//
//    public boolean get_is_increase_widget_lock_font() {
//        return b().getBoolean("increase_widget_lock_font", false);
//    }
//
//    public boolean get_is_not_correct_al_muaqita_font() {
//        return b().getBoolean("is_not_correct_al_muaqita_font", false);
//    }
//
//    public int get_prefs_translation_text_size() {
//        return b().getInt("prefs_translation_text_size", 15);
//    }
//
//    public int get_quran_readers_name() {
//        return b().getInt("quran_readers_name", 0);
//    }
//
//    public String get_secondsToAdd() {
//        return b().getString("secondsToAdd", "0");
//    }
//
//    public String get_secondsToAddTotal() {
//        return b().getString("secondsToAddTotal", "12");
//    }
//
//    public Boolean get_select_dynamic_design() {
//        return Boolean.valueOf(b().getBoolean("select_dynamic_design", false));
//    }
//
//    public boolean get_stop_DaemonService() {
//        return b().getBoolean("stop_DaemonService", true);
//    }
//
////    public int get_suhoor_val() {
////        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("suhoor_val", 60);
////    }
//
//    public String get_time_zone_city_2() {
//        return b().getString("time_zone_city_2", "3.00");
//    }
//
//    public Boolean get_transperant_on_off() {
//        return Boolean.valueOf(b().getBoolean("transperant_on_off", false));
//    }
//
//    public String get_turn_off_prayer_period() {
//        return b().getString("turn_off_prayer_period", "5");
//    }
//
//    public String get_turn_off_prayer_period_jomo3a() {
//        return b().getString("turn_off_prayer_period_jomo3a", "60");
//    }
//
//    public Boolean get_turn_off_prayer_screen() {
//        return Boolean.valueOf(b().getBoolean("turn_off_prayer_screen", false));
//    }
//
////    public String get_turn_off_prayer_starting() {
////        return b().getString("turn_off_prayer_starting", SettingsActivity_2.CityType_1);
////    }
////
////    public Boolean getis_hour_24_city_2_pref() {
////        return Boolean.valueOf(PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("is_hour_24_city_2_pref", true));
////    }
//
//    public boolean isAutoAzkarEnableed() {
//        return b().getBoolean("EnableAzkar", true);
//    }
//
//    public boolean isAutoScreenEnableed() {
//        return b().getBoolean("EnableScreen", true);
//    }
//
//    public boolean isAutoSilentEnableed() {
//        return b().getBoolean("EnableSilent", false);
//    }
//
//    public boolean isFirstStart() {
//        return b().getBoolean("firstStart", true);
//    }
//
//    public boolean isLandscapeScreenType() {
//        return b().getString("ScreenType", "landscape").equals("landscape");
//    }
//
////    public void setAL_MoakitaVisiblePrayerInfo(String str) {
////        d.b(b(), "AL_MoakitaVisiblePrayerInfo", str);
////    }
////
////    public void setAlarmSound(String str) {
////        d.b(b(), "notSound", str);
////    }
////
////    public void setAlarmTxtContent_AlarmID(String str) {
////        d.b(b(), "AlarmTxtContent_AlarmID", str);
////    }
////
////    public void setAlarmTxtContent_Time4Stop(int i) {
////        j0.a(b(), "AlarmTxtContent_Time4Stop", i);
////    }
////
////    public void setAlarmTxtContent_Title(String str) {
////        d.b(b(), "AlarmTxtContent_Title", str);
////    }
////
////    public void setAlarmTxtContent_Txt(String str) {
////        d.b(b(), "AlarmTxtContent_Txt", str);
////    }
////
////    public void setAlarmTxtContent_WindowTitle(String str) {
////        d.b(b(), "AlarmTxtContent_WindowTitle", str);
////    }
////
////    public void setAlarmTxtContent_isTxtContentCustom(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "AlarmTxtContent_isTxtContentCustom", z);
////    }
////
////    public void setAlarm_azkar_active(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "alarm_azkar_active", z);
////    }
////
////    public void setAnalogClockShape(int i) {
////        j0.a(b(), "AnalogClockShape", i);
////    }
////
////    public void setApkFileName(String str) {
////        d.b(b(), "apkFileName", str);
////    }
////
////    public void setAudioManagerStreamMode(int i) {
////        j0.a(b(), "AudioManagerStreamMode", i);
////    }
////
////    public void setAutoAzkarEnableed(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkar", z);
////    }
////
////    public void setAutoRestart4GPS(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "AutoRestart4GPS", z);
////    }
////
////    public void setAutoScreenEnableed(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreen", z);
////    }
////
////    public void setAutoSilentEnableed(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilent", z);
////    }
////
////    public void setAutoTurnOnWhenErroe(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "AutoTurnOnWhenErroe", z);
////    }
////
////    public void setAzan_Jomo3a(String str) {
////        d.b(b(), "azan_jomo3a", str);
////    }
////
////    public void setAzan_Jomo3aAudio(String str) {
////        d.b(b(), "azan_jomo3a_audio", str);
////    }
////
////    public void setAzan_Jomo3aAudio_Check(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "azan_jomo3a_audio_check", z);
////    }
//
////    public void setAzkarDurationAsr(String str) {
////        d.b(b(), "azkarDurationAsr", str);
////    }
////
////    public void setAzkarDurationDhuhr(String str) {
////        d.b(b(), "azkarDurationDhuhr", str);
////    }
////
////    public void setAzkarDurationFajr(String str) {
////        d.b(b(), "azkarDurationFajr", str);
////    }
////
////    public void setAzkarDurationIsha(String str) {
////        d.b(b(), "azkarDurationIsha", str);
////    }
////
////    public void setAzkarDurationJomo3a(String str) {
////        d.b(b(), "azkarDurationJomo3a", str);
////    }
////
////    public void setAzkarDurationMaghrib(String str) {
////        d.b(b(), "azkarDurationMaghrib", str);
////    }
////
////    public void setAzkarDurationShuruq(String str) {
////        d.b(b(), "azkarDurationShuruq", str);
////    }
////
////    public void setAzkarStartAsr(String str) {
////        d.b(b(), "azkarStartAsr", str);
////    }
////
////    public void setAzkarStartDhuhr(String str) {
////        d.b(b(), "azkarStartDhuhr", str);
////    }
////
////    public void setAzkarStartFajr(String str) {
////        d.b(b(), "azkarStartFajr", str);
////    }
////
////    public void setAzkarStartIsha(String str) {
////        d.b(b(), "azkarStartIsha", str);
////    }
////
////    public void setAzkarStartJomo3a(String str) {
////        d.b(b(), "azkarStartJomo3a", str);
////    }
////
////    public void setAzkarStartMaghrib(String str) {
////        d.b(b(), "azkarStartMaghrib", str);
////    }
////
////    public void setAzkarStartShuruq(String str) {
////        d.b(b(), "azkarStartShuruq", str);
////    }
////
////    public void setAzkar_notification_Settings(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "azkar_notification_Settings", z);
////    }
//
//    public void setBooleanPreferenceVal(String str, Boolean bool) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean(str, bool.booleanValue());
//        edit.apply();
//    }
//
////    public void setCase_Jom3a_Duhur(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Case_Jom3a_Duhur", z);
////    }
////
////    public void setChangLanApp(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "ChangLanApp", z);
////    }
////
////    public void setChangeSeasonPrayerNotShowAgainMsg(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "changeSeasonPrayerNotShowAgainMsg", z);
////    }
////
////    public void setChangeSeasonSummerWinterDLG_visible(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "changeSeasonSummerWinterDLG_visible", z);
////    }
////
////    public void setChangedVolume(int i) {
////        j0.a(b(), "changedVolume", i);
////    }
////
////    public void setCheck20180126(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Check20180126", z);
////    }
////
////    public void setCheckPermissionAndroid6(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "CheckPermissionAndroid6", z);
////    }
////
////    public void setCity_Selected(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "City_Selected", z);
////    }
////
////    public void setColorSelectPrayerTime(Integer num) {
////        a.b(num, b().edit(), "ColorSelectPrayerTime");
////    }
////
////    public void setColorSelectPrayerTimeScreen(Integer num) {
////        a.b(num, b().edit(), "ColorSelectPrayerTimeScreen");
////    }
////
////    public void setCurrentAlarmID(String str) {
////        d.b(b(), "CurrentAlarmID", str);
////    }
////
////    public void setCurrentVolume(int i) {
////        j0.a(b(), "currentVolume", i);
////    }
////
////    public void setCurrent_Prayer_Today(int i) {
////        j0.a(b(), "Current_Prayer_Today", i);
////    }
//
//    public void setDateTypeSelected(int i) {
//        String str;
//        if (i == 1) {
//            str = "gregorian";
//        } else if (i == 2) {
//            str = "hijri";
//        } else {
//            str = "both";
//        }
//        SharedPreferences.Editor edit = b().edit();
//        edit.putString("date_type_selected", str);
//        MyTool.MyLog("preferenceName22", " ".concat(str));
//        edit.apply();
//    }
//
////    public void setDefaultDesignTV(String str) {
////        d.b(b(), "DefaultDesignTV", str);
////    }
////
////    public void setDesignTV_Font(String str) {
////        d.b(b(), "designTV_Font", str);
////    }
//
//    public void setDeviceType(MyTool.MainActivityType mainActivityType) {
//        String str;
//        SharedPreferences.Editor edit = b().edit();
//        if (mainActivityType == MyTool.MainActivityType.MOBILE) {
//            str = "MOBILE";
//        } else {
//            MyTool.MainActivityType mainActivityType2 = MyTool.MainActivityType.MOBILE;
//            str = "TV";
//        }
//        edit.putString("devicetype", str);
//        edit.apply();
//    }
//
////    public void setDigitalDateFormat(String str) {
////        d.b(b(), "DigitalDateFormat", str);
////    }
////
////    public void setDigitalDateSep(String str) {
////        d.b(b(), "DigitalDateSep", str);
////    }
//
//    public void setDigitalDate_zero_day_month(Boolean bool) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean("DigitalDate_zero_day_month", bool.booleanValue());
//        edit.apply();
//    }
//
////    public void setDo_batteryOptimizationNot(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Do_batteryOptimizationNot", z);
////    }
////
////    public void setEnableAzkarAsr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarAsr", z);
////    }
////
////    public void setEnableAzkarDhuhr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarDhuhr", z);
////    }
////
////    public void setEnableAzkarFajr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarFajr", z);
////    }
////
////    public void setEnableAzkarIsha(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarIsha", z);
////    }
////
////    public void setEnableAzkarJomo3a(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarJomo3a", z);
////    }
////
////    public void setEnableAzkarMaghrib(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarMaghrib", z);
////    }
////
////    public void setEnableAzkarShuruq(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarShuruq", z);
////    }
////
////    public void setEnableScreenAsr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenAsr", z);
////    }
////
////    public void setEnableScreenDhuhr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenDhuhr", z);
////    }
////
////    public void setEnableScreenFajr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenFajr", z);
////    }
////
////    public void setEnableScreenIsha(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenIsha", z);
////    }
////
////    public void setEnableScreenJomo3a(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenJomo3a", z);
////    }
////
////    public void setEnableScreenMaghrib(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenMaghrib", z);
////    }
////
////    public void setEnableScreenShuruq(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenShuruq", z);
////    }
////
////    public void setEnableSilentAsr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentAsr", z);
////    }
////
////    public void setEnableSilentAtFirst(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentAtFirst", z);
////    }
////
////    public void setEnableSilentDhuhr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentDhuhr", z);
////    }
////
////    public void setEnableSilentFajr(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentFajr", z);
////    }
////
////    public void setEnableSilentIsha(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentIsha", z);
////    }
////
////    public void setEnableSilentJomo3a(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentJomo3a", z);
////    }
////
////    public void setEnableSilentMaghrib(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentMaghrib", z);
////    }
////
////    public void setEnableSilentSound(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentSound", z);
////    }
////
////    public void setEnableSilentTarawih(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentTarawih", z);
////    }
////
////    public void setEnableSilentVibration(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentVibration", z);
////    }
////
////    public void setFatawaFilePath(String str) {
////        d.b(b(), "fatawaFilePath", str);
////    }
////
////    public void setFirstStart(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "firstStart", z);
////    }
////
////    public void setFullScreen_Activity(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "FullScreen_Activity", z);
////    }
//
//    public void setFull_name_day_month_en(Boolean bool) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean("Full_name_day_month_en", bool.booleanValue());
//        edit.apply();
//    }//    public boolean getShowShurouqRemain() {
////        return b().getBoolean("show_shurouq_remain", false);
////    }
////
////    public Boolean getShow_azkar_notification_Window() {
////        return Boolean.valueOf(b().getBoolean("show_azkar_notification_Window", false));
////    }
////
////    public String getSilentBeginAudio() {
////        return b().getString("silent_begin_audio", MyTool.takbeerAzan);
////    }
////
////    public int getSilentDuration() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDuration", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationAsr() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationAsr", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationDhuhr() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationDhuhr", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationFajr() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_30.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationFajr", Integer.toString(DEFAULT_SILENT_DURATION_30.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationIsha() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationIsha", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationJomo3a() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_30.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationJomo3a", Integer.toString(DEFAULT_SILENT_DURATION_30.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationMaghrib() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_5.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationMaghrib", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentDurationTarawih() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_DURATION_30.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentDurationTarawih", Integer.toString(DEFAULT_SILENT_DURATION_30.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public String getSilentDuration_Str() {
////        return b().getString("silentDuration", Integer.toString(DEFAULT_SILENT_DURATION_5.intValue()));
////    }
////
////    public int getSilentStart() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStart", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartAsr() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartAsr", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartDhuhr() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartDhuhr", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartFajr() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartFajr", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartIsha() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartIsha", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartJomo3a() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartJomo3a", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartMaghrib() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartMaghrib", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public int getSilentStartTarawih() {
////        SharedPreferences b = b();
////        int intValue = DEFAULT_SILENT_START.intValue();
////        try {
////            return Integer.parseInt(b.getString("silentStartTarawih", Integer.toString(DEFAULT_SILENT_START.intValue())));
////        } catch (NumberFormatException e) {
////            e.printStackTrace();
////            return intValue;
////        }
////    }
////
////    public String getSilentStart_Str() {
////        return b().getString("silentStart", Integer.toString(DEFAULT_SILENT_START.intValue()));
////    }
////
////    public Boolean getSilent_notification_Settings() {
////        return Boolean.valueOf(b().getBoolean("silent_notification_Settings", false));
////    }
////
////    public Boolean getStartAlMokittaFromTCP() {
////        return Boolean.valueOf(b().getBoolean("StartAlMokittaFromTCP", false));
////    }
////
////    public boolean getStartOnBoot() {
////        return b().getBoolean("start_on_boot", false);
////    }
////
////    public Boolean getStartURLDownloadJustNews() {
////        return Boolean.valueOf(b().getBoolean("StartURLDownloadJustNews", false));
////    }
////
////    public Boolean getStartURLDownloadVideo() {
////        return Boolean.valueOf(b().getBoolean("StartURLDownloadVideo", false));
////    }
////
////    public Boolean getStart_ForegroundService() {
////        return Boolean.valueOf(b().getBoolean("Start_ForegroundService", false));
////    }
////
////    public boolean getStartingSilentVibration() {
////        return b().getBoolean("startingSilentVibration", false);
////    }
////
////    public boolean getStopSoundDialog() {
////        return b().getBoolean("stop_sound_dialog", false);
////    }
////
////    public boolean getStopSoundIfMovment() {
////        return b().getBoolean("stop_sound_if_movment", true);
////    }
////
////    public boolean getStopSoundIfPressVolumeKey() {
////        return b().getBoolean("stop_sound_if_press_volume_key", false);
////    }
////
////    public Boolean getStop_play_on_call() {
////        return Boolean.valueOf(b().getBoolean("stop_play_on_call", false));
////    }
////
//////    public String getStrPreferenceVal(String str, String str2) {
//////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString(str2, str);
//////    }
////
////    public Boolean getTCPServerService_preferences() {
////        return Boolean.valueOf(b().getBoolean("TCPServerService_preferences", false));
////    }
////
//////    public String getTime_minute_start_app() {
//////        return b().getString("time_minute_start_app", this.context.getString(R.string.time_minute_start_app));
//////    }
//////
//////    public String getToday() {
//////        return b().getString("today", HelpFormatter.DEFAULT_LONG_OPT_PREFIX);
//////    }
//////
//////    public String getUDP_IP() {
//////        return b().getString("UDP_IP", this.context.getString(R.string.UDP_IP_DEFAULT_VAL));
//////    }
//////
//////    public String getUDP_PORT() {
//////        return b().getString("UDP_PORT", this.context.getString(R.string.UDP_PORT_DEFAULT_VAL));
//////    }
////
////    public Boolean getURLCacheVideoP() {
////        return Boolean.valueOf(b().getBoolean("URLCacheVideo", false));
////    }
////
////    public Boolean getURLDownloadVideo() {
////        return Boolean.valueOf(b().getBoolean("URLDownloadVideo", false));
////    }
////
//////    public boolean getUseDynamicScreenTV() {
//////        return PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("UseDynamicScreenTV", true);
//////    }
////
////    public int getVibrationMode() {
////        return b().getInt("vibrationMode", 0);
////    }
////
////    public Boolean getVibration_on_azan() {
////        return Boolean.valueOf(b().getBoolean("vibration_on_azan", true));
////    }
////
////    public Boolean getVibration_on_ikama() {
////        return Boolean.valueOf(b().getBoolean("vibration_on_ikama", false));
////    }
////
////    public Boolean getVibration_on_silent() {
////        return Boolean.valueOf(b().getBoolean("vibration_on_silent", true));
////    }
////
//////    public String getVideoFilePath() {
//////        String string = b().getString("videoFilePath", "-1");
//////        if (string.equals("-1")) {
//////            return MyTool.getFirstVideoFile();
//////        }
//////        return string;
//////    }
////
////    public String getVideoList_Screen() {
////        return b().getString("VideoList_Screen", null);
////    }
////
//////    public String getWidget_color_transparent() {
//////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("widget_color_transparent", "4");
//////    }
//////
//////    public String getWidget_font() {
//////        return PreferenceManager.getDefaultSharedPreferences(this.context).getString("widget_font", "4");
//////    }
////
////    public int get__end_ayah() {
////        return b().getInt("_end_ayah", 0);
////    }
////
////    public int get__end_sura() {
////        return b().getInt("_end_sura", 0);
////    }
////
////    public int get__quran_ok_read() {
////        return b().getInt("_quran_ok_read", 0);
////    }
////
////    public int get__start_ayah() {
////        return b().getInt("_start_ayah", 0);
////    }
////
////    public int get__start_sura() {
////        return b().getInt("_start_sura", 0);
////    }
////
////    public boolean get_city_widget_lock_visible() {
////        return b().getBoolean("city_widget_lock_visible", false);
////    }
////
////    public boolean get_city_widget_visible() {
////        return b().getBoolean("city_widget_visible", true);
////    }
////
////    public boolean get_clock_widget_visible() {
////        return b().getBoolean("clock_widget_visible", false);
////    }
////
////    public String get_daysToAdd() {
////        return b().getString("daysToAdd", "0");
////    }
////
////    public String get_design_tv_background() {
////        return b().getString("design_tv_background", "");
////    }
////
////    public int get_design_tv_dynamic() {
////        return b().getInt("design_tv_dynamic", 0);
////    }
////
////    public String get_design_tv_dynamic_LanguageDescrList() {
////        return b().getString("design_tv_dynamic_LanguageDescrList", "all_langs");
////    }
////
////    public String get_design_tv_dynamic_LanguageIDList() {
////        return b().getString("design_tv_dynamic_LanguageIDList", "all_langs");
////    }
////
//////    public String get_design_tv_dynamic_code() {
//////        return b().getString("design_tv_dynamic_code", MainActivityControlsBasic.INSTANCE.getCurrent_design_tv_dynamic_code_default());
//////    }
////
////    public String get_design_tv_dynamic_langs_descr() {
////        return b().getString("design_tv_dynamic_langs_descr", "AR");
////    }
////
////    public String get_design_tv_dynamic_langs_id() {
////        return b().getString("design_tv_dynamic_langs_id", "59");
////    }
////
////    public String get_design_tv_dynamic_langs_index() {
////        return b().getString("design_tv_dynamic_langs_index", "0");
////    }
////
////    public int get_design_tv_dynamic_type() {
////        return b().getInt("design_tv_dynamic_type", 1);
////    }
////
////    public int get_design_tv_screen_dynamic_type() {
////        return b().getInt("design_tv_screen_dynamic_type", 1);
////    }
////
////    public String get_distance_nearest_mosques() {
////        return b().getString("distance_nearest_mosques", "0.5");
////    }
////
////    public boolean get_fasting_widget_lock_visible() {
////        return b().getBoolean("fasting_widget_lock_visible", false);
////    }
////
////    public boolean get_fasting_widget_visible() {
////        return b().getBoolean("fasting_widget_visible", true);
////    }
////
////    public boolean get_hijri_date_widget_lock_visible() {
////        return b().getBoolean("hijri_date_widget_lock_visible", true);
////    }
////
////    public boolean get_hijri_date_widget_visible() {
////        return b().getBoolean("hijri_date_widget_visible", false);
////    }
////
////    public boolean get_is_azan_only() {
////        return b().getBoolean("is_azan_only", false);
////    }
////
////    public boolean get_is_increase_widget_lock_font() {
////        return b().getBoolean("increase_widget_lock_font", false);
////    }
////
////    public boolean get_is_not_correct_al_muaqita_font() {
////        return b().getBoolean("is_not_correct_al_muaqita_font", false);
////    }
////
////    public int get_prefs_translation_text_size() {
////        return b().getInt("prefs_translation_text_size", 15);
////    }
////
////    public int get_quran_readers_name() {
////        return b().getInt("quran_readers_name", 0);
////    }
////
////    public String get_secondsToAdd() {
////        return b().getString("secondsToAdd", "0");
////    }
////
////    public String get_secondsToAddTotal() {
////        return b().getString("secondsToAddTotal", "12");
////    }
////
////    public Boolean get_select_dynamic_design() {
////        return Boolean.valueOf(b().getBoolean("select_dynamic_design", false));
////    }
////
////    public boolean get_stop_DaemonService() {
////        return b().getBoolean("stop_DaemonService", true);
////    }
////
//////    public int get_suhoor_val() {
//////        return PreferenceManager.getDefaultSharedPreferences(this.context).getInt("suhoor_val", 60);
//////    }
////
////    public String get_time_zone_city_2() {
////        return b().getString("time_zone_city_2", "3.00");
////    }
////
////    public Boolean get_transperant_on_off() {
////        return Boolean.valueOf(b().getBoolean("transperant_on_off", false));
////    }
////
////    public String get_turn_off_prayer_period() {
////        return b().getString("turn_off_prayer_period", "5");
////    }
////
////    public String get_turn_off_prayer_period_jomo3a() {
////        return b().getString("turn_off_prayer_period_jomo3a", "60");
////    }
////
////    public Boolean get_turn_off_prayer_screen() {
////        return Boolean.valueOf(b().getBoolean("turn_off_prayer_screen", false));
////    }
////
//////    public String get_turn_off_prayer_starting() {
//////        return b().getString("turn_off_prayer_starting", SettingsActivity_2.CityType_1);
//////    }
//////
//////    public Boolean getis_hour_24_city_2_pref() {
//////        return Boolean.valueOf(PreferenceManager.getDefaultSharedPreferences(this.context).getBoolean("is_hour_24_city_2_pref", true));
//////    }
////
////    public boolean isAutoAzkarEnableed() {
////        return b().getBoolean("EnableAzkar", true);
////    }
////
////    public boolean isAutoScreenEnableed() {
////        return b().getBoolean("EnableScreen", true);
////    }
////
////    public boolean isAutoSilentEnableed() {
////        return b().getBoolean("EnableSilent", false);
////    }
////
////    public boolean isFirstStart() {
////        return b().getBoolean("firstStart", true);
////    }
////
////    public boolean isLandscapeScreenType() {
////        return b().getString("ScreenType", "landscape").equals("landscape");
////    }
////
//////    public void setAL_MoakitaVisiblePrayerInfo(String str) {
//////        d.b(b(), "AL_MoakitaVisiblePrayerInfo", str);
//////    }
//////
//////    public void setAlarmSound(String str) {
//////        d.b(b(), "notSound", str);
//////    }
//////
//////    public void setAlarmTxtContent_AlarmID(String str) {
//////        d.b(b(), "AlarmTxtContent_AlarmID", str);
//////    }
//////
//////    public void setAlarmTxtContent_Time4Stop(int i) {
//////        j0.a(b(), "AlarmTxtContent_Time4Stop", i);
//////    }
//////
//////    public void setAlarmTxtContent_Title(String str) {
//////        d.b(b(), "AlarmTxtContent_Title", str);
//////    }
//////
//////    public void setAlarmTxtContent_Txt(String str) {
//////        d.b(b(), "AlarmTxtContent_Txt", str);
//////    }
//////
//////    public void setAlarmTxtContent_WindowTitle(String str) {
//////        d.b(b(), "AlarmTxtContent_WindowTitle", str);
//////    }
//////
//////    public void setAlarmTxtContent_isTxtContentCustom(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "AlarmTxtContent_isTxtContentCustom", z);
//////    }
//////
//////    public void setAlarm_azkar_active(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "alarm_azkar_active", z);
//////    }
//////
//////    public void setAnalogClockShape(int i) {
//////        j0.a(b(), "AnalogClockShape", i);
//////    }
//////
//////    public void setApkFileName(String str) {
//////        d.b(b(), "apkFileName", str);
//////    }
//////
//////    public void setAudioManagerStreamMode(int i) {
//////        j0.a(b(), "AudioManagerStreamMode", i);
//////    }
//////
//////    public void setAutoAzkarEnableed(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkar", z);
//////    }
//////
//////    public void setAutoRestart4GPS(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "AutoRestart4GPS", z);
//////    }
//////
//////    public void setAutoScreenEnableed(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreen", z);
//////    }
//////
//////    public void setAutoSilentEnableed(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilent", z);
//////    }
//////
//////    public void setAutoTurnOnWhenErroe(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "AutoTurnOnWhenErroe", z);
//////    }
//////
//////    public void setAzan_Jomo3a(String str) {
//////        d.b(b(), "azan_jomo3a", str);
//////    }
//////
//////    public void setAzan_Jomo3aAudio(String str) {
//////        d.b(b(), "azan_jomo3a_audio", str);
//////    }
//////
//////    public void setAzan_Jomo3aAudio_Check(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "azan_jomo3a_audio_check", z);
//////    }
////
//////    public void setAzkarDurationAsr(String str) {
//////        d.b(b(), "azkarDurationAsr", str);
//////    }
//////
//////    public void setAzkarDurationDhuhr(String str) {
//////        d.b(b(), "azkarDurationDhuhr", str);
//////    }
//////
//////    public void setAzkarDurationFajr(String str) {
//////        d.b(b(), "azkarDurationFajr", str);
//////    }
//////
//////    public void setAzkarDurationIsha(String str) {
//////        d.b(b(), "azkarDurationIsha", str);
//////    }
//////
//////    public void setAzkarDurationJomo3a(String str) {
//////        d.b(b(), "azkarDurationJomo3a", str);
//////    }
//////
//////    public void setAzkarDurationMaghrib(String str) {
//////        d.b(b(), "azkarDurationMaghrib", str);
//////    }
//////
//////    public void setAzkarDurationShuruq(String str) {
//////        d.b(b(), "azkarDurationShuruq", str);
//////    }
//////
//////    public void setAzkarStartAsr(String str) {
//////        d.b(b(), "azkarStartAsr", str);
//////    }
//////
//////    public void setAzkarStartDhuhr(String str) {
//////        d.b(b(), "azkarStartDhuhr", str);
//////    }
//////
//////    public void setAzkarStartFajr(String str) {
//////        d.b(b(), "azkarStartFajr", str);
//////    }
//////
//////    public void setAzkarStartIsha(String str) {
//////        d.b(b(), "azkarStartIsha", str);
//////    }
//////
//////    public void setAzkarStartJomo3a(String str) {
//////        d.b(b(), "azkarStartJomo3a", str);
//////    }
//////
//////    public void setAzkarStartMaghrib(String str) {
//////        d.b(b(), "azkarStartMaghrib", str);
//////    }
//////
//////    public void setAzkarStartShuruq(String str) {
//////        d.b(b(), "azkarStartShuruq", str);
//////    }
//////
//////    public void setAzkar_notification_Settings(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "azkar_notification_Settings", z);
//////    }
////
////    public void setBooleanPreferenceVal(String str, Boolean bool) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean(str, bool.booleanValue());
////        edit.apply();
////    }
////
//////    public void setCase_Jom3a_Duhur(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Case_Jom3a_Duhur", z);
//////    }
//////
//////    public void setChangLanApp(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "ChangLanApp", z);
//////    }
//////
//////    public void setChangeSeasonPrayerNotShowAgainMsg(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "changeSeasonPrayerNotShowAgainMsg", z);
//////    }
//////
//////    public void setChangeSeasonSummerWinterDLG_visible(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "changeSeasonSummerWinterDLG_visible", z);
//////    }
//////
//////    public void setChangedVolume(int i) {
//////        j0.a(b(), "changedVolume", i);
//////    }
//////
//////    public void setCheck20180126(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Check20180126", z);
//////    }
//////
//////    public void setCheckPermissionAndroid6(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "CheckPermissionAndroid6", z);
//////    }
//////
//////    public void setCity_Selected(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "City_Selected", z);
//////    }
//////
//////    public void setColorSelectPrayerTime(Integer num) {
//////        a.b(num, b().edit(), "ColorSelectPrayerTime");
//////    }
//////
//////    public void setColorSelectPrayerTimeScreen(Integer num) {
//////        a.b(num, b().edit(), "ColorSelectPrayerTimeScreen");
//////    }
//////
//////    public void setCurrentAlarmID(String str) {
//////        d.b(b(), "CurrentAlarmID", str);
//////    }
//////
//////    public void setCurrentVolume(int i) {
//////        j0.a(b(), "currentVolume", i);
//////    }
//////
//////    public void setCurrent_Prayer_Today(int i) {
//////        j0.a(b(), "Current_Prayer_Today", i);
//////    }
////
////    public void setDateTypeSelected(int i) {
////        String str;
////        if (i == 1) {
////            str = "gregorian";
////        } else if (i == 2) {
////            str = "hijri";
////        } else {
////            str = "both";
////        }
////        SharedPreferences.Editor edit = b().edit();
////        edit.putString("date_type_selected", str);
////        MyTool.MyLog("preferenceName22", " ".concat(str));
////        edit.apply();
////    }
////
//////    public void setDefaultDesignTV(String str) {
//////        d.b(b(), "DefaultDesignTV", str);
//////    }
//////
//////    public void setDesignTV_Font(String str) {
//////        d.b(b(), "designTV_Font", str);
//////    }
////
////    public void setDeviceType(MyTool.MainActivityType mainActivityType) {
////        String str;
////        SharedPreferences.Editor edit = b().edit();
////        if (mainActivityType == MyTool.MainActivityType.MOBILE) {
////            str = "MOBILE";
////        } else {
////            MyTool.MainActivityType mainActivityType2 = MyTool.MainActivityType.MOBILE;
////            str = "TV";
////        }
////        edit.putString("devicetype", str);
////        edit.apply();
////    }
////
//////    public void setDigitalDateFormat(String str) {
//////        d.b(b(), "DigitalDateFormat", str);
//////    }
//////
//////    public void setDigitalDateSep(String str) {
//////        d.b(b(), "DigitalDateSep", str);
//////    }
////
////    public void setDigitalDate_zero_day_month(Boolean bool) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean("DigitalDate_zero_day_month", bool.booleanValue());
////        edit.apply();
////    }
////
//////    public void setDo_batteryOptimizationNot(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Do_batteryOptimizationNot", z);
//////    }
//////
//////    public void setEnableAzkarAsr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarAsr", z);
//////    }
//////
//////    public void setEnableAzkarDhuhr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarDhuhr", z);
//////    }
//////
//////    public void setEnableAzkarFajr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarFajr", z);
//////    }
//////
//////    public void setEnableAzkarIsha(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarIsha", z);
//////    }
//////
//////    public void setEnableAzkarJomo3a(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarJomo3a", z);
//////    }
//////
//////    public void setEnableAzkarMaghrib(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarMaghrib", z);
//////    }
//////
//////    public void setEnableAzkarShuruq(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableAzkarShuruq", z);
//////    }
//////
//////    public void setEnableScreenAsr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenAsr", z);
//////    }
//////
//////    public void setEnableScreenDhuhr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenDhuhr", z);
//////    }
//////
//////    public void setEnableScreenFajr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenFajr", z);
//////    }
//////
//////    public void setEnableScreenIsha(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenIsha", z);
//////    }
//////
//////    public void setEnableScreenJomo3a(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenJomo3a", z);
//////    }
//////
//////    public void setEnableScreenMaghrib(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenMaghrib", z);
//////    }
//////
//////    public void setEnableScreenShuruq(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableScreenShuruq", z);
//////    }
//////
//////    public void setEnableSilentAsr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentAsr", z);
//////    }
//////
//////    public void setEnableSilentAtFirst(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentAtFirst", z);
//////    }
//////
//////    public void setEnableSilentDhuhr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentDhuhr", z);
//////    }
//////
//////    public void setEnableSilentFajr(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentFajr", z);
//////    }
//////
//////    public void setEnableSilentIsha(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentIsha", z);
//////    }
//////
//////    public void setEnableSilentJomo3a(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentJomo3a", z);
//////    }
//////
//////    public void setEnableSilentMaghrib(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentMaghrib", z);
//////    }
//////
//////    public void setEnableSilentSound(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentSound", z);
//////    }
//////
//////    public void setEnableSilentTarawih(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentTarawih", z);
//////    }
//////
//////    public void setEnableSilentVibration(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "EnableSilentVibration", z);
//////    }
//////
//////    public void setFatawaFilePath(String str) {
//////        d.b(b(), "fatawaFilePath", str);
//////    }
//////
//////    public void setFirstStart(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "firstStart", z);
//////    }
//////
//////    public void setFullScreen_Activity(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "FullScreen_Activity", z);
//////    }
////
////    public void setFull_name_day_month_en(Boolean bool) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean("Full_name_day_month_en", bool.booleanValue());
////        edit.apply();
////    }
////
//////    public void setGoToMain(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "GoToMain", z);
//////    }
//////
//////    public void setHijriDateEdit(EditText editText, EditText editText2, EditText editText3) {
//////        int hijriDatePreference = getHijriDatePreference();
//////        Calendar calendar = Calendar.getInstance();
//////        int i = calendar.get(5);
//////        int i2 = calendar.get(2) + 1;
//////        int i3 = calendar.get(1);
//////        String DateToStr = MyTool.DateToStr(i3, i2, i);
//////        Calendar calendar2 = Calendar.getInstance();
//////        calendar2.set(1, i3);
//////        calendar2.set(2, i2 - 1);
//////        calendar2.set(5, i);
//////        calendar2.add(5, hijriDatePreference);
//////        HijriCalendar hijriCalendar = new HijriCalendar(calendar2.get(1), calendar2.get(2) + 1, calendar2.get(5));
//////        editText.setText(hijriCalendar.getHijriYear() + "");
//////        editText2.setText(hijriCalendar.getHijriMonth() + "");
//////        editText3.setText(hijriCalendar.getHijriDay() + "");
//////        if (DateToStr.equals(getHijriDatePreference_30())) {
//////            editText3.setText("30");
//////        }
//////    }
////
//////    public void setHijriDatePreference(int i) {
//////        String DateToStr;
//////        int i2 = i - NumDayHijri;
//////        if (i2 > -3 && i2 < 3) {
//////            DateToStr = "$$$$$";
//////        } else {
//////            Calendar calendar = Calendar.getInstance();
//////            int i3 = calendar.get(5);
//////            int i4 = calendar.get(2) + 1;
//////            int i5 = calendar.get(1);
//////            if (PrayerTimesApplication.date4PrayerTime != null && !PrayerTimesApplication.checkTvAgentEdition()) {
//////                i3 = PrayerTimesApplication.date4PrayerTime.getDay();
//////                i4 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.MONTH java.lang.String() + 1;
//////                i5 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.YEAR java.lang.String();
//////            }
//////            DateToStr = MyTool.DateToStr(i5, i4, i3);
//////            i2 = -1;
//////        }
//////        SharedPreferences.Editor edit = b().edit();
//////        edit.putString("HijriDatePreference_30", DateToStr);
//////        edit.putInt("HijriDatePreference", i2);
//////        edit.apply();
//////    }
////
////    public void setHour12_24(Hour hour) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putString("hour", hour.hourString());
////        edit.apply();
////    }
////
//////    public void setIP1_screen(String str) {
//////        d.b(b(), "IP1_screen", str);
//////    }
//////
//////    public void setIP2_screen(String str) {
//////        d.b(b(), "IP2_screen", str);
//////    }
//////
//////    public void setIP3_screen(String str) {
//////        d.b(b(), "IP3_screen", str);
//////    }
//////
//////    public void setIP4_screen(String str) {
//////        d.b(b(), "IP4_screen", str);
//////    }
//////
//////    public void setIcon_alarm_notification_Settings(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "icon_alarm_notification_Settings", z);
//////    }
//////
//////    public void setImask_Time(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Imask_Time", z);
//////    }
//////
//////    public void setImsak_before_fajr_value_minutes(String str) {
//////        d.b(b(), "Imsak_before_fajr_value_minutes", str);
//////    }
//////
//////    public void setIntPreferenceVal(String str, int i) {
//////        j0.a(b(), str, i);
//////    }
//////
//////    public void setIsDownloadDesignTV(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "IsDownloadDesignTV", z);
//////    }
//////
//////    public void setIsEnabledChangeVolume(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "IsEnabledChangeVolume", z);
//////    }
////
////    public void setIsPressHome_ScreenActivity(boolean z) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean("IsPressHome_ScreenActivity", z);
////        edit.apply();
////        MyTool.MyLog("startScreenActivity", "222" + z);
////    }
////
//////    public void setIsRINGER_MODE_NORMAL(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "IsRINGER_MODE_NORMAL", z);
//////    }
//////
//////    public void setIsScreenOn(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "IsScreenOn", z);
//////    }
////
////    public void setIs_hour_24_pref(Boolean bool) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean("is_hour_24_pref", bool.booleanValue());
////        edit.apply();
////    }
////
//////    public void setIsha_Magrib_Between(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Isha_Magrib_Between", z);
//////    }
//////
//////    public void setIsha_Magrib_Between_value_minutes(String str) {
//////        d.b(b(), "Isha_Magrib_Between_value_minutes", str);
//////    }
//////
//////    public void setLanguageLocale(String str) {
//////        d.b(b(), "LanguageLocale", str);
//////    }
//////
//////    public void setLecturesFilePath(String str) {
//////        d.b(b(), "lecturesFilePath", str);
//////    }
//////
//////    public void setLoad_Default_Dynamic_Screen(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Load_Default_Dynamic_Screen", z);
//////    }
//////
//////    public void setLocation(float f2, float f3) {
//////        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(this.context).edit();
//////        edit.putFloat("location_latitude", f2);
//////        edit.putFloat("location_longitude", f3);
//////        edit.apply();
//////    }
//////
//////    public void setLongMonthNamesDecreaseFontSize(int i) {
//////        j0.a(b(), "long_month_names_Decrease_Font_Size", i);
//////    }
//////
//////    public void setMiladi_month_yanayer(String str) {
//////        d.b(b(), "miladi_month", str);
//////    }
//////
//////    public void setMobileBackgroundType(String str) {
//////        d.b(b(), "MobileBackgroundType", str);
//////    }
//////
//////    public void setMobileDesignType(String str) {
//////        d.b(b(), "MobileDesignType", str);
//////    }
//////
//////    public void setMode_orientation(String str) {
//////        d.b(b(), "mode_orientation", str);
//////    }
//////
//////    public void setMode_orientation_new(String str) {
//////        d.b(b(), "orientation_screen_preference_new", str);
//////    }
//////
//////    public void setMove_value(int i) {
//////        j0.a(b(), "Move_value", i);
//////    }
//////
//////    public void setMust_request_random(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "must_request_random", z);
//////    }
//////
//////    public void setMy_City_Selected(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "My_City_Selected", z);
//////    }
//////
//////    public void setNew_Mobile_Design(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "New_Mobile_Design", z);
//////    }
//////
//////    public void setNewsFilePath(String str) {
//////        d.b(b(), "newsFilePath", str);
//////    }
//////
//////    public void setNotiContentText(String str) {
//////        d.b(b(), "NotiContentText", str);
//////    }
//////
//////    public void setNotiContentTitle(String str) {
//////        d.b(b(), "NotiContentTitle", str);
//////    }
//////
//////    public void setNotiID(int i) {
//////        j0.a(b(), "NotiID", i);
//////    }
////
////    public void setNotiWhen(long j) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putLong("NotiWhen", j);
////        edit.apply();
////    }
////
//////    public void setOpenAppOnAzan(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "open_app_on_azan", z);
//////    }
//////
//////    public void setOrientaionTV(String str) {
//////        d.b(b(), "orientaionTV", str);
//////    }
//////
//////    public void setParamT4RandomRequest(String str) {
//////        d.b(b(), "ParamT4RandomRequest", str);
//////    }
//////
//////    public void setPauseVideoPosition(Integer num) {
//////        a.b(num, b().edit(), "pauseVideoPosition");
//////    }
//////
//////    public void setPause_productsList_index(Integer num) {
//////        a.b(num, b().edit(), "pause_productsList_index");
//////    }
////
////    public void setPause_resume_update_data(Boolean bool) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean("pause_resume_update_data", bool.booleanValue());
////        edit.apply();
////    }
////
//////    public void setPlayAzanSound(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "play_azan_sound", z);
//////    }
//////
//////    public void setPlayInSilentMode(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "PlayInSilentMode", z);
//////    }
//////
//////    public void setPlayQuranSound(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "play_quran_sound", z);
//////    }
//////
//////    public void setPlaySoundOnSilentBegin(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "PlaySoundOnSilentBegin", z);
//////    }
//////
//////    public void setPowerSaveAutoTurnOff(Integer num) {
//////        a.b(num, b().edit(), "powerSaveAutoTurnOff_preferen");
//////    }
//////
//////    public void setPowerSaveFajr(Integer num) {
//////        a.b(num, b().edit(), "powerSaveFajr_preferen");
//////    }
//////
//////    public void setPowerSaveIsha(Integer num) {
//////        a.b(num, b().edit(), "powerSaveIsha_preferen");
//////    }
//////
//////    public void setPrayerWidgetNotificationSettings(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "prayer_widget_notification_Settings", z);
//////    }
//////
//////    public void setPrayer_News(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Prayer_News", z);
//////    }
//////
//////    public void setPrayer_News_From_Screen(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Prayer_News_From_Screen", z);
//////    }
//////
//////    public void setPrayer_News_Old_Method(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Prayer_News_Old_Method", z);
//////    }
//////
//////    public void setPrayer_News_txt(String str) {
//////        d.b(b(), "Prayer_News_Txt", str);
//////    }
//////
//////    public void setPreference_Property_name(String str) {
//////        d.b(b(), "Preference_Property_name", str);
//////    }
//////
//////    public void setPrivateCodeDesignTV(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "PrivateCodeDesignTV", z);
//////    }
//////
//////    public void setQuran_audioOnly(int i) {
//////        j0.a(b(), "quran_audioOnly", i);
//////    }
//////
//////    public void setQuran_date_bagin(String str) {
//////        d.b(b(), "quran_date_bagin", str);
//////    }
//////
//////    public void setQuran_parts(int i) {
//////        j0.a(b(), "quran_parts", i);
//////    }
//////
//////    public void setQuran_quarters(int i) {
//////        j0.a(b(), "quran_quarters", i);
//////    }
//////
//////    public void setQuran_showTafseer(int i) {
//////        j0.a(b(), "quran_showTafseer", i);
//////    }
//////
//////    public void setQuran_time(String str) {
//////        d.b(b(), "quran_time", str);
//////    }
//////
//////    public void setQuran_time_prayer(String str) {
//////        d.b(b(), "quran_time_prayer", str);
//////    }
//////
//////    public void setQuran_times(String str) {
//////        d.b(b(), "quran_times", str);
//////    }
//////
//////    public void setRandom_Date4Request(String str) {
//////        d.b(b(), "Random_Date4Request", str);
//////    }
//////
//////    public void setRandom_NextDate4Request(String str) {
//////        d.b(b(), "Random_NextDate4Request", str);
//////    }
//////
//////    public void setRandom_days4Request(String str) {
//////        d.b(b(), "Random_days4Request", str);
//////    }
//////
//////    public void setRandom_number4Request(String str) {
//////        d.b(b(), "Random_number4Request", str);
//////    }
//////
//////    public void setRestartAppMSG(String str) {
//////        SharedPreferences.Editor edit = b().edit();
//////        if (PrayerTimesApplication.checkTvAgentEdition()) {
//////            str = "NONE";
//////        }
//////        edit.putString("RestartAppMSG", str);
//////        edit.apply();
//////    }
//////
//////    public void setRun_Full_Image(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Full_Image", z);
//////    }
//////
//////    public void setRun_Full_PDF(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Full_PDF", z);
//////    }
//////
//////    public void setRun_Full_Video(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Full_Video", z);
//////    }
//////
//////    public void setRun_Screen(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Run_Screen", z);
//////    }
//////
//////    public void setRun_Screen_Alwayes(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Run_Screen_Alwayes", z);
//////    }
//////
//////    public void setRun_Screen_Alwayes_with_events(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Run_Screen_Alwayes_with_events", z);
//////    }
//////
//////    public void setScreenBackground(String str) {
//////        d.b(b(), "ScreenBackground", str);
//////    }
//////
//////    public void setScreenDurationAsr(String str) {
//////        d.b(b(), "screenDurationAsr", str);
//////    }
//////
//////    public void setScreenDurationDhuhr(String str) {
//////        d.b(b(), "screenDurationDhuhr", str);
//////    }
//////
//////    public void setScreenDurationFajr(String str) {
//////        d.b(b(), "screenDurationFajr", str);
//////    }
//////
//////    public void setScreenDurationIsha(String str) {
//////        d.b(b(), "screenDurationIsha", str);
//////    }
//////
//////    public void setScreenDurationJomo3a(String str) {
//////        d.b(b(), "screenDurationJomo3a", str);
//////    }
//////
//////    public void setScreenDurationMaghrib(String str) {
//////        d.b(b(), "screenDurationMaghrib", str);
//////    }
//////
//////    public void setScreenDurationShuruq(String str) {
//////        d.b(b(), "screenDurationShuruq", str);
//////    }
//////
//////    public void setScreenFatawaURL(String str) {
//////        d.b(b(), "ScreenFatawaURL", str);
//////    }
//////
//////    public void setScreenFatawaURL_net(String str) {
//////        d.b(b(), "ScreenFatawaURL_net", str);
//////    }
//////
//////    public void setScreenFullTextURL(String str) {
//////        d.b(b(), "ScreenFullTextURL", str);
//////    }
//////
//////    public void setScreenFullTextURL_net(String str) {
//////        d.b(b(), "ScreenFullTextURL_net", str);
//////    }
//////
//////    public void setScreenFullVideoURL(String str) {
//////        d.b(b(), "ScreenFullVideoURL", str);
//////    }
//////
//////    public void setScreenFullVideoURL_net(String str) {
//////        d.b(b(), "ScreenFullVideoURL_net", str);
//////    }
//////
//////    public void setScreenLectureURL(String str) {
//////        d.b(b(), "ScreenLectureURL", str);
//////    }
//////
//////    public void setScreenLectureURL_net(String str) {
//////        d.b(b(), "ScreenLectureURL_net", str);
//////    }
//////
//////    public void setScreenManagementType(MyTool.ScreenManagementType screenManagementType) {
//////        SharedPreferences.Editor edit = b().edit();
//////        String str = "LOCAL";
//////        if (screenManagementType != MyTool.ScreenManagementType.LOCAL) {
//////            if (screenManagementType == MyTool.ScreenManagementType.REMOTE) {
//////                str = "REMOTE";
//////            } else if (screenManagementType == MyTool.ScreenManagementType.REMOTE_NET) {
//////                str = "REMOTE_NET";
//////            }
//////        }
//////        edit.putString("screenManagementType", str);
//////        edit.apply();
//////    }
//////
//////    public void setScreenNewsURL(String str) {
//////        d.b(b(), "ScreenNewsURL", str);
//////    }
//////
//////    public void setScreenNewsURL_net(String str) {
//////        d.b(b(), "ScreenNewsURL_net", str);
//////    }
//////
//////    public void setScreenOnAlwayes(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_On_Alwayes", z);
//////    }
//////
//////    public void setScreenOrientation(String str) {
//////        if (str.equals("")) {
//////            String mode_orientation = getMode_orientation();
//////            if (mode_orientation.equals("portrait")) {
//////                str = SettingsActivity_2.CityType_1;
//////            } else if (mode_orientation.equals("r_portrait")) {
//////                str = "2";
//////            } else {
//////                return;
//////            }
//////        }
//////        d.b(b(), "ScreenOrientation", str);
//////    }
//////
//////    public void setScreenStartAsr(String str) {
//////        d.b(b(), "screenStartAsr", str);
//////    }
//////
//////    public void setScreenStartDhuhr(String str) {
//////        d.b(b(), "screenStartDhuhr", str);
//////    }
//////
//////    public void setScreenStartFajr(String str) {
//////        d.b(b(), "screenStartFajr", str);
//////    }
//////
//////    public void setScreenStartIsha(String str) {
//////        d.b(b(), "screenStartIsha", str);
//////    }
//////
//////    public void setScreenStartJomo3a(String str) {
//////        d.b(b(), "screenStartJomo3a", str);
//////    }
//////
//////    public void setScreenStartMaghrib(String str) {
//////        d.b(b(), "screenStartMaghrib", str);
//////    }
//////
//////    public void setScreenStartShuruq(String str) {
//////        d.b(b(), "screenStartShuruq", str);
//////    }
//////
//////    public void setScreenTV_Alwayes(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_TV_Alwayes", z);
//////    }
//////
//////    public void setScreenType(String str) {
//////        d.b(b(), "ScreenType", str);
//////    }
//////
//////    public void setScreenURL(String str) {
//////        d.b(b(), "ScreenURL", str);
//////    }
//////
//////    public void setScreenURL_2(String str) {
//////        d.b(b(), "ScreenURL_2", str);
//////    }
//////
//////    public void setScreenURL_net(String str) {
//////        d.b(b(), "ScreenURL_net", str);
//////    }
//////
//////    public void setScreenVideoURL(String str) {
//////        d.b(b(), "ScreenVideoURL", str);
//////    }
//////
//////    public void setScreenVideoURL_net(String str) {
//////        d.b(b(), "ScreenVideoURL_net", str);
//////    }
//////
//////    public void setScreen_Font_Increase(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_Font_Increase", z);
//////    }
//////
//////    public void setScreen_Font_Increase_Tab(boolean z) {
//////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_Font_Increase_Tab", z);
//////    }
////
////    public void setSeason(String str) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putString("season", str);
////        MyTool.MyLog("m_season--", "setSeason 33 " + str + " " + getSeason());
////        edit.apply();
////    }
////
////    public void setSeasonPrayerNotChange(Boolean bool) {
////        SharedPreferences.Editor edit = b().edit();
////        edit.putBoolean("SeasonPrayerNotChange", bool.booleanValue());
////        edit.apply();
////    }
////
//
////    public void setGoToMain(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "GoToMain", z);
////    }
////
////    public void setHijriDateEdit(EditText editText, EditText editText2, EditText editText3) {
////        int hijriDatePreference = getHijriDatePreference();
////        Calendar calendar = Calendar.getInstance();
////        int i = calendar.get(5);
////        int i2 = calendar.get(2) + 1;
////        int i3 = calendar.get(1);
////        String DateToStr = MyTool.DateToStr(i3, i2, i);
////        Calendar calendar2 = Calendar.getInstance();
////        calendar2.set(1, i3);
////        calendar2.set(2, i2 - 1);
////        calendar2.set(5, i);
////        calendar2.add(5, hijriDatePreference);
////        HijriCalendar hijriCalendar = new HijriCalendar(calendar2.get(1), calendar2.get(2) + 1, calendar2.get(5));
////        editText.setText(hijriCalendar.getHijriYear() + "");
////        editText2.setText(hijriCalendar.getHijriMonth() + "");
////        editText3.setText(hijriCalendar.getHijriDay() + "");
////        if (DateToStr.equals(getHijriDatePreference_30())) {
////            editText3.setText("30");
////        }
////    }
//
////    public void setHijriDatePreference(int i) {
////        String DateToStr;
////        int i2 = i - NumDayHijri;
////        if (i2 > -3 && i2 < 3) {
////            DateToStr = "$$$$$";
////        } else {
////            Calendar calendar = Calendar.getInstance();
////            int i3 = calendar.get(5);
////            int i4 = calendar.get(2) + 1;
////            int i5 = calendar.get(1);
////            if (PrayerTimesApplication.date4PrayerTime != null && !PrayerTimesApplication.checkTvAgentEdition()) {
////                i3 = PrayerTimesApplication.date4PrayerTime.getDay();
////                i4 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.MONTH java.lang.String() + 1;
////                i5 = PrayerTimesApplication.date4PrayerTime.getCom.roomorama.caldroid.CaldroidFragment.YEAR java.lang.String();
////            }
////            DateToStr = MyTool.DateToStr(i5, i4, i3);
////            i2 = -1;
////        }
////        SharedPreferences.Editor edit = b().edit();
////        edit.putString("HijriDatePreference_30", DateToStr);
////        edit.putInt("HijriDatePreference", i2);
////        edit.apply();
////    }
//
//    public void setHour12_24(Hour hour) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putString("hour", hour.hourString());
//        edit.apply();
//    }
//
////    public void setIP1_screen(String str) {
////        d.b(b(), "IP1_screen", str);
////    }
////
////    public void setIP2_screen(String str) {
////        d.b(b(), "IP2_screen", str);
////    }
////
////    public void setIP3_screen(String str) {
////        d.b(b(), "IP3_screen", str);
////    }
////
////    public void setIP4_screen(String str) {
////        d.b(b(), "IP4_screen", str);
////    }
////
////    public void setIcon_alarm_notification_Settings(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "icon_alarm_notification_Settings", z);
////    }
////
////    public void setImask_Time(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Imask_Time", z);
////    }
////
////    public void setImsak_before_fajr_value_minutes(String str) {
////        d.b(b(), "Imsak_before_fajr_value_minutes", str);
////    }
////
////    public void setIntPreferenceVal(String str, int i) {
////        j0.a(b(), str, i);
////    }
////
////    public void setIsDownloadDesignTV(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "IsDownloadDesignTV", z);
////    }
////
////    public void setIsEnabledChangeVolume(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "IsEnabledChangeVolume", z);
////    }
//
//    public void setIsPressHome_ScreenActivity(boolean z) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean("IsPressHome_ScreenActivity", z);
//        edit.apply();
//        MyTool.MyLog("startScreenActivity", "222" + z);
//    }
//
////    public void setIsRINGER_MODE_NORMAL(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "IsRINGER_MODE_NORMAL", z);
////    }
////
////    public void setIsScreenOn(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "IsScreenOn", z);
////    }
//
//    public void setIs_hour_24_pref(Boolean bool) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean("is_hour_24_pref", bool.booleanValue());
//        edit.apply();
//    }
//
////    public void setIsha_Magrib_Between(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Isha_Magrib_Between", z);
////    }
////
////    public void setIsha_Magrib_Between_value_minutes(String str) {
////        d.b(b(), "Isha_Magrib_Between_value_minutes", str);
////    }
////
////    public void setLanguageLocale(String str) {
////        d.b(b(), "LanguageLocale", str);
////    }
////
////    public void setLecturesFilePath(String str) {
////        d.b(b(), "lecturesFilePath", str);
////    }
////
////    public void setLoad_Default_Dynamic_Screen(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Load_Default_Dynamic_Screen", z);
////    }
////
////    public void setLocation(float f2, float f3) {
////        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(this.context).edit();
////        edit.putFloat("location_latitude", f2);
////        edit.putFloat("location_longitude", f3);
////        edit.apply();
////    }
////
////    public void setLongMonthNamesDecreaseFontSize(int i) {
////        j0.a(b(), "long_month_names_Decrease_Font_Size", i);
////    }
////
////    public void setMiladi_month_yanayer(String str) {
////        d.b(b(), "miladi_month", str);
////    }
////
////    public void setMobileBackgroundType(String str) {
////        d.b(b(), "MobileBackgroundType", str);
////    }
////
////    public void setMobileDesignType(String str) {
////        d.b(b(), "MobileDesignType", str);
////    }
////
////    public void setMode_orientation(String str) {
////        d.b(b(), "mode_orientation", str);
////    }
////
////    public void setMode_orientation_new(String str) {
////        d.b(b(), "orientation_screen_preference_new", str);
////    }
////
////    public void setMove_value(int i) {
////        j0.a(b(), "Move_value", i);
////    }
////
////    public void setMust_request_random(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "must_request_random", z);
////    }
////
////    public void setMy_City_Selected(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "My_City_Selected", z);
////    }
////
////    public void setNew_Mobile_Design(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "New_Mobile_Design", z);
////    }
////
////    public void setNewsFilePath(String str) {
////        d.b(b(), "newsFilePath", str);
////    }
////
////    public void setNotiContentText(String str) {
////        d.b(b(), "NotiContentText", str);
////    }
////
////    public void setNotiContentTitle(String str) {
////        d.b(b(), "NotiContentTitle", str);
////    }
////
////    public void setNotiID(int i) {
////        j0.a(b(), "NotiID", i);
////    }
//
//    public void setNotiWhen(long j) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putLong("NotiWhen", j);
//        edit.apply();
//    }
//
////    public void setOpenAppOnAzan(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "open_app_on_azan", z);
////    }
////
////    public void setOrientaionTV(String str) {
////        d.b(b(), "orientaionTV", str);
////    }
////
////    public void setParamT4RandomRequest(String str) {
////        d.b(b(), "ParamT4RandomRequest", str);
////    }
////
////    public void setPauseVideoPosition(Integer num) {
////        a.b(num, b().edit(), "pauseVideoPosition");
////    }
////
////    public void setPause_productsList_index(Integer num) {
////        a.b(num, b().edit(), "pause_productsList_index");
////    }
//
//    public void setPause_resume_update_data(Boolean bool) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean("pause_resume_update_data", bool.booleanValue());
//        edit.apply();
//    }
//
////    public void setPlayAzanSound(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "play_azan_sound", z);
////    }
////
////    public void setPlayInSilentMode(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "PlayInSilentMode", z);
////    }
////
////    public void setPlayQuranSound(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "play_quran_sound", z);
////    }
////
////    public void setPlaySoundOnSilentBegin(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "PlaySoundOnSilentBegin", z);
////    }
////
////    public void setPowerSaveAutoTurnOff(Integer num) {
////        a.b(num, b().edit(), "powerSaveAutoTurnOff_preferen");
////    }
////
////    public void setPowerSaveFajr(Integer num) {
////        a.b(num, b().edit(), "powerSaveFajr_preferen");
////    }
////
////    public void setPowerSaveIsha(Integer num) {
////        a.b(num, b().edit(), "powerSaveIsha_preferen");
////    }
////
////    public void setPrayerWidgetNotificationSettings(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "prayer_widget_notification_Settings", z);
////    }
////
////    public void setPrayer_News(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Prayer_News", z);
////    }
////
////    public void setPrayer_News_From_Screen(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Prayer_News_From_Screen", z);
////    }
////
////    public void setPrayer_News_Old_Method(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Prayer_News_Old_Method", z);
////    }
////
////    public void setPrayer_News_txt(String str) {
////        d.b(b(), "Prayer_News_Txt", str);
////    }
////
////    public void setPreference_Property_name(String str) {
////        d.b(b(), "Preference_Property_name", str);
////    }
////
////    public void setPrivateCodeDesignTV(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "PrivateCodeDesignTV", z);
////    }
////
////    public void setQuran_audioOnly(int i) {
////        j0.a(b(), "quran_audioOnly", i);
////    }
////
////    public void setQuran_date_bagin(String str) {
////        d.b(b(), "quran_date_bagin", str);
////    }
////
////    public void setQuran_parts(int i) {
////        j0.a(b(), "quran_parts", i);
////    }
////
////    public void setQuran_quarters(int i) {
////        j0.a(b(), "quran_quarters", i);
////    }
////
////    public void setQuran_showTafseer(int i) {
////        j0.a(b(), "quran_showTafseer", i);
////    }
////
////    public void setQuran_time(String str) {
////        d.b(b(), "quran_time", str);
////    }
////
////    public void setQuran_time_prayer(String str) {
////        d.b(b(), "quran_time_prayer", str);
////    }
////
////    public void setQuran_times(String str) {
////        d.b(b(), "quran_times", str);
////    }
////
////    public void setRandom_Date4Request(String str) {
////        d.b(b(), "Random_Date4Request", str);
////    }
////
////    public void setRandom_NextDate4Request(String str) {
////        d.b(b(), "Random_NextDate4Request", str);
////    }
////
////    public void setRandom_days4Request(String str) {
////        d.b(b(), "Random_days4Request", str);
////    }
////
////    public void setRandom_number4Request(String str) {
////        d.b(b(), "Random_number4Request", str);
////    }
////
////    public void setRestartAppMSG(String str) {
////        SharedPreferences.Editor edit = b().edit();
////        if (PrayerTimesApplication.checkTvAgentEdition()) {
////            str = "NONE";
////        }
////        edit.putString("RestartAppMSG", str);
////        edit.apply();
////    }
////
////    public void setRun_Full_Image(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Full_Image", z);
////    }
////
////    public void setRun_Full_PDF(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Full_PDF", z);
////    }
////
////    public void setRun_Full_Video(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Full_Video", z);
////    }
////
////    public void setRun_Screen(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Run_Screen", z);
////    }
////
////    public void setRun_Screen_Alwayes(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Run_Screen_Alwayes", z);
////    }
////
////    public void setRun_Screen_Alwayes_with_events(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Run_Screen_Alwayes_with_events", z);
////    }
////
////    public void setScreenBackground(String str) {
////        d.b(b(), "ScreenBackground", str);
////    }
////
////    public void setScreenDurationAsr(String str) {
////        d.b(b(), "screenDurationAsr", str);
////    }
////
////    public void setScreenDurationDhuhr(String str) {
////        d.b(b(), "screenDurationDhuhr", str);
////    }
////
////    public void setScreenDurationFajr(String str) {
////        d.b(b(), "screenDurationFajr", str);
////    }
////
////    public void setScreenDurationIsha(String str) {
////        d.b(b(), "screenDurationIsha", str);
////    }
////
////    public void setScreenDurationJomo3a(String str) {
////        d.b(b(), "screenDurationJomo3a", str);
////    }
////
////    public void setScreenDurationMaghrib(String str) {
////        d.b(b(), "screenDurationMaghrib", str);
////    }
////
////    public void setScreenDurationShuruq(String str) {
////        d.b(b(), "screenDurationShuruq", str);
////    }
////
////    public void setScreenFatawaURL(String str) {
////        d.b(b(), "ScreenFatawaURL", str);
////    }
////
////    public void setScreenFatawaURL_net(String str) {
////        d.b(b(), "ScreenFatawaURL_net", str);
////    }
////
////    public void setScreenFullTextURL(String str) {
////        d.b(b(), "ScreenFullTextURL", str);
////    }
////
////    public void setScreenFullTextURL_net(String str) {
////        d.b(b(), "ScreenFullTextURL_net", str);
////    }
////
////    public void setScreenFullVideoURL(String str) {
////        d.b(b(), "ScreenFullVideoURL", str);
////    }
////
////    public void setScreenFullVideoURL_net(String str) {
////        d.b(b(), "ScreenFullVideoURL_net", str);
////    }
////
////    public void setScreenLectureURL(String str) {
////        d.b(b(), "ScreenLectureURL", str);
////    }
////
////    public void setScreenLectureURL_net(String str) {
////        d.b(b(), "ScreenLectureURL_net", str);
////    }
////
////    public void setScreenManagementType(MyTool.ScreenManagementType screenManagementType) {
////        SharedPreferences.Editor edit = b().edit();
////        String str = "LOCAL";
////        if (screenManagementType != MyTool.ScreenManagementType.LOCAL) {
////            if (screenManagementType == MyTool.ScreenManagementType.REMOTE) {
////                str = "REMOTE";
////            } else if (screenManagementType == MyTool.ScreenManagementType.REMOTE_NET) {
////                str = "REMOTE_NET";
////            }
////        }
////        edit.putString("screenManagementType", str);
////        edit.apply();
////    }
////
////    public void setScreenNewsURL(String str) {
////        d.b(b(), "ScreenNewsURL", str);
////    }
////
////    public void setScreenNewsURL_net(String str) {
////        d.b(b(), "ScreenNewsURL_net", str);
////    }
////
////    public void setScreenOnAlwayes(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_On_Alwayes", z);
////    }
////
////    public void setScreenOrientation(String str) {
////        if (str.equals("")) {
////            String mode_orientation = getMode_orientation();
////            if (mode_orientation.equals("portrait")) {
////                str = SettingsActivity_2.CityType_1;
////            } else if (mode_orientation.equals("r_portrait")) {
////                str = "2";
////            } else {
////                return;
////            }
////        }
////        d.b(b(), "ScreenOrientation", str);
////    }
////
////    public void setScreenStartAsr(String str) {
////        d.b(b(), "screenStartAsr", str);
////    }
////
////    public void setScreenStartDhuhr(String str) {
////        d.b(b(), "screenStartDhuhr", str);
////    }
////
////    public void setScreenStartFajr(String str) {
////        d.b(b(), "screenStartFajr", str);
////    }
////
////    public void setScreenStartIsha(String str) {
////        d.b(b(), "screenStartIsha", str);
////    }
////
////    public void setScreenStartJomo3a(String str) {
////        d.b(b(), "screenStartJomo3a", str);
////    }
////
////    public void setScreenStartMaghrib(String str) {
////        d.b(b(), "screenStartMaghrib", str);
////    }
////
////    public void setScreenStartShuruq(String str) {
////        d.b(b(), "screenStartShuruq", str);
////    }
////
////    public void setScreenTV_Alwayes(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_TV_Alwayes", z);
////    }
////
////    public void setScreenType(String str) {
////        d.b(b(), "ScreenType", str);
////    }
////
////    public void setScreenURL(String str) {
////        d.b(b(), "ScreenURL", str);
////    }
////
////    public void setScreenURL_2(String str) {
////        d.b(b(), "ScreenURL_2", str);
////    }
////
////    public void setScreenURL_net(String str) {
////        d.b(b(), "ScreenURL_net", str);
////    }
////
////    public void setScreenVideoURL(String str) {
////        d.b(b(), "ScreenVideoURL", str);
////    }
////
////    public void setScreenVideoURL_net(String str) {
////        d.b(b(), "ScreenVideoURL_net", str);
////    }
////
////    public void setScreen_Font_Increase(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_Font_Increase", z);
////    }
////
////    public void setScreen_Font_Increase_Tab(boolean z) {
////        androidx.appcompat.graphics.drawable.b.d(b(), "Screen_Font_Increase_Tab", z);
////    }
//
//    public void setSeason(String str) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putString("season", str);
//        MyTool.MyLog("m_season--", "setSeason 33 " + str + " " + getSeason());
//        edit.apply();
//    }
//
//    public void setSeasonPrayerNotChange(Boolean bool) {
//        SharedPreferences.Editor edit = b().edit();
//        edit.putBoolean("SeasonPrayerNotChange", bool.booleanValue());
//        edit.apply();
//    }
//
//    public void setSerialVersion__app(String str) {
//        d.b(b(), "is_demo_version", str);
//    }
//
//    public void setSerialVersion__screen(String str) {
//        d.b(b(), "is_demo_version__screen", str);
//    }
//
//    public void setShowBlackWhenTurnOff(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "showBlackWhenTurnOff", z);
//    }
//
//    public void setShowPowerManagerSettings_not_visible_again(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "ShowPowerManagerSettings_not_visible_again", z);
//    }
//
//    public void setShowPowerManagerSettings_other_DLG_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "ShowPowerManagerSettings_other_DLG_visible", z);
//    }
//
//    public void setShow_azkar_notification_Window(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "show_azkar_notification_Window", z);
//    }
//
//    public void setSilentBeginAudio(String str) {
//        d.b(b(), "silent_begin_audio", str);
//    }
//
//    public void setSilentDuration(String str) {
//        d.b(b(), "silentDuration", str);
//    }
//
//    public void setSilentDurationAsr(String str) {
//        d.b(b(), "silentDurationAsr", str);
//    }
//
//    public void setSilentDurationDhuhr(String str) {
//        d.b(b(), "silentDurationDhuhr", str);
//    }
//
//    public void setSilentDurationFajr(String str) {
//        d.b(b(), "silentDurationFajr", str);
//    }
//
//    public void setSilentDurationIsha(String str) {
//        d.b(b(), "silentDurationIsha", str);
//    }
//
//    public void setSilentDurationJomo3a(String str) {
//        d.b(b(), "silentDurationJomo3a", str);
//    }
//
//    public void setSilentDurationMaghrib(String str) {
//        d.b(b(), "silentDurationMaghrib", str);
//    }
//
//    public void setSilentDurationTarawih(String str) {
//        d.b(b(), "silentDurationTarawih", str);
//    }
//
//    public void setSilentStart(String str) {
//        d.b(b(), "silentStart", str);
//    }
//
//    public void setSilentStartAsr(String str) {
//        d.b(b(), "silentStartAsr", str);
//    }
//
//    public void setSilentStartDhuhr(String str) {
//        d.b(b(), "silentStartDhuhr", str);
//    }
//
//    public void setSilentStartFajr(String str) {
//        d.b(b(), "silentStartFajr", str);
//    }
//
//    public void setSilentStartIsha(String str) {
//        d.b(b(), "silentStartIsha", str);
//    }
//
//    public void setSilentStartJomo3a(String str) {
//        d.b(b(), "silentStartJomo3a", str);
//    }
//
//    public void setSilentStartMaghrib(String str) {
//        d.b(b(), "silentStartMaghrib", str);
//    }
//
//    public void setSilentStartTarawih(String str) {
//        d.b(b(), "silentStartTarawih", str);
//    }
//
//    public void setStartAlMokittaFromTCP(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "StartAlMokittaFromTCP", z);
//    }
//
//    public void setStartOnBoot(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "start_on_boot", z);
//    }
//
//    public void setStartURLDownloadJustNews(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "StartURLDownloadJustNews", z);
//    }
//
//    public void setStartURLDownloadVideo(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "StartURLDownloadVideo", z);
//    }
//
//    public void setStart_ForegroundService(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "Start_ForegroundService", z);
//    }
//
//    public void setStartingSilentVibration(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "startingSilentVibration", z);
//    }
//
//    public void setStopSoundDialog(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "stop_sound_dialog", z);
//    }
//
//    public void setStopSoundIfMovment(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "stop_sound_if_movment", z);
//    }
//
//    public void setStopSoundIfPressVolumeKey(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "stop_sound_if_press_volume_key", z);
//    }
//
//    public void setStop_play_on_call(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "stop_play_on_call", z);
//    }
//
//    public void setStrPreferenceVal(String str, String str2) {
//        d.b(b(), str, str2);
//    }
//
//    public void setTCPServerService_preferences(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "TCPServerService_preferences", z);
//    }
//
//    public void setTime_minute_start_app(String str) {
//        d.b(b(), "time_minute_start_app", str);
//    }
//
//    public void setToday(String str) {
//        d.b(b(), "today", str);
//    }
//
//    public void setUDP_IP(String str) {
//        d.b(b(), "UDP_IP", str);
//    }
//
//    public void setUDP_PORT(String str) {
//        d.b(b(), "UDP_PORT", str);
//    }
//
//    public void setURLCacheVideoP(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "URLCacheVideo", z);
//    }
//
//    public void setURLDownloadVideo(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "URLDownloadVideo", z);
//    }
//
//    public void setUseDynamicScreenTV(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "UseDynamicScreenTV", z);
//    }
//
//    public void setVibrationMode(int i) {
//        j0.a(b(), "vibrationMode", i);
//    }
//
//    public void setVibration_on_azan(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "vibration_on_azan", z);
//    }
//
//    public void setVibration_on_ikama(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "vibration_on_ikama", z);
//    }
//
//    public void setVibration_on_silent(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "vibration_on_silent", z);
//    }
//
//    public void setVideoFilePath(String str) {
//        d.b(b(), "videoFilePath", str);
//    }
//
//    public void setVideoList_Screen(String str) {
//        d.b(b(), "VideoList_Screen", str);
//    }
//
//    public void set__end_ayah(int i) {
//        j0.a(b(), "_end_ayah", i);
//    }
//
//    public void set__end_sura(int i) {
//        j0.a(b(), "_end_sura", i);
//    }
//
//    public void set__quran_ok_read(int i) {
//        j0.a(b(), "_quran_ok_read", i);
//    }
//
//    public void set__start_ayah(int i) {
//        j0.a(b(), "_start_ayah", i);
//    }
//
//    public void set__start_sura(int i) {
//        j0.a(b(), "_start_sura", i);
//    }
//
//    public void set_city_widget_lock_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "city_widget_lock_visible", z);
//    }
//
//    public void set_city_widget_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "city_widget_visible", z);
//    }
//
//    public void set_clock_widget_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "clock_widget_visible", z);
//    }

//    public void set_daysToAdd(String str) {
//        d.b(b(), "daysToAdd", str);
//    }

//    public void set_design_tv_background(String str) {
//        d.b(b(), "design_tv_background", str);
//    }

//    public void set_design_tv_dynamic(int i) {
//        j0.a(b(), "design_tv_dynamic", i);
//    }

//    public void set_design_tv_dynamic_LanguageDescrList(String str) {
//        d.b(b(), "design_tv_dynamic_LanguageDescrList", str);
//    }

//    public void set_design_tv_dynamic_LanguageIDList(String str) {
//        d.b(b(), "design_tv_dynamic_LanguageIDList", str);
//    }

//    public void set_design_tv_dynamic_code(String str) {
//        d.b(b(), "design_tv_dynamic_code", str);
//    }

//    public void set_design_tv_dynamic_langs_descr(String str) {
//        d.b(b(), "design_tv_dynamic_langs_descr", str);
//    }

//    public void set_design_tv_dynamic_langs_id(String str) {
//        d.b(b(), "design_tv_dynamic_langs_id", str);
//    }

//    public void set_design_tv_dynamic_langs_index(String str) {
//        d.b(b(), "design_tv_dynamic_langs_index", str);
//    }

//    public void set_design_tv_dynamic_type(int i) {
//        j0.a(b(), "design_tv_dynamic_type", i);
//    }

//    public void set_design_tv_screen_dynamic_type(int i) {
//        j0.a(b(), "design_tv_screen_dynamic_type", i);
//    }

//    public void set_distance_nearest_mosques(String str) {
//        d.b(b(), "distance_nearest_mosques", str);
//    }

//    public void set_fasting_widget_lock_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "fasting_widget_lock_visible", z);
//    }

//    public void set_fasting_widget_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "fasting_widget_visible", z);
//    }

//    public void set_hijri_date_widget_lock_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "hijri_date_widget_lock_visible", z);
//    }

//    public void set_hijri_date_widget_visible(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "hijri_date_widget_visible", z);
//    }

//    public void set_is_azan_only(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "is_azan_only", z);
//    }

//    public void set_prefs_translation_text_size(int i) {
//        j0.a(b(), "prefs_translation_text_size", i);
//    }

//    public void set_quran_readers_name(int i) {
//        j0.a(b(), "quran_readers_name", i);
//    }

//    public void set_secondsToAdd(String str) {
//        d.b(b(), "secondsToAdd", str);
//    }

//    public void set_secondsToAddTotal(String str) {
//        d.b(b(), "secondsToAddTotal", str);
//    }

//    public void set_select_dynamic_design(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "select_dynamic_design", z);
//    }

//    public void set_stop_DaemonService(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "stop_DaemonService", z);
//    }

//    public void set_suhoor_val(Integer num) {
//        a.b(num, b().edit(), "suhoor_val");
//    }

//    public void set_time_zone_city_2(String str) {
//        MyTool.MyLog("time_zone_city_2", "00 " + str);
//        d.b(b(), "time_zone_city_2", str);
//    }

//    public void set_transperant_on_off(boolean z) {
//        androidx.appcompat.graphics.drawable.b.d(b(), "transperant_on_off", z);
//    }

//    public void set_turn_off_prayer_period(String str) {
//        d.b(b(), "turn_off_prayer_period", str);
//    }

//    public void set_turn_off_prayer_period_jomo3a(String str) {
//        d.b(b(), "turn_off_prayer_period_jomo3a", str);
//    }

//    public void setHour12_24(String str) {
//        d.b(b(), "hour", str);
//    }

//    public void setScreenManagementType(String str) {
//        d.b(b(), "screenManagementType", str);
//    }

//    public HijriDate getHijriDate(Calendar calendar, int i) {
//        MainActivityControls.Companion companion = MainActivityControls.INSTANCE;
//        if (companion.getSecondsToAddInUsing() && companion.getDaysToAdd() > 0) {
//            calendar.add(5, companion.getDaysToAdd());
//        }
//        calendar.add(5, i);
//        int i2 = calendar.get(5);
//        HijriCalendar hijriCalendar = new HijriCalendar(calendar.get(1), calendar.get(2) + 1, i2);
//        Calendar calendar2 = Calendar.getInstance();
//        int i3 = calendar2.get(5);
//        String DateToStr = MyTool.DateToStr(calendar2.get(1), calendar2.get(2) + 1, i3);
//        int hijriYear = hijriCalendar.getHijriYear();
//        int hijriMonth = hijriCalendar.getHijriMonth();
//        int hijriDay = hijriCalendar.getHijriDay();
//        if (DateToStr.equals(getHijriDatePreference_30())) {
//            hijriDay = 30;
//        }
//        return new HijriDate(hijriYear, hijriMonth, hijriDay);
//    }

//    public HijriDate getHijriDate() {
//        int hijriDatePreference = getHijriDatePreference();
//        Calendar calendar = Calendar.getInstance();
//        MainActivityControls.Companion companion = MainActivityControls.INSTANCE;
//        if (companion.getSecondsToAddInUsing() && companion.getDaysToAdd() > 0) {
//            calendar.add(5, companion.getDaysToAdd());
//        }
//        calendar.add(5, hijriDatePreference);
//        int i = calendar.get(5);
//        HijriCalendar hijriCalendar = new HijriCalendar(calendar.get(1), calendar.get(2) + 1, i);
//        Calendar calendar2 = Calendar.getInstance();
//        int i2 = calendar2.get(5);
//        String DateToStr = MyTool.DateToStr(calendar2.get(1), calendar2.get(2) + 1, i2);
//        int hijriYear = hijriCalendar.getHijriYear();
//        int hijriMonth = hijriCalendar.getHijriMonth();
//        int hijriDay = hijriCalendar.getHijriDay();
//        if (DateToStr.equals(getHijriDatePreference_30())) {
//            hijriDay = 30;
//        }
//        return new HijriDate(hijriYear, hijriMonth, hijriDay);
//    }
}
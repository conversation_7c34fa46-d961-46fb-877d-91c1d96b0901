package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class PrayerApi {

    @Expose
    @SerializedName("code")
    private int code;
    @Expose
    @SerializedName("status")
    private String status;
    @Expose
    @SerializedName("data")
    private PrayerList prayerList;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public PrayerList getPrayerList() {
        return prayerList;
    }

    public void setPrayerList(PrayerList prayerList) {
        this.prayerList = prayerList;
    }

    public List<TimingsAlrabeeaTimes> getAllTimings() {
        List<TimingsAlrabeeaTimes> timings = new ArrayList<>();
        List<Prayer> allPrayer = prayerList.getAll();
        for (Prayer prayer : allPrayer) {
            timings.add(prayer.getTimings());
        }
        return timings;
    }
}

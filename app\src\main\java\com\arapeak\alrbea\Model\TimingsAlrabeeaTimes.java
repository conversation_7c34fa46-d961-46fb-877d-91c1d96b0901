package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.text.SimpleDateFormat;
import java.util.Date;

public class TimingsAlrabeeaTimes {

    public int Year;
    @Expose
    @SerializedName("Midnight")
    private String Midnight;
    @Expose
    @SerializedName("Imsak")
    private String Imsak;
    @Expose
    @SerializedName("Isha")
    private String Isha;
    @Expose
    @SerializedName("Maghrib")
    private String Maghrib;
    @Expose
    @SerializedName("Sunset")
    private String Sunset;
    @Expose
    @SerializedName("Asr")
    private String Asr;
    @Expose
    @SerializedName("Dhuhr")
    private String Dhuhr;
    @Expose
    @SerializedName("Sunrise")
    private String Sunrise;
    @Expose
    @SerializedName("Fajr")
    private String Fajr;
    @Expose
    @SerializedName("Month")
    private String Month;
    @Expose
    @SerializedName("Day")
    private String Day;

    public String getDate() {
        return String.format("%02d", getIntDay()) + "-" + String.format("%02d", getIntMonth()) + "-" + String.format("%02d", Year);
    }

    public String getDate(String format) {
        Date date = new Date(Year, getIntMonth(), getIntDay());
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    public String getIsha() {
        return Isha;
    }

    public void setIsha(String Isha) {
        this.Isha = Isha;
    }

    public String getMaghrib() {
        return Maghrib;
    }

    public void setMaghrib(String Maghrib) {
        this.Maghrib = Maghrib;
    }

    public String getAsr() {
        return Asr;
    }

    public void setAsr(String Asr) {
        this.Asr = Asr;
    }

    public String getDhuhr() {
        return Dhuhr;
    }

    public void setDhuhr(String Dhuhr) {
        this.Dhuhr = Dhuhr;
    }

    public String getSunrise() {
        return Sunrise;
    }

    public void setSunrise(String Sunrise) {
        this.Sunrise = Sunrise;
    }

    public String getFajr() {
        return Fajr;
    }

    public void setFajr(String Fajr) {
        this.Fajr = Fajr;
    }

    public String getMonth() {
        return Month;
    }

    public void setMonth(String month) {
        Month = month;
    }

    public void setMonth(int month) {
        Month = String.valueOf(month);
    }

    public int getIntMonth() {
        try {
            return Integer.parseInt(Month.trim());
        } catch (Exception e) {
            return 0;
        }
    }

    public String getDay() {
        return Day;
    }

    public void setDay(String day) {
        Day = day;
    }

    public void setDay(int day) {
        Day = String.valueOf(day);
    }

    public int getIntDay() {
        try {
            return Integer.parseInt(Day.trim());
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (obj == this) return true;
        if (!(obj instanceof TimingsAlrabeeaTimes)) return false;
        TimingsAlrabeeaTimes timingsAlrabeeaTimes = (TimingsAlrabeeaTimes) obj;
        if (timingsAlrabeeaTimes.getIntMonth() < 1 || getIntMonth() < 1
                || timingsAlrabeeaTimes.getIntDay() < 1 || getIntDay() < 1) return false;
        return timingsAlrabeeaTimes.getIntMonth() == getIntMonth() && getIntDay() == timingsAlrabeeaTimes.getIntDay();
    }

    @Override
    public String toString() {
        return "TimingsAlrabeeaTimes{" +
                "Midnight='" + Midnight + '\'' +
                ", Imsak='" + Imsak + '\'' +
                ", Isha='" + Isha + '\'' +
                ", Maghrib='" + Maghrib + '\'' +
                ", Sunset='" + Sunset + '\'' +
                ", Asr='" + Asr + '\'' +
                ", Dhuhr='" + Dhuhr + '\'' +
                ", Sunrise='" + Sunrise + '\'' +
                ", Fajr='" + Fajr + '\'' +
                ", Month='" + Month + '\'' +
                ", Day='" + Day + '\'' +
                '}';
    }
}









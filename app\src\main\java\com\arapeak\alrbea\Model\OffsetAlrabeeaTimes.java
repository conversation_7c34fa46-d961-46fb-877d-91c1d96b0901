package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class OffsetAlrabeeaTimes {
    @Expose
    @SerializedName("Midnight")
    private String Midnight;
    @Expose
    @SerializedName("Isha")
    private String Isha;
    @Expose
    @SerializedName("Sunset")
    private String Sunset;
    @Expose
    @SerializedName("Maghrib")
    private String Maghrib;
    @Expose
    @SerializedName("Asr")
    private String Asr;
    @Expose
    @SerializedName("Dhuhr")
    private String Dhuhr;
    @Expose
    @SerializedName("Sunrise")
    private String Sunrise;
    @Expose
    @SerializedName("Fajr")
    private String Fajr;
    @Expose
    @SerializedName("Imsak")
    private String Imsak;

    public String getMidnight() {
        return Midnight;
    }

    public void setMidnight(String midnight) {
        Midnight = midnight;
    }

    public String getIsha() {
        return Isha;
    }

    public void setIsha(String isha) {
        Isha = isha;
    }

    public String getSunset() {
        return Sunset;
    }

    public void setSunset(String sunset) {
        Sunset = sunset;
    }

    public String getMaghrib() {
        return Maghrib;
    }

    public void setMaghrib(String maghrib) {
        Maghrib = maghrib;
    }

    public String getAsr() {
        return Asr;
    }

    public void setAsr(String asr) {
        Asr = asr;
    }

    public String getDhuhr() {
        return Dhuhr;
    }

    public void setDhuhr(String dhuhr) {
        Dhuhr = dhuhr;
    }

    public String getSunrise() {
        return Sunrise;
    }

    public void setSunrise(String sunrise) {
        Sunrise = sunrise;
    }

    public String getFajr() {
        return Fajr;
    }

    public void setFajr(String fajr) {
        Fajr = fajr;
    }

    public String getImsak() {
        return Imsak;
    }

    public void setImsak(String imsak) {
        Imsak = imsak;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_ScrollView_InitialSetupActivity"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true"
    android:paddingBottom="12dp"
    tools:context=".UI.Activity.InitialSetupActivity"
    tools:layoutDirection="rtl">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_ConstraintLayout_InitialSetupActivity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout_AppBarLayout_InitialSetupActivity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/AppTheme.AppBarOverlay"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:layout_width="match_parent"
                android:layout_height="@dimen/_40sdp"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/AppTheme.PopupOverlay">

                <TextView
                    android:id="@+id/title_TextView_InitialSetupActivity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/activate_application"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_14sdp"
                    android:textStyle="bold" />
            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <!--
 without_corners_bottom_10_background_gray_with_stroke_gray-->

        <LinearLayout
            android:id="@+id/optionChoose_LinearLayout_InitialSetupActivity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_15sdp"
            android:layout_marginEnd="@dimen/_15sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/appBarLayout_AppBarLayout_InitialSetupActivity"
            app:layout_constraintVertical_bias="0.25">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/optionChoose_RecyclerView_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:overScrollMode="never"

                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:reverseLayout="false"
                tools:itemCount="2"
                tools:listitem="@layout/layout_list_item_option_choose" />

            <Button
                android:id="@+id/saveSelect_Button_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:gravity="center_vertical|center_horizontal"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/_5sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_5sdp"
                android:paddingBottom="@dimen/_5sdp"
                android:text="@string/save"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_13sdp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/debug1_TextView_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_15sdp"
                tools:text="vrfervemrgveromvoenroiv" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/activeCode_LinearLayout_InitialSetupActivity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_15sdp"
            android:layout_marginEnd="@dimen/_15sdp"
            android:layout_marginBottom="@dimen/_8sdp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/appBarLayout_AppBarLayout_InitialSetupActivity"
            app:layout_constraintVertical_bias="0.25">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/please_enter_activate_code"
                android:textColor="#474747"
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/activeCode_EditText_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="start|center_vertical"
                android:hint="@string/activate_code"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/_15sdp"
                android:inputType="number|text"
                android:paddingTop="@dimen/_7sdp"
                android:paddingEnd="@dimen/_15sdp"
                android:paddingBottom="@dimen/_7sdp"
                android:singleLine="true"
                android:textAlignment="viewStart"
                android:textColor="@android:color/black"
                android:textColorHint="#474747"
                android:textSize="@dimen/_15sdp" />


            <Button
                android:id="@+id/enable_Button_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_14sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:gravity="center_vertical|center_horizontal"
                android:includeFontPadding="false"
                android:text="@string/active"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold" />

            <!--<TextView
                android:id="@+id/doNotHaveCode_TextView_InitialSetupActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/forgot_or_do_not_have"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="#474747"
                android:textSize="18sp"
                android:textStyle="bold" />-->


            <Button
                android:id="@+id/contactUs_Button_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:background="@drawable/without_corners_50_background_dark_gray"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:gravity="center_vertical|center_horizontal"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/_5sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_5sdp"
                android:paddingBottom="@dimen/_5sdp"
                android:text="@string/contact_us"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/debug2_TextView_InitialSetupActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_15sdp"
                tools:text="vrfervemrgveromvoenroiv" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
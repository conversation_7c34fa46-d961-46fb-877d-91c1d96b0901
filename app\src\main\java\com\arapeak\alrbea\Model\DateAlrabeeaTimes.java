package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;


public class DateAlrabeeaTimes {
    @Expose
    @SerializedName("hijri")
    private HijriDateAlrabeeaTimes hijri;
    @Expose
    @SerializedName("gregorian")
    private GregorianAlrabeeaTimes gregorian;
    @Expose
    @SerializedName("timestamp")
    private String timestamp;
    @Expose
    @SerializedName("readable")
    private String readable;

    public HijriDateAlrabeeaTimes getHijri() {
        return hijri;
    }

    public void setHijri(HijriDateAlrabeeaTimes hijri) {
        this.hijri = hijri;
    }

    public GregorianAlrabeeaTimes getGregorian() {
        return gregorian;
    }

    public void setGregorian(GregorianAlrabeeaTimes gregorian) {
        this.gregorian = gregorian;
    }

    public boolean isJomaa() {
        return Utils.isJomaa();
//        return Utils.isDayJomaa(dateAlrabeeaTimes.getGregorian().getWeekday().getEn());
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getReadable() {
        return readable;
    }

    public void setReadable(String readable) {
        this.readable = readable;
    }
}

package com.arapeak.alrbrea.core_ktx.repo


import android.content.Context
import android.location.Location
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_adhan.AdhanPrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.AzanPrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.DbPrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.KacstPrayerTimeProvider
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.Bahrain
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.Jordan
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.KSA
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.Kuwait
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.Qatar
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.UAE
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum.Yemen
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.automatic
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.customCalendar
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.egyptianSurvey
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.france
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.gulf_region
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.karachi
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.kuwait
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.muslimLeague
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.northAmerica
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.russia
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.singapore
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.turkey
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.ummAlQurra
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers
import com.batoulapps.adhan2.Madhab
import java.util.Calendar

class PrayerTimeRepo(
    private val location: Location,
    private val calculationMethod: CalculationMethod,
) {


    fun getPrayerTimes(date: Calendar, country: CountriesSupportedEnum, context: Context): DayPrayers {
        val madhab = Madhab.SHAFI

        return when (calculationMethod) {

            automatic -> officialMethod(location, date, country, context)

            ummAlQurra -> KacstPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            egyptianSurvey -> AzanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            karachi -> AzanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            muslimLeague -> AzanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            northAmerica -> AzanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            kuwait -> KacstPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            singapore -> AdhanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                Madhab.SHAFI,
                date
            )

            france -> KacstPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                madhab,
                date
            )

            turkey -> AdhanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                Madhab.SHAFI,
                date
            )

            //Not Available
            gulf_region, russia, customCalendar -> {
                AzanPrayerTimeProvider().getPrayerTime(
                    location,
                    calculationMethod,
                    madhab,
                    date
                )
            }
        }

    }

    private fun officialMethod(location: Location, date: Calendar, country: CountriesSupportedEnum, context: Context): DayPrayers {
        return when (country) {
            KSA -> KacstPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                Madhab.SHAFI,
                date
            )

            Bahrain -> AdhanPrayerTimeProvider().getPrayerTime(
                location,
                calculationMethod,
                Madhab.SHAFI,
                date
            )

            Kuwait -> {
                DbPrayerTimeProvider(context).getPrayerTimeKuwait(
                    location, date
                )
            }

            Jordan -> {
                AzanPrayerTimeProvider().getPrayerTimeJordan(
                    location.apply {
                        latitude = 31.946060
                        longitude = 35.933583
                    },
                    date
                )
            }

            Yemen ->{
                DbPrayerTimeProvider(context).getPrayerTimeAden(
                    location, date
                )
            }

            Qatar -> TODO()
            UAE -> TODO()
        }
    }
}
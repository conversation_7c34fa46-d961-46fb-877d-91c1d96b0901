<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:layoutDirection="rtl"
    android:orientation="vertical">

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.custom_1"
        android:layoutDirection="locale"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/azan_TextView_MainActivity"
            style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
            android:text="@string/adhaan"
            android:textColor="#906e44" />

        <TextView
            android:id="@+id/prayer_TextView_MainActivity"
            style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
            android:layout_weight="1"
            android:text="@string/prayer"
            android:textColor="#906e44" />

        <TextView
            android:id="@+id/ikama_TextView_MainActivity"
            style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
            android:text="@string/ikama"
            android:textColor="#906e44" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentFajr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/fajr_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
                        android:text="@string/fajr" />
                    <include
                        android:id="@+id/tv_prayer_ikama_time_fajr"
                        layout="@layout/textview_circle_grey"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/fajrATime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
                        android:text="@string/dhuhr" />
                    <include
                        android:id="@+id/tv_prayer_ikama_time_dhur"
                        layout="@layout/textview_circle_grey"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/dhuhrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/dhuhrATime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentAsr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/asr_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
                        android:text="@string/asr" />
                    <include
                        android:id="@+id/tv_prayer_ikama_time_asr"
                        layout="@layout/textview_circle_grey"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/asrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/asrATime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/maghrib_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
                        android:text="@string/maghrib" />
                    <include
                        android:id="@+id/tv_prayer_ikama_time_maghrib"
                        layout="@layout/textview_circle_grey"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/maghribATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/maghribATime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentIsha_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />
            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/isha_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
                        android:text="@string/isha" />
                    <include
                        android:id="@+id/tv_prayer_ikama_time_isha"
                        layout="@layout/textview_circle_grey"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ishaATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/ishaATime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentSunrise_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.custom_1_land"
        android:layout_marginStart="@dimen/_20sdp"
        android:layout_marginEnd="@dimen/_20sdp"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/sunriseTime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama"
                    android:textSize="@dimen/_16sdp" />

            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity"
                        style="@style/TimeTextView.custom_1_land.TimeNameAR.WithIkama"
                        android:text="@string/duha"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_16sdp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side.CustomWithIkama">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/TimeTextView.custom_1_land.Time.WithIkama"
                    android:textSize="@dimen/_16sdp" />


            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
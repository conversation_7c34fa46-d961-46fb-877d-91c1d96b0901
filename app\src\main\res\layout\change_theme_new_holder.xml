<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/theme_ConstraintLayout_ThemeViewHolder"
    android:layout_width="@dimen/theme_item_width"
    android:layout_height="@dimen/theme_item_height"
    android:layout_marginBottom="@dimen/_5sdp"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/imageTheme_ImageView_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/selectedTheme_View_ThemeViewHolder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#4D1DAFEC"
        android:gravity="bottom"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/customize_ThemeViewHolder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_30sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:text="@string/customize"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_14sdp" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
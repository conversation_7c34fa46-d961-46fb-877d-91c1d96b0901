package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.dao

import androidx.room.Dao
import androidx.room.Query
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model.PrayerTimeKuwaitDbEntity


@Dao
interface PrayerTimeKuwaitDAO {
    @Query("SELECT * FROM kuwait")
    fun getAll(): List<PrayerTimeKuwaitDbEntity>

    @Query("SELECT * FROM kuwait WHERE date LIKE :arg LIMIT 1")
    fun findByDate(arg: String): PrayerTimeKuwaitDbEntity

}


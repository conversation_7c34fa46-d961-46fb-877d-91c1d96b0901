//MainSettingsFragment
package com.arapeak.alrbea.UI.Fragment.settings.content.main;


import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.InitialSetupActivity;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.GeneralSettingsFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.alerts.AzanIkamaFrag;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.DesignFolderSettingsFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsFragment;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;

import java.util.ArrayList;
import java.util.List;

public class MainSettingsFrag extends AlrabeeaTimesFragment implements AdapterCallback {
    private static final String TAG = "DesignFolderSettingsFragment";

    private View mainSettingsFragment;
    private RecyclerView settingItemRecyclerView;
    private Dialog loadingDialog;

    //    private SettingsAdapter settingsAdapter;
    private SettingsAdapter settingsAdapter;

    public MainSettingsFrag() {

    }

    public static MainSettingsFrag newInstance() {
        return new MainSettingsFrag();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mainSettingsFragment = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return mainSettingsFragment;
    }

    private void initView() {
        settingItemRecyclerView = mainSettingsFragment.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));
        loadingDialog = Utils.initLoadingDialog(getAppCompatActivity());

        settingsAdapter = new SettingsAdapter(getContext(), setupSettings(), this);
    }

    private void SetParameter() {
//        if (Utils.isLandscape()){
//            SettingsLandscapeActivity.setTextTite(getString(R.string.general_settings_title));
//        }else {
//            SettingsActivity.setTextTite(getString(R.string.general_settings_title));
//        }
        SettingsActivity.setTextTite(getString(R.string.general_settings_title));
        settingItemRecyclerView.setAdapter(settingsAdapter);

    }

    private void SetAction() {


    }

    @Override
    public void onItemClick(int position, String tag) {

        switch (position) {
            case 0:
                Utils.loadFragment(MainSettingsFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 1:
                Utils.loadFragment(DesignFolderSettingsFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 2:
                Utils.loadFragment(new AzanIkamaFrag()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 3:
                HawkSettings.setUpdateLocation(true);
                startActivity(new Intent(getAppCompatActivity(), InitialSetupActivity.class));
                break;
            case 4:
                Utils.loadFragment(GeneralSettingsFragment.newInstance()
                        , getAppCompatActivity()
                        , 0);
                break;
        }

    }

    private List<SettingAlrabeeaTimes> setupSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.settings_main);
        String[] generalSettingsDescriptionArray = getResources().getStringArray(R.array.settings_main_description);

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[0]
                , generalSettingsDescriptionArray[0]
                , R.drawable.setting));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[1]
                , generalSettingsDescriptionArray[1]
                , R.drawable.theme));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[3]
                , generalSettingsDescriptionArray[3]
                , R.drawable.noticee));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[2]
                , generalSettingsDescriptionArray[2]
                , R.drawable.location));


        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[4]
                , generalSettingsDescriptionArray[4]
                , R.drawable.setting));

//
//        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[5]
//                , generalSettingsDescriptionArray[5]
//                , R.drawable.screensaver));


        return settingAlrabeeaTimes;
    }
}



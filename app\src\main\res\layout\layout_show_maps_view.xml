<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="4dp"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:minWidth="360dp"
    android:orientation="vertical"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="14.5dp"
    app:cardElevation="0dp"
    tools:layoutDirection="rtl">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/optionChoose_RecyclerView_ShowMapsViewDialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        android:layout_margin="8dp"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:reverseLayout="false"
        tools:listitem="@layout/layout_list_item_option_choose" />
</LinearLayout>

package com.arapeak.alrbrea.core_ktx.repo.utils

import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object NetworkClient {

    private val TIMEOUT_IN_SECONDS = 30L

    private var retrofit: Retrofit? = null

    fun getRetrofitClient(isDebug: Boolean): Retrofit {
        if (retrofit == null)
            retrofit = initRetrofitClient(isDebug)

        return retrofit !!
    }


    private fun initRetrofitClient(isDebug: Boolean): Retrofit {
        val gson = GsonBuilder()
            .create()

        val loggingInterceptor = HttpLoggingInterceptor()
        loggingInterceptor.level = if (isDebug) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE


        val okHttpClient = OkHttpClient.Builder()
            .readTimeout(TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
            .writeTimeout(TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
            .connectTimeout(TIMEOUT_IN_SECONDS, TimeUnit.SECONDS)
            .addInterceptor(loggingInterceptor)
            .build()

        val retrofit = Retrofit.Builder()
            .client(okHttpClient)
            .baseUrl("https://www.alrebea.com")
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()

        return retrofit
    }
}
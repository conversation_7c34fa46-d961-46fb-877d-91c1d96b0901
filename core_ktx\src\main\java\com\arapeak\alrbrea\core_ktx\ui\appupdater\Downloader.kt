package com.arapeak.alrbrea.core_ktx.ui.appupdater

import android.content.Context
import android.os.Environment
import android.util.Log
import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.ui.utils.ConnectionExt
import com.tonyodev.fetch2.Download
import com.tonyodev.fetch2.EnqueueAction
import com.tonyodev.fetch2.Error
import com.tonyodev.fetch2.Fetch
import com.tonyodev.fetch2.FetchConfiguration
import com.tonyodev.fetch2.FetchGroup
import com.tonyodev.fetch2.FetchGroupListener
import com.tonyodev.fetch2.FetchListener
import com.tonyodev.fetch2.NetworkType
import com.tonyodev.fetch2.Priority
import com.tonyodev.fetch2.Request
import com.tonyodev.fetch2core.DownloadBlock


class Downloader(val context: Context) : FetchListener {
    private val fetch: Fetch
    private val TAG = "AppUpdater"

    private var onSuccess: ((String) -> Unit) = {}
    private var onError: ((String) -> Unit) = {}
    private var onLoading: ((Int) -> Unit) = {}

    init {
        val fetchConfiguration = FetchConfiguration.Builder(context)
            .setDownloadConcurrentLimit(3)
            .build()
        fetch = Fetch.Impl.getInstance(fetchConfiguration)

    }

    fun downloadUpdate(link: String, filename: String, clean: Boolean = true, onSuccess: ((String) -> Unit), onError: ((String) -> Unit), onLoading: ((Int) -> Unit) = {}) {
        this.onSuccess = onSuccess
        this.onError = onError
        this.onLoading = onLoading

        val file = context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath + "/" + filename

        Log.i(TAG, "file path : $file")

        val request = Request(link, file).apply {
            priority = Priority.HIGH
            networkType = NetworkType.ALL
            downloadOnEnqueue = true
            enqueueAction = if (clean) EnqueueAction.REPLACE_EXISTING else EnqueueAction.UPDATE_ACCORDINGLY
        }

        fetch.addListener(this)
        fetch.enqueue(request, {
            Log.i(TAG, "onAdded")
        }, {
            Log.i(TAG, "Error $it")
        })
    }

    fun downloadFile(request: DownloadRequest, onProgress: ((Int) -> Unit)): DownloadResult? {
        var file: DownloadResult? = null

        fetch.enqueue(
            Request(
                request.link,
                context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath + "/" + request.fileName
            ).apply {
                priority = Priority.HIGH
                networkType = NetworkType.ALL
                downloadOnEnqueue = true
                enqueueAction = EnqueueAction.REPLACE_EXISTING
            }
        )
        var progress = 0
        val listener = object : FetchListener {
            override fun onAdded(download: Download) {

            }

            override fun onCancelled(download: Download) {
            }

            override fun onCompleted(it: Download) {
                file = DownloadResult(
                    it.identifier,
                    request.callback,
                    it.file
                )

                Log.i(TAG, "onCompleted")
            }

            override fun onDeleted(download: Download) {
            }

            override fun onDownloadBlockUpdated(download: Download, downloadBlock: DownloadBlock, totalBlocks: Int) {
            }

            override fun onError(download: Download, error: Error, throwable: Throwable?) {
                progress = 999
            }

            override fun onPaused(download: Download) {
            }

            override fun onProgress(download: Download, etaInMilliSeconds: Long, downloadedBytesPerSecond: Long) {
                progress = download.progress
                onProgress.invoke(download.progress)
            }

            override fun onQueued(download: Download, waitingOnNetwork: Boolean) {
            }

            override fun onRemoved(download: Download) {
            }

            override fun onResumed(download: Download) {
            }

            override fun onStarted(download: Download, downloadBlocks: List<DownloadBlock>, totalBlocks: Int) {
            }

            override fun onWaitingNetwork(download: Download) {
            }

        }

        fetch.addListener(listener)

        var connectionLostAttempts = 0

        while (progress < 99 && connectionLostAttempts < 3) {
            Thread.sleep(1000)

            if (ConnectionExt().isNetworkConnected(context).not()) {
                connectionLostAttempts ++
            }

        }

        return file

    }

    fun downloadBulk(links: List<DownloadRequest>, onProgress: ((Int) -> Unit)): MutableSet<DownloadResult> {
        if (links.isEmpty())
            return mutableSetOf();

        var files = mutableSetOf<DownloadResult>()
        fetch.enqueue(links.mapIndexed { index, it ->
            Request(
                it.link,
                context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath + "/" + it.fileName
            ).apply {
                priority = if (index == 0) Priority.HIGH else Priority.LOW
                networkType = NetworkType.ALL
                identifier = index.toLong()
                downloadOnEnqueue = true
                groupId = 1
                enqueueAction = EnqueueAction.REPLACE_EXISTING
            }
        })
        var progress = 0
        val listener = object : FetchGroupListener {
            override fun onAdded(groupId: Int, download: Download, fetchGroup: FetchGroup) {
                Log.i(TAG, "onAdded")

            }

            override fun onAdded(download: Download) {
                Log.i(TAG, "onAdded")
            }

            override fun onCancelled(groupId: Int, download: Download, fetchGroup: FetchGroup) {
                Log.i(TAG, "onCancelled")

            }

            override fun onCancelled(download: Download) {
                Log.i(TAG, "onCancelled")

            }

            override fun onCompleted(groupId: Int, download: Download, fetchGroup: FetchGroup) {
                files = fetchGroup.completedDownloads.map {
                    DownloadResult(
                        it.identifier,
                        links.get(it.identifier.toInt()).callback,
                        it.file
                    )
                }.toMutableSet()

                Log.i(TAG, "onCompleted groupe")

            }

            override fun onCompleted(it: Download) {
                files.add(
                    DownloadResult(
                        it.identifier,
                        links.get(it.identifier.toInt()).callback,
                        it.file
                    )
                )
                Log.i(TAG, "onCompleted")

            }

            override fun onDeleted(groupId: Int, download: Download, fetchGroup: FetchGroup) {
                Log.i(TAG, "onDeleted")

            }

            override fun onDeleted(download: Download) {
                Log.i(TAG, "onDeleted")

            }

            override fun onDownloadBlockUpdated(groupId: Int, download: Download, downloadBlock: DownloadBlock, totalBlocks: Int, fetchGroup: FetchGroup) {
                Log.i(TAG, "onDownloadBlockUpdated")

            }

            override fun onDownloadBlockUpdated(download: Download, downloadBlock: DownloadBlock, totalBlocks: Int) {
                Log.i(TAG, "onDownloadBlockUpdated")

            }

            override fun onError(groupId: Int, download: Download, error: Error, throwable: Throwable?, fetchGroup: FetchGroup) {
                progress = 999
                Log.i(TAG, "onError")

            }

            override fun onError(download: Download, error: Error, throwable: Throwable?) {
                Log.i(TAG, "onError")

            }

            override fun onPaused(groupId: Int, download: Download, fetchGroup: FetchGroup) {
                Log.i(TAG, "onPaused")

            }

            override fun onPaused(download: Download) {
                Log.i(TAG, "onPaused")

            }

            override fun onProgress(groupId: Int, download: Download, etaInMilliSeconds: Long, downloadedBytesPerSecond: Long, fetchGroup: FetchGroup) {
                Log.i(TAG, "onProgress")
                progress = fetchGroup.groupDownloadProgress
                onProgress.invoke(fetchGroup.groupDownloadProgress)
            }

            override fun onProgress(download: Download, etaInMilliSeconds: Long, downloadedBytesPerSecond: Long) {
            }

            override fun onQueued(groupId: Int, download: Download, waitingNetwork: Boolean, fetchGroup: FetchGroup) {
            }

            override fun onQueued(download: Download, waitingOnNetwork: Boolean) {
            }

            override fun onRemoved(groupId: Int, download: Download, fetchGroup: FetchGroup) {
            }

            override fun onRemoved(download: Download) {
            }

            override fun onResumed(groupId: Int, download: Download, fetchGroup: FetchGroup) {
            }

            override fun onResumed(download: Download) {
            }

            override fun onStarted(groupId: Int, download: Download, downloadBlocks: List<DownloadBlock>, totalBlocks: Int, fetchGroup: FetchGroup) {
            }

            override fun onStarted(download: Download, downloadBlocks: List<DownloadBlock>, totalBlocks: Int) {
            }

            override fun onWaitingNetwork(groupId: Int, download: Download, fetchGroup: FetchGroup) {
            }

            override fun onWaitingNetwork(download: Download) {
            }

        }

        fetch.addListener(listener)

        var connectionLostAttempts = 0

        while (progress < 99 && connectionLostAttempts < 3) {
            Thread.sleep(1000)

            if (ConnectionExt().isNetworkConnected(context).not()) {
                connectionLostAttempts ++
            }

        }

        return files
    }

    fun close() {
        fetch.removeListener(this)
        fetch.close()
    }

    override fun onAdded(download: Download) {
        Log.i(TAG, "onAdded")
    }

    override fun onCancelled(download: Download) {
        onError.invoke(context.getString(R.string.dowaload_canceled))
    }

    override fun onCompleted(download: Download) {
        Log.i(TAG, "onCompleted: file " + download.file)
        Log.i(TAG, "onCompleted: uri " + download.url)
        onSuccess.invoke(download.file)
    }

    override fun onDeleted(download: Download) {
        onError.invoke(context.getString(R.string.dowaload_deleted))
    }

    override fun onDownloadBlockUpdated(download: Download, downloadBlock: DownloadBlock, totalBlocks: Int) {}

    override fun onError(download: Download, error: Error, throwable: Throwable?) {
        Log.e(TAG, "onError: ", throwable)
        Log.e(TAG, "onError: " + error.name ?: "")
        onError.invoke(context.getString(R.string.dowaload_error))
    }

    override fun onPaused(download: Download) {
        Log.i(TAG, "onPaused")

    }

    override fun onProgress(download: Download, etaInMilliSeconds: Long, downloadedBytesPerSecond: Long) {
        Log.i(TAG, "onProgress " + download.progress)

        onLoading.invoke(download.progress)
    }

    override fun onQueued(download: Download, waitingOnNetwork: Boolean) {
        Log.i(TAG, "onQueued")
        onLoading.invoke(- 1)

    }

    override fun onRemoved(download: Download) {
        Log.i(TAG, "onRemoved")

    }

    override fun onResumed(download: Download) {
        Log.i(TAG, "onResumed")

    }

    override fun onStarted(download: Download, downloadBlocks: List<DownloadBlock>, totalBlocks: Int) {
        Log.i(TAG, "onStarted")

    }

    override fun onWaitingNetwork(download: Download) {
        Log.i(TAG, "onWaitingNetwork")

        onLoading.invoke(- 1)
    }
}
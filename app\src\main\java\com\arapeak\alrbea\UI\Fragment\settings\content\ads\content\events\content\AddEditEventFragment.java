package com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.events.content;

import android.annotation.SuppressLint;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;

import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.Repositories;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.Calendar;

public class AddEditEventFragment extends AlrabeeaTimesFragment {
    private final boolean isNew;
    Event event;

    SwitchCompat sw;
    EditText text;
    Button sDate, sTime, eDate, eTime, save, cancel, delete;

    public AddEditEventFragment(Event event) {
        this.event = event;
        isNew = false;
    }

    public AddEditEventFragment() {
        event = new Event();
        isNew = true;
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View parent = inflater.inflate(R.layout.fragment_add_edit_event, container, false);
        initView(parent);
        setAction();
        reloadBtns();
        return parent;
    }

    private void setAction() {
        sw.setChecked(event.enabled);
        sw.setOnClickListener(i -> event.enabled = sw.isChecked());
        text.setText(event.text);
        sDate.setOnClickListener((i) -> pickDate(0));
        eDate.setOnClickListener((i) -> pickDate(1));
        sTime.setOnClickListener((i) -> pickTime(0));
        eTime.setOnClickListener((i) -> pickTime(1));

        save.setOnClickListener(this::save);
        cancel.setOnClickListener(this::cancel);
        delete.setOnClickListener(this::delete);
    }

    private void delete(View view) {
        Repositories.getEventDb().delete(getCurrentEvent());
        onBackPressed();
    }

    private void cancel(View view) {
        onBackPressed();
    }

    private Event getCurrentEvent() {
        event.text = text.getText().toString();
        return event;
    }

    private void save(View view) {
        if (isNew)
            Repositories.getEventDb().add(getCurrentEvent());
        else
            Repositories.getEventDb().update(getCurrentEvent());
        onBackPressed();
    }

    void pickDate(int index) {
        UmmalquraCalendar now = index == 0 ? event.sCal : event.eCal;
        Utils.initConfirmDialogDate(getAppCompatActivity(), now, cal -> {
            now.setTimeInMillis(cal.getTimeInMillis());
            reloadBtns();
        });
//        FragmentManager mngr = getAppCompatActivity().getSupportFragmentManager();
        /*HijriDatePickerDialog dialog = HijriDatePickerDialog.newInstance(
                (view, year, monthOfYear, dayOfMonth) -> {
                    now.set(year,monthOfYear,dayOfMonth);
                    reloadBtns();
//                    if(index == )
                },
                now.get(UmmalquraCalendar.YEAR),
                now.get(UmmalquraCalendar.MONTH),
                now.get(UmmalquraCalendar.DAY_OF_MONTH));
        dialog.show(mngr.beginTransaction(), "HijriDatePickerDialog");*/
    }

    void pickTime(int index) {
        UmmalquraCalendar now = index == 0 ? event.sCal : event.eCal;
        new TimePickerDialog(
                requireContext(),
                (view, hourOfDay, minute) -> {
                    now.set(Calendar.HOUR_OF_DAY, hourOfDay);
                    now.set(Calendar.MINUTE, minute);
                    reloadBtns();
                },
                now.get(Calendar.HOUR_OF_DAY),
                now.get(Calendar.MINUTE),
                false
        ).show();
    }

    @SuppressLint("SetTextI18n")
    private void reloadBtns() {
        sDate.setText(event.sCal.getDisplayName(UmmalquraCalendar.MONTH, Calendar.LONG, HawkSettings.getLocale())
                + " " + Utils.formatUmmalquraCalendar("dd", event.sCal.getTime()));
        sTime.setText(Utils.formatUmmalquraCalendar("hh:mm a", event.sCal.getTime()));

        eDate.setText(event.eCal.getDisplayName(UmmalquraCalendar.MONTH, Calendar.LONG, HawkSettings.getLocale())
                + " " + Utils.formatUmmalquraCalendar("dd", event.eCal.getTime()));
        eTime.setText(Utils.formatUmmalquraCalendar("hh:mm a", event.eCal.getTime()));

        /*
        dateFormat.setCalendar(event.sCal);
        dateFormat.applyPattern("dd");
        sDate.setText(event.sCal.getDisplayName(UmmalquraCalendar.MONTH, Calendar.LONG,Utils.getLocale())+" "+dateFormat.format(event.sCal.getTime()));
        dateFormat.applyPattern("hh:mm a");
        sTime.setText(dateFormat.format(event.sCal.getTime()));

        dateFormat.setCalendar(event.eCal);
        dateFormat.applyPattern("dd");
        eDate.setText(event.eCal.getDisplayName(UmmalquraCalendar.MONTH, Calendar.LONG,Utils.getLocale())+" "+dateFormat.format(event.eCal.getTime()));
        dateFormat.applyPattern("hh:mm a");
        eTime.setText(dateFormat.format(event.eCal.getTime()));*/


        delete.setVisibility(isNew ? View.GONE : View.VISIBLE);
        sw.setVisibility(isNew ? View.GONE : View.VISIBLE);
    }

    private void initView(View parent) {
        sw = parent.findViewById(R.id.enableDisablePhoto_SwitchCompat_AddPhotoFragment);
        text = parent.findViewById(R.id.message_EditText_CreateMovingMessageFragment);
        sDate = parent.findViewById(R.id.btn_sDate);
        eDate = parent.findViewById(R.id.btn_eDate);
        sTime = parent.findViewById(R.id.btn_sTime);
        eTime = parent.findViewById(R.id.btn_eTime);
        save = parent.findViewById(R.id.btn_add);
        cancel = parent.findViewById(R.id.btn_cancel);
        delete = parent.findViewById(R.id.btn_delete);
    }
}

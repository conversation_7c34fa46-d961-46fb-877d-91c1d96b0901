//package com.arapeak.alrbea.UI.Activity.HomeUi;
//import android.app.Activity;
//import android.graphics.PorterDuff;
//import android.util.Log;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import androidx.constraintlayout.widget.ConstraintLayout;
//import androidx.core.content.ContextCompat;
//import androidx.core.content.res.ResourcesCompat;
//
//import com.arapeak.alrbea.APIs.ConstantsOfApp;
//import com.arapeak.alrbea.Enum.PrayerType;
//import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
//import com.arapeak.alrbea.R;
//import com.arapeak.alrbea.UI.Activity.HomeUi.MainLayoutManager;
//import com.arapeak.alrbea.Utils;
//import com.arapeak.alrbea.hawk.HawkSettings;
//import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
//import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;
//
//import java.util.Calendar;
//import java.util.GregorianCalendar;
//import java.util.Locale;
//
//import static com.arapeak.alrbea.APIs.ConstantsOfApp.EN_LANGUAGE;
//import static com.arapeak.alrbea.UI.Activity.mainActivityExt.ThemeRes.getDayImageRes;
//import static com.arapeak.alrbea.UI.Activity.mainActivityExt.ThemeRes.getGregorianMonthImageRes;
//import static com.arapeak.alrbea.UI.Activity.mainActivityExt.ThemeRes.getHijriMonthImageRes;
//
//// This class handles all UI updates related to prayer times and dates
//public class PrayerTimeUIManager {
//
//    private Activity activity;
//    private MainLayoutManager mainLayoutManager; // To access theme settings, colors etc.
//
//    // UI elements directly managed by this class
//    private TextView timeNowTextView, timeNowTypeTextView, dateNowTextView, dateNowTextView2, dateHTextView,
//            datehm, datehy, datey, datem, dayText, athkarTimeTextView,
//            fajrTextView, azanTextView, prayerTextView, ikamaTextView,
//            fajrTimeTextView, fajrATimeTextView, remainingFajrTextView,
//            sunriseTextView, sunriseTimeTextView, sunriseATimeTextView, remainingSunriseTextView,
//            dhuhrTextView, dhuhrTextViewe, sunriseTextViewe, dhuhrTimeTextView, dhuhrATimeTextView, remainingDhuhrTextView,
//            asrTextView, asrTimeTextView, asrATimeTextView, remainingAsrTextView,
//            maghribTextView, maghribTimeTextView, maghribATimeTextView, remainingMaghribTextView,
//            ishaTextView, ishaTimeTextView, ishaATimeTextView, remainingIshaTextView,
//            remainingPrayerTextView, movingMessageTextView,
//            fajrTimeTypeTextView, sunriseTimeTypeTextView, dhuhrTimeTypeTextView, asrTimeTypeTextView, maghribTimeTypeTextView, ishaTimeTypeTextView,
//            fajrATimeTypeTextView, sunriseATimeTypeTextView, dhuhrATimeTypeTextView, asrATimeTypeTextView, maghribATimeTypeTextView, ishaATimeTypeTextView;
//
//    private ViewGroup fajrTimeLinearLayout, sunriseTimeLinearLayout, dhuhrTimeLinearLayout, asrTimeLinearLayout,
//            maghribTimeLinearLayout, ishaTimeLinearLayout, fajrATimeLinearLayout, sunriseATimeLinearLayout,
//            dhuhrATimeLinearLayout, asrATimeLinearLayout, maghribATimeLinearLayout, ishaATimeLinearLayout,
//            contentFajrLayout, contentSunriseLayout, contentDhuhrLayout, contentAsrLayout, contentMaghribLayout, contentIshaLayout;
//
//    private ImageView imageSunriseView, imagefajrView, imagedhuhrView, imageasrView, imagemaghribView, imageishaView,
//            gregorian_month_image, hijri_month_image, dayimage;
//
//    private TextView tvIkamaDelayFajr, tvIkamaDelayDhur, tvIkamaDelayAsr, tvIkamaDelayMaghreb, tvIkamaDelayIsha;
//
//
//    // Data for date/time calculations (should come from BackgroundTaskManager)
//    private UmmalquraCalendar hijriCalendar;
//    private GregorianCalendar gregorianCalendar;
//    private boolean isJomaa;
//    private boolean isHijri = true; // For alternating display
//
//    private String gYear, gMonthAr, gMonthEn, gMonthNum, gDayNum, gDayNameAr, gDayNameEn;
//    private String hYear, hMonthAr, hMonthEn, hDayNum;
//    private boolean _dateDarkGreenLandscape_isHijri = false;
//    private boolean _lastRefreshedIsDayName = false;
//    private int _blueCurrentDate = 1;
//    private int _athkarCurrentDate = 1; // for getAthkarDate
//
//    public PrayerTimeUIManager(Activity activity, MainLayoutManager mainLayoutManager) {
//        this.activity = activity;
//        this.mainLayoutManager = mainLayoutManager;
//        initViews();
//    }
//
//    private void initViews() {
//        ViewGroup container = mainLayoutManager.getContainerLinearLayout();
//
//        timeNowTextView = container.findViewById(R.id.timeNow_TextView_MainActivity);
//        timeNowTypeTextView = container.findViewById(R.id.timeNowType_TextView_MainActivity);
//        dateNowTextView = container.findViewById(R.id.dateNow_TextView_MainActivity);
//        dateNowTextView2 = container.findViewById(R.id.dateNow2_TextView_MainActivity);
//        dateHTextView = container.findViewById(R.id.dateNow1_TextView_MainActivity);
//        datehm = container.findViewById(R.id.datehm);
//        datehy = container.findViewById(R.id.datehy);
//        datey = container.findViewById(R.id.datey);
//        datem = container.findViewById(R.id.datem);
//        dayText = container.findViewById(R.id.day_text);
//        athkarTimeTextView = container.findViewById(R.id.tv_athkar_time);
//        movingMessageTextView = container.findViewById(R.id.movingMessage_TextView_MainActivity);
//        remainingPrayerTextView = container.findViewById(R.id.remainingPrayer_TextView_MainActivity);
//
//        // Prayer row views
//        fajrTextView = container.findViewById(R.id.fajr_TextView_MainActivity);
//        azanTextView = container.findViewById(R.id.azan_TextView_MainActivity);
//        prayerTextView = container.findViewById(R.id.prayer_TextView_MainActivity);
//        ikamaTextView = container.findViewById(R.id.ikama_TextView_MainActivity);
//
//        fajrTimeTextView = container.findViewById(R.id.fajrTime_TextView_MainActivity);
//        fajrATimeTextView = container.findViewById(R.id.fajrATime_TextView_MainActivity);
//        remainingFajrTextView = container.findViewById(R.id.remainingFajr_TextView_MainActivity);
//        fajrTimeTypeTextView = container.findViewById(R.id.fajrTimeType_TextView_MainActivity);
//        fajrATimeTypeTextView = container.findViewById(R.id.fajrATimeType_TextView_MainActivity);
//        fajrTimeLinearLayout = container.findViewById(R.id.fajrTime_LinearLayout_MainActivity);
//        fajrATimeLinearLayout = container.findViewById(R.id.fajrATime_LinearLayout_MainActivity);
//        contentFajrLayout = container.findViewById(R.id.contentFajr_LinearLayout_MainActivity);
//        imagefajrView = container.findViewById(R.id.imageFajr_View_MainActivity);
//
//        // ... repeat for Sunrise, Dhuhr, Asr, Maghrib, Isha (all views)
//        sunriseTextView = container.findViewById(R.id.sunrise_TextView_MainActivity);
//        sunriseTimeTextView = container.findViewById(R.id.sunriseTime_TextView_MainActivity);
//        sunriseATimeTextView = container.findViewById(R.id.sunriseATime_TextView_MainActivity);
//        remainingSunriseTextView = container.findViewById(R.id.remainingSunrise_TextView_MainActivity);
//        sunriseTimeTypeTextView = container.findViewById(R.id.sunriseTimeType_TextView_MainActivity);
//        sunriseATimeTypeTextView = container.findViewById(R.id.sunriseATimeType_TextView_MainActivity);
//        sunriseTimeLinearLayout = container.findViewById(R.id.sunriseTime_LinearLayout_MainActivity);
//        sunriseATimeLinearLayout = container.findViewById(R.id.sunriseATime_LinearLayout_MainActivity);
//        contentSunriseLayout = container.findViewById(R.id.contentSunrise_LinearLayout_MainActivity);
//        imageSunriseView = container.findViewById(R.id.imageSunrise_View_MainActivity);
//        sunriseTextViewe = container.findViewById(R.id.sunrise_TextView_MainActivityE); // Added for themes with EN label
//
//        dhuhrTextView = container.findViewById(R.id.dhuhr_TextView_MainActivity);
//        dhuhrTimeTextView = container.findViewById(R.id.dhuhrTime_TextView_MainActivity);
//        dhuhrATimeTextView = container.findViewById(R.id.dhuhrATime_TextView_MainActivity);
//        remainingDhuhrTextView = container.findViewById(R.id.remainingDhuhr_TextView_MainActivity);
//        dhuhrTimeTypeTextView = container.findViewById(R.id.dhuhrTimeType_TextView_MainActivity);
//        dhuhrATimeTypeTextView = container.findViewById(R.id.dhuhrATimeType_TextView_MainActivity);
//        dhuhrTimeLinearLayout = container.findViewById(R.id.dhuhrTime_LinearLayout_MainActivity);
//        dhuhrATimeLinearLayout = container.findViewById(R.id.dhuhrATime_LinearLayout_MainActivity);
//        contentDhuhrLayout = container.findViewById(R.id.contentDhuhr_LinearLayout_MainActivity);
//        imagedhuhrView = container.findViewById(R.id.imageDhuhr_View_MainActivity);
//        dhuhrTextViewe = container.findViewById(R.id.dhuhr_TextView_MainActivityE); // Added for themes with EN label
//
//        asrTextView = container.findViewById(R.id.asr_TextView_MainActivity);
//        asrTimeTextView = container.findViewById(R.id.asrTime_TextView_MainActivity);
//        asrATimeTextView = container.findViewById(R.id.asrATime_TextView_MainActivity);
//        remainingAsrTextView = container.findViewById(R.id.remainingAsr_TextView_MainActivity);
//        asrTimeTypeTextView = container.findViewById(R.id.asrTimeType_TextView_MainActivity);
//        asrATimeTypeTextView = container.findViewById(R.id.asrATimeType_TextView_MainActivity);
//        asrTimeLinearLayout = container.findViewById(R.id.asrTime_LinearLayout_MainActivity);
//        asrATimeLinearLayout = container.findViewById(R.id.asrATime_LinearLayout_MainActivity);
//        contentAsrLayout = container.findViewById(R.id.contentAsr_LinearLayout_MainActivity);
//        imageasrView = container.findViewById(R.id.imageAsr_View_MainActivity);
//
//        maghribTextView = container.findViewById(R.id.maghrib_TextView_MainActivity);
//        maghribTimeTextView = container.findViewById(R.id.maghribTime_TextView_MainActivity);
//        maghribATimeTextView = container.findViewById(R.id.maghribATime_TextView_MainActivity);
//        remainingMaghribTextView = container.findViewById(R.id.remainingMaghrib_TextView_MainActivity);
//        maghribTimeTypeTextView = container.findViewById(R.id.maghribTimeType_TextView_MainActivity);
//        maghribATimeTypeTextView = container.findViewById(R.id.maghribATimeType_TextView_MainActivity);
//        maghribTimeLinearLayout = container.findViewById(R.id.maghribTime_LinearLayout_MainActivity);
//        maghribATimeLinearLayout = container.findViewById(R.id.maghribATime_LinearLayout_MainActivity);
//        contentMaghribLayout = container.findViewById(R.id.contentMaghrib_LinearLayout_MainActivity);
//        imagemaghribView = container.findViewById(R.id.imageMaghrib_View_MainActivity);
//
//        ishaTextView = container.findViewById(R.id.isha_TextView_MainActivity);
//        ishaTimeTextView = container.findViewById(R.id.ishaTime_TextView_MainActivity);
//        ishaATimeTextView = container.findViewById(R.id.ishaATime_TextView_MainActivity);
//        remainingIshaTextView = container.findViewById(R.id.remainingIsha_TextView_MainActivity);
//        ishaTimeTypeTextView = container.findViewById(R.id.ishaTimeType_TextView_MainActivity);
//        ishaATimeTypeTextView = container.findViewById(R.id.ishaATimeType_TextView_MainActivity);
//        ishaTimeLinearLayout = container.findViewById(R.id.ishaTime_LinearLayout_MainActivity);
//        ishaATimeLinearLayout = container.findViewById(R.id.ishaATime_LinearLayout_MainActivity);
//        contentIshaLayout = container.findViewById(R.id.contentIsha_LinearLayout_MainActivity);
//        imageishaView = container.findViewById(R.id.imageIsha_View_MainActivity);
//
//        gregorian_month_image = container.findViewById(R.id.gregorian_month_image);
//        hijri_month_image = container.findViewById(R.id.hijri_month_image);
//        dayimage = container.findViewById(R.id.dayimage);
//
//        tvIkamaDelayFajr = container.findViewById(R.id.tv_prayer_ikama_time_fajr);
//        tvIkamaDelayDhur = container.findViewById(R.id.tv_prayer_ikama_time_dhur);
//        tvIkamaDelayAsr = container.findViewById(R.id.tv_prayer_ikama_time_asr);
//        tvIkamaDelayMaghreb = container.findViewById(R.id.tv_prayer_ikama_time_maghrib);
//        tvIkamaDelayIsha = container.findViewById(R.id.tv_prayer_ikama_time_isha);
//
//
//        // Apply scaling for English language if needed (moved from MainActivity init)
//        if (HawkSettings.getLocaleLanguage().equals(EN_LANGUAGE) && mainLayoutManager.getUiTheme() != com.arapeak.alrbea.Enum.UITheme.NEW_GREEN) {
//            float percent = 0.70f;
//            scaleTextViewSize(percent, azanTextView, prayerTextView, ikamaTextView,
//                    fajrTextView, sunriseTextView, dhuhrTextView, asrTextView, maghribTextView, ishaTextView,
//                    fajrTimeTextView, sunriseTimeTextView, dhuhrTimeTextView, asrTimeTextView, maghribTimeTextView, ishaTimeTextView,
//                    fajrATimeTextView, sunriseATimeTextView, dhuhrATimeTextView, asrATimeTextView, maghribATimeTextView, ishaATimeTextView,
//                    fajrTimeTypeTextView, sunriseTimeTypeTextView, dhuhrTimeTypeTextView, asrTimeTypeTextView, maghribTimeTypeTextView, ishaTimeTypeTextView,
//                    fajrATimeTypeTextView, sunriseATimeTypeTextView, dhuhrATimeTypeTextView, asrATimeTypeTextView, maghribATimeTypeTextView, ishaATimeTypeTextView);
//        }
//        initIkamaTimeViewsDisplay();
//    }
//
//    private void scaleTextViewSize(float percent, TextView... textViews) {
//        if (textViews == null) return;
//        for (TextView tv : textViews) {
//            if (tv != null) {
//                tv.setTextSize(tv.getTextSize() * percent);
//            }
//        }
//    }
//
//    private void initIkamaTimeViewsDisplay() {
//        // This method was in MainActivity onCreate, now it's initialized here
//        // The display logic needs to be updated during runtime if settings change.
//        if (!HawkSettings.getEnableShowIkamaDelay()) {
//            Utils.setVisibility(tvIkamaDelayFajr, View.GONE);
//            Utils.setVisibility(tvIkamaDelayDhur, View.GONE);
//            Utils.setVisibility(tvIkamaDelayAsr, View.GONE);
//            Utils.setVisibility(tvIkamaDelayMaghreb, View.GONE);
//            Utils.setVisibility(tvIkamaDelayIsha, View.GONE);
//            return;
//        }
//
//        boolean isRamadanSettings = false; // Check for Ramadan if this is a feature
//
//        int timeFajrDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT;
//        int timeDhurDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT;
//        int timeAsrDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT;
//        int timeMaghrebDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT;
//        int timeIshaDefault = isRamadanSettings ? ConstantsOfApp.RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT : ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT;
//
//        int fajrTime = HawkSettings.getAdhanIkamaDelay(ConstantsOfApp.FAJR_KEY, timeFajrDefault);
//        int dhurTime = HawkSettings.getAdhanIkamaDelay(ConstantsOfApp.DHUHR_KEY, timeDhurDefault);
//        int asrTime = HawkSettings.getAdhanIkamaDelay(ConstantsOfApp.ASR_KEY, timeAsrDefault);
//        int maghrebTime = HawkSettings.getAdhanIkamaDelay(ConstantsOfApp.MAGHRIB_KEY, timeMaghrebDefault);
//        int ishaTime = HawkSettings.getAdhanIkamaDelay(ConstantsOfApp.ISHA_KEY, timeIshaDefault);
//
//        String minute = activity.getString(R.string.minute_abbrev);
//
//        Utils.setText(tvIkamaDelayFajr, fajrTime + minute);
//        Utils.setText(tvIkamaDelayDhur, dhurTime + minute);
//        Utils.setText(tvIkamaDelayAsr, asrTime + minute);
//        Utils.setText(tvIkamaDelayMaghreb, maghrebTime + minute);
//        Utils.setText(tvIkamaDelayIsha, ishaTime + minute);
//
//        Utils.setVisibility(tvIkamaDelayFajr, View.VISIBLE);
//        Utils.setVisibility(tvIkamaDelayDhur, View.VISIBLE);
//        Utils.setVisibility(tvIkamaDelayAsr, View.VISIBLE);
//        Utils.setVisibility(tvIkamaDelayMaghreb, View.VISIBLE);
//        Utils.setVisibility(tvIkamaDelayIsha, View.VISIBLE);
//    }
//
//    public void updateLiveTime(String time, String timeType, String athkarDate) {
//        Utils.setText(timeNowTextView, time);
//        Utils.setText(timeNowTypeTextView, timeType);
//        Utils.setText(athkarTimeTextView, athkarDate);
//    }
//
//
//    public void refreshDate(UmmalquraCalendar hijriCalendar, GregorianCalendar gregorianCalendar, boolean isJomaa, String gYear, String gMonthAr, String gMonthEn, String gMonthNum, String gDayNum, String gDayNameAr, String gDayNameEn, String hYear, String hMonthAr, String hMonthEn, String hDayNum) {
//        this.hijriCalendar = hijriCalendar;
//        this.gregorianCalendar = gregorianCalendar;
//        this.isJomaa = isJomaa;
//        this.gYear = gYear; this.gMonthAr = gMonthAr; this.gMonthEn = gMonthEn; this.gMonthNum = gMonthNum; this.gDayNum = gDayNum; this.gDayNameAr = gDayNameAr; this.gDayNameEn = gDayNameEn;
//        this.hYear = hYear; this.hMonthAr = hMonthAr; this.hMonthEn = hMonthEn; this.hDayNum = hDayNum;
//
//        if (dhuhrTextViewe != null) {
//            Utils.setText(dhuhrTextViewe, isJomaa ? "Jomaa" : "Dhuhr");
//            Utils.setText(dhuhrTextView, isJomaa ? "الجمعة" : "الظهر");
//        } else {
//            Utils.setText(dhuhrTextView, Utils.getString(activity, isJomaa ? R.string.jomaa : R.string.dhuhr));
//        }
//
//        // Apply background image if URL is set (moved from MainActivity)
//        final float progress = HawkSettings.getBackgroundImageAlpha();
//        if (HawkSettings.getBackgroundImageUrl() != null && HawkSettings.isBackgroundImageEnabled()) {
//            Utils.setImage(mainLayoutManager.containerImageView, "file://" + HawkSettings.getBackgroundImageUrl());
//            mainLayoutManager.containerImageView.setAlpha(progress);
//            mainLayoutManager.containerImageView.setColorFilter(HawkSettings.getBackgroundImageColor(), PorterDuff.Mode.MULTIPLY);
//            Utils.setVisibility(mainLayoutManager.containerImageView, View.VISIBLE);
//        }
//
//        // Handle date display based on theme
//        switch (mainLayoutManager.getUiTheme()) {
//            case BLUE: case DARK_GREEN: case DARK_GRAY: case BROWN:
//                blueTime();
//                break;
//            case GREEN:
//                if (mainLayoutManager.isLandscape()) dateDarkGreenLandscape();
//                else refreshFullDateAndDayName();
//                break;
//            case RED:
//                if (mainLayoutManager.isLandscape()) dateRedLandscape();
//                else dateRedPortrait();
//                break;
//            case NEW_GREEN:
//                if (mainLayoutManager.isLandscape()) dateNewGreenLandscape();
//                else dateNewGreenPortrait();
//                break;
//            case WHITE: case WHITE_NEW:
//                setDayMonthImage();
//                dateRedPortrait(); // Using RedPortrait style for date display
//                break;
//            case BLUE_NEW: case BROWN_NEW:
//                refreshFullDateAndDayName();
//                break;
//            case BLUE_LET:
//                dateBlueLett();
//                break;
//            case BROWN_NEW_3:
//                setDayMonthImage();
//                Utils.setText(dayText, gDayNameEn);
//                Utils.setText(datey, gYear);
//                Utils.setText(datem, gMonthEn);
//                Utils.setText(dateHTextView, gDayNum);
//                Utils.setText(datehy, hYear);
//                Utils.setText(datehm, hMonthEn);
//                Utils.setText(dateNowTextView, hDayNum);
//                break;
//            case CUSTOM_1: case CUSTOM_FIREBASE:
//                if (mainLayoutManager.isLandscape()) {
//                    Utils.setImageResource(dayimage, getDayImageRes(gregorianCalendar));
//                    Utils.setText(dayText, gDayNameEn);
//                    // The alternating logic for Custom themes in landscape is complex
//                    // It needs `executeEvery` from BackgroundTaskManager to manage the Runnable that updates the text
//                    // This method should probably just set the initial state, and `executeEvery` handles the toggling
//                    // For now, setting initial state:
//                    Utils.setText(dateHTextView, isHijri ? hDayNum : gDayNum);
//                    Utils.setText(datem, isHijri ? hMonthEn : gMonthEn);
//                    Utils.setText(datey, isHijri ? hYear + Utils.getString(activity, R.string.code_hegira) : gYear);
//                    Utils.setImageResource(gregorian_month_image, isHijri ? getHijriMonthImageRes(hijriCalendar) : getGregorianMonthImageRes(gregorianCalendar));
//                    isHijri = !isHijri; // Prepare for next toggle
//                } else {
//                    setDayMonthImage();
//                    Utils.setText(dayText, gDayNameEn);
//                    Utils.setText(datey, gYear);
//                    Utils.setText(datem, gMonthEn);
//                    Utils.setText(dateHTextView, gDayNum);
//                    Utils.setText(datehy, hYear + Utils.getString(activity, R.string.code_hegira));
//                    Utils.setText(datehm, hMonthEn);
//                    Utils.setText(dateNowTextView, hDayNum);
//                }
//                break;
//        }
//    }
//
//    public void setDayMonthImage() {
//        Utils.setImageResource(hijri_month_image, getHijriMonthImageRes(hijriCalendar));
//        Utils.setImageResource(gregorian_month_image, getGregorianMonthImageRes(gregorianCalendar));
//        Utils.setImageResource(dayimage, getDayImageRes(gregorianCalendar));
//    }
//
//    private void blueTime() {
//        boolean isAr = HawkSettings.isArabic();
//        String date;
//        switch (_blueCurrentDate) {
//            case 1: date = hDayNum + " " + (isAr ? hMonthAr : hMonthEn) + " " + hYear; break;
//            case 2: date = gDayNum + " " + (isAr ? gMonthAr : gMonthEn) + " " + gYear; break;
//            default: _blueCurrentDate = 0; date = isAr ? gDayNameAr : gDayNameEn; break;
//        }
//        _blueCurrentDate++;
//        Utils.setText(dateNowTextView, date);
//    }
//
//    private void dateDarkGreenLandscape() {
//        if (_dateDarkGreenLandscape_isHijri) {
//            Utils.setText(dateNowTextView, hDayNum);
//            Utils.setText(datehm, HawkSettings.getLocaleLanguage().equals(AR_LANGUAGE) ? hMonthAr : hMonthEn);
//            Utils.setText(datehy, hYear + Utils.getString(activity, R.string.code_hegira));
//        } else {
//            Utils.setText(dateNowTextView, gDayNum);
//            Utils.setText(datehm, HawkSettings.getLocaleLanguage().equals(AR_LANGUAGE) ? gMonthAr : gMonthEn);
//            Utils.setText(datehy, gYear);
//        }
//        _dateDarkGreenLandscape_isHijri = !_dateDarkGreenLandscape_isHijri;
//    }
//
//    private void dateRedPortrait() {
//        String lan = HawkSettings.getLocaleLanguage();
//        if (lan.equals(EN_LANGUAGE)) {
//            Utils.setText(datehm, hMonthEn);
//            Utils.setText(datem, gMonthEn);
//        } else {
//            Utils.setText(datehm, hMonthAr);
//            Utils.setText(datem, gMonthAr);
//        }
//        Utils.setText(datehy, hYear + Utils.getString(activity, R.string.code_hegira));
//        Utils.setText(dateNowTextView, hDayNum);
//        Utils.setText(datey, gYear);
//        Utils.setText(dateHTextView, gDayNum);
//    }
//
//    private void dateNewGreenPortrait() {
//        String lan = HawkSettings.getLocaleLanguage();
//        if (lan.equals(EN_LANGUAGE)) {
//            Utils.setText(datehm, hMonthEn + " " + hYear + Utils.getString(activity, R.string.code_hegira));
//            Utils.setText(datem, gMonthEn + " " + gYear + Utils.getString(activity, R.string.code_greg));
//            Utils.setText(datey, gDayNameEn);
//        } else {
//            Utils.setText(datehm, hMonthAr + " " + hYear + Utils.getString(activity, R.string.code_hegira));
//            Utils.setText(datem, gMonthAr + " " + gYear + Utils.getString(activity, R.string.code_greg));
//            Utils.setText(datey, gDayNameAr);
//        }
//        Utils.setText(dateNowTextView, hDayNum);
//        Utils.setText(dateHTextView, gDayNum);
//    }
//
//    private void dateNewGreenLandscape() {
//        String lan = HawkSettings.getLocaleLanguage();
//        if (lan.equals(EN_LANGUAGE)) {
//            Utils.setText(datey, gDayNameEn);
//            Utils.setText(datehm, hMonthEn);
//            Utils.setText(datem, gDayNum + " " + gMonthEn + " " + gYear + Utils.getString(activity, R.string.code_greg));
//        } else {
//            Utils.setText(datey, gDayNameAr);
//            Utils.setText(datehm, hMonthAr);
//            Utils.setText(datem, gDayNum + " " + gMonthAr + " " + gYear + Utils.getString(activity, R.string.code_greg));
//        }
//        Utils.setText(datehy, hYear + Utils.getString(activity, R.string.code_hegira));
//        Utils.setText(dateNowTextView, hDayNum);
//    }
//
//    private void dateRedLandscape() {
//        String date1;
//        String date2;
//        if (HawkSettings.isArabic()) {
//            date1 = hDayNum + " " + hMonthAr + " " + hYear;
//            date2 = gDayNameAr + " | " + gDayNum + " " + gMonthNum + " " + gYear;
//        } else {
//            date1 = hDayNum + " " + hMonthEn + " " + hYear;
//            date2 = gDayNameEn + " | " + gDayNum + " " + gMonthNum + " " + gYear;
//        }
//        Utils.setText(dateNowTextView, date1);
//        Utils.setText(dateNowTextView2, date2);
//    }
//
//    private void refreshFullDateAndDayName() {
//        if (_lastRefreshedIsDayName) {
//            Utils.setVisibility(datem, View.GONE);
//            Utils.setVisibility(datey, View.GONE);
//            Utils.setVisibility(datehm, View.GONE);
//            Utils.setVisibility(datehy, View.GONE);
//            Utils.setVisibility(dateNowTextView, View.GONE);
//            Utils.setText(dateHTextView, HawkSettings.isArabic() ? gDayNameAr : gDayNameEn);
//        } else {
//            Utils.setVisibility(datem, View.VISIBLE);
//            Utils.setVisibility(datey, View.VISIBLE);
//            Utils.setVisibility(datehm, View.VISIBLE);
//            Utils.setVisibility(datehy, View.VISIBLE);
//            Utils.setVisibility(dateNowTextView, View.VISIBLE);
//
//            Utils.setText(datehm, HawkSettings.isArabic() ? hMonthAr : hMonthEn);
//            Utils.setText(datem, HawkSettings.isArabic() ? gMonthAr : gMonthEn);
//            Utils.setText(datehy, hYear + Utils.getString(activity, R.string.code_hegira));
//            Utils.setText(dateNowTextView, hDayNum);
//            Utils.setText(datey, gYear);
//            Utils.setText(dateHTextView, gDayNum);
//        }
//        _lastRefreshedIsDayName = !_lastRefreshedIsDayName;
//    }
//
//    private void dateBlueLett() {
//        if (dateNowTextView != null) {
//            boolean isAr = HawkSettings.isArabic();
//            String dayNumber, monthString, dayName;
//            if (isHijri) {
//                dayNumber = hDayNum;
//                monthString = isAr ? hMonthAr : hMonthEn + " " + hYear;
//                dayName = isAr ? gDayNameAr : gDayNameEn;
//            } else {
//                dayNumber = gDayNum;
//                monthString = isAr ? gMonthAr : gMonthEn + " " + gYear;
//                dayName = isAr ? gDayNameAr : gDayNameEn;
//            }
//            Utils.setText(dateNowTextView, dayNumber);
//            Utils.setText(datem, monthString);
//            Utils.setText(datey, dayName);
//            isHijri = !isHijri; // Toggle for next update
//        }
//    }
//
//
//    // This method needs to be called by BackgroundTaskManager.timeUpdaterRunnable
//    public String getAthkarDate(String ssMiladiDate, String ssHijriDate, String ssDayName) {
//        String date;
//        boolean isAr = HawkSettings.isArabic();
//
//        if (_athkarCurrentDate <= 30) {
//            date = hDayNum + " " + (isAr ? hMonthAr : hMonthEn) + " " + hYear + " " + (isAr ? gDayNameAr : gDayNameEn);
//        } else {
//            date = gDayNum + " " + (isAr ? gMonthAr : gMonthEn) + " " + gYear + " " + (isAr ? gDayNameAr : gDayNameEn);
//            if (_athkarCurrentDate >= 60) _athkarCurrentDate = 0;
//        }
//        // Update values for Screensaver (passed to BackgroundTaskManager)
//        // These need to be passed back to BackgroundTaskManager to set them
//        // BackgroundTaskManager.setSsHijriDate(hDayNum + " " + (isAr ? hMonthAr : hMonthEn) + " " + hYear);
//        // BackgroundTaskManager.setSsDayName((isAr ? gDayNameAr : gDayNameEn));
//        // BackgroundTaskManager.setSsMiladiDate(gDayNum + " " + (isAr ? gMonthAr : gMonthEn) + " " + gYear);
//        _athkarCurrentDate++;
//        return date;
//    }
//
//    public void addAllPrayerTimes(TimingsAlrabeeaTimes timingsAlrabeeaTimes) {
//        if (timingsAlrabeeaTimes == null) {
//            Log.e("PrayerTimeUIManager", "TimingsAlrabeeaTimes is null, cannot add prayers.");
//            return;
//        }
//        addPrayer(PrayerType.Fajr, timingsAlrabeeaTimes);
//        addPrayer(PrayerType.Sunrise, timingsAlrabeeaTimes);
//        addPrayer(PrayerType.Dhuhr, timingsAlrabeeaTimes);
//        addPrayer(PrayerType.Asr, timingsAlrabeeaTimes);
//        addPrayer(PrayerType.Maghrib, timingsAlrabeeaTimes);
//        addPrayer(PrayerType.Isha, timingsAlrabeeaTimes);
//    }
//
//    private void addPrayer(PrayerType type, TimingsAlrabeeaTimes timingsAlrabeeaTimes) {
//        try {
//            String[] time = Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(type.prayerTime.getAzanTime(), "hh:mm a"));
//            String[] ikamaTimeArr = Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(type.prayerTime.getIkamaTime(), "hh:mm a"));
//
//            switch (type) {
//                case Fajr:
//                    Utils.setText(fajrATimeTextView, Utils.getSpannableText(ikamaTimeArr[0]));
//                    Utils.setText(fajrATimeTypeTextView, Utils.getSpannableText(ikamaTimeArr[1]));
//                    Utils.setText(fajrTimeTextView, Utils.getSpannableText(time[0]));
//                    Utils.setText(fajrTimeTypeTextView, Utils.getSpannableText(time[1]));
//                    break;
//                case Sunrise: // Handles Duha display based on settings
//                    if (HawkSettings.getShowDuhaSetting() == 0 || HawkSettings.getShowDuhaSetting() == 1) { // Show Sunrise or Duha
//                        String[] duhaTimeArr = Utils.convertDateTimeToTimeAndTypeTime(Utils.getTimeFormatted(PrayerType.Duha.prayerTime.getAzanTime(), "hh:mm a"));
//                        // If only one set of fields for sunrise/duha, use sunrise for both.
//                        // If separate fields, use sunriseTime and duhaTime for their respective values.
//                        if (sunriseTimeTextView == null || sunriseATimeTextView == null) { // Assuming only one set of fields
//                            Utils.setText(sunriseTimeTextView, Utils.getSpannableText(time[0]));
//                            Utils.setText(sunriseATimeTextView, Utils.getSpannableText(time[0]));
//                            Utils.setText(sunriseTimeTypeTextView, Utils.getSpannableText(time[1]));
//                            Utils.setText(sunriseATimeTypeTextView, Utils.getSpannableText(time[1]));
//                        } else { // Assuming separate fields for Sunrise and Duha
//                            Utils.setText(sunriseTimeTextView, Utils.getSpannableText(time[0]));
//                            Utils.setText(sunriseTimeTypeTextView, Utils.getSpannableText(time[1]));
//                            Utils.setText(sunriseATimeTextView, Utils.getSpannableText(duhaTimeArr[0]));
//                            Utils.setText(sunriseATimeTypeTextView, Utils.getSpannableText(duhaTimeArr[1]));
//                        }
//                    } else if (HawkSettings.getShowDuhaSetting() == 2) { // Hide Duha/Sunrise
//                        // Handled by init in MainLayoutManager to set visibility to GONE
//                    }
//                    break;
//                case Dhuhr:
//                    Utils.setText(dhuhrATimeTextView, Utils.getSpannableText(ikamaTimeArr[0]));
//                    Utils.setText(dhuhrATimeTypeTextView, Utils.getSpannableText(ikamaTimeArr[1]));
//                    Utils.setText(dhuhrTimeTextView, Utils.getSpannableText(time[0]));
//                    Utils.setText(dhuhrTimeTypeTextView, Utils.getSpannableText(time[1]));
//                    break;
//                case Asr:
//                    Utils.setText(asrATimeTextView, Utils.getSpannableText(ikamaTimeArr[0]));
//                    Utils.setText(asrATimeTypeTextView, Utils.getSpannableText(ikamaTimeArr[1]));
//                    Utils.setText(asrTimeTextView, Utils.getSpannableText(time[0]));
//                    Utils.setText(asrTimeTypeTextView, Utils.getSpannableText(time[1]));
//                    break;
//                case Maghrib:
//                    Utils.setText(maghribATimeTextView, Utils.getSpannableText(ikamaTimeArr[0]));
//                    Utils.setText(maghribATimeTypeTextView, Utils.getSpannableText(ikamaTimeArr[1]));
//                    Utils.setText(maghribTimeTextView, Utils.getSpannableText(time[0]));
//                    Utils.setText(maghribTimeTypeTextView, Utils.getSpannableText(time[1]));
//                    break;
//                case Isha:
//                    Utils.setText(ishaATimeTextView, Utils.getSpannableText(ikamaTimeArr[0]));
//                    Utils.setText(ishaATimeTypeTextView, Utils.getSpannableText(ikamaTimeArr[1]));
//                    Utils.setText(ishaTimeTextView, Utils.getSpannableText(time[0]));
//                    Utils.setText(ishaTimeTypeTextView, Utils.getSpannableText(time[1]));
//                    break;
//            }
//            // Set default prayer labels
//            Utils.setText(fajrTextView, Utils.getString(activity, R.string.fajr));
//            // Sunrise/Duha handled by setSunriseOrDuhaNamesTextView
//            Utils.setText(dhuhrTextView, Utils.getString(activity, R.string.dhuhr)); // Will be overridden for Jomaa
//            Utils.setText(asrTextView, Utils.getString(activity, R.string.asr));
//            Utils.setText(maghribTextView, Utils.getString(activity, R.string.maghrib));
//            Utils.setText(ishaTextView, Utils.getString(activity, R.string.isha));
//
//            mainLayoutManager.setLayoutDirection(fajrTimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(sunriseTimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(dhuhrTimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(asrTimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(maghribTimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(ishaTimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(fajrATimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(sunriseATimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(dhuhrATimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(asrATimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(maghribATimeLinearLayout);
//            mainLayoutManager.setLayoutDirection(ishaATimeLinearLayout);
//
//        } catch (Exception e) {
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    public void updateSunriseDuhaNames(boolean showDuha) {
//        try {
//            if (HawkSettings.getShowDuhaSetting() == 0 || showDuha) {
//                if (sunriseTextViewe != null) {
//                    Utils.setText(sunriseTextViewe, "Duha");
//                    Utils.setText(sunriseTextView, "الضحى");
//                } else
//                    Utils.setText(sunriseTextView, Utils.getString(activity, R.string.duha));
//            } else if (HawkSettings.getShowDuhaSetting() == 1 || !showDuha) {
//                if (sunriseTextViewe != null) {
//                    Utils.setText(sunriseTextViewe, "Sunrise");
//                    Utils.setText(sunriseTextView, "الشروق");
//                } else
//                    Utils.setText(sunriseTextView, Utils.getString(activity, R.string.sunrise));
//            }
//            // If showDuhaSetting is 2, views are already GONE/Invisible, no update needed here.
//        } catch (Exception e) {
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//
//    public void updateNextPrayerRemaining(PrayerType type, String timeRemaining) {
//        disableAllRemainingPrayerText(); // First disable all
//        Utils.setText(remainingPrayerTextView, timeRemaining); // Update main remaining text
//        changePrayerRemainingHighlight(type, true, timeRemaining); // Highlight the specific prayer
//        darkenNextPrayerIkamaTime(type);
//    }
//
//    public void disableAllRemainingPrayerText() {
//        changePrayerRemainingHighlight(PrayerType.Fajr, false, "");
//        changePrayerRemainingHighlight(PrayerType.Sunrise, false, "");
//        changePrayerRemainingHighlight(PrayerType.Dhuhr, false, "");
//        changePrayerRemainingHighlight(PrayerType.Asr, false, "");
//        changePrayerRemainingHighlight(PrayerType.Maghrib, false, "");
//        changePrayerRemainingHighlight(PrayerType.Isha, false, "");
//    }
//
//    private void changePrayerRemainingHighlight(PrayerType type, boolean isNext, String timeRemaining) {
//        switch (type) {
//            case Fajr: Utils.setText(remainingFajrTextView, timeRemaining); Utils.setVisibility(remainingFajrTextView, isNext ? View.VISIBLE : View.GONE);
//                setColorToTextViewPray(fajrTextView, fajrTimeTextView, fajrTimeTypeTextView, isNext);
//                setColorToTextViewPray(fajrTextView, fajrATimeTextView, fajrATimeTypeTextView, isNext);
//                setToImageViewPray(imagefajrView, isNext);
//                if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) setBackgroundToTextViewPray(type, isNext);
//                else setBackgroundToTextViewPray(contentFajrLayout, isNext); break;
//            case Sunrise: case PrayerType.Duha:
//                if (HawkSettings.getShowDuhaSetting() == 2) break; // Skip if hidden
//                Utils.setText(remainingSunriseTextView, timeRemaining); Utils.setVisibility(remainingSunriseTextView, isNext ? View.VISIBLE : View.GONE);
//                if (mainLayoutManager.getUiTheme() != com.arapeak.alrbea.Enum.UITheme.NEW_GREEN) setColorToTextViewPray(sunriseTextView, sunriseATimeTextView, sunriseATimeTypeTextView, isNext);
//                setToImageViewPray(imageSunriseView, isNext);
//                if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) setBackgroundToTextViewPray(type, isNext);
//                else setBackgroundToTextViewPray(contentSunriseLayout, isNext); break;
//            case Dhuhr: Utils.setText(remainingDhuhrTextView, timeRemaining); Utils.setVisibility(remainingDhuhrTextView, isNext ? View.VISIBLE : View.GONE);
//                setColorToTextViewPray(dhuhrTextView, dhuhrTimeTextView, dhuhrTimeTypeTextView, isNext);
//                setColorToTextViewPray(dhuhrTextView, dhuhrATimeTextView, dhuhrATimeTypeTextView, isNext);
//                setToImageViewPray(imagedhuhrView, isNext);
//                if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) setBackgroundToTextViewPray(type, isNext);
//                else setBackgroundToTextViewPray(contentDhuhrLayout, isNext); break;
//            case Asr: Utils.setText(remainingAsrTextView, timeRemaining); Utils.setVisibility(remainingAsrTextView, isNext ? View.VISIBLE : View.GONE);
//                setColorToTextViewPray(asrTextView, asrTimeTextView, asrTimeTypeTextView, isNext);
//                setColorToTextViewPray(asrTextView, asrATimeTextView, asrATimeTypeTextView, isNext);
//                setToImageViewPray(imageasrView, isNext);
//                if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) setBackgroundToTextViewPray(type, isNext);
//                else setBackgroundToTextViewPray(contentAsrLayout, isNext); break;
//            case Maghrib: Utils.setText(remainingMaghribTextView, timeRemaining); Utils.setVisibility(remainingMaghribTextView, isNext ? View.VISIBLE : View.GONE);
//                setColorToTextViewPray(maghribTextView, maghribTimeTextView, maghribTimeTypeTextView, isNext);
//                setColorToTextViewPray(maghribTextView, maghribATimeTextView, maghribATimeTypeTextView, isNext);
//                setToImageViewPray(imagemaghribView, isNext);
//                if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) setBackgroundToTextViewPray(type, isNext);
//                else setBackgroundToTextViewPray(contentMaghribLayout, isNext); break;
//            case Isha: Utils.setText(remainingIshaTextView, timeRemaining); Utils.setVisibility(remainingIshaTextView, isNext ? View.VISIBLE : View.GONE);
//                setColorToTextViewPray(ishaTextView, ishaTimeTextView, ishaTimeTypeTextView, isNext);
//                setColorToTextViewPray(ishaTextView, ishaATimeTextView, ishaATimeTypeTextView, isNext);
//                setToImageViewPray(imageishaView, isNext);
//                if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BROWN_NEW_3) setBackgroundToTextViewPray(type, isNext);
//                else setBackgroundToTextViewPray(contentIshaLayout, isNext); break;
//        }
//    }
//
//    private void darkenNextPrayerIkamaTime(PrayerType type) {
//        if (dhuhrTextView == null || tvIkamaDelayFajr == null) return;
//        int activeColor = dhuhrTextView.getCurrentTextColor(); // Get current active text color from any active text view
//        int defaultColor = 0xCF9E9E9E; // Default inactive color
//
//        // Reset all to default first
//        Utils.setTextColor(tvIkamaDelayFajr, defaultColor);
//        Utils.setTextColor(tvIkamaDelayDhur, defaultColor);
//        Utils.setTextColor(tvIkamaDelayAsr, defaultColor);
//        Utils.setTextColor(tvIkamaDelayMaghreb, defaultColor);
//        Utils.setTextColor(tvIkamaDelayIsha, defaultColor);
//
//        // Then set the active one
//        try {
//            switch (type) {
//                case Fajr: Utils.setTextColor(tvIkamaDelayFajr, activeColor); break;
//                case Dhuhr: Utils.setTextColor(tvIkamaDelayDhur, activeColor); break;
//                case Asr: Utils.setTextColor(tvIkamaDelayAsr, activeColor); break;
//                case Maghrib: Utils.setTextColor(tvIkamaDelayMaghreb, activeColor); break;
//                case Isha: Utils.setTextColor(tvIkamaDelayIsha, activeColor); break;
//                default: break; // Do nothing for other prayer types
//            }
//        } catch (Exception e) {
//            Log.e("PrayerTimeUIManager", "Error darkening Ikama time: " + e.getMessage());
//            CrashlyticsUtils.INSTANCE.logException(e);
//        }
//    }
//
//    private void setBackgroundToTextViewPray(PrayerType type, boolean isNextPray) {
//        // This is complex due to various themes. Re-uses logic from MainLayoutManager where possible.
//        ViewGroup targetLayout = null;
//        switch (type) {
//            case Fajr: targetLayout = fajrATimeLinearLayout; break;
//            case Sunrise: case PrayerType.Duha: targetLayout = sunriseATimeLinearLayout; break;
//            case Dhuhr: targetLayout = dhuhrATimeLinearLayout; break;
//            case Asr: targetLayout = asrATimeLinearLayout; break;
//            case Maghrib: targetLayout = maghribATimeLinearLayout; break;
//            case Isha: targetLayout = ishaATimeLinearLayout; break;
//        }
//
//        if (targetLayout != null) {
//            if (isNextPray) {
//                targetLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_active);
//                // Also set bottom for BROWN_NEW_3
//                ViewGroup bottomLayout = null;
//                switch (type) {
//                    case Fajr: bottomLayout = fajrTimeLinearLayout; break;
//                    case Sunrise: case PrayerType.Duha: bottomLayout = sunriseTimeLinearLayout; break;
//                    case Dhuhr: bottomLayout = dhuhrTimeLinearLayout; break;
//                    case Asr: bottomLayout = asrTimeLinearLayout; break;
//                    case Maghrib: bottomLayout = maghribTimeLinearLayout; break;
//                    case Isha: bottomLayout = ishaTimeLinearLayout; break;
//                }
//                if (bottomLayout != null) bottomLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_active);
//            } else {
//                targetLayout.setBackgroundResource(R.drawable.theme_brown_new_3_top_rounded_inactive);
//                ViewGroup bottomLayout = null;
//                switch (type) {
//                    case Fajr: bottomLayout = fajrTimeLinearLayout; break;
//                    case Sunrise: case PrayerType.Duha: bottomLayout = sunriseTimeLinearLayout; break;
//                    case Dhuhr: bottomLayout = dhuhrTimeLinearLayout; break;
//                    case Asr: bottomLayout = asrTimeLinearLayout; break;
//                    case Maghrib: bottomLayout = maghribTimeLinearLayout; break;
//                    case Isha: bottomLayout = ishaTimeLinearLayout; break;
//                }
//                if (bottomLayout != null) bottomLayout.setBackgroundResource(R.drawable.theme_brown_new_3_bottom_rounded_inactive);
//            }
//        }
//    }
//
//    private void setBackgroundToTextViewPray(ViewGroup layoutPray, boolean isNextPray) {
//        if (layoutPray == null) return;
//        switch (mainLayoutManager.getUiTheme()) {
//            case BROWN:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.text_view_next_pray_brown : R.drawable.text_view_pray_white_new2);
//                mainLayoutManager.fixBackGroundStretching(layoutPray);
//                break;
//            case BLUE:
//                layoutPray.setBackgroundColor(isNextPray ? ContextCompat.getColor(activity, R.color.blue_transparent) : android.graphics.Color.TRANSPARENT);
//                break;
//            case DARK_GREEN:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.dark_green_transperant : R.drawable.gradient_semi_transperant_green);
//                break;
//            case CUSTOM_1:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.gradient_semi_transperant_custom : 0);
//                break;
//            case RED:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.gradient_semi_transperant_red2 : R.drawable.gradient_semi_transperant_red);
//                break;
//            case WHITE:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.text_view_next_pray_darkw : 0);
//                break;
//            case DARK_GRAY:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.without_corners_bottom_solid_yellow : R.drawable.without_corners_bottom_solid_gray);
//                break;
//            case GREEN:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.text_view_next_pray_darkb : 0);
//                if (isNextPray) mainLayoutManager.fixBackGroundStretching(layoutPray);
//                break;
//            case BLUE_NEW:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.text_view_pray_bluenewnext : 0);
//                if (isNextPray) mainLayoutManager.fixBackGroundStretching(layoutPray);
//                break;
//            case BLUE_LET:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.background_prays_blue_lett_now : R.drawable.background_prays_blue_lett_r);
//                break;
//            case NEW_GREEN:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.background_prays_new_green_now : R.drawable.background_prays_new_green_r);
//                break;
//            case WHITE_NEW:
//                layoutPray.setBackgroundResource(isNextPray ? R.drawable.text_view_remining_white_new : R.drawable.text_view_pray_white_new2);
//                mainLayoutManager.fixBackGroundStretching(layoutPray);
//                break;
//            case BROWN_NEW_3: // Specific handling for this theme, already done above
//                break;
//        }
//    }
//
//
//    private void setColorToTextViewPray(TextView pray, TextView prayTime, TextView prayTimeType, boolean isNextPray) {
//        int color;
//        switch (mainLayoutManager.getUiTheme()) {
//            case BLUE_LET:
//                color = isNextPray ? ContextCompat.getColor(activity, R.color.bluelett) : ContextCompat.getColor(activity, R.color.white);
//                Utils.setTextColor(pray, color);
//                break;
//            case NEW_GREEN:
//                color = isNextPray ? ContextCompat.getColor(activity, R.color.new_green_2) : ContextCompat.getColor(activity, R.color.white);
//                Utils.setTextColor(prayTime, color);
//                Utils.setTextColor(prayTimeType, color);
//                break;
//            case BLUE_NEW:
//                color = isNextPray ? ContextCompat.getColor(activity, R.color.white) : ContextCompat.getColor(activity, R.color.ambluenew);
//                Utils.setTextColor(prayTimeType, color);
//                break;
//            case DARK_GRAY:
//                color = isNextPray ? ContextCompat.getColor(activity, R.color.colorGrayDark) : ContextCompat.getColor(activity, R.color.white);
//                Utils.setTextColor(pray, color);
//                Utils.setTextColor(prayTime, color);
//                Utils.setTextColor(prayTimeType, color);
//                break;
//            default: // Other themes might use default or no special color change
//                break;
//        }
//    }
//
//    private void setToImageViewPray(ImageView pray, boolean isNextPray) {
//        if (pray == null) return;
//        if (mainLayoutManager.getUiTheme() == com.arapeak.alrbea.Enum.UITheme.BLUE) {
//            int height = activity.getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._15sdp);
//            int heightMax = activity.getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._30sdp);
//            int width = activity.getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._15sdp);
//            int widthMax = activity.getResources().getDimensionPixelSize(com.intuit.sdp.R.dimen._30sdp);
//
//            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) pray.getLayoutParams();
//            if (layoutParams == null) return; // defensive check
//            if (isNextPray) {
//                layoutParams.height = heightMax;
//                layoutParams.width = widthMax;
//            } else {
//                layoutParams.height = height;
//                layoutParams.width = width;
//            }
//            pray.requestLayout();
//        }
//    }
//
//    // Methods to update calendars and Jomaa status from BackgroundTaskManager
//    public void updateCalendarData(UmmalquraCalendar hijriCalendar, GregorianCalendar gregorianCalendar, boolean isJomaa) {
//        this.hijriCalendar = hijriCalendar;
//        this.gregorianCalendar = gregorianCalendar;
//        this.isJomaa = isJomaa;
//        // Also update the string representations if needed immediately
//        calcDateStrings();
//    }
//
//    private void calcDateStrings() {
//        Locale arabic = new Locale("ar");
//        Locale english = new Locale("en");
//        gYear = "" + gregorianCalendar.get(Calendar.YEAR);
//        gMonthAr = gregorianCalendar.getDisplayName(Calendar.MONTH, Calendar.LONG, arabic);
//        gMonthEn = gregorianCalendar.getDisplayName(Calendar.MONTH, Calendar.LONG, english);
//        gMonthNum = "" + (gregorianCalendar.get(Calendar.MONTH) + 1); // Month is 0-indexed
//        gDayNum = "" + gregorianCalendar.get(Calendar.DAY_OF_MONTH);
//        gDayNameAr = gregorianCalendar.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, arabic);
//        gDayNameEn = gregorianCalendar.getDisplayName(Calendar.DAY_OF_WEEK, Calendar.LONG, english);
//
//        hYear = "" + hijriCalendar.get(Calendar.YEAR);
//        hMonthAr = hijriCalendar.getDisplayName(Calendar.MONTH, Calendar.SHORT, arabic);
//        hMonthEn = activity.getResources().getStringArray(R.array.hijri_months_en)[hijriCalendar.get(Calendar.MONTH)];
//        hDayNum = "" + hijriCalendar.get(Calendar.DAY_OF_MONTH);
//    }
//}
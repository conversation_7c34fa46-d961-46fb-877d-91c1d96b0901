<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".ui.activity.MainActivity">

    <!-- Date and Time Section -->
    <TextView
        android:id="@+id/textViewCurrentDateGregorian"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Gregorian Date"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/textViewCurrentDateHijri"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Hijri Date"
        android:textSize="18sp"
        android:textStyle="normal"
        android:paddingBottom="8dp"/>

    <TextView
        android:id="@+id/textViewCurrentTime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Time"
        android:textSize="24sp"
        android:textStyle="bold"
        android:paddingBottom="16dp"/>

    <!-- Prayer Times Section - Grouped for easy visibility toggle -->
    <LinearLayout
        android:id="@+id/prayerTimesSection"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Prayer Times"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:paddingBottom="8dp"/>

        <!-- Individual Prayer Times -->
        <!-- Consider including a separate layout file here for clean XML: -->
        <!-- <include layout="@layout/prayer_times_list"/> -->
        <!-- If included, ensure the IDs within prayer_times_list.xml match -->
        <!-- OR define them directly here: -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="4dp">
            <TextView android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="Fajr" android:textSize="16sp"/>
            <TextView android:id="@+id/textViewFajrTime" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="--:--" android:textSize="16sp" android:gravity="end"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="4dp">
            <TextView android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="Sunrise" android:textSize="16sp"/>
            <TextView android:id="@+id/textViewSunriseTime" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="--:--" android:textSize="16sp" android:gravity="end"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="4dp">
            <TextView android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="Dhuhr" android:textSize="16sp"/>
            <TextView android:id="@+id/textViewDhuhrTime" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="--:--" android:textSize="16sp" android:gravity="end"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="4dp">
            <TextView android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="Asr" android:textSize="16sp"/>
            <TextView android:id="@+id/textViewAsrTime" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="--:--" android:textSize="16sp" android:gravity="end"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="4dp">
            <TextView android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="Maghrib" android:textSize="16sp"/>
            <TextView android:id="@+id/textViewMaghribTime" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="--:--" android:textSize="16sp" android:gravity="end"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="16dp">
            <TextView android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="Isha" android:textSize="16sp"/>
            <TextView android:id="@+id/textViewIshaTime" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:text="--:--" android:textSize="16sp" android:gravity="end"/>
        </LinearLayout>
    </LinearLayout>


    <!-- Mode Selection Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:paddingBottom="16dp">

        <Button
            android:id="@+id/buttonModePrayer"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="Prayer"
            android:layout_marginEnd="8dp"/>

        <Button
            android:id="@+id/buttonModeIkama"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="Ikama"
            android:layout_marginEnd="8dp"/>

        <Button
            android:id="@+id/buttonModeAthkar"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="Athkar" />

    </LinearLayout>

    <!-- Mode Content Section -->
    <!-- This section displays the title and the dynamic content for Ikama/Athkar -->
    <TextView
        android:id="@+id/textViewModeTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="App Mode"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:paddingBottom="8dp"/>

    <TextView
        android:id="@+id/textViewModeContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="Content specific to the current mode will appear here (Ikama list or Athkar text)."
        android:textSize="16sp"
        android:gravity="center_vertical"
        android:scrollbars="vertical"/> <!-- Make content scrollable -->

    <!-- If you need separate UI for Ikama Offset editing, add it here -->
    <!-- E.g., a LinearLayout for Fajr offset editing, shown only in IKAMA mode -->
    <!-- <LinearLayout
        android:id="@+id/fajrOffsetEditLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"> // Start hidden
        <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Fajr Offset (mins):"/>
        <EditText android:id="@+id/editTextFajrOffset" android:layout_width="0dp" android:layout_weight="1" android:layout_height="wrap_content" android:inputType="number"/>
        <Button android:id="@+id/buttonSaveFajrOffset" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Save"/>
    </LinearLayout> -->


</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/theme_item_width"
    android:layout_height="@dimen/theme_item_height"
    android:layout_marginBottom="@dimen/_5sdp"
    android:gravity="center"
    android:orientation="vertical">

    <pl.droidsonroids.gif.GifImageView
        android:id="@+id/gif"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#4D1DAFEC"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/enable"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_24sdp" />

</androidx.constraintlayout.widget.ConstraintLayout>
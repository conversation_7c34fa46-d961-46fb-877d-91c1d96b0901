package com.arapeak.alrbea.database;

import static com.arapeak.alrbea.AppController.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.ArrayList;

public class PhotoGalleryRepository {

    public static PhotoGallery insert(PhotoGallery gallery) {
        PhotoGallery result = null;
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            ContentValues values = new ContentValues();
            values.put("name", gallery.name);
            long id = database.insert(MainDatabase.PHOTO_GALLERY, null, values);
            if (id >= 0) {
                PhotoGalleryImagesRepository.insert(id, gallery.images);
                result = get(id);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return result;
    }

    public static PhotoGallery update(PhotoGallery gallery) {
        PhotoGallery result;
        PhotoGalleryImagesRepository.deleteAllByGalleryId(gallery.id);
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            ContentValues values = new ContentValues();
            values.put("name", gallery.name);
            database.update(MainDatabase.PHOTO_GALLERY, values, "id=" + gallery.id, null);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        PhotoGalleryImagesRepository.insert(gallery.id, gallery.images);
        result = get(gallery.id);
        return result;
    }

    /*public static PhotoGallery update(PhotoGallery gallery){
        delete(gallery);
        return insert(gallery);
    }*/
    public static PhotoGallery get(long id) {
        PhotoGallery result = null;
        try (SQLiteDatabase database = db.getReadableDatabase()) {
            Cursor cursor = database.rawQuery("select * from " + MainDatabase.PHOTO_GALLERY + " where id = " + id, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    PhotoGallery gallery = new PhotoGallery();
                    gallery.id = cursor.getLong(0);
                    gallery.name = cursor.getString(1);
                    gallery.images = PhotoGalleryImagesRepository.getFromGallery(gallery.id);
                    result = gallery;
                    cursor.close();
                }
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return result;
    }

    public static ArrayList<PhotoGallery> getAll() {
        ArrayList<PhotoGallery> galleries = new ArrayList<>();
        try (SQLiteDatabase database = db.getReadableDatabase()) {
            Cursor cursor = database.rawQuery("select * from " + MainDatabase.PHOTO_GALLERY, null);
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    do {
                        PhotoGallery gallery = new PhotoGallery();
                        gallery.id = cursor.getLong(0);
                        gallery.name = cursor.getString(1);
                        gallery.images = PhotoGalleryImagesRepository.getFromGallery(gallery.id);
                        galleries.add(gallery);
                    }
                    while (cursor.moveToNext());
                    cursor.close();
                }
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return galleries;
    }

    public static void delete(PhotoGallery gallery) {
        PhotoGalleryImagesRepository.deleteAllByGalleryId(gallery.id);
        try (SQLiteDatabase database = db.getWritableDatabase()) {
            database.execSQL("delete from " + MainDatabase.PHOTO_GALLERY + " where id = " + gallery.id);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }
}

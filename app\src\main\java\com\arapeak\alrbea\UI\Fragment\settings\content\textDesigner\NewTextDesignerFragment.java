package com.arapeak.alrbea.UI.Fragment.settings.content.textDesigner;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsoluteLayout;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapter;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapterFont;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignFontEnum;
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignPosition;
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignToggleEnum;
import com.arapeak.alrbrea.core_ktx.ui.textdesign.TextDesignSettingsManager;
import com.orhanobut.hawk.Hawk;
import com.skydoves.colorpickerview.ColorPickerDialog;
import com.skydoves.colorpickerview.ColorPickerView;
import com.skydoves.colorpickerview.listeners.ColorEnvelopeListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

public class NewTextDesignerFragment extends AlrabeeaTimesFragment {
    private static final String TAG = "NewTextDesignerFragment";
    TextView tvDemo1, tvDemo2;
    LinearLayout llEnabled;
    LinearLayout hsvTextFeatures;
    Spinner spLines, spFonts;
    boolean isEnabled = false;
    private View subSettingsView;
    private FontMapper mapper;
    private TextDesignSettingsManager manager;
    private FrameLayout flmain;
    private boolean lineOneSelected = true;
    private CardView vColor;
    public NewTextDesignerFragment() {
    }

    public static NewTextDesignerFragment newInstance() {
        return new NewTextDesignerFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        subSettingsView = inflater.inflate(R.layout.textdesign_settings_new, container, false);

        manager = new TextDesignSettingsManager();
        mapper = new FontMapper();

        return subSettingsView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initToggleView();
        initTwoLinesViews();
        initTextViews();
        initPreview();
        initDragViews();
        initFontViews();
        initColorView();
        initSizeViews();
        initInputViews();

    }

    private void initToggleView() {
        llEnabled = subSettingsView.findViewById(R.id.ll_td_enable);
        hsvTextFeatures = subSettingsView.findViewById(R.id.hsv_text_features);
        spLines = subSettingsView.findViewById(R.id.lines_Spinner_textdesign);
        CheckBox showInPrayers = subSettingsView.findViewById(R.id.td_cb_show_in_prayers);

        List<String> valuesList = manager.getRepo().getTogglesNamesLocalized(requireContext());
        int selected = manager.getRepo().getToggle(requireContext()).ordinal();

        int spinnerId = R.id.options_Spinner_textdesign;

        Spinner optionsSpinner = subSettingsView.findViewById(spinnerId);
        List<Object> spinnerList = new ArrayList<>(valuesList);
        SpinnerAdapter<Object> spinnerAdapter = new SpinnerAdapter<>(requireContext(), spinnerList);
        optionsSpinner.setAdapter(spinnerAdapter);
        optionsSpinner.setSelection(selected);
        optionsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                TextDesignToggleEnum res = TextDesignToggleEnum.getEntries().get(subPosition);
                manager.getRepo().updateToggle(requireContext(), res);

                isEnabled = res == TextDesignToggleEnum.Enabled;
                if (isEnabled) {
                    hsvTextFeatures.setVisibility(View.VISIBLE);
                    spLines.setVisibility(View.VISIBLE);
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            restoreTextPosition();
                        }
                    }, manager.getPB_DELAY());
                } else {
                    spLines.setVisibility(View.INVISIBLE);
                    tvDemo1.setVisibility(View.INVISIBLE);
                    tvDemo2.setVisibility(View.INVISIBLE);
                    hsvTextFeatures.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        showInPrayers.setChecked(manager.getRepo().getDuringPrayersSetting(requireContext()));
        showInPrayers.setOnCheckedChangeListener((buttonView, isChecked) -> {
            manager.getRepo().updateDuringPrayersSetting(requireContext(), isChecked);
        });
    }

    private void initTwoLinesViews() {
        List<String> valuesList = manager.getRepo().getTwoLinesNamesLocalized(requireContext());

        int selected = 0;
        if (manager.getRepo().getTwoLines(requireContext())) {
            selected = 1;
        }


        List<Object> spinnerList = new ArrayList<>(valuesList);
        SpinnerAdapter<Object> spinnerAdapter = new SpinnerAdapter<>(requireContext(), spinnerList);
        spLines.setAdapter(spinnerAdapter);
        spLines.setSelection(selected);
        spLines.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                boolean enabled = subPosition == 1;
                manager.getRepo().updateTwoLines(requireContext(), enabled);
                if (enabled) {
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            restoreTextPosition();
                        }
                    }, manager.getPB_DELAY());
                } else {
                    tvDemo2.setVisibility(View.INVISIBLE);
                    selectLine(true);
                }

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
    }


    private void initTextViews() {
        try {
            tvDemo1 = subSettingsView.findViewById(R.id.td_tv_demo_down1);
            tvDemo2 = subSettingsView.findViewById(R.id.td_tv_demo_down2);

            tvDemo1.setTypeface(ResourcesCompat.getFont(requireContext(), (new FontMapper()).getFontRes(manager.getRepo().getFont(requireContext(), false))));
            tvDemo2.setTypeface(ResourcesCompat.getFont(requireContext(), (new FontMapper()).getFontRes(manager.getRepo().getFont(requireContext(), true))));

            int color1 = manager.getRepo().getColor(requireContext(), false);
            int color2 = manager.getRepo().getColor(requireContext(), true);

            tvDemo1.setTextColor(color1);
            tvDemo2.setTextColor(color2);

            float size1 = manager.getRepo().getSizeParsed(requireContext(), false);
            float size2 = manager.getRepo().getSizeParsed(requireContext(), true);

            tvDemo1.setTextSize(size1);
            tvDemo2.setTextSize(size2);

            String input1 = manager.getRepo().getText(requireContext(), false);
            String input2 = manager.getRepo().getText(requireContext(), true);

            if (!input1.isEmpty())
                tvDemo1.setText(input1);
            else
                tvDemo1.setText(getString(com.arapeak.alrbrea.core_ktx.R.string.example));


            if (!input2.isEmpty())
                tvDemo2.setText(input2);
            else
                tvDemo2.setText(getString(com.arapeak.alrbrea.core_ktx.R.string.example));

            selectLine(lineOneSelected);

            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    restoreTextPosition();
                }
            }, manager.getPB_DELAY());

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }


    private void restoreTextPosition() {
        try {

            TextDesignPosition old1 = manager.getRepo().getPosition(requireContext(), false);
            TextDesignPosition old2 = manager.getRepo().getPosition(requireContext(), true);

            ViewGroup parent = (ViewGroup) tvDemo1.getParent();
            int parentWidth = parent.getWidth();
            int parentHeight = parent.getHeight();


            if (!old1.isDefault()) {
                AbsoluteLayout.LayoutParams layoutParams1 = (AbsoluteLayout.LayoutParams) tvDemo1.getLayoutParams();
                layoutParams1.x = (int) (parentWidth * old1.getX());
                layoutParams1.y = (int) (parentHeight * old1.getY());
                tvDemo1.setLayoutParams(layoutParams1);
            }

            if (!old2.isDefault()) {
                AbsoluteLayout.LayoutParams layoutParams2 = (AbsoluteLayout.LayoutParams) tvDemo2.getLayoutParams();
                layoutParams2.x = (int) (parentWidth * old2.getX());
                layoutParams2.y = (int) (parentHeight * old2.getY());
                tvDemo2.setLayoutParams(layoutParams2);
            }

            if (isEnabled) {
                tvDemo1.setVisibility(View.VISIBLE);
                if (manager.getRepo().getTwoLines(requireContext()))
                    tvDemo2.setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void initPreview() {
        try {
            ImageView iv = subSettingsView.findViewById(R.id.iv_bitmap);
            Bitmap bitmap = BitmapFactory.decodeFile(manager.getPreviewImagePath(requireContext(), (HawkSettings.getAppOrientation() == 1)));

            if (bitmap != null) {
                iv.setImageBitmap(bitmap);
            } else {
                Toast.makeText(requireContext(), getString(R.string.error_preview_not_found), Toast.LENGTH_SHORT).show();
                setAppTheme(subSettingsView);
            }

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void setAppTheme(View view) {
        UITheme uiTheme = HawkSettings.getCurrentTheme();

        switch (uiTheme) {
            case BROWN:
                View.inflate(getContext(), R.layout.content_main_brown, flmain);
                break;
            case BLUE:
                View.inflate(getContext(), R.layout.content_main_blue, flmain);
                break;
            case DARK_GREEN:
                View.inflate(getContext(), R.layout.content_main_dark_green, flmain);
                break;
            case DARK_GRAY:
                View.inflate(getContext(), R.layout.content_main_drak_gray, flmain);
                break;
            case GREEN:
                View.inflate(getContext(), R.layout.content_main_green, flmain);
                break;
            case WHITE:
                View.inflate(getContext(), R.layout.content_main_white, flmain);
                break;
            case RED:
                View.inflate(getContext(), R.layout.content_main_red, flmain);
                break;
            case BROWN_NEW:
                View.inflate(getContext(), R.layout.content_main_brown_new, flmain);
                break;
            case BLUE_NEW:
                View.inflate(getContext(), R.layout.content_main_blue_new, flmain);
                break;
            case NEW_GREEN:
                View.inflate(getContext(), R.layout.content_main_new_green, flmain);
                break;
            case BLUE_LET:
                View.inflate(getContext(), R.layout.content_main_blue_lett, flmain);
                break;
            case WHITE_NEW:
                View.inflate(getContext(), R.layout.content_main_white_new, flmain);
                break;
            case BROWN_NEW_3:
                View.inflate(getContext(), R.layout.content_main_brown_3, flmain);
                break;
            case CUSTOM_1:
                if (Hawk.get(uiTheme.name() + "_show_ikama", false))
                    View.inflate(getContext(), R.layout.content_main_custom_1_with_ikama, flmain);
                else
                    View.inflate(getContext(), R.layout.content_main_custom_1, flmain);
                break;
            default:
                if (Hawk.get(uiTheme.name() + "_show_ikama", false))
                    View.inflate(getContext(), R.layout.content_main_firebase_with_ikama, flmain);
                else
                    View.inflate(getContext(), R.layout.content_main_firebase, flmain);
                break;
        }
        view.findViewById(R.id.abs_main).bringToFront();
        tvDemo1.bringToFront();
        tvDemo2.bringToFront();

        /*
         List<TextView> textViews = new ArrayList<>();
         findTextViews(flmain, textViews);
         for (TextView textView : textViews) {
         if (textView.getId() != tvDemo.getId())
         textView.setTextSize(textView.getTextSize() * manager.getPREVIEW_SCALE());
         }
         **/


        try {
            ((TextView) (view.findViewById(R.id.timeNow_TextView_MainActivity))).setText("12:55");

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }


    private void initDragViews() {

        View.OnTouchListener list = new View.OnTouchListener() {
            private int initialX, initialY;
            private float downX, downY;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                try {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_UP: {
                            Log.e(TAG, "x " + v.getX() + " ||||| " + "y " + v.getY());
                            Log.e(TAG, "left " + v.getLeft() + " ||||| " + "right " + v.getRight() + " ||||| " + "top " + v.getTop() + " ||||| " + "bottom " + v.getBottom());

                            llEnabled.setVisibility(View.VISIBLE);
                            hsvTextFeatures.setVisibility(View.VISIBLE);

                            ViewGroup parent = (ViewGroup) v.getParent();

                            manager.savePosition(
                                    requireContext(),
                                    v.getLeft(),
                                    v.getTop(),
                                    parent.getWidth(),
                                    parent.getHeight(),
                                    v.getId() == R.id.td_tv_demo_down2
                            )
                            ;
                            break;
                        }

                        case MotionEvent.ACTION_DOWN: {
                            initialX = v.getLeft();
                            initialY = v.getTop();
                            downX = event.getRawX();
                            downY = event.getRawY();
                            llEnabled.setVisibility(View.GONE);
                            hsvTextFeatures.setVisibility(View.GONE);
                            selectLine(v.getId() == R.id.td_tv_demo_down1);

                            break;
                        }

                        case MotionEvent.ACTION_MOVE: {

                            int dx = (int) (event.getRawX() - downX);
                            int dy = (int) (event.getRawY() - downY);

                            AbsoluteLayout.LayoutParams layoutParams = (AbsoluteLayout.LayoutParams) v.getLayoutParams();

                            int parentWidth = ((ViewGroup) v.getParent()).getWidth();
                            int parentHeight = ((ViewGroup) v.getParent()).getHeight();

                            int newX = initialX + dx;
                            int newY = initialY + dy;

                            int offsetX = v.getWidth() / 3;
                            int offsetY = v.getHeight() / 3;

                            // Ensure the view stays within the parent's boundaries
                            if (newX + offsetX < 0) newX = -offsetX;
                            if (newY + offsetY < 0) newY = -offsetY;

                            if (newX + v.getWidth() - offsetX > parentWidth)
                                newX = parentWidth - v.getWidth() + offsetX;

                            if (newY + v.getHeight() - offsetY > parentHeight)
                                newY = parentHeight - v.getHeight() + offsetY;

                            layoutParams.x = newX;
                            layoutParams.y = newY;


                            v.setLayoutParams(layoutParams);
                            break;
                        }

                    }
                    return true;
                } catch (Exception e) {
                    CrashlyticsUtils.INSTANCE.logException(e);
                    return false;
                }
            }
        };

        tvDemo1.setOnTouchListener(list);
        tvDemo2.setOnTouchListener(list);

        tvDemo1.bringToFront();
        tvDemo2.bringToFront();
    }

    private void selectLine(boolean los) {
        Drawable background = ContextCompat.getDrawable(requireContext(), R.drawable.tv_box);

        int font1 = manager.getRepo().getFont(requireContext(), false).ordinal();
        int font2 = manager.getRepo().getFont(requireContext(), true).ordinal();

        int color1 = manager.getRepo().getColor(requireContext(), false);
        int color2 = manager.getRepo().getColor(requireContext(), true);

        String text1 = manager.getRepo().getText(requireContext(), false);
        String text2 = manager.getRepo().getText(requireContext(), true);


        if (!text1.isEmpty()) {
            tvDemo1.setText(text1);
        }

        if (!text2.isEmpty()) {
            tvDemo2.setText(text2);
        }


        if (los) {
            tvDemo1.setBackground(background);
            tvDemo2.setBackground(null);
            if (spFonts != null) {
                spFonts.setSelection(los ? font1 : font2);
            }
            vColor.setCardBackgroundColor(color1);

        } else {
            tvDemo2.setBackground(background);
            tvDemo1.setBackground(null);
            spFonts.setSelection(font2);
            vColor.setCardBackgroundColor(color2);
        }

        lineOneSelected = los;
    }


    private void initFontViews() {
        spFonts = subSettingsView.findViewById(R.id.sp_fonts);

        List<String> valuesList = manager.getRepo().getFontNamesLocalized(requireContext());
        int selected1 = manager.getRepo().getFont(requireContext(), false).ordinal();
        int selected2 = manager.getRepo().getFont(requireContext(), true).ordinal();


        List<Object> spinnerList = new ArrayList<>(valuesList);
        SpinnerAdapterFont<Object> spinnerAdapter = new SpinnerAdapterFont<>(requireContext(), spinnerList, mapper.getFontResList());

        spFonts.setAdapter(spinnerAdapter);

        if (lineOneSelected)
            spFonts.setSelection(selected1);
        else
            spFonts.setSelection(selected2);

        spFonts.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                if (lineOneSelected)
                    tvDemo1.setTypeface(ResourcesCompat.getFont(requireContext(), mapper.getFontRes(subPosition)));
                else
                    tvDemo2.setTypeface(ResourcesCompat.getFont(requireContext(), mapper.getFontRes(subPosition)));

                manager.getRepo().updateFont(requireContext(), TextDesignFontEnum.getEntries().get(subPosition), !lineOneSelected);
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });


    }


    /**
     * @noinspection t
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initColorView() {
        vColor = subSettingsView.findViewById(R.id.v_color);

        int old1 = manager.getRepo().getColor(requireContext(), false);
        int old2 = manager.getRepo().getColor(requireContext(), true);

        if (lineOneSelected)
            vColor.setCardBackgroundColor(old1);
        else
            vColor.setCardBackgroundColor(old2);

        tvDemo1.setTextColor(old1);
        tvDemo2.setTextColor(old2);


        vColor.setOnClickListener(v -> new ColorPickerDialog.Builder(requireContext())
                .setTitle(getString(R.string.pick_color))
                .setPreferenceName("MyColorPickerDialogTextDialog")
                .setColorPickerView(new ColorPickerView.Builder(requireContext())
                        .setInitialColor(lineOneSelected ? old1 : old2)
                        .setPreferenceName("MyColorPickerDialogTextDialog")
                        .build())
                .setPositiveButton(getString(R.string.ok), (ColorEnvelopeListener) (envelope, fromUser) -> {
                    vColor.setCardBackgroundColor(envelope.getColor());
                    if (lineOneSelected)
                        tvDemo1.setTextColor(envelope.getColor());
                    else
                        tvDemo2.setTextColor(envelope.getColor());

                    manager.getRepo().updateColor(requireContext(), envelope.getColor(), !lineOneSelected);
                })
                .setNegativeButton(getString(R.string.cancel), (dialogInterface, i) -> dialogInterface.dismiss())
                .attachAlphaSlideBar(true)
                .attachBrightnessSlideBar(true)
                .setBottomSpace(48) // set a bottom space between the last slidebar and buttons.
                .setNeutralButton(getString(R.string.choose_from_theme), (dialogInterface, i) -> {

                    AtomicInteger color = new AtomicInteger();
                    llEnabled.setVisibility(View.GONE);
                    hsvTextFeatures.setVisibility(View.GONE);
                    Toast.makeText(requireContext(), getString(R.string.choose_color), Toast.LENGTH_LONG).show();

                    ImageView imageView = subSettingsView.findViewById(R.id.iv_bitmap);

                    imageView.setOnTouchListener((v1, event) -> {
                        if (event.getAction() == MotionEvent.ACTION_UP) {
                            int x = (int) event.getX();
                            int y = (int) event.getY();

                            Bitmap bitmap = ((BitmapDrawable) imageView.getDrawable()).getBitmap();
                            int pixelColor = bitmap.getPixel(x, y);

                            vColor.setCardBackgroundColor(pixelColor);
                            if (lineOneSelected)
                                tvDemo1.setTextColor(pixelColor);
                            else
                                tvDemo2.setTextColor(pixelColor);
                            color.set(pixelColor);


                            manager.getRepo().updateColor(requireContext(), color.get(), !lineOneSelected);

                            llEnabled.setVisibility(View.VISIBLE);
                            hsvTextFeatures.setVisibility(View.VISIBLE);
                            imageView.setOnTouchListener(null);
                        }
                        if (event.getAction() == MotionEvent.ACTION_DOWN) {
                            llEnabled.setVisibility(View.GONE);
                            hsvTextFeatures.setVisibility(View.GONE);
                        }
                        if (event.getAction() == MotionEvent.ACTION_MOVE) {
                            int x = (int) event.getX();
                            int y = (int) event.getY();

                            Bitmap bitmap = ((BitmapDrawable) imageView.getDrawable()).getBitmap();
                            int pixelColor = bitmap.getPixel(x, y);

                            vColor.setCardBackgroundColor(pixelColor);
                            if (lineOneSelected)
                                tvDemo1.setTextColor(pixelColor);
                            else
                                tvDemo2.setTextColor(pixelColor);
                            color.set(pixelColor);
                        }
                        return true;
                    });
                })
                .show());

    }

    private void initSizeViews() {
        float old1 = manager.getRepo().getSizeParsed(requireContext(), false);
        float old2 = manager.getRepo().getSizeParsed(requireContext(), true);

        tvDemo1.setTextSize(old1);
        tvDemo2.setTextSize(old2);

        subSettingsView.findViewById(R.id.btn_size_add).setOnClickListener(v -> {
            if (lineOneSelected) {
                int scale = manager.getRepo().getSize(requireContext(), false);
                int nextScale = manager.getTextSizes().getNextSize(scale);
                int nextSize = manager.getTextSizes().getNextSizeRes(scale);
                tvDemo1.setTextSize(getResources().getDimension(nextSize));
                manager.getRepo().updateSize(requireContext(), nextScale, false);

            } else {
                int scale = manager.getRepo().getSize(requireContext(), true);
                int nextScale = manager.getTextSizes().getNextSize(scale);
                int nextSize = manager.getTextSizes().getNextSizeRes(scale);
                tvDemo2.setTextSize(getResources().getDimension(nextSize));
                manager.getRepo().updateSize(requireContext(), nextScale, true);
            }

        });

        subSettingsView.findViewById(R.id.btn_size_minus).setOnClickListener(v -> {
            if (lineOneSelected) {
                int scale = manager.getRepo().getSize(requireContext(), false);
                int nextScale = manager.getTextSizes().getPreviousSize(scale);
                int nextSize = manager.getTextSizes().getPreviousSizeRes(scale);
                tvDemo1.setTextSize(getResources().getDimension(nextSize));
                manager.getRepo().updateSize(requireContext(), nextScale, false);

            } else {
                int scale = manager.getRepo().getSize(requireContext(), true);
                int nextScale = manager.getTextSizes().getPreviousSize(scale);
                int nextSize = manager.getTextSizes().getPreviousSizeRes(scale);
                tvDemo2.setTextSize(getResources().getDimension(nextSize));
                manager.getRepo().updateSize(requireContext(), nextScale, true);
            }

        });
    }

    private void initInputViews() {
        subSettingsView.findViewById(R.id.sp_text_input).setOnClickListener(v -> {
            TextDesignInputDialog dialog = TextDesignInputDialog.newInstance(manager, lineOneSelected);
            if (getChildFragmentManager().findFragmentByTag(dialog.TAG) == null || !Objects.requireNonNull(getChildFragmentManager().findFragmentByTag(dialog.TAG)).isVisible()) {
                manager.setUpdateText(() -> {
                    selectLine(lineOneSelected);
                    return "";
                });
                dialog.display(getChildFragmentManager());
            }
        });
    }


}

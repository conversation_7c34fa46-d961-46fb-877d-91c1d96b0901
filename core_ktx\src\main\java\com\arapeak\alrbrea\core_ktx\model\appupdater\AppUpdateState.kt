package com.arapeak.alrbrea.core_ktx.model.appupdater

sealed class AppUpdateState {
    data object Init : AppUpdateState()
    data object Checking : AppUpdateState()

    //check for update
    data object UpToDate : AppUpdateState()

    //download update
    data class Downloading(val percentage: Int) : AppUpdateState()
    data class Downloaded(val path: String) : AppUpdateState()

    //install update
    data object Refused : AppUpdateState()

    data class Failed(val error: String) : AppUpdateState()

}

sealed class AppCheckState {
    data object Checking : AppCheckState()
    data object UpToDate : AppCheckState()
    data class Failed(val error: String) : AppCheckState()
    data class Outdated(val link: String) : AppCheckState()


}

interface AppUpdaterListener {
    fun onUpdateState(state: AppUpdateState)

}

interface AppCheckListener {
    fun onUpdateState(state: AppCheckState)

}
package com.arapeak.alrbea.Enum;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

public enum IslamicEvent {
    NINETH_OF_MUHARRAM(UmmalquraCalendar.MUHARRAM, 8),
    TENTH_OF_MUHARRAM(UmmalquraCalendar.MUHARRAM, 9),
    CRESCENT_SIGHTING_FOR_RAMADAN(UmmalquraCalendar.SHAABAN, 29),
    FIRST_DAY_OF_RAMADAN(UmmalquraCalendar.RAMADHAN, 1),
    THE_NIGHT_OF_DECREE(UmmalquraCalendar.RAMADHAN, 20),
    FIRST_DAYS_OF_EID_AL_FITR(UmmalquraCalendar.SHAWWAL, 1),
//    SECOND_DAYS_OF_EID_AL_FITR(UmmalquraCalendar.SHAWWAL,2),
//    THIRD_DAYS_OF_EID_AL_FITR(UmmalquraCalendar.SHAWWAL,3),

    SIX_FROM_SHAWWAL(UmmalquraCalendar.SHAWWAL, 2),
    DAY_OF_ARAFAH(UmmalquraCalendar.THUL_HIJJAH, 9),
    EID_AL_ADHA(UmmalquraCalendar.THUL_HIJJAH, 10),
    FIRST_DAYS_OF_Al_TASHREEQ(UmmalquraCalendar.THUL_HIJJAH, 11),
    SECOND_DAYS_OF_Al_TASHREEQ(UmmalquraCalendar.THUL_HIJJAH, 12),
    THIRD_DAYS_OF_Al_TASHREEQ(UmmalquraCalendar.THUL_HIJJAH, 13);
    public final int sMonth, sDay;

    IslamicEvent(int sMonth, int sDay) {
        this.sMonth = sMonth;
        this.sDay = sDay;
    }

    public boolean isEventDayArrived() {
        UmmalquraCalendar calendar = Utils.getUmmalquraCalendar();
        int month = calendar.get(UmmalquraCalendar.MONTH);
        int day = calendar.get(UmmalquraCalendar.DAY_OF_MONTH);
        int hour = calendar.get(UmmalquraCalendar.HOUR_OF_DAY);
        if (month != sMonth) return false;
        if (this == THE_NIGHT_OF_DECREE && day >= 20) {
//            return day >= 20 && day % 2 == 0 && hour > 18;
            boolean firstCond = day % 2 == 0 && hour > 18; //after 6 pm
            boolean secondCond = day % 2 == 1 && hour < 5; // before 5 am
            return firstCond || secondCond;
        }
        return day == sDay;
    }

    public String getName() {
        return Utils.getStringArray(R.array.islamic_events_names)[ordinal()];
    }
}

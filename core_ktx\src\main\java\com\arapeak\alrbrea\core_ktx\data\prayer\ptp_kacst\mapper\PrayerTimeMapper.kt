package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper

import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaInstant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Calendar
import java.util.GregorianCalendar

class PrayerTimeMapper {

    fun map(instant: Instant): Calendar {
        val zdt = ZonedDateTime.ofInstant(instant.toJavaInstant(), ZoneId.systemDefault());
        return GregorianCalendar.from(zdt)
    }


    fun map(time: Double): Calendar {
        val calendar = Calendar.getInstance()
        calendar.time = Calendar.getInstance().time
        var i: Int = time.toInt()
        var i2: Int = ((time - i) * 60.0).toInt()
        if (i2 == 60) {
            i ++
            i2 = 0
        }
        if (i2 < 0) {
            i2 = - i2
        }
        calendar[11] = i
        calendar[12] = i2
        calendar[13] = 0
        calendar[14] = 0
        return calendar
    }
}
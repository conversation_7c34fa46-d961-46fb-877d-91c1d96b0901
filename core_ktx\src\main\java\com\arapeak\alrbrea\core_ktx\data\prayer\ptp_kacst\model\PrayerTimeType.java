package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model;

import java.util.Calendar;

public class PrayerTimeType {
    public static final int ASER_ID_HANAFI = 0;
    public static final int ASER_ID_SHAFI = 1;
    public static final int METHOD_ID_EGYPTIAN_GENERAL = 2;
    public static final int METHOD_ID_ISLAMIC_SOCITY_OF_NORTH_AMERICA = 4;
    public static final int METHOD_ID_MINISTRY_OF_AWQAF_AND_ISLAMIC_AFFAIRS = 6; // kuwait
    public static final int METHOD_ID_MUSLIM_WORLD_LEAGUE = 1;
    public static final int METHOD_ID_UMMALQURA = 0;
    public static final int METHOD_ID_UNION_OF_ISLAMIC_ORGANAIZATIONS = 5; //France
    public static final int METHOD_ID_UNIVERSITY_OF_ISLAMIC_SCIENCES = 3; // Karachi
    public double Aser;
    public double EidAngle;
    public double FajarAngle;
    public double HeightDiffEast;
    public double HeightDiffWest;
    public double IshaAngle;
    public double IshaFixedSunset;
    public double RabitaSuggestedAngle;
    public double SafetyTime;
    public double TimeZone;

    public static PrayerTimeType DefaultUmmulQuraTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.32288591161895097d;
        prayerTimeType.IshaAngle = 0.0d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 1.5d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultMuslimWorldLeagueTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.3141592653589793d;
        prayerTimeType.IshaAngle = 0.29670597283903605d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 0.0d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultEgyptianGeneralTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.34033920413889424d;
        prayerTimeType.IshaAngle = 0.30543261909900765d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 0.0d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultUniversityOfIslamicSciencesTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.3141592653589793d;
        prayerTimeType.IshaAngle = 0.3141592653589793d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 0.0d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultIslamicSocietyOfNorthAmericaTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.2617993877991494d;
        prayerTimeType.IshaAngle = 0.2617993877991494d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 0.0d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultUnionOfIslamicOrganaizationsTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.20943951023931956d;
        prayerTimeType.IshaAngle = 0.20943951023931956d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 0.0d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType DefaultMinistryOfAwqafAndIslamicAffairsTimeType() {
        PrayerTimeType prayerTimeType = new PrayerTimeType();
        prayerTimeType.SafetyTime = 0.016388d;
        prayerTimeType.HeightDiffEast = 0.0d;
        prayerTimeType.HeightDiffWest = 0.0d;
        prayerTimeType.FajarAngle = 0.3141592653589793d;
        prayerTimeType.IshaAngle = 0.30543261909900765d;
        prayerTimeType.TimeZone = (Calendar.getInstance().getTimeZone().getRawOffset() / 360000.0d) / 10.0d;
        prayerTimeType.Aser = 1.0d;
        prayerTimeType.RabitaSuggestedAngle = 0.7853981633974483d;
        prayerTimeType.IshaFixedSunset = 0.0d;
        prayerTimeType.EidAngle = 0.07330382858376185d;
        return prayerTimeType;
    }

    public static PrayerTimeType getPrayerTimeType(int i) {
        switch (i) {
            case 0:
                return DefaultUmmulQuraTimeType();
            case 1:
                return DefaultMuslimWorldLeagueTimeType();
            case 2:
                return DefaultEgyptianGeneralTimeType();
            case 3:
                return DefaultUniversityOfIslamicSciencesTimeType();
            case 4:
                return DefaultIslamicSocietyOfNorthAmericaTimeType();
            case 5:
                return DefaultUnionOfIslamicOrganaizationsTimeType();
            case 6:
                return DefaultMinistryOfAwqafAndIslamicAffairsTimeType();
            default:
                return DefaultUmmulQuraTimeType();
        }
    }

    public static PrayerTimeType getPrayerTimeType(int i, int i2) {
        PrayerTimeType prayerTimeType = getPrayerTimeType(i);
        prayerTimeType.Aser = i2;
        return prayerTimeType;
    }
}
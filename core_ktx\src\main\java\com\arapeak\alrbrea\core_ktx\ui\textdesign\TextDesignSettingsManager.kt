package com.arapeak.alrbrea.core_ktx.ui.textdesign

import android.content.Context
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignPosition
import com.arapeak.alrbrea.core_ktx.repo.TextDesignRepo


class TextDesignSettingsManager {
    val SIZE_FACTOR_ADD = 1.05f
    val SIZE_FACTOR_MINUS = 0.8f
    val SIZE_MAX = 800
    val SIZE_MIN = 20
    val PB_DELAY = 1 * 1000

    val repo: TextDesignRepo = TextDesignRepo()
    val textSizes: TextDesignSizes = TextDesignSizes()
    val prvManager: TextDesignPreviewManager = TextDesignPreviewManager()


    fun savePosition(context: Context, left: Int, top: Int, width: Int, height: Int, secondLine: Boolean) {
        val xPercent: Double = left.toDouble() / width
        val yPercent: Double = top.toDouble() / height

        repo.updatePosition(context, TextDesignPosition(x = xPercent, y = yPercent), secondLine)
    }


    fun getPreviewImagePath(context: Context, isLand: Boolean = false): String {
        return prvManager.getPreviewImagePath(context, isLand) ?: ""
    }

    fun updateText() {
        updateTextCallback.invoke()
    }

    fun setUpdateText(u: () -> String) {
        this.updateTextCallback = u
    }

    private var updateTextCallback: () -> String = { "" }


}
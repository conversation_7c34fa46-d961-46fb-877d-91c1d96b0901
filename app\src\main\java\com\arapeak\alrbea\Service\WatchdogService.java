//package com.arapeak.alrbea.Service;
//
//import android.app.Notification;
//import android.app.NotificationChannel;
//import android.app.NotificationManager;
//import android.content.Intent;
//import android.os.Build;
//import android.os.IBinder;
//
//import java.util.concurrent.TimeUnit;
//
//public class WatchdogService extends Service {
//
//    private static final String CHANNEL_ID = "WatchdogChannel";
//
//    @Override
//    public void onCreate() {
//        super.onCreate();
//        createNotificationChannel();
//        Notification notification = new Notification.Builder(this, CHANNEL_ID)
//                .setContentTitle("App Watchdog Running")
//                .setSmallIcon(R.drawable.ic_launcher_foreground)
//                .build();
//        startForeground(1, notification);
//
//        scheduleRepeatingTask();
//    }
//
//    private void createNotificationChannel() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            NotificationChannel channel = new NotificationChannel(
//                    CHANNEL_ID,
//                    "Watchdog Service Channel",
//                    NotificationManager.IMPORTANCE_LOW
//            );
//            NotificationManager manager = getSystemService(NotificationManager.class);
//            manager.createNotificationChannel(channel);
//        }
//    }
//
//    private void scheduleRepeatingTask() {
//        WorkManager workManager = WorkManager.getInstance(this);
//
//        PeriodicWorkRequest watchdogWork = new PeriodicWorkRequest.Builder(WatchdogWorker.class, 3, TimeUnit.MINUTES)
//                .build();
//
//        workManager.enqueueUniquePeriodicWork("AppWatchdog", ExistingPeriodicWorkPolicy.REPLACE, watchdogWork);
//    }
//
//    @Override
//    public IBinder onBind(Intent intent) {
//        return null;
//    }
//}
package com.arapeak.alrbea.Enum;

import androidx.annotation.StringRes;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod;

public enum PrayerMethod {
    automatic(R.string.automatic_prayer_method, 0),
    ummAlQurra(R.string.umm_al_qura_university_makkah, 1),
    egyptian<PERSON>urvey(R.string.egyptian_general_authority_of_survey, 2),
    karachi(R.string.university_of_islamic_sciences_karachi, 3),
    muslimLeague(R.string.muslim_world_league, 4),
    northAmerica(R.string.islamic_society_of_north_america, 5),
    gulf_region(R.string.gulf_region, 6),
    kuwait(R.string.kuwait, 7),
    //    qatar(R.string.qatar, 8),
    singapore(R.string.majlis_ugama_islam_singapura_singapore, 8),
    france(R.string.union_organization_islamic_de_france, 9),
    turkey(R.string.head_of_religious_affairs_turkey, 10),
    russia(R.string.spiritual_administration_of_muslims_of_russia, 11),
    customCalendar(R.string.custome_calendar, 12);

    public final int value;
    @StringRes
    final int stringRes;

    PrayerMethod(int stringRes, int value) {
        this.stringRes = stringRes;
        this.value = value;
    }

    @Override
    public String toString() {
        return Utils.getString(stringRes);
    }

    public CalculationMethod toKtxMethods() {
        switch (this) {
            case automatic:
                return CalculationMethod.automatic;

            case ummAlQurra:
                return CalculationMethod.ummAlQurra;

            case egyptianSurvey:
                return CalculationMethod.egyptianSurvey;

            case karachi:
                return CalculationMethod.karachi;

            case muslimLeague:
                return CalculationMethod.muslimLeague;

            case northAmerica:
                return CalculationMethod.northAmerica;

            case gulf_region:
                return CalculationMethod.gulf_region;

            case kuwait:
                return CalculationMethod.kuwait;

            case singapore:
                return CalculationMethod.singapore;

            case france:
                return CalculationMethod.france;

            case turkey:
                return CalculationMethod.turkey;

            case russia:
                return CalculationMethod.russia;
            case customCalendar:
                return CalculationMethod.customCalendar;
        }
        return CalculationMethod.automatic;
    }
}

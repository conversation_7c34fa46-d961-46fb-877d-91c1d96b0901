package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;

import java.util.Objects;

public class PrayerSystemsSchools {
    private String name;
    private int value;

    public PrayerSystemsSchools(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return Utils.getValueWithoutNull(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (obj == this) return true;
        if (!(obj instanceof PrayerSystemsSchools)) return false;
        PrayerSystemsSchools that = (PrayerSystemsSchools) obj;
        if (that.getName().isEmpty() || getName().isEmpty()) return false;
        return getValue() == that.getValue() &&
                Objects.equals(getName(), that.getName());
    }
}

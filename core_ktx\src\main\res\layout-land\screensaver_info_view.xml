<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:gravity="center"
    android:orientation="vertical"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="4"
        android:gravity="top"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_clock_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            tools:text="AM" />

        <TextView
            android:id="@+id/textClock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            tools:text="00:00" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_date_day"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:text=""
        android:textColor="#FFFFFF"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_clock_type"
        tools:text="Saturday" />

    <TextView
        android:id="@+id/tv_date_hijri"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:text=""
        android:textColor="#FFFFFF"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_date_day"
        tools:text="4 Moharam 1445" />

    <TextView
        android:id="@+id/tv_date_miladi"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:text=""
        android:textColor="#FFFFFF"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_date_hijri"
        tools:text="4 July 2024" />

    <TextView
        android:id="@+id/tv_funeral"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:text="@string/there_is_funeral_x"
        android:textColor="#000000"
        android:background="#ABFFFFFF"
        android:visibility="gone"
        tools:visibility="visible"
        android:textSize="@dimen/_14sdp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_date_hijri" />


</LinearLayout>


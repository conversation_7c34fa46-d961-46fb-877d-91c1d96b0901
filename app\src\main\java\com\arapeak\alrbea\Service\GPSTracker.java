package com.arapeak.alrbea.Service;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.Looper;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.BaseAppCompatActivity;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.orhanobut.hawk.Hawk;

public class GPSTracker {

    private final LocationRequest locationRequest;
    private final LocationCallback locationCallback;
    private final Object locker = new Object();
    private final boolean isGPS = false;
    public Location location;
    FusedLocationProviderClient mFusedLocationClient;
    private BaseAppCompatActivity activity = null;
    private LocationState state = LocationState.NotStarted;
    private Dialog loadingDialog = null;

    @SuppressLint("MissingPermission")
    public GPSTracker(BaseAppCompatActivity activity) {
        this.activity = activity;
        mFusedLocationClient = LocationServices.getFusedLocationProviderClient(activity);
        locationRequest = LocationRequest.create();
        locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        locationRequest.setInterval(20 * 1000);
        locationRequest.setFastestInterval(5 * 1000); // 5 seconds

        locationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(LocationResult locationResult) {
                if (locationResult != null && locationResult.getLocations().size() > 0) {
                    if (mFusedLocationClient != null) {
                        mFusedLocationClient.removeLocationUpdates(locationCallback);
                    }
                    location = locationResult.getLocations().get(0);
//                    saveLocation();
                    setState(LocationState.Received);
                } else
                    setState(LocationState.Failed);
            }
        };
        mFusedLocationClient.requestLocationUpdates(locationRequest, locationCallback, null);
    }

    public LocationState getState() {
        synchronized (locker) {
            return state;
        }
    }

    private void setState(LocationState state) {
        synchronized (locker) {
            this.state = state;
        }
    }

    public boolean isLocationEnabled() {
        LocationManager lm = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
// This is new method provided in API 28
            return lm.isLocationEnabled();
        } else {
// This is Deprecated in API 28
//            int mode = Settings.Secure.getInt(activity.getContentResolver(), Settings.Secure.LOCATION_MODE,
//                    Settings.Secure.LOCATION_MODE_OFF);
            return lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER) || lm.isProviderEnabled(LocationManager.GPS_PROVIDER);
//            return  (mode != Settings.Secure.LOCATION_MODE_OFF);

        }
    }

    public void getLocation(Runnable onSuccessful, Runnable onFail) {
        new Thread(() -> {
            Looper.prepare();
            long timeUntilTimeOut = System.currentTimeMillis() + ConstantsOfApp.MINUTES_MILLI_SECOND;
            try {
//                if(!isLocationEnabled()){
//                    activity.safeRunOnUi(()-> {
//                        Utils.showFailAlert(activity,Utils.getString(R.string.failed_to_load_location),Utils.getString(R.string.failed_to_load_location_info));
//                        if(onFail != null)
//                            onFail.run();
//                    });
//                    Thread.sleep(1000);
//                    activity.startActivity(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS));
//                    return;
//                }

                activity.safeRunOnUi(() -> {
                    loadingDialog = Utils.initLoadingDialogWithString(activity, Utils.getString(R.string.loading_location));
                    loadingDialog.show();
                });
                setState(LocationState.Loading);
                getLastLocation();
                while (getState() == LocationState.Loading && System.currentTimeMillis() < timeUntilTimeOut) {
                    Thread.sleep(5000);
                }
                activity.safeRunOnUi(() -> loadingDialog.dismiss());
                if (getState() == LocationState.Received) {
                    saveLocation();
                    activity.safeRunOnUi(() -> Utils.showSuccessAlert(activity, Utils.getString(R.string.your_location_has_been_successfully_updated)));

//                    if(Utils.getCountryCode(location.getLatitude(),location.getLongitude()) != "om"){
//                        saveLocation();
//                        activity.safeRunOnUi(()-> Utils.showSuccessAlert(activity, Utils.getString(R.string.your_location_has_been_successfully_updated)));
//                    }
//                    else{
//                        activity.safeRunOnUi(()-> Utils.showFailAlert(activity,"خطأ", "يرجي تحديد المدينة يدويا لدولة عمان"));
//                        setState(LocationState.Failed);
//                    }
                } else {
                    activity.safeRunOnUi(() -> Utils.showFailAlert(activity, Utils.getString(R.string.failed_to_load_location), Utils.getString(R.string.failed_to_load_location_info)));
                }
                Thread.sleep(2000);
            } catch (Exception e) {
                CrashlyticsUtils.INSTANCE.logException(e);
                activity.safeRunOnUi(() -> loadingDialog.dismiss());
                activity.safeRunOnUi(() -> Utils.showFailAlert(activity, Utils.getString(R.string.failed_to_load_location), Utils.getString(R.string.failed_to_load_location_info)));
            } finally {
                if (onSuccessful != null && getState() == LocationState.Received)
                    onSuccessful.run();
                else if (onFail != null)
                    onFail.run();
            }
        }).start();
    }

    private void saveLocation() {
        HawkSettings.putLatLong(location.getLatitude(), location.getLongitude());
        Hawk.put(ConstantsOfApp.IS_INITIAL_SETUP_KEY, false);
        Hawk.put(ConstantsOfApp.IS_CUSTOM_KEY, false);
        Hawk.put(ConstantsOfApp.IS_UPDATE_PRAY_TIME, true);
    }

    @SuppressLint("MissingPermission")
    private void getLastLocation() {
        if (location != null)
            setState(LocationState.Received);
        else
            mFusedLocationClient.requestLocationUpdates(locationRequest, locationCallback, null);

//        mFusedLocationClient.getLastLocation()
//                .addOnSuccessListener(loc -> {
//                    // GPS location can be null if GPS is switched off
//                    if (loc != null) {
//                        location = loc;
//                        setState(LocationState.Received);
//                    }
//                    else
//                        setState(LocationState.Failed);
//                })
//                .addOnFailureListener(e -> {
//                    Log.d("MapDemoActivity", "Error trying to get last GPS location");
//                    e.printStackTrace();
//                    setState(LocationState.Failed);
//                });
    }

    private enum LocationState {
        NotStarted,
        Loading,
        Received,
        Failed
    }

}
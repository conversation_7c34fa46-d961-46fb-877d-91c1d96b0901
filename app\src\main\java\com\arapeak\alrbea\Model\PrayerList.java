package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class PrayerList {

    @Expose
    @SerializedName("1")
    private List<Prayer> january;
    @Expose
    @SerializedName("2")
    private List<Prayer> february;
    @Expose
    @SerializedName("3")
    private List<Prayer> march;
    @Expose
    @SerializedName("4")
    private List<Prayer> april;
    @Expose
    @SerializedName("5")
    private List<Prayer> may;
    @Expose
    @SerializedName("6")
    private List<Prayer> june;
    @Expose
    @SerializedName("7")
    private List<Prayer> july;
    @Expose
    @SerializedName("8")
    private List<Prayer> august;
    @Expose
    @SerializedName("9")
    private List<Prayer> september;
    @Expose
    @SerializedName("10")
    private List<Prayer> october;
    @Expose
    @SerializedName("11")
    private List<Prayer> november;
    @Expose
    @SerializedName("12")
    private List<Prayer> december;

    public List<Prayer> getJanuary() {
        return january;
    }

    public void setJanuary(List<Prayer> january) {
        this.january = january;
    }

    public List<Prayer> getFebruary() {
        return february;
    }

    public void setFebruary(List<Prayer> february) {
        this.february = february;
    }

    public List<Prayer> getMarch() {
        return march;
    }

    public void setMarch(List<Prayer> march) {
        this.march = march;
    }

    public List<Prayer> getApril() {
        return april;
    }

    public void setApril(List<Prayer> april) {
        this.april = april;
    }

    public List<Prayer> getMay() {
        return may;
    }

    public void setMay(List<Prayer> may) {
        this.may = may;
    }

    public List<Prayer> getJune() {
        return june;
    }

    public void setJune(List<Prayer> june) {
        this.june = june;
    }

    public List<Prayer> getJuly() {
        return july;
    }

    public void setJuly(List<Prayer> july) {
        this.july = july;
    }

    public List<Prayer> getAugust() {
        return august;
    }

    public void setAugust(List<Prayer> august) {
        this.august = august;
    }

    public List<Prayer> getSeptember() {
        return september;
    }

    public void setSeptember(List<Prayer> september) {
        this.september = september;
    }

    public List<Prayer> getOctober() {
        return october;
    }

    public void setOctober(List<Prayer> october) {
        this.october = october;
    }

    public List<Prayer> getNovember() {
        return november;
    }

    public void setNovember(List<Prayer> november) {
        this.november = november;
    }

    public List<Prayer> getDecember() {
        return december;
    }

    public void setDecember(List<Prayer> december) {
        this.december = december;
    }

    public List<Prayer> getPrayerTimesThisMonth(int month) {
        switch (month) {
            case 1:
                return getJanuary();
            case 2:
                return getFebruary();
            case 3:
                return getMarch();
            case 4:
                return getApril();
            case 5:
                return getMay();
            case 6:
                return getJune();
            case 7:
                return getJuly();
            case 8:
                return getAugust();
            case 9:
                return getSeptember();
            case 10:
                return getOctober();
            case 11:
                return getNovember();
            default:
                return getDecember();
        }
    }

    public List<Prayer> getAll() {
        List<Prayer> list = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            list.addAll(getPrayerTimesThisMonth(i));
        }
        return list;
    }
}

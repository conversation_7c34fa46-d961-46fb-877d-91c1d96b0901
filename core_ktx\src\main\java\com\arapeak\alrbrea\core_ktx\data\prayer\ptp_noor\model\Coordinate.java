package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


public final class Coordinate {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata */
    private double longitude;

    /* renamed from: b, reason: from kotlin metadata */
    private double latitude;

    /* renamed from: c, reason: from kotlin metadata */
    private float zone;

    public Coordinate() {
    }

    public Coordinate(double longitude, double latitude, float zone) {
        this.longitude = longitude;
        this.latitude = latitude;
        this.zone = zone;
    }

    public final double getLatitude() {
        return this.latitude;
    }

    public final void setLatitude(double d) {
        this.latitude = d;
    }

    public final double getLongitude() {
        return this.longitude;
    }

    public final void setLongitude(double d) {
        this.longitude = d;
    }

    public final float getZone() {
        return this.zone;
    }

    public final void setZone(float f2) {
        this.zone = f2;
    }
}

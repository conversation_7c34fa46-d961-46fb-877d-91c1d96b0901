package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.athkarThemes;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.AzkarTheme;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.CustomView.GridAutofitLayoutManager;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

public class AzkarThemesFragment extends AlrabeeaTimesFragment {
    private View changeThemeView;
    private RecyclerView themesRecyclerView;
    private Button saveButton;
    private Button updateButton;
    private Button upButton;
    private Button downButton;
    private GridAutofitLayoutManager mGridAutofitLayoutManager;
    private Dialog loadingDialog;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        changeThemeView = inflater.inflate(R.layout.fragment_themes, container, false);
        initView();
        SetParameter();
        SetAction();
        return changeThemeView;
    }

    private void initView() {
        themesRecyclerView = changeThemeView.findViewById(R.id.themes_RecyclerView_ThemesFragment);
        saveButton = changeThemeView.findViewById(R.id.save_Button_ThemesFragment);
        updateButton = changeThemeView.findViewById(R.id.update_Button_ThemesFragment);
        upButton = changeThemeView.findViewById(R.id.btn_moveUp);
        downButton = changeThemeView.findViewById(R.id.btn_moveDown);
        mGridAutofitLayoutManager = new GridAutofitLayoutManager(getAppCompatActivity(), 2, GridLayoutManager.VERTICAL, false);
        loadingDialog = Utils.initLoadingDialog(getContext());
    }

    private void SetParameter() {
        if (Utils.isLandscape()) {
            mGridAutofitLayoutManager.setColumnWidth((int) getResources().getDimension(R.dimen.theme_item_width_land));
        } else {
            mGridAutofitLayoutManager.setColumnWidth((int) getResources().getDimension(R.dimen.theme_item_width));
        }
        themesRecyclerView.setLayoutManager(mGridAutofitLayoutManager);
        attachRecyclerViewAthkarAdapter();
    }

    public void attachRecyclerViewAthkarAdapter() {
        final RecyclerView.Adapter adapter = newAdapterAthkar();
        // Scroll to bottom on new messages
        adapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                themesRecyclerView.smoothScrollToPosition(adapter.getItemCount());
            }
        });
        themesRecyclerView.setAdapter(adapter);
    }

    @NonNull
    protected RecyclerView.Adapter<AzkarThemeHolder> newAdapterAthkar() {

        return new RecyclerView.Adapter<AzkarThemeHolder>() {
            @NonNull
            @Override
            public AzkarThemeHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                return new AzkarThemeHolder(
                        LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_list_item_azkar, parent, false)
                );
            }

            @Override
            public void onBindViewHolder(@NonNull AzkarThemeHolder holder, int position) {
                holder.Bind(AzkarTheme.values()[position], getAppCompatActivity(), loadingDialog);
            }

            @Override
            public int getItemCount() {
                return AzkarTheme.values().length;
            }
        };
        /*FirebaseRecyclerOptions<AzkarModel> options =
                new FirebaseRecyclerOptions.Builder<AzkarModel>()
                        .setQuery(sChatQueryAthkar, AzkarModel.class)
                        .setLifecycleOwner(this)
                        .build();

        return new FirebaseRecyclerAdapter<AzkarModel, ThemeHolder>(options) {
            @Override
            public ThemeHolder onCreateViewHolder(ViewGroup parent, int viewType) {
                @LayoutRes
                int layout = Utils.isLandscape() ? R.layout.layout_list_item_theme_land : R.layout.layout_list_item_theme;
                return new ThemeHolder(LayoutInflater.from(parent.getContext()).inflate(layout, parent, false));
            }
            @Override
            protected void onBindViewHolder(@NonNull ThemeHolder holder, int position, @NonNull AzkarModel model) {
                holder.Bind(model,(BaseAppCompatActivity) getAppCompatActivity());
                loadingDialog.dismiss();
                holder.gifImageView.setOnClickListener(v -> {
                    Hawk.put(ConstantsOfApp.APP_THEME_KEY_M,  model.value);
                    attachRecyclerViewAthkarAdapter();

                });
                String fileName = Utils.isLandscape() ? "/yl.gif" : "/y.gif";
                File file = new File(Environment.getExternalStorageDirectory() + "/athkar/" + model.value + fileName);
                if (!file.isFile()) {
                    holder.selectedThemeImageView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {


                            holder.downlo(model.value, new OnSuccessful() {
                                @Override
                                public void onSuccessful(boolean isSuccessful) {
                                    super.onSuccessful(isSuccessful);
                                    if (isSuccessful)
                                        attachRecyclerViewAthkarAdapter();
                                }
                            });

                            if (NetworkUtils.isOnline(getContext())) {

                                try {
                                    loadingDialog.show();
                                    PackageInfo pInfo = getAppCompatActivity().getPackageManager().getPackageInfo(getAppCompatActivity().getPackageName(), 0);
                                    final String versionName = pInfo.versionName;
                                    final int versionCode = pInfo.versionCode;
                                    loadingDialog.show();

                                    AlrabeeaTimesRequests.getAppInfo(getAppCompatActivity()
                                            , GET_APP_INFO
                                            , new OnCompleteListener<AppInfo, Object>() {
                                                @Override
                                                public void onSuccess(final AppInfo appInfo) {
                                                    if (appInfo == null) {
                                                        Utils.showFailAlert(getAppCompatActivity()
                                                                , getString(R.string.there_is_a_problem)
                                                                , getString(R.string.try_again));
                                                    } else {
                                                        if (appInfo.getVersion() > versionCode) {
                                                            Toast.makeText(getContext(),"يجب التحديث",Toast.LENGTH_LONG).show();
                                                        } else {

                                                            if (NetworkUtils.isOnline(getContext())) {
                                                                if(Utils.boolAthkar(model.value)) {
                                                                    holder.downlo(model.value, new OnSuccessful() {
                                                                        @Override
                                                                        public void onSuccessful(boolean isSuccessful) {
                                                                            super.onSuccessful(isSuccessful);
                                                                            if (isSuccessful)

                                                                                attachRecyclerViewAthkarAdapter();
                                                                        }
                                                                    });
                                                                }else {
                                                                    Toast.makeText(getContext(),"قيد التطوير",Toast.LENGTH_LONG).show();
                                                                }
                                                            }else {
                                                                Toast.makeText(getContext(),"لا يوجد انترنت",Toast.LENGTH_LONG).show();
                                                            }
                                                        }
                                                    }
                                                    loadingDialog.dismiss();
                                                }

                                                @Override
                                                public void onFail(Object object) {
                                                    if (object == null) {
                                                        Utils.showFailAlert(getAppCompatActivity()
                                                                , getString(R.string.there_is_a_problem)
                                                                , getString(R.string.try_again));
                                                    } else {
                                                        Utils.showFailAlert(getAppCompatActivity()
                                                                , getString(R.string.there_is_a_problem)
                                                                , "" + object);
                                                    }
                                                    loadingDialog.dismiss();

                                                }
                                            });
                                } catch (PackageManager.NameNotFoundException e) {
                                    Utils.showFailAlert(getAppCompatActivity()
                                            , getString(R.string.there_is_a_problem)
                                            , getString(R.string.try_again));
                                    Log.e(TAG, "Error: " + e.getMessage());
                                    e.printStackTrace();
                                    loadingDialog.dismiss();
                                } catch (Exception e) {
                                    Utils.showFailAlert(getAppCompatActivity()
                                            , getString(R.string.there_is_a_problem)
                                            , getString(R.string.try_again));
                                    Log.e(TAG, "Error: " + e.getMessage());
                                    e.printStackTrace();
                                    loadingDialog.dismiss();
                                }



                            } else {
                                Toast.makeText(getContext(), "يجب توفر الانترنت", Toast.LENGTH_LONG).show();
                            }
                        }
                    });
                }
            }

            @Override
            public void onDataChanged() {
            }
        };*/
    }

    private void SetAction() {
        updateButton.setVisibility(View.GONE);
        saveButton.setOnClickListener(v -> {
            Intent intentMainActivity = new Intent(getAppCompatActivity(), MainActivity.class);
            intentMainActivity.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intentMainActivity);
            getAppCompatActivity().finish();
        });
        upButton.setOnClickListener(v -> moveUp());
        downButton.setOnClickListener(v -> moveDown());
    }

    private void moveDown() {
        try {
            GridLayoutManager lm = (GridLayoutManager) themesRecyclerView.getLayoutManager();
            if (lm != null) {
                int lastItem = lm.findLastCompletelyVisibleItemPosition();
                int count = lm.getItemCount();
                if (lastItem < count - 1)
                    lastItem++;
                themesRecyclerView.smoothScrollToPosition(lastItem);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    private void moveUp() {
        try {
            GridLayoutManager lm = (GridLayoutManager) themesRecyclerView.getLayoutManager();
            if (lm != null) {
                int lastItem = lm.findFirstCompletelyVisibleItemPosition();
                if (lastItem > 0)
                    lastItem--;
                themesRecyclerView.smoothScrollToPosition(lastItem);
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

}

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib

/**
 * This class is used to round prayer times (seconds).
 */
class Rounding(val name: String = "") {
    companion object {
        /**
         * No Rounding. second is set to the amount of computed seconds.
         */
        val NONE = Rounding("NONE")

        /**
         * Normal Rounding. If seconds are equal to 30 or above, add 1 minute. Sets "Time.seconds" to zero.
         */
        val NORMAL = Rounding("NORMAL")

        /**
         * Special Rounding. Similar to normal rounding but we always round down for Shurooq and Imsaak times. (default)
         */
        val SPECIAL = Rounding("SPECIAL")

        /**
         * Aggressive Rounding. Similar to Special Rounding but we add 1 minute if the seconds value are equal to 1 second or more.
         */
        val AGRESSIVE = Rounding("AGRESSIVE")
    }

}

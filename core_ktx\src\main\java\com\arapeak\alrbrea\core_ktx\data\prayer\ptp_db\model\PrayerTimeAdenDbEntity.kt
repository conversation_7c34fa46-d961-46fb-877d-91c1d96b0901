package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_db.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import kotlinx.datetime.LocalTime

@Entity(tableName = "Aden_prayers", primaryKeys = ["date"])
data class PrayerTimeAdenDbEntity(
    val date: String,
    @ColumnInfo(name = "fajr") val fajr: String?,
    @ColumnInfo(name = "sunrise") val sunrise: String?,
    @ColumnInfo(name = "dhur") val dhur: String?,
    @ColumnInfo(name = "asr") val asr: String?,
    @ColumnInfo(name = "maghreb") val maghreb: String?,
    @ColumnInfo(name = "isha") val isha: String?,
) {

    private fun toLocalTime(time: String): LocalTime {
        val split = time.split(":")
        val res = LocalTime(split[0].toInt(), split[1].toInt())
        return res
    }

    fun getFajrTime(): LocalTime {
        fajr?.let {
            return toLocalTime(it)
        }
        return toLocalTime("0:0")
    }

    fun getSunriseTime(): LocalTime {
        sunrise?.let {
            return toLocalTime(it)
        }
        return toLocalTime("0:0")
    }

    fun getDhurTime(): LocalTime {
        dhur?.let {
            return toLocalTime(it)
        }
        return toLocalTime("0:0")
    }

    fun getAsrTime(): LocalTime {
        asr?.let {
            return toLocalTime(it)
        }
        return toLocalTime("0:0")
    }

    fun getMaghrebTime(): LocalTime {
        maghreb?.let {
            return toLocalTime(it)
        }
        return toLocalTime("0:0")
    }

    fun getIshaTime(): LocalTime {
        isha?.let {
            return toLocalTime(it)
        }
        return toLocalTime("0:0")
    }

    fun getPrayerTime(): List<LocalTime> {
        return listOf(getFajrTime(), getSunriseTime(), getDhurTime(), getAsrTime(), getMaghrebTime(), getIshaTime())
    }

    fun getPrayerTimes(): String {
        return getPrayerTime().joinToString(", ") { it.toString() }
    }


}
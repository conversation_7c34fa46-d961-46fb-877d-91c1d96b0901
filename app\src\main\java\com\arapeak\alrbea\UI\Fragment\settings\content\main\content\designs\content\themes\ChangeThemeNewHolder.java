package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.themes;

import android.app.Dialog;
import android.graphics.Color;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.BaseAppCompatActivity;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.downloader.Error;
import com.downloader.OnDownloadListener;
import com.downloader.PRDownloader;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.Picasso;

public class ChangeThemeNewHolder extends RecyclerView.ViewHolder {

    ImageView background_iv;
    View selected_tv;
    Button customize_btn;
    UITheme theme;
    ThemeModel themeModel;
    BaseAppCompatActivity activity;
    volatile int failedDownloads = 0;
    volatile int currentDownloads = 0;
    Dialog loadingDialog = null;

    public ChangeThemeNewHolder(@NonNull View view) {
        super(view);
        background_iv = view.findViewById(R.id.imageTheme_ImageView_ThemeViewHolder);
        selected_tv = view.findViewById(R.id.selectedTheme_View_ThemeViewHolder);
        customize_btn = view.findViewById(R.id.customize_ThemeViewHolder);
        setAction();
    }

    private void setAction() {
        background_iv.setOnClickListener(i -> {
            downloadAllFiles();
        });
        customize_btn.setOnClickListener(i -> {
            Utils.loadFragment(new CustomTheme(theme), activity, 0);
        });
    }

    private void downloadAllFiles() {
        if (isActive()) return;
        if (loadingDialog != null)
            loadingDialog.show();

        if (theme == UITheme.CUSTOM_FIREBASE) {
            currentDownloads = 4;
            failedDownloads = 0;
//            String folder = Environment.getExternalStorageDirectory()+"/theme_model/";
            String folder = Utils.getDownloadPath() + "/theme_model/";

            downloadFile(themeModel.bg_port, folder, "port.png");
            downloadFile(themeModel.bg_land, folder, "land.png");
            downloadFile(themeModel.bg_date_port, folder, "date_port.png");
            downloadFile(themeModel.bg_date_land, folder, "date_land.png");
            return;
        }
        selectAsDefaultTheme();
        if (loadingDialog != null)
            loadingDialog.dismiss();

        /*new Thread(()->{
            try{
                if (theme != currentTheme) {
                    if (theme > 13) {
                        String folder = Environment.getExternalStorageDirectory()+"/theme_model/";
                        downloadFile(themeModel.bg_port,folder,"port.png");
                        downloadFile(themeModel.bg_land,folder,"land.png");
                        downloadFile(themeModel.bg_date_port,folder,"date_port.png");
                        downloadFile(themeModel.bg_date_land,folder,"date_land.png");

                        Hawk.put("14_show_ikama",themeModel.showIkama);
                        Hawk.put("14_move_date_to_up",themeModel.move_date_to_up);
                        Hawk.put("14_color1",Color.parseColor(themeModel.color1));
                        Hawk.put("14_color2", Color.parseColor(themeModel.color2));
                        Hawk.put("14_background_portrait",Environment.getExternalStorageDirectory()+"/theme_model/port.png");
                        Hawk.put("14_background_landscape",Environment.getExternalStorageDirectory()+"/theme_model/land.png");
                        Hawk.put("14_date_background_portrait",Environment.getExternalStorageDirectory()+"/theme_model/date_port.png");
                        Hawk.put("14_date_background_landscape",Environment.getExternalStorageDirectory()+"/theme_model/date_land.png");

                    }
                    activity.runOnUiThread(this::selectAsDefaultTheme);
                }
            }
            catch (Exception e){
                e.printStackTrace();
                activity.runOnUiThread(()->{
                    Utils.showFailAlert(activity,"خطأ","فشل تحميل ملفات القالب");
                    Toast.makeText(activity.getBaseContext(), "فشل تحميل ملفات القالب", Toast.LENGTH_LONG).show();
                });
            }
            finally {
                activity.runOnUiThread(()->{
                    if(loadingDialog != null)
                        loadingDialog.dismiss();
                });
            }
        }).start();*/
    }

    private void downloadFile(String url, String dirPath, String fileName) {
        PRDownloader.download(url, dirPath, fileName).build().start(new OnDownloadListener() {
            @Override
            public void onDownloadComplete() {
                currentDownloads--;
                if (currentDownloads <= 0) downloadEnded();
            }

            @Override
            public void onError(Error error) {
                CrashlyticsUtils.INSTANCE.logException(error.getConnectionException());

                currentDownloads--;
                failedDownloads++;
                if (currentDownloads <= 0) downloadEnded();
            }
        });
    }

    /*private void downloadFile(String url,String dirPath,String fileName) throws Exception {
        AtomicBoolean isLoading = new AtomicBoolean(true);
        AtomicBoolean isDownloaded = new AtomicBoolean(false);
        PRDownloader.download(url,dirPath,fileName).build().start(new OnDownloadListener() {
            @Override
            public void onDownloadComplete() {
                isDownloaded.set(true);
                isLoading.set(false);
            }

            @Override
            public void onError(Error error) {
                error.getConnectionException().printStackTrace();
                isDownloaded.set(false);
                isLoading.set(false);
            }
        });
        while(isLoading.get())
            Thread.sleep(100);
        if(!isDownloaded.get())
            throw new Exception("Failed To Download Theme Files...");
    }*/

    private void downloadEnded() {
        if (failedDownloads > 0) {
            Utils.showFailAlert(activity, "خطأ", "فشل تحميل ملفات القالب");
            Toast.makeText(activity.getBaseContext(), "فشل تحميل ملفات القالب", Toast.LENGTH_LONG).show();
        } else {
            String folder = Utils.getDownloadPath() + "/theme_model/";

            String th = theme.name();
            Hawk.put(th + "_show_ikama", themeModel.showIkama);
            Hawk.put(th + "_move_date_to_up", themeModel.move_date_to_up);
            Hawk.put(th + "_color1", Color.parseColor(themeModel.color1));
            Hawk.put(th + "_color2", Color.parseColor(themeModel.color2));
            Hawk.put(th + "_background_portrait", folder + "port.png");
            Hawk.put(th + "_background_landscape", folder + "land.png");
            Hawk.put(th + "_date_background_portrait", folder + "date_port.png");
            Hawk.put(th + "_date_background_landscape", folder + "date_land.png");
            selectAsDefaultTheme();
        }
        if (loadingDialog != null)
            loadingDialog.dismiss();
    }

    private void selectAsDefaultTheme() {
        UITheme currentTheme = HawkSettings.getCurrentTheme();
        int oldPos = currentTheme.ordinal();
        if (currentTheme == UITheme.CUSTOM_FIREBASE)
            oldPos += HawkSettings.getCurrentFirebaseThemeIndex();
        HawkSettings.setCurrentTheme(theme);
        if (theme == UITheme.CUSTOM_FIREBASE)
            HawkSettings.setCurrentFirebaseThemeIndex(getAbsoluteAdapterPosition() - theme.ordinal());

        RecyclerView.Adapter adapter = this.getBindingAdapter();
        if (adapter != null) {
            adapter.notifyItemChanged(oldPos);
            adapter.notifyItemChanged(getAbsoluteAdapterPosition());
        }
    }

    public void Bind(UITheme theme, AppCompatActivity activity) {
        this.activity = (BaseAppCompatActivity) activity;
        this.theme = theme;
        loadBackground();
        setVisibility();
    }

    public void Bind(ThemeModel themeModel, Dialog loadingDialog, AppCompatActivity activity) {
        this.activity = (BaseAppCompatActivity) activity;
        this.theme = UITheme.CUSTOM_FIREBASE;
        this.themeModel = themeModel;
        this.loadingDialog = loadingDialog;
        loadBackground();
        setVisibility();
    }

    private void loadBackground() {
        boolean isLand = activity.isLandscape();
        @DrawableRes int bg_id;
        switch (theme) {
            case BROWN:
                bg_id = isLand ? R.drawable.img_theme_brown_land : R.drawable.img_theme_brown;
                break;
            case DARK_GREEN:
                bg_id = isLand ? R.drawable.img_theme_dark_green_land : R.drawable.img_theme_dark_green;
                break;
            case DARK_GRAY:
                bg_id = isLand ? R.drawable.img_theme_dark_gray_land : R.drawable.img_theme_dark_gray;
                break;
            case BLUE:
                bg_id = isLand ? R.drawable.img_theme_blue_land : R.drawable.img_theme_blue;
                break;
            case RED:
                bg_id = isLand ? R.drawable.img_theme_red_land : R.drawable.img_theme_red;
                break;
            case GREEN:
                bg_id = isLand ? R.drawable.img_theme_green_land : R.drawable.img_theme_green;
                break;
            case BROWN_NEW:
                bg_id = isLand ? R.drawable.img_theme_brown_new_land : R.drawable.img_theme_brown_new;
                break;
            case BLUE_NEW:
                bg_id = isLand ? R.drawable.img_theme_blue_new_land : R.drawable.img_theme_blue_new;
                break;
            case WHITE:
                bg_id = isLand ? R.drawable.img_theme_white_land : R.drawable.img_theme_white;
                break;
            case BLUE_LET:
                bg_id = isLand ? R.drawable.img_theme_blue_lett_land : R.drawable.img_theme_blue_lett;
                break;
            case NEW_GREEN:
                bg_id = isLand ? R.drawable.img_theme_new_green_land : R.drawable.img_theme_new_green;
                break;
            case WHITE_NEW:
                bg_id = isLand ? R.drawable.img_theme_white_new_land : R.drawable.img_theme_white_new;
                break;
            case BROWN_NEW_3:
                bg_id = isLand ? R.drawable.theme_brown_3_landscape : R.drawable.theme_brown_3_portirait;
                break;
            case CUSTOM_1:
                bg_id = isLand ? R.drawable.bg_custom_theme_land : R.drawable.bg_custom_theme;
                break;
            default:
                background_iv.setBackgroundResource(0);
                Picasso.get().load(isLand ? themeModel.bg_temp_land : themeModel.bg_temp_port).into(background_iv);
                return;
        }
        Picasso.get().load(bg_id).into(background_iv);
//        background_iv.setBackgroundResource(bg_id);
    }

    private void setVisibility() {
        selected_tv.setVisibility(isActive() ? View.VISIBLE : View.GONE);
    }

    private boolean isActive() {
        if (!theme.isActive())
            return false;
        if (theme == UITheme.CUSTOM_FIREBASE) {
            int ordinal = theme.ordinal();
            int currentIndex = HawkSettings.getCurrentFirebaseThemeIndex();
            int position = getAbsoluteAdapterPosition();
            return ordinal + currentIndex == position;
        }
        return true;
    }
}

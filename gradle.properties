# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this files.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The settingAlrabeeaTimes is particularly useful for tweaking memory settings.
#org.gradle.jvmargs=-Xmx1536m
android.enableJetifier=true
android.useAndroidX=true
android.injected.androidTest.leaveApksInstalledAfterRun=true
#org.gradle.jvmargs=-Xmx1024m
#add tags to fix jdk integrati
org.gradle.jvmargs=-Xmx1024m --add-exports=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED


ALREBEA_KEY_PASSWORD=commander451
ALREBEA_KEY_ALIAS=key0
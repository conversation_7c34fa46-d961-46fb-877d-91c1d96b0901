package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import kotlin.jvm.internal.Intrinsics;


public final class Mazhab {

    public static final String CityType_1 = "1";
    public static final String CityType_2 = "2";
    @Nullable
    private Type f3758a;


    public Mazhab() {
        this.f3758a = Type.Default;
    }

    public Mazhab(@NotNull Type T) {
        Intrinsics.checkNotNullParameter(T, "T");
        this.f3758a = T;
    }

    @Nullable
    /* renamed from: mazhabInt, reason: from getter */
    public final Type getF3758a() {
        return this.f3758a;
    }

    @NotNull
    public final String mazhabString() {
        if (this.f3758a == Type.Default) {
            return "Default";
        }
        return "Hanafi";
    }

    public final void setMazhab(@NotNull Type mazhab) {
        Intrinsics.checkNotNullParameter(mazhab, "mazhab");
        this.f3758a = mazhab;
    }

    public final int setMazhabString(@NotNull String stype) {
        Intrinsics.checkNotNullParameter(stype, "stype");
        if (Intrinsics.areEqual(stype, "0")) {
            return 0;
        }
        if (Intrinsics.areEqual(stype, CityType_1)) {
            return 1;
        }
        return -1;
    }

    public final int toInt() {
        if (this.f3758a == Type.Default) {
            return 0;
        }
        return 1;
    }

    @Nullable
    public final Type type() {
        return getF3758a();
    }

    public final void setMazhab(int type) {
        this.f3758a = Type.values()[type];
    }

    public final void setMazhab(@NotNull String mazhab) {
        Intrinsics.checkNotNullParameter(mazhab, "mazhab");
        try {
            if (Intrinsics.areEqual(mazhab, "Default")) {
                this.f3758a = Type.Default;
            } else {
                this.f3758a = Type.Hanafi;
            }
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public enum Type {
        Default,
        Hanafi
    }
}

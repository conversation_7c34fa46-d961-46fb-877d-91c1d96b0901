package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import org.jetbrains.annotations.NotNull;

import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;


public final class PrayerTime_Database extends PrayerTime {

    @NotNull
    public static final Companion INSTANCE = new Companion(null);
    private static double H = -1.0d;
    private static double I = -1.0d;
    private static double J = -1.0d;
    private static double K = -1.0d;
    private static double L = -1.0d;
    private static double M = -1.0d;
    private static double N = -1.0d;
    private static double O = -1.0d;
    private static double P = -1.0d;
    private static double Q = -1.0d;
    private static double R = -1.0d;
    private static double S = -1.0d;
    private static double T = -1.0d;
    private static double U = -1.0d;

    @NotNull
    private static String V = "-1";

    /* renamed from: G, reason: from kotlin metadata */
    @NotNull
    private City m_city;


    public PrayerTime_Database(@NotNull City m_city, int i, int i2, int i3) {
        Intrinsics.checkNotNullParameter(m_city, "m_city");
        this.m_city = m_city;
        setM_date(new MiladiDate(i, i2, i3));
        setM_season(new Season());
        setM_hour(new Hour());
    }

    /* JADX WARN: Removed duplicated region for block: B:37:0x0299 A[Catch: Exception -> 0x030d, TryCatch #2 {Exception -> 0x030d, blocks: (B:4:0x000c, B:5:0x0011, B:69:0x0023, B:72:0x0036, B:73:0x0055, B:75:0x0062, B:77:0x007b, B:78:0x0080, B:80:0x0097, B:81:0x009c, B:83:0x00b1, B:84:0x00b6, B:86:0x00cd, B:87:0x00d2, B:89:0x00e9, B:90:0x00ee, B:92:0x0105, B:93:0x0108, B:94:0x00ec, B:95:0x00d0, B:96:0x00b4, B:97:0x009a, B:98:0x007e, B:99:0x010a, B:102:0x011b, B:103:0x0121, B:105:0x0127, B:109:0x0146, B:110:0x0155, B:112:0x016f, B:115:0x0176, B:116:0x018d, B:118:0x019a, B:120:0x01b1, B:121:0x01b4, B:122:0x01b6, B:124:0x0182, B:127:0x01bb, B:129:0x01c0, B:131:0x0046, B:9:0x01c5, B:11:0x01d6, B:13:0x01e7, B:15:0x01f8, B:17:0x0209, B:19:0x021a, B:21:0x022b, B:22:0x023a, B:24:0x0249, B:27:0x0260, B:29:0x026f, B:31:0x027d, B:35:0x028f, B:37:0x0299, B:39:0x02a7, B:43:0x02b8, B:45:0x02c6, B:49:0x02d7, B:51:0x02e5, B:56:0x02f7, B:61:0x0233, B:62:0x0222, B:63:0x0211, B:64:0x0200, B:65:0x01ef, B:66:0x01de, B:67:0x01cd, B:135:0x010f, B:133:0x0114, B:136:0x000f), top: B:2:0x000a, inners: #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:45:0x02c6 A[Catch: Exception -> 0x030d, TryCatch #2 {Exception -> 0x030d, blocks: (B:4:0x000c, B:5:0x0011, B:69:0x0023, B:72:0x0036, B:73:0x0055, B:75:0x0062, B:77:0x007b, B:78:0x0080, B:80:0x0097, B:81:0x009c, B:83:0x00b1, B:84:0x00b6, B:86:0x00cd, B:87:0x00d2, B:89:0x00e9, B:90:0x00ee, B:92:0x0105, B:93:0x0108, B:94:0x00ec, B:95:0x00d0, B:96:0x00b4, B:97:0x009a, B:98:0x007e, B:99:0x010a, B:102:0x011b, B:103:0x0121, B:105:0x0127, B:109:0x0146, B:110:0x0155, B:112:0x016f, B:115:0x0176, B:116:0x018d, B:118:0x019a, B:120:0x01b1, B:121:0x01b4, B:122:0x01b6, B:124:0x0182, B:127:0x01bb, B:129:0x01c0, B:131:0x0046, B:9:0x01c5, B:11:0x01d6, B:13:0x01e7, B:15:0x01f8, B:17:0x0209, B:19:0x021a, B:21:0x022b, B:22:0x023a, B:24:0x0249, B:27:0x0260, B:29:0x026f, B:31:0x027d, B:35:0x028f, B:37:0x0299, B:39:0x02a7, B:43:0x02b8, B:45:0x02c6, B:49:0x02d7, B:51:0x02e5, B:56:0x02f7, B:61:0x0233, B:62:0x0222, B:63:0x0211, B:64:0x0200, B:65:0x01ef, B:66:0x01de, B:67:0x01cd, B:135:0x010f, B:133:0x0114, B:136:0x000f), top: B:2:0x000a, inners: #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:51:0x02e5 A[Catch: Exception -> 0x030d, TryCatch #2 {Exception -> 0x030d, blocks: (B:4:0x000c, B:5:0x0011, B:69:0x0023, B:72:0x0036, B:73:0x0055, B:75:0x0062, B:77:0x007b, B:78:0x0080, B:80:0x0097, B:81:0x009c, B:83:0x00b1, B:84:0x00b6, B:86:0x00cd, B:87:0x00d2, B:89:0x00e9, B:90:0x00ee, B:92:0x0105, B:93:0x0108, B:94:0x00ec, B:95:0x00d0, B:96:0x00b4, B:97:0x009a, B:98:0x007e, B:99:0x010a, B:102:0x011b, B:103:0x0121, B:105:0x0127, B:109:0x0146, B:110:0x0155, B:112:0x016f, B:115:0x0176, B:116:0x018d, B:118:0x019a, B:120:0x01b1, B:121:0x01b4, B:122:0x01b6, B:124:0x0182, B:127:0x01bb, B:129:0x01c0, B:131:0x0046, B:9:0x01c5, B:11:0x01d6, B:13:0x01e7, B:15:0x01f8, B:17:0x0209, B:19:0x021a, B:21:0x022b, B:22:0x023a, B:24:0x0249, B:27:0x0260, B:29:0x026f, B:31:0x027d, B:35:0x028f, B:37:0x0299, B:39:0x02a7, B:43:0x02b8, B:45:0x02c6, B:49:0x02d7, B:51:0x02e5, B:56:0x02f7, B:61:0x0233, B:62:0x0222, B:63:0x0211, B:64:0x0200, B:65:0x01ef, B:66:0x01de, B:67:0x01cd, B:135:0x010f, B:133:0x0114, B:136:0x000f), top: B:2:0x000a, inners: #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:56:0x02f7 A[Catch: Exception -> 0x030d, TRY_LEAVE, TryCatch #2 {Exception -> 0x030d, blocks: (B:4:0x000c, B:5:0x0011, B:69:0x0023, B:72:0x0036, B:73:0x0055, B:75:0x0062, B:77:0x007b, B:78:0x0080, B:80:0x0097, B:81:0x009c, B:83:0x00b1, B:84:0x00b6, B:86:0x00cd, B:87:0x00d2, B:89:0x00e9, B:90:0x00ee, B:92:0x0105, B:93:0x0108, B:94:0x00ec, B:95:0x00d0, B:96:0x00b4, B:97:0x009a, B:98:0x007e, B:99:0x010a, B:102:0x011b, B:103:0x0121, B:105:0x0127, B:109:0x0146, B:110:0x0155, B:112:0x016f, B:115:0x0176, B:116:0x018d, B:118:0x019a, B:120:0x01b1, B:121:0x01b4, B:122:0x01b6, B:124:0x0182, B:127:0x01bb, B:129:0x01c0, B:131:0x0046, B:9:0x01c5, B:11:0x01d6, B:13:0x01e7, B:15:0x01f8, B:17:0x0209, B:19:0x021a, B:21:0x022b, B:22:0x023a, B:24:0x0249, B:27:0x0260, B:29:0x026f, B:31:0x027d, B:35:0x028f, B:37:0x0299, B:39:0x02a7, B:43:0x02b8, B:45:0x02c6, B:49:0x02d7, B:51:0x02e5, B:56:0x02f7, B:61:0x0233, B:62:0x0222, B:63:0x0211, B:64:0x0200, B:65:0x01ef, B:66:0x01de, B:67:0x01cd, B:135:0x010f, B:133:0x0114, B:136:0x000f), top: B:2:0x000a, inners: #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:58:? A[RETURN, SYNTHETIC] */
    @Override // com.alawail.prayertimes.moazen.PrayerTime
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void calculate(boolean r9, boolean r10) {
        /*
            Method dump skipped, instructions count: 786
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.alawail.prayertimes.moazen.PrayerTime_Database.calculate(boolean, boolean):void");
    }


//    private static void b(boolean z) {
//        if (z) {
//            PrayerTime.Companion companion = PrayerTime.INSTANCE;
//            double fajr = companion.getFajr();
//            double d = 60;
//            Double.isNaN(d);
//            Double.isNaN(d);
//            companion.setFajr(fajr + d);
//            return;
//        }
//        PrayerTime.Companion companion2 = PrayerTime.INSTANCE;
//        double fajr_ikama = companion2.getFajr_ikama();
//        double d2 = 60;
//        Double.isNaN(d2);
//        Double.isNaN(d2);
//        companion2.setFajr_ikama(fajr_ikama + d2);
//    }
//
//    private static void c(boolean z) {
//        if (z) {
//            PrayerTime.Companion companion = PrayerTime.INSTANCE;
//            double isha = companion.getIsha();
//            double d = 60;
//            Double.isNaN(d);
//            Double.isNaN(d);
//            companion.setIsha(isha + d);
//            return;
//        }
//        PrayerTime.Companion companion2 = PrayerTime.INSTANCE;
//        double isha_ikama = companion2.getIsha_ikama();
//        double d2 = 60;
//        Double.isNaN(d2);
//        Double.isNaN(d2);
//        companion2.setIsha_ikama(isha_ikama + d2);
//    }
//
//    private static void d(boolean z) {
//        if (z) {
//            PrayerTime.Companion companion = PrayerTime.INSTANCE;
//            double jomo3a = companion.getJomo3a();
//            double d = 60;
//            Double.isNaN(d);
//            Double.isNaN(d);
//            companion.setJomo3a(jomo3a + d);
//            return;
//        }
//        PrayerTime.Companion companion2 = PrayerTime.INSTANCE;
//        double jomo3a_ikama = companion2.getJomo3a_ikama();
//        double d2 = 60;
//        Double.isNaN(d2);
//        Double.isNaN(d2);
//        companion2.setJomo3a_ikama(jomo3a_ikama + d2);
//    }
//
//    private static void e(boolean z) {
//        if (z) {
//            PrayerTime.Companion companion = PrayerTime.INSTANCE;
//            double maghrib = companion.getMaghrib();
//            double d = 60;
//            Double.isNaN(d);
//            Double.isNaN(d);
//            companion.setMaghrib(maghrib + d);
//            return;
//        }
//        PrayerTime.Companion companion2 = PrayerTime.INSTANCE;
//        double maghrib_ikama = companion2.getMaghrib_ikama();
//        double d2 = 60;
//        Double.isNaN(d2);
//        Double.isNaN(d2);
//        companion2.setMaghrib_ikama(maghrib_ikama + d2);
//    }
//
//    private static void f(boolean z) {
//        if (z) {
//            PrayerTime.Companion companion = PrayerTime.INSTANCE;
//            double shrouk = companion.getShrouk();
//            double d = 60;
//            Double.isNaN(d);
//            Double.isNaN(d);
//            companion.setShrouk(shrouk + d);
//            return;
//        }
//        PrayerTime.Companion companion2 = PrayerTime.INSTANCE;
//        double shrouk_ikama = companion2.getShrouk_ikama();
//        double d2 = 60;
//        Double.isNaN(d2);
//        Double.isNaN(d2);
//        companion2.setShrouk_ikama(shrouk_ikama + d2);
//    }
//
//    private static void g(boolean z) {
//        if (z) {
//            PrayerTime.Companion companion = PrayerTime.INSTANCE;
//            double zuhr = companion.getZuhr();
//            double d = 60;
//            Double.isNaN(d);
//            Double.isNaN(d);
//            companion.setZuhr(zuhr + d);
//            return;
//        }
//        PrayerTime.Companion companion2 = PrayerTime.INSTANCE;
//        double zuhr_ikama = companion2.getZuhr_ikama();
//        double d2 = 60;
//        Double.isNaN(d2);
//        Double.isNaN(d2);
//        companion2.setZuhr_ikama(zuhr_ikama + d2);
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time asrTime(boolean isAzan) {
//        if (isAzan) {
//            return new Time_Database(PrayerTime.INSTANCE.getAsr(), true, getM_hour());
//        }
//        return new Time_Database(PrayerTime.INSTANCE.getAsr_ikama(), true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time asrTimeIkama(double ikama) {
//        return new Time_Database(PrayerTime.INSTANCE.getAsr() + ikama, true, getM_hour());
//    }

    public static final class Companion {
        private Companion() {
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        public final double getAsr_db() {
            return PrayerTime_Database.K;
        }

        public final void setAsr_db(double d) {
            PrayerTime_Database.K = d;
        }

        public final double getAsr_ikama_db() {
            return PrayerTime_Database.R;
        }

        public final void setAsr_ikama_db(double d) {
            PrayerTime_Database.R = d;
        }

        @NotNull
        public final String getCurrentDate() {
            return PrayerTime_Database.V;
        }

        public final void setCurrentDate(@NotNull String str) {
            Intrinsics.checkNotNullParameter(str, "<set-?>");
            PrayerTime_Database.V = str;
        }

        public final double getFajr_db() {
            return PrayerTime_Database.H;
        }

        public final void setFajr_db(double d) {
            PrayerTime_Database.H = d;
        }

        public final double getFajr_ikama_db() {
            return PrayerTime_Database.O;
        }

        public final void setFajr_ikama_db(double d) {
            PrayerTime_Database.O = d;
        }

        public final double getIsha_db() {
            return PrayerTime_Database.M;
        }

        public final void setIsha_db(double d) {
            PrayerTime_Database.M = d;
        }

        public final double getIsha_ikama_db() {
            return PrayerTime_Database.T;
        }

        public final void setIsha_ikama_db(double d) {
            PrayerTime_Database.T = d;
        }

        public final double getJomo3a_db() {
            return PrayerTime_Database.N;
        }

        public final void setJomo3a_db(double d) {
            PrayerTime_Database.N = d;
        }

        public final double getJomo3a_ikama_db() {
            return PrayerTime_Database.U;
        }

        public final void setJomo3a_ikama_db(double d) {
            PrayerTime_Database.U = d;
        }

        public final double getMaghrib_db() {
            return PrayerTime_Database.L;
        }

        public final void setMaghrib_db(double d) {
            PrayerTime_Database.L = d;
        }

        public final double getMaghrib_ikama_db() {
            return PrayerTime_Database.S;
        }

        public final void setMaghrib_ikama_db(double d) {
            PrayerTime_Database.S = d;
        }

        public final double getShrouk_db() {
            return PrayerTime_Database.I;
        }

        public final void setShrouk_db(double d) {
            PrayerTime_Database.I = d;
        }

        public final double getShrouk_ikama_db() {
            return PrayerTime_Database.P;
        }

        public final void setShrouk_ikama_db(double d) {
            PrayerTime_Database.P = d;
        }

        public final double getZuhr_db() {
            return PrayerTime_Database.J;
        }

        public final void setZuhr_db(double d) {
            PrayerTime_Database.J = d;
        }

        public final double getZuhr_ikama_db() {
            return PrayerTime_Database.Q;
        }

        public final void setZuhr_ikama_db(double d) {
            PrayerTime_Database.Q = d;
        }

        public final void initPrayerTimes() {
            setFajr_db(-1.0d);
            setShrouk_db(-1.0d);
            setZuhr_db(-1.0d);
            setAsr_db(-1.0d);
            setMaghrib_db(-1.0d);
            setIsha_db(-1.0d);
            setJomo3a_db(-1.0d);
            setFajr_ikama_db(-1.0d);
            setShrouk_ikama_db(-1.0d);
            setZuhr_ikama_db(-1.0d);
            setAsr_ikama_db(-1.0d);
            setMaghrib_ikama_db(-1.0d);
            setIsha_ikama_db(-1.0d);
            setJomo3a_ikama_db(-1.0d);
        }
    }


//    public Time fajrTime(boolean isAzan) {
//        if (isAzan) {
//            return new Time_Database(PrayerTime.INSTANCE.getFajr(), true, getM_hour());
//        }
//        return new Time_Database(PrayerTime.INSTANCE.getFajr_ikama(), true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time fajrTimeIkama(double ikama) {
//        return new Time_Database(PrayerTime.INSTANCE.getFajr() + ikama, true, getM_hour());
//    }
//
//    @NotNull
//    protected final City getM_city() {
//        return this.m_city;
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time ishaTime(boolean isAzan) {
//        if (isAzan) {
//            return new Time_Database(PrayerTime.INSTANCE.getIsha(), true, getM_hour());
//        }
//        return new Time_Database(PrayerTime.INSTANCE.getIsha_ikama(), true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time ishaTimeIkama(double ikama) {
//        return new Time_Database(PrayerTime.INSTANCE.getIsha() + ikama, true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time maghribTime(boolean isAzan) {
//        if (isAzan) {
//            return new Time_Database(PrayerTime.INSTANCE.getMaghrib(), true, getM_hour());
//        }
//        return new Time_Database(PrayerTime.INSTANCE.getMaghrib_ikama(), true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time maghribTimeIkama(double ikama) {
//        return new Time_Database(PrayerTime.INSTANCE.getMaghrib() + ikama, true, getM_hour());
//    }
//
//    protected final void setM_city(@NotNull City city) {
//        Intrinsics.checkNotNullParameter(city, "<set-?>");
//        this.m_city = city;
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time shroukTime(boolean isAzan) {
//        if (isAzan) {
//            return new Time_Database(PrayerTime.INSTANCE.getShrouk(), true, getM_hour());
//        }
//        return new Time_Database(PrayerTime.INSTANCE.getShrouk_ikama(), true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time shroukTimeIkama(double ikama) {
//        return new Time_Database(PrayerTime.INSTANCE.getShrouk() + ikama, true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time zuhrTime(boolean isAzan) {
//        if (isAzan) {
//            return new Time_Database(PrayerTime.INSTANCE.getZuhr(), true, getM_hour());
//        }
//        return new Time_Database(PrayerTime.INSTANCE.getZuhr_ikama(), true, getM_hour());
//    }
//
//    @Override // com.alawail.prayertimes.moazen.PrayerTime
//    @NotNull
//    public Time zuhrTimeIkama(double ikama) {
//        return new Time_Database(PrayerTime.INSTANCE.getZuhr() + ikama, true, getM_hour());
//    }
}
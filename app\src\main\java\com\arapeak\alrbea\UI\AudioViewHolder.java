package com.arapeak.alrbea.UI;

import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.IAudio;
import com.arapeak.alrbea.R;

public class AudioViewHolder extends RecyclerView.ViewHolder {

    public int position = 0;
    ViewGroup container;
    TextView tv_title;
    ImageButton ib_play;
    boolean selected = false;
    IAudio audio;

    public AudioViewHolder(@NonNull View itemView) {
        super(itemView);
        initView(itemView);
    }

    private void initView(View itemView) {
        container = itemView.findViewById(R.id.container);
        tv_title = itemView.findViewById(R.id.tv_title);
        ib_play = itemView.findViewById(R.id.ib_play);

    }

    public void Bind(int position, boolean isPlaying, boolean selected, IAudio audio, View.OnClickListener onPlayAudio, View.OnClickListener onAzanSelected) {
        this.position = position;
        ib_play.setOnClickListener(onPlayAudio);
        container.setOnClickListener(onAzanSelected);
        this.selected = selected;
        this.audio = audio;
        load(isPlaying);
    }

    private void load(boolean isPlaying) {
//        if(Utils.getCurrentAzan() == position)
        if (selected)
            container.setBackgroundColor(Color.parseColor("#4001A0C6"));
        else
            container.setBackgroundColor(Color.WHITE);

        if (isPlaying)
            ib_play.setImageResource(R.drawable.ic_baseline_pause_24);
        else
            ib_play.setImageResource(R.drawable.ic_baseline_play_arrow_24);

        tv_title.setText(audio.getName());
//        tv_title.setText(AzanAudio.values()[position].getName());
    }


}

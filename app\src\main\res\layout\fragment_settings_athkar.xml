<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="@dimen/_15sdp"
    tools:context=".UI.Fragment.settings.content.athkar.content.SettingsAthkarsForMorningAndEveningFragment"
    tools:layoutDirection="rtl">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/_15sdp">

        <CheckBox
            android:id="@+id/citationForMorning_CheckBox_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            app:buttonTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="@id/citationForMorningDescribe_TextView_AthkarFragment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/citationForMorning_TextView_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:includeFontPadding="false"
            android:text="@string/citation_for_morning"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/citationForMorningDescribe_TextView_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/after_fajr_until_sunrise"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_13sdp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
            app:layout_constraintTop_toBottomOf="@id/citationForMorning_TextView_AthkarFragment" />

        <Button
            android:id="@+id/b1"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/_5sdp"
            android:paddingTop="@dimen/_5sdp"
            android:paddingEnd="@dimen/_5sdp"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/edit"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_13sdp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorningTime_TextView_AthkarFragment" />

        <TextView
            android:id="@+id/citationForMorningTime_TextView_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/athkar_time_after_or_before_sunrise"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_12sdp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorningDescribe_TextView_AthkarFragment" />

        <SeekBar
            android:id="@+id/citationForMorningTime_SeekBar_AthkarFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:progress="50"
            android:progressDrawable="@drawable/seek_bar_shape"
            android:visibility="gone"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/citationForMorningTime_TextView_AthkarFragment" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/settingItem_RecyclerView_PrayerTimesSettingFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:overScrollMode="never"

            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/b1"
            app:reverseLayout="false"
            tools:listitem="@layout/layout_list_item_option_choose" />


        <View
            android:id="@+id/space1_View_AthkarFragment"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/settingItem_RecyclerView_PrayerTimesSettingFragment" />

        <CheckBox
            android:id="@+id/citationForEvening_CheckBox_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            app:buttonTint="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="@id/citationForEveningDescribe_TextView_AthkarFragment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/citationForEvening_TextView_AthkarFragment" />

        <TextView
            android:id="@+id/citationForEvening_TextView_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginTop="@dimen/_15sdp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:includeFontPadding="false"
            android:text="@string/citation_for_evening"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/citationForEvening_CheckBox_AthkarFragment"
            app:layout_constraintTop_toBottomOf="@id/settingItem_RecyclerView_PrayerTimesSettingFragment" />

        <TextView
            android:id="@+id/citationForEveningDescribe_TextView_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/after_asr_until_sunset"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_13sdp"

            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/citationForEvening_CheckBox_AthkarFragment"
            app:layout_constraintTop_toBottomOf="@id/citationForEvening_TextView_AthkarFragment" />

        <Button
            android:id="@+id/b2"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/_5sdp"
            android:paddingTop="@dimen/_5sdp"
            android:paddingEnd="@dimen/_5sdp"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/edit"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_13sdp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/settingItem_RecyclerView_PrayerTimesSettingFragmentE"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForEveningTime_TextView_AthkarFragment" />


        <TextView
            android:id="@+id/citationForEveningTime_TextView_AthkarFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginTop="@dimen/_5sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/athkar_time_after_or_before_sunset"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_13sdp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForEveningDescribe_TextView_AthkarFragment" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/settingItem_RecyclerView_PrayerTimesSettingFragmentE"
            android:layout_width="0dp"
            android:overScrollMode="never"

            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/citationForEveningTime_TextView_AthkarFragment"
            app:reverseLayout="false"
            tools:listitem="@layout/layout_list_item_option_choose" />


        <SeekBar
            android:id="@+id/citationForEveningTime_SeekBar_AthkarFragment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_5sdp"
            android:progress="50"
            android:progressDrawable="@drawable/seek_bar_shape"
            android:visibility="gone"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/citationForEveningTime_TextView_AthkarFragment" />

        <View
            android:id="@+id/space2_View_AthkarFragment"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/settingItem_RecyclerView_PrayerTimesSettingFragmentE" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</ScrollView>
package com.arapeak.alrbrea.core_ktx.ui.screensaver

import android.app.Activity
import android.content.Context
import android.util.Log
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverTimingEnum
import com.arapeak.alrbrea.core_ktx.repo.ScreenSaverRepo
import kotlinx.datetime.toJavaLocalTime
import java.time.LocalTime
import kotlin.math.max

@Suppress("t")
class ScreensaverScheduler(context: Context) {
    companion object {
        private val CheckFrequencyDefault: Long = 5 //Seconds

        @JvmField
        var CheckFrequency: Long = CheckFrequencyDefault //Seconds
    }

    private var manager: ScreensaverUiManager = ScreensaverUiManager() {
        CheckFrequency = CheckFrequencyDefault * 3
        isEnabled = false
    }

    private val repo = ScreenSaverRepo()
    private var timing = repo.getAllTimings(context).filter { it.value }
    private var creationTime = LocalTime.now()
    var isEnabled = false

    private var isPrayingTime = false
    private var isShowingAthkar = false
    private var isBetweenPrayerIkama = false
    var prayers: HashMap<String, String> = hashMapOf()


    init {
        Log.e("Screensaver", "timings = " + timing.map {
            it.key.name
        }.joinToString(" / "))
    }


    fun checkAndStart(activity: Activity) {
        val now = LocalTime.now()

        val shouldShow = shouldShow(now, activity)
        Log.e("screensaver", "should show $shouldShow");

        if (isEnabled == shouldShow)
            return

        if (shouldShow)
            manager.startScreenSaver(activity)
        else
            manager.stopScreenSaver(activity)

        isEnabled = shouldShow
        if (shouldShow) {
            CheckFrequency = CheckFrequencyDefault
        }
    }

    private fun shouldShow(now: LocalTime, context: Context): Boolean {
        Log.e("isBetweenAzanAndIkama", isBetweenPrayerIkama.toString())
        if (isBetweenPrayerIkama || isShowingAthkar)
            return false

        val periodConfig = repo.getTimingConfigPeriod(context)
        val delayConfig = repo.getTimingConfigDelay(context)
        val intervalConfig = repo.getTimingConfigInterval(context)


        timing.forEach {
            when (it.key) {
                ScreensaverTimingEnum.BetweenPrayers -> {
                    val prayersEnabled = repo.getTimingPrayers(context)
                    val delay = repo.getTimingPrayersDelay(context)
                    val preDelay = repo.getTimingPrayersPreDelay(context)

                    Log.i("screensaver", "BetweenPrayers prayersEnabled: ${prayersEnabled.joinToString(" ")}")
                    Log.i("screensaver", "BetweenPrayers prayersEnabled delay: ${delay}")
                    Log.i("screensaver", "BetweenPrayers prayersEnabled preDelay: ${preDelay}")

                    return shouShowBetweenPrayers(now, prayersEnabled, delay, preDelay)
                }

                ScreensaverTimingEnum.AfterDelay -> {
                    if (now.isAfter(creationTime.plusMinutes(delayConfig.getDelayMinutes().toLong())))
                        return true
                }

                ScreensaverTimingEnum.Period -> {
                    if (now.isAfter(periodConfig.getFrom().toJavaLocalTime()) && now.isBefore(periodConfig.getTo().toJavaLocalTime()))
                        return true
                }

                ScreensaverTimingEnum.DuringPrayers -> {
                    Log.e("isPrayingTime", isPrayingTime.toString())
                    return isPrayingTime
                }

                ScreensaverTimingEnum.Intervals -> {
                    val onFor = intervalConfig.getOnIntervalMinutes()
                    val offFor = intervalConfig.getOffIntervalMinutes()
                    Log.e("Interval", "onFor = $onFor offFor = $offFor")

                    var tineWithIntervals = creationTime.plusMinutes(onFor.toLong())
                    var tick = true

                    while (tineWithIntervals.isBefore(now.plusMinutes(max(onFor, offFor).toLong()))) {
                        if (tick) {
                            if (now.isBefore(tineWithIntervals) && now.isAfter(tineWithIntervals.minusMinutes(onFor.toLong()))) {
                                return true
                            }
                            tineWithIntervals = tineWithIntervals.plusMinutes(offFor.toLong())
                            tick = false
                        } else {
                            if (now.isBefore(tineWithIntervals) && now.isAfter(tineWithIntervals.minusMinutes(offFor.toLong()))) {
                                return false
                            }
                            tineWithIntervals = tineWithIntervals.plusMinutes(onFor.toLong())
                            tick = true
                        }
                    }
                }
            }
        }

        return false
    }

    private fun shouShowBetweenPrayers(now: LocalTime, prayersEnabled: List<Boolean>, delay: Int, preDelay: Int): Boolean {
        try {
            prayersEnabled.forEachIndexed { index, b ->
                if (b)
                    when (index) {
                        0 -> {
                            if (
                                now.isAfter(getLegacyPrayerTime(LegacyPrayersKeyEnum.fajrPrayer).plusMinutes(delay.toLong())) &&
                                now.isBefore(getLegacyPrayerTime(LegacyPrayersKeyEnum.sunrisePrayer).minusMinutes(preDelay.toLong()))
                            )
                                return true
                        }

                        1 -> {
                            if (
                                now.isAfter(getLegacyPrayerTime(LegacyPrayersKeyEnum.sunrisePrayer).plusMinutes(delay.toLong())) &&
                                now.isBefore(getLegacyPrayerTime(LegacyPrayersKeyEnum.dhuhrPrayer).minusMinutes(preDelay.toLong()))
                            )
                                return true
                        }

                        2 -> {
                            if (
                                now.isAfter(getLegacyPrayerTime(LegacyPrayersKeyEnum.dhuhrPrayer).plusMinutes(delay.toLong())) &&
                                now.isBefore(getLegacyPrayerTime(LegacyPrayersKeyEnum.asrPrayer).minusMinutes(preDelay.toLong()))
                            )
                                return true
                        }

                        3 -> {
                            if (
                                now.isAfter(getLegacyPrayerTime(LegacyPrayersKeyEnum.asrPrayer).plusMinutes(delay.toLong())) &&
                                now.isBefore(getLegacyPrayerTime(LegacyPrayersKeyEnum.maghribPrayer).minusMinutes(preDelay.toLong()))
                            )
                                return true
                        }

                        4 -> {
                            if (
                                now.isAfter(getLegacyPrayerTime(LegacyPrayersKeyEnum.maghribPrayer).plusMinutes(delay.toLong())) &&
                                now.isBefore(getLegacyPrayerTime(LegacyPrayersKeyEnum.ishaPrayer).minusMinutes(preDelay.toLong()))
                            )
                                return true
                        }

                        5 -> {
                            if (now.isAfter(getLegacyPrayerTime(LegacyPrayersKeyEnum.ishaPrayer).plusMinutes(delay.toLong())))
                                return true
                            if (now.isBefore(getLegacyPrayerTime(LegacyPrayersKeyEnum.fajrPrayer).minusMinutes(preDelay.toLong())))
                                return true

                        }
                    }


            }


        } catch (e: Exception) {
            logException(e)
        }
        return false

    }


    private fun getLegacyPrayerTime(enum: LegacyPrayersKeyEnum): LocalTime {
        // use millis
        val time = prayers[enum.name]
        var res = LocalTime.now()
        time?.split(":")?.get(0)?.toInt()?.let { res = res.withHour(it) }
        time?.split(":")?.get(1)?.toInt()?.let { res = res.withMinute(it) }

        Log.i("screensaver", "BetweenPrayers getLegacyPrayerTime enum: ${enum.name}")
        Log.i("screensaver", "BetweenPrayers getLegacyPrayerTime raw: ${time}")
        Log.i("screensaver", "BetweenPrayers getLegacyPrayerTime res: ${res.hour}:${res.minute}")

        return res
    }


    fun setIsPrayingTime(isPraying: Boolean) {
        Log.i("screensaver", "setIsPrayingTime $isPraying")
        isPrayingTime = isPraying
    }


    fun isShowingAthkar(isAthkar: Boolean) {
        isShowingAthkar = isAthkar
    }


    fun setIsBetweenPrayerIkama(isit: Boolean) {
        isBetweenPrayerIkama = isit
    }

    fun updateData(timers: String, timeType: String, mildadi: String, hijri: String, dayName: String, funeral: String?) {
        try {
            manager.updateData(timers, timeType, mildadi, hijri, dayName, funeral)
        } catch (e: Exception) {
            logException(e)
        }
    }


}

enum class LegacyPrayersKeyEnum {
    fajrPrayer,
    duhaPrayer,
    sunrisePrayer,
    dhuhrPrayer,
    asrPrayer,
    maghribPrayer,
    ishaPrayer,
    jomaaPrayer,
    semonPrayer,
    tarawihPrayer,
    tahajjudPrayer
}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="@dimen/_24sdp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title_TextView_text_design2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:text="@string/text"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_11sdp"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/et_input"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:backgroundTint="@color/colorBlue"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:maxLines="1"
        android:minWidth="@dimen/_160sdp"
        android:shadowColor="@color/colorBlue"
        android:text="مثال"
        android:textColor="@color/bluelett"
        android:textSize="@dimen/_11sdp" />

    <Button
        android:id="@+id/btn_save"
        android:layout_width="@dimen/_120sdp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="64dp"
        android:background="@drawable/button_gray_without_corners_shape"
        android:backgroundTint="@color/bluelett"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:padding="8dp"
        android:text="@string/save"
        android:textColor="@color/white"
        android:textSize="@dimen/_11sdp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/btn_dismmis"
        android:layout_width="@dimen/_120sdp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_gray_without_corners_shape"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:padding="8dp"
        android:text="@string/close"
        android:textColor="@color/bluelett"
        android:textSize="@dimen/_11sdp"
        android:textStyle="bold" />

</LinearLayout>

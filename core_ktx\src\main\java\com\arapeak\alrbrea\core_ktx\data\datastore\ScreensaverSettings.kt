package com.arapeak.alrbrea.core_ktx.data.datastore

import android.content.Context
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoElementsEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoSizeEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverModeEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverTimingEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverToggleEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigDelay
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigInterval
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigPeriod
import kotlinx.datetime.LocalTime


class ScreensaverSettings() {
    private val prefsFileName = "settings_ktx"

    private val toggleKey = "screensaver_toggle"
    private val modeKey = "screensaver_mode"
    private val infoSizeKey = "screensaver_info_size"
    private val infoElementsKey = "screensaver_info_elements"

    private val timingDelayKey = "screensaver_timing_delay"
    private val timingPeriodKey = "screensaver_timing_period"
    private val timingIntervalKey = "screensaver_timing_interval"
    private val timingBetweenPrayersKey = "screensaver_timing_prayers"
    private val timingBetweenPrayersDelayKey = "screensaver_timing_prayers_delay"
    private val timingBetweenPrayersPreDelayKey = "screensaver_timing_prayers_pre_delay"

    private val toggleDefault = ScreensaverToggleEnum.Disabled.ordinal
    private val modeDefault = ScreensaverModeEnum.Empty.ordinal
    private val infoSizeDefault = ScreenSaverInfoSizeEnum.Large.ordinal
    private val infoElementsDefault = ScreenSaverInfoElementsEnum.TimeOnly.ordinal

    private val timingDelayDefault = "0"
    private val timingPeriodDefault = "01:00-02:00"
    private val timingIntervalDefault = "1-1"
    private val timingPrayersDefault = "111111"
    private val timingPrayersDelayDefault = 0
    private val timingPrayersPreDelayDefault = 0


    fun getToggle(context: Context): ScreensaverToggleEnum {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(toggleKey, toggleDefault)

        return ScreensaverToggleEnum.entries[modeInt]
    }

    fun setToggle(context: Context, toggle: ScreensaverToggleEnum) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(toggleKey, toggle.ordinal)
            apply()
        }
    }

    fun getMode(context: Context): ScreensaverModeEnum {
        val modeInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(modeKey, modeDefault)

        return ScreensaverModeEnum.entries[modeInt]
    }

    fun setMode(context: Context, mode: ScreensaverModeEnum) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(modeKey, mode.ordinal)
            apply()
        }
    }

    fun getInfoSize(context: Context): ScreenSaverInfoSizeEnum {
        val resInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(infoSizeKey, infoSizeDefault)

        return ScreenSaverInfoSizeEnum.entries[resInt]
    }

    fun setInfoSize(context: Context, mode: ScreenSaverInfoSizeEnum) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(infoSizeKey, mode.ordinal)
            apply()
        }
    }

    fun getInfoElements(context: Context): ScreenSaverInfoElementsEnum {
        val resInt = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(infoElementsKey, infoElementsDefault)

        return ScreenSaverInfoElementsEnum.entries[resInt]
    }

    fun setInfoElements(context: Context, mode: ScreenSaverInfoElementsEnum) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(infoElementsKey, mode.ordinal)
            apply()
        }
    }

    fun setTiming(context: Context, enum: ScreensaverTimingEnum, enable: Boolean) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putBoolean(enum.name.lowercase(), enable)
            apply()
        }
    }

    fun getTiming(context: Context, enum: ScreensaverTimingEnum): Boolean {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getBoolean(enum.name.lowercase(), false)

        return res
    }

    fun setTimingDelay(context: Context, save: String) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putString(timingDelayKey, save)
            apply()
        }
    }

    fun getTimingDelay(context: Context): TimingConfigDelay {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getString(timingDelayKey, timingDelayDefault) ?: timingDelayDefault

        return TimingConfigDelay(0).apply {
            parseFromSave(res)
        }
    }

    fun setTimingPeriod(context: Context, save: String) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putString(timingPeriodKey, save)
            apply()
        }
    }

    fun getTimingPeriod(context: Context): TimingConfigPeriod {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getString(timingPeriodKey, timingPeriodDefault) ?: timingPeriodDefault

        return TimingConfigPeriod(LocalTime(1, 0), LocalTime(2, 0)).apply {
            parseFromSave(res)
        }
    }

    fun setTimingInterval(context: Context, save: String) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putString(timingIntervalKey, save)
            apply()
        }
    }

    fun getTimingInterval(context: Context): TimingConfigInterval {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getString(timingIntervalKey, timingIntervalDefault) ?: timingIntervalDefault

        return TimingConfigInterval(1, 1).apply {
            parseFromSave(res)
        }
    }

    fun getTimingPrayers(context: Context): String {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getString(timingBetweenPrayersKey, timingPrayersDefault)

        return res ?: timingPrayersDefault
    }

    fun setTimingPrayers(context: Context, toggle: String) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putString(timingBetweenPrayersKey, toggle)
            apply()
        }
    }

    fun getTimingPrayersDelay(context: Context): Int {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(timingBetweenPrayersDelayKey, timingPrayersDelayDefault)

        return res ?: timingPrayersDelayDefault
    }

    fun setTimingPrayersDelay(context: Context, delay: Int) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(timingBetweenPrayersDelayKey, delay)
            apply()
        }
    }

    fun getTimingPrayersPreDelay(context: Context): Int {
        val res = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE)
            .getInt(timingBetweenPrayersPreDelayKey, timingPrayersPreDelayDefault)

        return res ?: timingPrayersPreDelayDefault
    }

    fun setTimingPrayersPreDelay(context: Context, delay: Int) {
        val prefs = context.getSharedPreferences(prefsFileName, Context.MODE_PRIVATE) ?: return
        with(prefs.edit()) {
            putInt(timingBetweenPrayersPreDelayKey, delay)
            apply()
        }
    }
}
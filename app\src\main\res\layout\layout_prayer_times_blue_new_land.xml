<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:layoutDirection="rtl"
    android:orientation="vertical"
    tools:background="@android:color/black">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:background="@drawable/gradient_semi_transperant"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.blue_new_land">

            <LinearLayout style="@style/PrayerTimeLayout.blue_new.Right">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/fajr_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new_land.TimeNameAR"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="الفجر" />

                    <TextView
                        style="@style/TimeTextView.blue_new_land.TimeNameEN"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="Fajr"
                        android:textSize="@dimen/_16sdp" />
                </LinearLayout>

                <include
                    android:id="@+id/tv_prayer_ikama_time_fajr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">


                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.Time"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.TimeType"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.blue_new_land">

            <LinearLayout style="@style/PrayerTimeLayout.blue_new.Right">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new_land.TimeNameAR"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="الضحى" />

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity_en"
                        style="@style/TimeTextView.blue_new_land.TimeNameEN"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="Duha"

                        android:textSize="@dimen/_16sdp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.Time"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.TimeType"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />


            </LinearLayout>

        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.blue_new_land">

            <LinearLayout style="@style/PrayerTimeLayout.blue_new.Right">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new_land.TimeNameAR"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="الظهر" />

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivityE"
                        style="@style/TimeTextView.blue_new_land.TimeNameEN"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="Dhuhr"

                        android:textSize="@dimen/_16sdp" />
                </LinearLayout>

                <include
                    android:id="@+id/tv_prayer_ikama_time_dhur"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.Time"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.TimeType"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />


            </LinearLayout>


        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:background="@drawable/gradient_semi_transperant"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.blue_new_land">

            <LinearLayout style="@style/PrayerTimeLayout.blue_new.Right">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/asr_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new_land.TimeNameAR"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="العصر" />

                    <TextView
                        style="@style/TimeTextView.blue_new_land.TimeNameEN"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="Asr"

                        android:textSize="@dimen/_16sdp" />
                </LinearLayout>

                <include
                    android:id="@+id/tv_prayer_ikama_time_asr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.Time"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.TimeType"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />


            </LinearLayout>


        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.blue_new_land">

            <LinearLayout style="@style/PrayerTimeLayout.blue_new.Right">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/maghrib_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new_land.TimeNameAR"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="المغرب" />

                    <TextView
                        style="@style/TimeTextView.blue_new_land.TimeNameEN"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="Maghrib"

                        android:textSize="@dimen/_15sdp" />
                </LinearLayout>

                <include
                    android:id="@+id/tv_prayer_ikama_time_maghrib"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.Time"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.TimeType"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />


            </LinearLayout>


        </LinearLayout>

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow.blue_new_land">

            <LinearLayout style="@style/PrayerTimeLayout.blue_new.Right">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content">

                    <TextView
                        android:id="@+id/isha_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new_land.TimeNameAR"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="العشاء" />

                    <TextView
                        style="@style/TimeTextView.blue_new_land.TimeNameEN"
                        android:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:text="Isha"
                        android:textSize="@dimen/_16sdp" />
                </LinearLayout>

                <include
                    android:id="@+id/tv_prayer_ikama_time_isha"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.Time"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new_land.TimeType"
                    android:autoSizeTextType="uniform"
                    android:maxLines="1" />


            </LinearLayout>


        </LinearLayout>
    </LinearLayout>

</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="4dp"
    android:paddingTop="20dp"
    android:paddingEnd="4dp"
    android:paddingBottom="20dp"
    tools:context=".UI.Fragment.settings.content.athkar.content.AthkarsAfterPrayerFragment"
    tools:layoutDirection="rtl">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/settingItem_RecyclerView_AthkarsAfterPrayerFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:overScrollMode="never"

        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:reverseLayout="false"
        tools:listitem="@layout/layout_list_item_option_choose" />

</androidx.constraintlayout.widget.ConstraintLayout>
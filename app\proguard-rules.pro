# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles settingAlrabeeaTimes in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with J<PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source files name.
#-renamesourcefileattribute SourceFile
-dontwarn java.beans.ConstructorProperties
-dontwarn java.beans.Transient
-dontwarn com.daimajia.easing.Glider
-dontwarn com.daimajia.easing.Skill
-dontwarn kotlinx.serialization.KSerializer
-dontwarn kotlinx.serialization.Serializable
-dontwarn kotlinx.serialization.internal.AbstractPolymorphicSerializer
-keepparameternames

#-keep class com.arapeak.alrbea.** {
#    *;
#}

-keep class com.arapeak.** {*;}
-keep class com.github.msarhan.** {*;}
-keep class com.batoulapps.adhan2.** {*;}
-keep class com.github.tapadoo.** {*;}



#-keep class com.arapeak.alrbea.Model.* {
#    *;
#}
#-keep class com.arapeak.alrbea.Enum.* {
#    *;
#}
#-keep class com.arapeak.alrbea.hawk.* {
#    *;
#}
#-keep class com.arapeak.alrbea.Interface.* {
#    *;
#}
#-keep class com.arapeak.alrbea.TestingFlags.* {
#    *;
#}
#-keep class com.arapeak.alrbea.Utils {
#    *;
#}
#
#-keep class com.arapeak.alrbea.APIs.ConstantsOfApp {
#    *;
#}

-keep enum **

-dontwarn java.awt.Color

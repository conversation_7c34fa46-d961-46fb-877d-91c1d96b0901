# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles settingAlrabeeaTimes in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Aggressive optimization settings for smaller APK size
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify
-repackageclasses ''

# Remove debug information for smaller size (comment out for debugging)
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove line numbers and source file names for smaller size
-renamesourcefileattribute SourceFile
-dontwarn java.beans.ConstructorProperties
-dontwarn java.beans.Transient
-dontwarn com.daimajia.easing.Glider
-dontwarn com.daimajia.easing.Skill
-dontwarn kotlinx.serialization.KSerializer
-dontwarn kotlinx.serialization.Serializable
-dontwarn kotlinx.serialization.internal.AbstractPolymorphicSerializer
-keepparameternames

#-keep class com.arapeak.alrbea.** {
#    *;
#}

-keep class com.arapeak.** {*;}
-keep class com.github.msarhan.** {*;}
-keep class com.batoulapps.adhan2.** {*;}
-keep class com.github.tapadoo.** {*;}



#-keep class com.arapeak.alrbea.Model.* {
#    *;
#}
#-keep class com.arapeak.alrbea.Enum.* {
#    *;
#}
#-keep class com.arapeak.alrbea.hawk.* {
#    *;
#}
#-keep class com.arapeak.alrbea.Interface.* {
#    *;
#}
#-keep class com.arapeak.alrbea.TestingFlags.* {
#    *;
#}
#-keep class com.arapeak.alrbea.Utils {
#    *;
#}
#
#-keep class com.arapeak.alrbea.APIs.ConstantsOfApp {
#    *;
#}

-keep enum **

-dontwarn java.awt.Color

package com.arapeak.alrbea.Model;

public class PhotoGalleryImage {
    public long id = 0;
    public long galleryId = 0;
    public String imageUri;

    //    public PhotoGalleryImage(byte[] data){
//        imageData = data;
//    }
    public PhotoGalleryImage(String uri) {
        imageUri = uri;
    }

    public PhotoGalleryImage() {

    }
//
//    public void setImageUri(Bitmap image) {
//        this.imageUri = getBitmapAsByteArray(image);
//    }
//    public void setImageUri(Bitmap image) {
//        this.imageUri = getBitmapAsByteArray(image);
//    }
//    public  byte[] getBitmapAsByteArray(Bitmap bitmap) {
//        byte[] result = null;
//        try{
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            bitmap.compress(Bitmap.CompressFormat.PNG, 0, outputStream);
//            result = outputStream.toByteArray();
//            outputStream.close();
//        }
//        catch (Exception e){
//            e.printStackTrace();
//        }
//        return result;
//    }
//    public Bitmap getImage(){
//        return BitmapFactory.decodeByteArray(imageUri,0, imageUri.length);
//    }
}

package com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery;


import static com.arapeak.alrbea.hawk.HawkConstants.IS_ENABLE_CREATE_PHOTO_GALLERY;
import static com.arapeak.alrbea.hawk.HawkConstants.IS_ENABLE_CREATE_PHOTO_GALLERY_DEFAULT;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Model.PhotoGallery;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.PhotoGalleryRepository;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;

public class PhotoGalleriesSettingFragment extends Fragment implements GalleriesAdapter.Listener {

    private SwitchCompat photoGalleryEnable;
    private LinearLayout addNewGalleryButton;
    private LinearLayout container;
    private GalleriesAdapter adapter;
    private RecyclerView recyclerView;
    private ArrayList<PhotoGallery> galleries = new ArrayList<>();

    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_photo_galleries_setting, container, false);
        initUI(view);
        return view;
    }

    public void initUI(View view) {
        photoGalleryEnable = view.findViewById(R.id.enable_gallery_button);
        addNewGalleryButton = view.findViewById(R.id.uploadPhoto_LinearLayout_PhotoGalleryFragment);
        container = view.findViewById(R.id.linear_layout_galleries_setting);
        recyclerView = view.findViewById(R.id.photo_RecyclerView_PhotoGalleryFragment);
        adapter = new GalleriesAdapter(this);
        recyclerView.setAdapter(adapter);
        adapter.setGalleries(galleries);
        addNewGalleryButton.setOnClickListener(l -> {
            Utils.loadFragment(new AddGalleryFragment(), (AppCompatActivity) requireActivity(), 0);
        });
        boolean isEnabled = Hawk.get(IS_ENABLE_CREATE_PHOTO_GALLERY, IS_ENABLE_CREATE_PHOTO_GALLERY_DEFAULT);
        photoGalleryEnable.setChecked(isEnabled);

        photoGalleryEnable.setOnClickListener(i -> {
            boolean enabled = !Hawk.get(IS_ENABLE_CREATE_PHOTO_GALLERY, IS_ENABLE_CREATE_PHOTO_GALLERY_DEFAULT);//reverse the old boolean
            Hawk.put(IS_ENABLE_CREATE_PHOTO_GALLERY, enabled);
            container.setVisibility(enabled ? View.VISIBLE : View.GONE);
        });
        container.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onStart() {
        super.onStart();
        loadGalleries();
    }

    private void loadGalleries() {
        galleries = PhotoGalleryRepository.getAll();
        adapter.setGalleries(galleries);
    }

    @Override
    public void loadGallery(PhotoGallery gallery) {
        Utils.loadFragment(new AddGalleryFragment(gallery), (AppCompatActivity) requireActivity(), 0);
    }
}
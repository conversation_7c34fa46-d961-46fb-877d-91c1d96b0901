<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraintLayoutall"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_6sdp"
    tools:layoutDirection="rtl">

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/isEnable_CheckBox_SubSettingsHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_10sdp"
        android:checked="true"
        android:fontFamily="@font/droid_arabic_kufi"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_10sdp"
        app:buttonTint="@color/colorPrimary"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title_TextView_SubSettingsHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="14dp"
        android:layout_marginEnd="14dp"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_11sdp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/isEnable_CheckBox_SubSettingsHolder"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="تنسيق أرقام الوقت" />

    <Button
        android:id="@+id/addPrayerTime_Button_SubSettingsHolderSetting"
        android:layout_width="@dimen/_20sdp"
        android:layout_height="@dimen/_20sdp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="@dimen/_5sdp"
        android:background="@drawable/iconsetting"
        android:gravity="center"
        android:includeFontPadding="false"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/title_TextView_SubSettingsHolder"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/description_TextView_SubSettingsHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_40sdp"
        android:layout_marginTop="@dimen/_4sdp"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="start|top"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_10sdp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_TextView_SubSettingsHolder"
        tools:text="تعديل وقت صلاة الفجر" />


    <LinearLayout
        android:id="@+id/views_LinearLayout_SubSettingsHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="14dp"
        android:orientation="horizontal"
        android:visibility="visible"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/description_TextView_SubSettingsHolder">


        <RadioGroup
            android:id="@+id/options_RadioGroup_SubSettingsHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/option1_AppCompatRadioButton_SubSettingsHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"

                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                app:buttonTint="@color/colorPrimary"
                tools:text="عربي" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/option2_AppCompatRadioButton_SubSettingsHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="28dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                app:buttonTint="@color/colorPrimary"
                tools:text="عربي" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/option3_AppCompatRadioButton_SubSettingsHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="28dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                app:buttonTint="@color/colorPrimary"
                tools:text="عربي" />

            <androidx.appcompat.widget.AppCompatRadioButton
                android:id="@+id/option4_AppCompatRadioButton_SubSettingsHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="28dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                app:buttonTint="@color/colorPrimary"
                tools:text="عربي" />

            <!--            <androidx.appcompat.widget.AppCompatRadioButton-->
            <!--                android:id="@+id/option4_AppCompatRadioButton_SubSettingsHolder"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginStart="28dp"-->
            <!--                android:fontFamily="@font/droid_arabic_kufi"-->
            <!--                android:textColor="@android:color/black"-->
            <!--                android:textSize="@dimen/_10sdp"-->
            <!--                app:buttonTint="@color/colorPrimary"-->
            <!--                tools:text="عربي" />-->
        </RadioGroup>

        <Spinner
            android:id="@+id/options_Spinner_SubSettingsHolder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_40sdp"
            android:layout_gravity="center"
            android:layout_marginStart="16dp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:dropDownVerticalOffset="40dp"
            android:padding="8dp"
            android:spinnerMode="dropdown"
            android:visibility="gone"
            tools:listitem="@layout/layout_list_item_spinner_text" />

        <LinearLayout
            android:id="@+id/options_LinearLayout_SubSettingsHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:orientation="horizontal"
            android:visibility="gone">

            <Switch
                android:id="@+id/option_Switch_SubSettingsHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:theme="@style/SCBSwitch" />

            <TextView
                android:id="@+id/option_TextView_SubSettingsHolder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                tools:text="التوقيت الهجري الحالي" />
        </LinearLayout>


        <CheckBox
            android:id="@+id/option_CheckBox_SubSettingsHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:button="@drawable/check_box_blue_selector"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_10sdp"
            android:visibility="gone"
            tools:text="التوقيت الهجري الحالي" />


        <LinearLayout
            android:id="@+id/addMinusNumber_LinearLayout_SubSettingsHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:gravity="center"
            android:visibility="visible">
            <!--            android:visibility="gone">-->

            <Button
                android:id="@+id/addPrayerTime_Button_SubSettingsHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:background="@drawable/without_corners_20_background_blue_with_drawable_add"
                android:gravity="center"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/editPrayerTime_TextView_SubSettingsHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginStart="8dp"
                android:background="@drawable/without_corners_20_background_white_with_stroke_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:inputType="numberSigned"
                android:maxLines="1"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/minusPrayerTime_Button_SubSettingsHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginStart="8dp"
                android:background="@drawable/without_corners_20_background_blue_with_drawable_minus"
                android:gravity="center"
                android:includeFontPadding="false" />

        </LinearLayout>

        <Button
            android:id="@+id/add_Button_SubSettingsHolder"
            android:layout_width="@dimen/_200sdp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_10sdp"
            android:visibility="gone"
            tools:text="@string/active" />

        <TextView
            android:id="@+id/addTime_TextView_SubSettingsHolder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:background="@drawable/without_corners_20_stroke_1_background_transparent"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center"
            android:minWidth="200dp"
            android:padding="4dp"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_15sdp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="12:44"
            tools:visibility="visible" />

    </LinearLayout>


    <View
        android:id="@+id/space_View_SubSettingsHolder"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="#D1D1D1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/views_LinearLayout_SubSettingsHolder" />

</androidx.constraintlayout.widget.ConstraintLayout>
package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.mapper

import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_EGYPTIAN_GENERAL
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_ISLAMIC_SOCITY_OF_NORTH_AMERICA
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_MINISTRY_OF_AWQAF_AND_ISLAMIC_AFFAIRS
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_MUSLIM_WORLD_LEAGUE
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_UMMALQURA
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_UNION_OF_ISLAMIC_ORGANAIZATIONS
import com.arapeak.alrbrea.core_ktx.data.prayer.ptp_kacst.model.PrayerTimeType.METHOD_ID_UNIVERSITY_OF_ISLAMIC_SCIENCES
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.automatic
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.customCalendar
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.egyptianSurvey
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.france
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.gulf_region
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.karachi
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.kuwait
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.muslimLeague
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.northAmerica
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.russia
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.singapore
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.turkey
import com.arapeak.alrbrea.core_ktx.model.prayer.CalculationMethod.ummAlQurra
import com.batoulapps.adhan2.CalculationMethod as AdhanCalculationMethod


class CalculationMethodMapper {

    fun map(method: AdhanCalculationMethod): Int {
        val default = METHOD_ID_UMMALQURA
        return when (method) {
            AdhanCalculationMethod.MUSLIM_WORLD_LEAGUE -> METHOD_ID_MUSLIM_WORLD_LEAGUE
            AdhanCalculationMethod.EGYPTIAN -> METHOD_ID_EGYPTIAN_GENERAL
            AdhanCalculationMethod.UMM_AL_QURA -> METHOD_ID_UMMALQURA
            AdhanCalculationMethod.NORTH_AMERICA -> METHOD_ID_ISLAMIC_SOCITY_OF_NORTH_AMERICA
            AdhanCalculationMethod.KARACHI -> METHOD_ID_UNIVERSITY_OF_ISLAMIC_SCIENCES
            AdhanCalculationMethod.KUWAIT -> METHOD_ID_MINISTRY_OF_AWQAF_AND_ISLAMIC_AFFAIRS
            AdhanCalculationMethod.DUBAI,
            AdhanCalculationMethod.MOON_SIGHTING_COMMITTEE,
            AdhanCalculationMethod.QATAR,
            AdhanCalculationMethod.SINGAPORE,
            AdhanCalculationMethod.TURKEY,
            AdhanCalculationMethod.OTHER -> default
        }
    }
}


fun CalculationMethod.toKacstMethod(): Int {
    val default = METHOD_ID_UMMALQURA
    return when (this) {
        automatic -> default
        ummAlQurra -> METHOD_ID_UMMALQURA
        egyptianSurvey -> METHOD_ID_EGYPTIAN_GENERAL
        karachi -> METHOD_ID_UNIVERSITY_OF_ISLAMIC_SCIENCES
        muslimLeague -> METHOD_ID_MUSLIM_WORLD_LEAGUE
        northAmerica -> METHOD_ID_ISLAMIC_SOCITY_OF_NORTH_AMERICA
        gulf_region -> default
        kuwait -> METHOD_ID_MINISTRY_OF_AWQAF_AND_ISLAMIC_AFFAIRS
        singapore -> default
        france -> METHOD_ID_UNION_OF_ISLAMIC_ORGANAIZATIONS
        turkey -> default
        russia -> default
        customCalendar -> default
    }
}
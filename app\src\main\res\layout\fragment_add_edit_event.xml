<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="locale"
    android:orientation="vertical"
    android:paddingLeft="@dimen/_5sdp"
    android:paddingRight="@dimen/_5sdp">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/enableDisablePhoto_SwitchCompat_AddPhotoFragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10sdp"
        android:checked="true"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:includeFontPadding="false"
        android:padding="@dimen/_5sdp"
        android:text="@string/enable_disable_events"
        android:textColor="#474747"
        android:textSize="@dimen/_12sdp"
        app:switchMinWidth="@dimen/_30sdp" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <EditText
            android:id="@+id/message_EditText_CreateMovingMessageFragment"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_140sdp"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:background="@drawable/without_corners_bottom_20_background_gray"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="start|top"
            android:hint="@string/text"
            android:includeFontPadding="false"
            android:padding="@dimen/_10sdp"
            android:textColor="@android:color/black"
            android:textColorHint="@color/colorGrayDarkTwo"
            android:textSize="@dimen/_12sdp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/from"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_sDate"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi"
                android:padding="@dimen/_5sdp"
                android:text="@string/save"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/time"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_sTime"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi"
                android:padding="@dimen/_5sdp"
                android:text="@string/save"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/to"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_eDate"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi"
                android:padding="@dimen/_5sdp"
                android:text="@string/save"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/time"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />

            <Button
                android:id="@+id/btn_eTime"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_4sdp"
                android:background="@drawable/without_corners_50_background_blue"
                android:fontFamily="@font/droid_arabic_kufi"
                android:padding="@dimen/_5sdp"
                android:text="@string/save"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_10sdp"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi"
            android:padding="@dimen/_5sdp"
            android:text="@string/cancel"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_12sdp" />

        <Button
            android:id="@+id/btn_add"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi"
            android:padding="@dimen/_5sdp"
            android:text="@string/save"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_12sdp" />

        <Button
            android:id="@+id/btn_delete"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_5sdp"
            android:background="@drawable/without_corners_50_background_red"
            android:fontFamily="@font/droid_arabic_kufi"
            android:padding="@dimen/_5sdp"
            android:text="@string/delete"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_12sdp" />
    </LinearLayout>

</LinearLayout>
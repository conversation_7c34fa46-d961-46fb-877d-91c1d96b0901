<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal|top"
    android:layout_margin="@dimen/_15sdp"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:minWidth="@dimen/_200sdp"
    android:orientation="vertical"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="@dimen/_15sdp"
    app:cardElevation="0dp"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:minWidth="@dimen/_200sdp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/layout_gray_without_top_corners"
            android:gravity="center"
            android:padding="@dimen/_5sdp">

            <ImageView
                android:id="@+id/icon_ImageView_ConfirmationDialog"
                android:layout_width="@dimen/_13sdp"
                android:layout_height="@dimen/_13sdp"
                android:layout_gravity="center" />


            <TextView
                android:id="@+id/title_TextView_ConfirmationDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:includeFontPadding="false"
                android:textColor="#343434"
                android:textSize="@dimen/_14sdp"
                tools:hint="+966"
                tools:text="سبب الغياب" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_5sdp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/bodyMessage_TextView_ConfirmationDialog"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_7sdp"
                android:layout_marginEnd="@dimen/_7sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:lineSpacingExtra="@dimen/_7sdp"
                android:textColor="#cf9e9e9e"
                android:textSize="@dimen/_11sdp"
                tools:text="الطالب : محمد أحمد
مريض وقد يحتاج الى مدة أسبوع او اسبوعين
بسبب السفر الى بلاد أخرى" />

            <EditText
                android:id="@+id/synce"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ems="10"
                android:hint="code"
                android:inputType="textPersonName"
                android:textSize="@dimen/_11sdp"
                android:visibility="gone" />

            <EditText
                android:id="@+id/username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_5sdp"
                android:hint="Password"
                android:inputType="textPassword"
                android:textSize="@dimen/_11sdp" />


            <LinearLayout
                android:id="@+id/athkarl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:visibility="gone">

                <Spinner
                    android:id="@+id/spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <EditText
                    android:id="@+id/number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ems="10"
                    android:inputType="number"
                    android:textSize="@dimen/_11sdp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="30dp"
                android:gravity="center">

                <Button
                    android:id="@+id/confirm_Button_ConfirmationDialog"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:layout_weight="1"
                    android:background="@drawable/button_green_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/yes"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_11sdp" />

                <Button
                    android:id="@+id/cancel_Button_ConfirmationDialog"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_weight="1"
                    android:background="@drawable/button_red_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/no"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_11sdp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</androidx.cardview.widget.CardView>

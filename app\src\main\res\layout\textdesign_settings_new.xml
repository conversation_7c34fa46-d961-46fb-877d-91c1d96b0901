<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <ImageView
        android:id="@+id/iv_bitmap"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <AbsoluteLayout
        android:id="@+id/abs_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true">

        <include
            android:id="@+id/td_tv_demo_down1"
            layout="@layout/textdesign_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_x="150dp"
            android:layout_y="100dp"
            android:text="مثال"
            android:visibility="invisible"
            tools:visibility="visible" />

        <include
            android:id="@+id/td_tv_demo_down2"
            layout="@layout/textdesign_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_x="180dp"
            android:layout_y="170dp"
            android:text="مثال"
            android:visibility="invisible"
            tools:visibility="visible" />

    </AbsoluteLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:orientation="vertical">


        <Space
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/hsv_text_features"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="@drawable/bg_black_fade_bottom"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="16dp"
            android:paddingBottom="8dp">

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:orientation="vertical">

                <Spinner
                    android:id="@+id/sp_fonts"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:background="@drawable/ic_fonts"
                    android:backgroundTint="@color/white"
                    android:dropDownVerticalOffset="40dp"
                    android:padding="4dp"
                    android:spinnerMode="dialog"
                    tools:listitem="@layout/layout_list_item_spinner_text_hidden" />


                <TextView
                    android:id="@+id/title_TextView_screensaver"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:text="@string/font"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_10sdp" />


            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/v_color2"
                        android:layout_width="@dimen/_40sdp"
                        android:layout_height="@dimen/_40sdp"
                        android:elevation="16dp"
                        android:minWidth="30dp"
                        app:cardBackgroundColor="#FFFFFF"
                        app:cardCornerRadius="128dp" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/v_color"
                        android:layout_width="@dimen/_36sdp"
                        android:layout_height="@dimen/_36sdp"
                        android:layout_gravity="center"
                        android:elevation="16dp"

                        android:minWidth="30dp"
                        app:cardBackgroundColor="#444444"
                        app:cardCornerRadius="128dp" />


                </FrameLayout>


                <TextView
                    android:id="@+id/ticolore_TextView_screensaver"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:text="@string/color"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_10sdp" />


            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:orientation="vertical">

                <Button
                    android:id="@+id/btn_size_add"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:background="@drawable/without_corners_20_background_white_with_drawable_add"
                    android:elevation="0dp"
                    android:gravity="center"
                    android:includeFontPadding="false" />

                <View
                    android:layout_width="@dimen/_5sdp"
                    android:layout_height="@dimen/_5sdp" />

                <Button
                    android:id="@+id/btn_size_minus"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_20sdp"
                    android:background="@drawable/without_corners_20_background_white_with_drawable_minus"
                    android:elevation="0dp"
                    android:gravity="center"
                    android:includeFontPadding="false" />


                <TextView
                    android:id="@+id/title2_TextView_screensaver"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:text="@string/size"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_10sdp" />


            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:orientation="vertical">

                <View
                    android:id="@+id/sp_text_input"
                    android:layout_width="@dimen/_40sdp"
                    android:layout_height="@dimen/_40sdp"
                    android:background="@drawable/ic_typeface"
                    android:backgroundTint="@color/white"
                    android:padding="4dp" />


                <TextView
                    android:id="@+id/title_TextVifew_screensaver"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:text="@string/text"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_10sdp" />


            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_td_enable"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="#E6000000"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="8dp">

            <TextView
                android:id="@+id/title_TextView_text_design"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/textdesign_toggle"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold" />

            <Spinner
                android:id="@+id/options_Spinner_textdesign"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_36sdp"
                android:layout_gravity="start"
                android:layout_marginStart="16dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:padding="8dp"
                android:spinnerMode="dialog"
                tools:listitem="@layout/layout_list_item_spinner_text" />

            <Spinner
                android:id="@+id/lines_Spinner_textdesign"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_36sdp"
                android:layout_gravity="start"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:dropDownVerticalOffset="@dimen/_40sdp"
                android:padding="8dp"
                android:spinnerMode="dialog"
                tools:listitem="@layout/layout_list_item_spinner_text" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_td_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="#E6000000"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="8dp">

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/td_cb_show_in_prayers"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingHorizontal="@dimen/_8sdp"
                android:layout_marginHorizontal="@dimen/_16sdp"
                android:buttonTint="@color/white"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/show_during_prayers"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold">

            </com.google.android.material.checkbox.MaterialCheckBox>

        </LinearLayout>


    </LinearLayout>

</FrameLayout>
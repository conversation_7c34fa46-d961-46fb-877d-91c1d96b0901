<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal|top"
    android:layout_margin="24dp"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:minWidth="320dp"
    android:orientation="vertical"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:minWidth="320dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/layout_gray_without_top_corners"
            android:gravity="center"
            android:paddingStart="20dp"
            android:paddingTop="12dp"
            android:paddingEnd="20dp"
            android:paddingBottom="12dp">

            <ImageView
                android:id="@+id/icon_ImageView_ConfirmationDialog"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                tools:srcCompat="@drawable/ic_menu_gallery" />


            <TextView
                android:id="@+id/title_TextView_ConfirmationDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:includeFontPadding="false"
                android:textColor="#343434"
                android:textSize="18sp"
                tools:hint="+966"
                tools:text="سبب الغياب" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/bodyMessage_TextView_ConfirmationDialog"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:lineSpacingExtra="10sp"
                android:textColor="@color/colorblack"
                android:textSize="14sp"
                tools:text="ادخال خطوط العرض والطول يدوي " />

            <EditText
                android:id="@+id/LATITUDE"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_marginTop="6dp"
                android:layout_marginRight="4dp"
                android:layout_marginBottom="4dp"
                android:hint="خط العرض"
                android:inputType="numberDecimal" />


            <EditText
                android:id="@+id/LONGITUDE"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_marginTop="6dp"
                android:layout_marginRight="4dp"
                android:layout_marginBottom="4dp"
                android:hint="خط الطول"
                android:inputType="numberDecimal" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="30dp"
                android:gravity="center">

                <Button
                    android:id="@+id/confirm_Button_ConfirmationDialog"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_green_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingStart="20dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="8dp"
                    android:text="@string/yes"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/cancel_Button_ConfirmationDialog"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/button_red_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingStart="20dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="20dp"
                    android:paddingBottom="8dp"
                    android:text="@string/no"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</androidx.cardview.widget.CardView>

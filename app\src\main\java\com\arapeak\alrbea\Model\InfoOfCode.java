package com.arapeak.alrbea.Model;

import com.arapeak.alrbea.Utils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class InfoOfCode {
    @Expose
    @SerializedName("id")
    private int id;
    @Expose
    @SerializedName("is_used")
    private String is_used;
    @Expose
    @SerializedName("status_id")
    private String status_id;
    @Expose
    @SerializedName("device_id")
    private String device_id;
    @Expose
    @SerializedName("code")
    private String code;
    @Expose
    @SerializedName("phone")
    private String phone;
    @Expose
    @SerializedName("name")
    private String name;
    @Expose
    @SerializedName("message")
    private String message;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getIs_used() {
        return Utils.getValueWithoutNull(is_used);
    }

    public void setIs_used(String is_used) {
        this.is_used = is_used;
    }

    public String getStatus_id() {
        return Utils.getValueWithoutNull(status_id);
    }

    public void setStatus_id(String status_id) {
        this.status_id = status_id;
    }

    public String getDevice_id() {
        return Utils.getValueWithoutNull(device_id);
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public String getCode() {
        return Utils.getValueWithoutNull(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPhone() {
        return Utils.getValueWithoutNull(phone);
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return Utils.getValueWithoutNull(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMessage() {
        return Utils.getValueWithoutNull(message);
    }

    public void setMessage(String message) {
        this.message = message;
    }

/*@Expose
    @SerializedName("req_date")
    private String req_date;
    @Expose
    @SerializedName("kind")
    private String kind;
    @Expose
    @SerializedName("mac")
    private String mac;
    @Expose
    @SerializedName("code")
    private String code;

    public String getReq_date() {
        return Utils.getValueWithoutNull(req_date);
    }

    public void setReq_date(String req_date) {
        this.req_date = req_date;
    }

    public String getKind() {
        return Utils.getValueWithoutNull(kind);
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getMac() {
        return Utils.getValueWithoutNull(mac);
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getCode() {
        return Utils.getValueWithoutNull(code);
    }

    public void setCode(String code) {
        this.code = code;
    }*/
}

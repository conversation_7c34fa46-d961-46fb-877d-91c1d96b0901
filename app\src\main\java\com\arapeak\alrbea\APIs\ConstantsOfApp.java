package com.arapeak.alrbea.APIs;

import com.arapeak.alrbea.hawk.HawkSettings;

import io.realm.Realm;

public class ConstantsOfApp {

    /////////////////////////////////////////////////////////////////////// URL  ///////////////////////////////////////////////////////////////////////

    //    public static final String BASE_URL_ALRABEEA_TIMES = "http://alrbea.com/Pray/api/api.php";
    public static final String BASE_URL_ALADHAN = "http://api.aladhan.com/v1/calendar";
    public static final String BASE_URL_ALRABEEA_TIMES = "https://alrbea.com/";
    //  public static final String CHECK_OR_ADD_LICENSES = BASE_URL_ALRABEEA_TIMES_WITH_LANGUAGE + "api/license";
    public static final String CHECK_OR_ADD_LICENSES = BASE_URL_ALRABEEA_TIMES + "wp-json/api/license";
    public static final String GET_AL_AHADETH = "http://alrbea.com/Pray/api/api.php?do=getHadeth";
    public static final String FILE_INFO = "https://alrbea.com/en/api/files";
    public static final String FILE_INFO_PRAY = "https://alrbea.com/download/files";
    public static final double LATITUDE_DEFAULT_KEY = 21.4359571;
    public static final double LONGITUDE_DEFAULT_KEY = 39.7064612;
    public static final int PRAYER_METHOD_DEFAULT_KEY = 4;


    //    public static String CHECK_ACTIVATION_CODE = BASE_URL_ALRABEEA_TIMES + "?do=isCodeExist&code=";
//    public static String USE_ACTIVATION_CODE = BASE_URL_ALRABEEA_TIMES + "?do=useCode&code=";
    public static final int ADJUST_HIJRI_DATE_DEFAULT_KEY = 0;
    public static final int APP_THEME_DEFAULT_KEY = 9;
    public static final long PRAYER_TIMES_UPDATE_DELAY = 30000;
    public static final String APP_ISHA = "appIsha";
    //  public static final String APP_THEME_KEY = "appTheme";
    public static final String APP_FIREBASE_STORAGE_CUSTOM_ICONS = "custom_icons";

    /////////////////////////////////////////////////////////////////////// Constants String  ///////////////////////////////////////////////////////////////////////
    public static final String APP_FIREBASE_DATABASE_PREMIUM_DEVICES = "premium_devices_new";
    //  public static final String APP_THEME_BROWN_KEY = "brownTheme";
//  public static final String APP_THEME_GREEN_KEY = "greenTheme";
//  public static final String APP_THEME_BLUE_KEY = "blueTheme";
//  public static final String APP_THEME_DARK_GREEN_KEY = "darkGreenTheme";
//  public static final String APP_THEME_RED_KEY = "redTheme";
//  public static final String APP_THEME_DARK_GRAY_KEY = "darkGrayTheme";
//  public static final String APP_THEME_DARK_Green_KEY = "darkGreenThemeN";
//  public static final String APP_THEME_RED_NEW_KEY = "redNewThemeN";
//  public static final String APP_THEME_BROWN_NEW_KEY = "brownNewThemeN";
//  public static final String APP_THEME_BLUE_NEW_KEY = "blueNewThemeN";
    public static final String APP_THEME_WHITE_KEY = "whiteTheme";
    //  public static final String APP_THEME_KEY_M = "appThemem";
    public static final String APP_THEME_KEY_E = "appThemee";
    public static final String APP_THEME_MORNING_BROWN_KEY = "brownMorningTheme";
    public static final String APP_THEME_MORNING_GREEN_KEY = "greenMorningTheme";
    public static final String APP_THEME_MORNING_BLUE_KEY = "blueMorningTheme";
    //  public static final String APP_THEME = "Themekey";
    public static final String APP_THEME_EVENING_BROWN_KEY = "brownEveningTheme";
    public static final String APP_THEME_EVENING_GREEN_KEY = "greenEveningTheme";
    public static final String APP_THEME_EVENING_BLUE_KEY = "blueEveningTheme";
    public static final String REALM_DB_ALRABEEA_TIMES_KEY = "realm_alrabeea_times.realm";
    public static final String REALM_DB_ALRABEEA_TIMES_OLD_KEY = "realm_alrabeea_times_old.realm";
    public static final String IS_ADD_COUNTRY_CITY_KEY = "isAddCountryCity";
    public static final String SUPPORT_APKS_VERSION = "support_apks_version";
    public static final String TIME_OF_ATHKAR_AFTER_KEY = "timeOfAthkarAfter";
    //  public static final String LATITUDE_KEY = "latitude";
//  public static final String LONGITUDE_KEY = "longitude";
    public static final String IS_INITIAL_SETUP_KEY = "isInitialSetup";
    public static final String INFO_OF_CODE_KEY = "infoOfCode";
    public static final String PRAYER_METHOD_KEY = "prayerMethod";
    public static final String SYSTEM_TIME_KEY = "0";
    public static final int SECONDS_SHOW_KEY = 0;
    public static final int PACKAGE_INSTALL_PERMISSION_REQUEST_CODE = 1;
    public static final int PACKAGE_INSTALLING_APK_REQUEST_CODE = 2;
    public static final String TEAM_VIEWER_QUICK_SUPPORT_FILENAME = "teamviewer.apk";
    public static final String TEAM_VIEWER_QUICK_SUPPORT_PACKAGE_NAME = "com.teamviewer.quicksupport.market";
    public static final String TEAM_VIEWER_QUICK_SUPPORT_URL =
            "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/teamapk%2Fteamviewer-quicksupport-15-54-476.apk?alt=media&token=0d73ff89-2633-4aa5-812c-8d238b438dd7";
    public static final String TEAM_VIEWER_QUICK_SUPPORT_ADDON_FILENAME = "team.apk";
    //  public static final String TEAM_VIEWER_QUICK_SUPPORT_ADDON_PACKAGE_NAME = "com.teamviewer.quicksupport.addon.aosp14";
    public static final String TEAM_VIEWER_QUICK_SUPPORT_ADDON_PACKAGE_NAME = "com.teamviewer.quicksupport.addon.universal";
    //  public static final String TEAM_VIEWER_QUICK_SUPPORT_ADDON_URL = "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/teamapk%2Fteam.apk?alt=media&token=4c125359-aed8-48d2-9673-6e3ddd4b4a2f";
    public static final String TEAM_VIEWER_QUICK_SUPPORT_ADDON_URL =
            "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/teamapk%2FTeamViewer%20Universal%20Add-On_15.51.393.apk?alt=media&token=16631a64-5e37-4bf7-bec9-21356b6008c1";
    //  public static final String TEAM_VIEWER_QUICK_SUPPORT_URL = "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/teamapk%2Fteamviewer_quicksupport.apk?alt=media&token=d4a3e23f-45be-45b1-9b66-7904cb738c08";
//  public static final String TEAM_VIEWER_QUICK_SUPPORT_URL = "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/teamapk%2Fteamviewer.apk?alt=media&token=2b2e7d1a-8391-4e9a-8871-a8fed4686847";
    public static final int LOCATION_REQUEST = 1000;
    public static final int GPS_REQUEST = 1001;
    public static final String EVENT_ENABLE = "event_enable";
    public static final String PRAYER_API_KEY = "prayerApi";
    public static final String ADJUST_HIJRI_DATE_KEY = "adjustHijriDate";
    /*  public static final String HIJRI_DATE_KEY = "HijriDate";
      public static final String HIJRI_DATE_KEYD = "HijriDated";
      public static final int HIJRI_DATE_Def = 2;*/
    public static final String IS_ARABIC_NUMBER = "isArabicNumber";
    public static final String IS_SYSTEM_TIME = "isSystemTime";


    public static final String IS_SECONEDS_SHOW = "isSeconedsShow";
    public static final String UPDATE_LOCATION_KEY = "updateLocation";
    public static final String LAST_UPDATE_DATA_PRAYER_TIME_KEY = "lastUpdateDataPrayerTime";

    public static final String IS_LANDSCAPE_BOOLEAN_KEY = "isLandscapeBoolean";
    public static final String EVENT_KEY = "event";
    public static final String FUNERAL_MESSAGES_KEY = "funeralMessages";
    public static final String EVENT_IMAGE_KEY = "imageEvent";
    public static final String APPEARANCE_ALTERNATELY_KEY = "appearanceAlternately";
    public static final String APPEARANCE_TIME_KEY = "appearanceTime";
    /*  public static final int APPEARANCE_ALTERNATELY_MINUTES_PRAYER_DEF = 5;

      public static final int APPEARANCE_ALTERNATELY_MINUTES_PHOTO_DEF = 5;*/
    public static final String APPEARANCE_ALTERNATELY_MINUTES_PRAYER = "appearanceAlternatelyMinutesprayer";
    public static final String APPEARANCE_ALTERNATELY_HOURS_PRAYER = "appearanceAlternatelyMinutesprayer";
    public static final String APPEARANCE_ALTERNATELY_SECONDS_PRAYER = "appearanceAlternatelyMinutesprayer";
    public static final String APPEARANCE_ALTERNATELY_MINUTES_PHOTO = "appearanceAlternatelyMinutesphoto";
    public static final String APPEARANCE_ALTERNATELY_HOURS_PHOTO = "appearanceAlternatelyMinutesphoto";
    public static final String APPEARANCE_ALTERNATELY_SECONDS_PHOTO = "appearanceAlternatelyMinutesphoto";
    public static final String NEWS_TICKER_KEY = "newsTicker";
    public static final String ALWAYS_AND_CLOSE_ON_PRAYER_TIME_KEY = "alwaysAndCloseOnPrayerTime";
    public static final String TIMINGS_ALRABEEA_TIMES_LIST_KEY = "timingsAlrabeeaTimesList";
    public static final String IS_CUSTOM_KEY = "isCustom";
    public static final String IS_UPDATE_PRAY_TIME = "isUpdatePrayTime";
    public static final String IS_ENABLE_KEY = /*RAMADAN_KEY +*/ "isEnable";
    public static final String FAJR_KEY = "fajrPrayer";
    public static final String DUHA_KEY = "duhaPrayer";
    public static final String SUNRISE_KEY = "sunrisePrayer";
    public static final String DHUHR_KEY = "dhuhrPrayer";
    public static final String ASR_KEY = "asrPrayer";
    public static final String MAGHRIB_KEY = "maghribPrayer";
    public static final String ISHA_KEY = "ishaPrayer";
    public static final String JOMAA_KEY = "jomaaPrayer";
    public static final String SEMON_KEY = "semonPrayer";
    public static final String TARAWIH_KEY = "tarawihPrayer";
    public static final String TAHAJJUD_KEY = "tahajjudPrayer";
    public static final String CITATION_FOR_MORNING_TIME_KEY = "citationForMorningTime";
    public static final String CITATION_FOR_EVENING_TIME_KEY = "citationForEveningTime";
    public static final int SCREEN_SLEEP = 0;
    public static final String MORNING_TIME_KEY = "morningkey";
    public static final String MORNING_TIME_NAME = "قبل الشروق";
    public static final String EVENING_TIME_KEY = "eveningkey";
    public static final String EVENING_TIME_NAME = "بعد العصر";
    public static final String TARAWIH_TIME_KEY = "tarawihkey";
    public static final String ATHKARS_AFTER_KEY = "athkarsAfter";
    public static final String ATHKARS_AFTER_FAJER_PRAYER_KEY = ATHKARS_AFTER_KEY + FAJR_KEY;
    public static final String IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_KEY = IS_ENABLE_KEY + ATHKARS_AFTER_FAJER_PRAYER_KEY;
    public static final String ATHKARS_AFTER_DHUHR_PRAYER_KEY = ATHKARS_AFTER_KEY + DHUHR_KEY;
    public static final String IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_KEY = IS_ENABLE_KEY + ATHKARS_AFTER_DHUHR_PRAYER_KEY;
    public static final String ATHKARS_AFTER_ASER_PRAYER_KEY = ATHKARS_AFTER_KEY + ASR_KEY;
    public static final String IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_KEY = IS_ENABLE_KEY + ATHKARS_AFTER_ASER_PRAYER_KEY;
    public static final String ATHKARS_AFTER_MAGHRIB_PRAYER_KEY = ATHKARS_AFTER_KEY + MAGHRIB_KEY;
    public static final String IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_KEY = IS_ENABLE_KEY + ATHKARS_AFTER_MAGHRIB_PRAYER_KEY;
    public static final String ATHKARS_AFTER_ISHA_PRAYER_KEY = ATHKARS_AFTER_KEY + ISHA_KEY;
    public static final String IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_KEY = IS_ENABLE_KEY + ATHKARS_AFTER_ISHA_PRAYER_KEY;
    public static final String ATHKARS_AFTER_JOMAA_PRAYER_KEY = ATHKARS_AFTER_KEY + JOMAA_KEY;
    public static final String IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_KEY = IS_ENABLE_KEY + ATHKARS_AFTER_JOMAA_PRAYER_KEY;
    public static final String ATHKARS_MORNING_PRAYER_KEY = "athkarsmorningPrayer";
    public static final String ATHKARS_EVNING_PRAYER_KEY = "athkarsevningPrayer";
    public static final String TITLE_JOMAA = "titlejomaa";


    public static final String IS_ENABLE_FUNERAL_MESSAGES_KEY = "isEnableFuneralMessages";
    public static final String IS_ENABLE_ADD_HALF_HOUR_ISHA_KEY = "isEnableAddHalfHourIsha";
    public static final String IS_THERE_CITATION_FOR_MORNING_KEY = "isThereCitationForMorningTime";
    public static final String IS_THERE_CITATION_FOR_EVENING_KEY = "isThereCitationForEveningTime";
    public static final String IS_SHOW_CITATION_FOR_MORNING_KEY = "isShowCitationForMorningTime";
    public static final String IS_SHOW_CITATION_FOR_EVENING_KEY = "isShowCitationForEveningTime";
    public static final String RAMADAN_KEY = "ramadan";
    public static final String TYPE_OF_AZHAR_MORNING_EVENING_KEY = "azharMorningEveningType";
    public static final String TYPE_OF_AL_AHADETH_KEY = "alAhadethType";
    public static final String TYPE_OF_SHOW_DATE_KEY = "typeOfShowDate";
    public static final String TYPE_OF_SCREEN_SLEEP_KEY = "typeOfScreenSleep";
    public static final String SHOW_SELINT = "showselint";
    public static final String SCREEN_NUMBER = "screennumber";
    public static final String SCREEN_NUMB = "screennumb";
    public static final int SCREEN_NUMBER_D = 0;
    public static final String SYNCE_USER_KEY = "synceuser";
    public static final String SYNCE_USER_DEF = "12345";
    public static final String SYNCE_MAIN = "syncemain";
    public static final String SYNCE_PRAYER = "synceprayer";
    public static final String SYNCE_ATHKAR = "synceathakar";
    public static final String SYNCE_THEME = "syncetheme";
    public static final String SYNCE_LOCATION = "syncelocation";
    public static final String SYNCE_NEWS = "syncenews";
    public static final String SYNCE_MESSAGE = "syncemessage";
    public static final String SOUND_AZAN_ENABLE = "soundenable";
    public static final String SOUND_IKAMA_ENABLE = "soundIkamaenable";
    public static final String SOUND_ENABLE_PRAYER = "soundenableprayer";
    public static final String DARAGT_SOUND = "dargtsoundall";
    public static final String TYPE_SOUND = "typesound";
    public static final String NAME_SOUND = "namesound";
    public static final String CUSTOM_AZAN = "customAzan";
    public static final String CUSTOM_IKAMA = "customIkama";
    public static final int SOUND_NORMAL = 10;
    public static final int SOUND_ESLAM = 0;
    public static final int SOUND_HAMED = 1;
    public static final int SOUND_SAID = 2;
    public static final int SOUND_ABDUALL = 3;
    public static final int SOUND_ABDUALMAJED = 4;
    public static final int SOUND_MOHAMME = 5;
    public static final int SOUND_MASHARY = 6;
    public static final int SOUND_OADIA = 7;


    public static final int FONT_NORMAL = 10;
    public static final int FONT_A_JANNAT = 0;
    public static final int FONT_ALHURRA = 1;
    public static final int FONT_ALJAZEERA = 2;
    public static final int FONT_DIN_NEXT = 3;
    public static final int FONT_DIODRUM = 4;
    public static final int FONT_DROID_KUFI = 5;
    public static final int FONT_NAWAR = 6;
    public static final int FONT_ROBOTO = 7;
    public static final String WIGTT_FONT = "wigitfont";
    public static final boolean SYNCE_TRUE = true;
    public static final boolean SYNCE_FALSE = false;
    public static final String AL_AHADETH_KEY = "alAhadeth";
    public static final String AL_AHADETH_LIST_KEY = AL_AHADETH_KEY + "List";
    public static final String IS_ENABLE_AL_AHADETH_KEY = IS_ENABLE_KEY + AL_AHADETH_KEY;
    public static final String AL_AHADETH_DURATION_LONG_KEY = AL_AHADETH_KEY + "DurationLong";
    public static final String AL_AHADETH_DURATION_STRING_KEY = AL_AHADETH_KEY + "DurationString";
    public static final String AL_AHADETH_LAST_TIME_SHOW_KEY = AL_AHADETH_KEY + "lastTimeShow";
    public static final String POST_OR_PRE_TO_KEY = "postOrPreToPray";
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_KEY = "timeBetweenAdanAndIkama";
    public static final String TIME_OF_IKAMA_KEY = "timeOfIkama";
    public static final String ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY = "announcementShowTimeIkama";
    public static final String RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_KEY = RAMADAN_KEY + "announcementShowTimeIkama";
    public static final String PRAYER_TIME_ANNOUNCEMENT_KEY = "prayerTimeAnnouncement";
    public static final String LOCK_DURING_PRAYER_KEY = "lockDuringPrayer";
    public static final String TIMINGS_ALRABEEA_TIMES_NOW_KEY = "timingsAlrabeeaTimesNow";
    public static final String DATE_ALRABEEA_TIMES_NOW_KEY = "dateAlrabeeaTimesNow";
    public static final String IS_START = "isStart";
    public static final String POST_OR_PRE_TO_FAJR_KEY = POST_OR_PRE_TO_KEY + FAJR_KEY;
    public static final String POST_OR_PRE_TO_SUNRISE_KEY = POST_OR_PRE_TO_KEY + SUNRISE_KEY;
    public static final String POST_OR_PRE_TO_DUHA_KEY = POST_OR_PRE_TO_KEY + DUHA_KEY;
    public static final String POST_OR_PRE_TO_DHUHR_KEY = POST_OR_PRE_TO_KEY + DHUHR_KEY;
    public static final String POST_OR_PRE_TO_ASR_KEY = POST_OR_PRE_TO_KEY + ASR_KEY;
    public static final String POST_OR_PRE_TO_MAGHRIB_KEY = POST_OR_PRE_TO_KEY + MAGHRIB_KEY;
    public static final String POST_OR_PRE_TO_ISHA_KEY = POST_OR_PRE_TO_KEY + ISHA_KEY;
    public static final String POST_OR_PRE_TO_JOMAA_KEY = POST_OR_PRE_TO_KEY + JOMAA_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_FAJR_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + FAJR_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_SUNRISE_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + SUNRISE_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_DUHA_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + SUNRISE_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_DHUHR_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + DHUHR_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_ASR_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + ASR_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_MAGHRIB_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + MAGHRIB_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_ISHA_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + ISHA_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_JOMAA_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + JOMAA_KEY;
    public static final String OPTION_OF_TARAWIH_KEY = "optionOfTarawihOfTarawih";
    public static final String OPTION_OF_TAHAJJUD_KEY = "optionOfTahajjudOfTahajjud";
    public static final String EVENT_ALRABEEA_TIMES_ID_KEY = "eventAlrabeeaTimesId";
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + FAJR_KEY;
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + SUNRISE_KEY;
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + DHUHR_KEY;
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ASR_KEY;
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + MAGHRIB_KEY;
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ISHA_KEY;
    public static final String TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY = TIME_BETWEEN_ADAN_AND_IKAMA_KEY + JOMAA_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + FAJR_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + SUNRISE_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + DHUHR_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ASR_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + MAGHRIB_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ISHA_KEY;
    public static final String RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY = RAMADAN_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + JOMAA_KEY;
    public static final String TIME_OF_IKAMA_FAJR_KEY = TIME_OF_IKAMA_KEY + FAJR_KEY;
    public static final String TIME_OF_IKAMA_SUNRISE_KEY = TIME_OF_IKAMA_KEY + SUNRISE_KEY;
    public static final String TIME_OF_IKAMA_DHUHR_KEY = TIME_OF_IKAMA_KEY + DHUHR_KEY;
    public static final String TIME_OF_IKAMA_ASR_KEY = TIME_OF_IKAMA_KEY + ASR_KEY;
    public static final String TIME_OF_IKAMA_MAGHRIB_KEY = TIME_OF_IKAMA_KEY + MAGHRIB_KEY;
    public static final String TIME_OF_IKAMA_ISHA_KEY = TIME_OF_IKAMA_KEY + ISHA_KEY;
    public static final String TIME_OF_IKAMA_JOMAA_KEY = TIME_OF_IKAMA_KEY + JOMAA_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_FAJR_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + FAJR_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_SUNRISE_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + SUNRISE_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_DHUHR_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + DHUHR_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_ASR_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + ASR_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_MAGHRIB_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + MAGHRIB_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_ISHA_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + ISHA_KEY;
    public static final String RAMADAN_TIME_OF_IKAMA_JOMAA_KEY = RAMADAN_KEY + TIME_OF_IKAMA_KEY + JOMAA_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_PRAY_KEY = "isEnablePostOrPrePray";
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_PRAY_KEY = RAMADAN_KEY + "isEnablePostOrPrePray";
    public static final String IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + FAJR_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + SUNRISE_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + DHUHR_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_TO_ASR_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + ASR_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + MAGHRIB_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + ISHA_KEY;
    public static final String IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY = IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + JOMAA_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_FAJR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + FAJR_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_SUNRISE_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + SUNRISE_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_DHUHR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + DHUHR_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ASR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + ASR_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_MAGHRIB_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + MAGHRIB_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_ISHA_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + ISHA_KEY;
    public static final String RAMADAN_IS_ENABLE_POST_OR_PRE_TO_JOMAA_KEY = RAMADAN_KEY + IS_ENABLE_KEY + POST_OR_PRE_TO_KEY + JOMAA_KEY;
    public static final String RAMADAN_IS_ENABLE_TARAWIH_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TARAWIH_KEY;
    public static final String RAMADAN_IS_ENABLE_TAHAJJUD_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TAHAJJUD_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + TARAWIH_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY_H = RAMADAN_KEY + POST_OR_PRE_TO_KEY + TARAWIH_KEY + "h";
    public static final String RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY = RAMADAN_KEY + POST_OR_PRE_TO_KEY + TAHAJJUD_KEY;
    public static final String RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME = RAMADAN_KEY + POST_OR_PRE_TO_KEY + TAHAJJUD_KEY + "timeb";
    public static final String TAHAJJUD_PRAYER_TIME_MINUTE = "tahajjudPrayerTimeMinute";
    public static final String TAHAJJUD_PRAYER_TIME_HOUR = "tahajjudPrayerTimeHour";
    public static final String RAMADAN_DURATION_OF_TARAWIH_KEY = "ramadanDurationOfTarawih";
    public static final String RAMADAN_DURATION_OF_TARAWIH_KEY_H = "ramadanDurationOfTarawihh";
    public static final String RAMADAN_DURATION_OF_TAHAJJUD_KEY = "ramadanDurationOfTahajjud";
    public static final String RAMADAN_DURATION_OF_TAHAJJUD_KEY_H = "ramadanDurationOfTahajjudh";
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + FAJR_KEY;
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + SUNRISE_KEY;
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + DHUHR_KEY;
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ASR_KEY;
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + MAGHRIB_KEY;
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ISHA_KEY;
    public static final String IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY = IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + JOMAA_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + FAJR_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_SUNRISE_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + SUNRISE_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + DHUHR_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ASR_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + MAGHRIB_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + ISHA_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_BETWEEN_ADAN_AND_IKAMA_KEY + JOMAA_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + FAJR_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + SUNRISE_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + DHUHR_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_ASR_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + ASR_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + MAGHRIB_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + ISHA_KEY;
    public static final String IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY = IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + JOMAA_KEY;
    public static final String IS_ENABLE_CREATE_PHOTO_GALLERY_IKAMA = "createPhotoGalleryikama";
    public static final String IS_ENABLE_CREATE_PHOTO_GALLERY_IKAMA_JOMAA = "createPhotoGalleryikamajomaa";
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_FAJR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + FAJR_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_SUNRISE_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + SUNRISE_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_DHUHR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + DHUHR_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ASR_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + ASR_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_MAGHRIB_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + MAGHRIB_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_ISHA_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + ISHA_KEY;
    public static final String RAMADAN_IS_ENABLE_TIME_OF_IKAMA_JOMAA_KEY = RAMADAN_KEY + IS_ENABLE_KEY + TIME_OF_IKAMA_KEY + JOMAA_KEY;
    public static final String BACKGROUND_IMAGE_KEY = "backgroundImage";
    public static final String BACKGROUND_IMAGE_ALPHA_KEY = "backgroundImageAlpha";
    public static final String LOCALE_LANGUAGE_KEY = "localeLanguage";
    public static final String YEAR = "yyyy";
    public static final String MONTH = "MM";
    public static final String DAY = "dd";
    public static final String DEFAULT_LANGUAGE = "ar";
    public static final String BASE_URL_ALRABEEA_TIMES_WITH_LANGUAGE = HawkSettings.isArabic() ? BASE_URL_ALRABEEA_TIMES + "ar/" : BASE_URL_ALRABEEA_TIMES + "en/";
    public static final String CONTACT_US = BASE_URL_ALRABEEA_TIMES_WITH_LANGUAGE + "api/contact-us";
    public static final String GET_APP_INFO = BASE_URL_ALRABEEA_TIMES_WITH_LANGUAGE + "api/license/version";

    public static final String EN_LANGUAGE = "en";
    public static final String TIME_OF_IKAMA_FAJR_DEFAULT = "00:00";
    public static final String TIME_OF_IKAMA_DHUHR_DEFAULT = "00:00";
    public static final String TIME_OF_IKAMA_ASR_DEFAULT = "00:00";
    public static final String TIME_OF_IKAMA_MAGHRIB_DEFAULT = "00:00";
    public static final String TIME_OF_IKAMA_ISHA_DEFAULT = "00:00";
    public static final String TIME_OF_IKAMA_JOMAA_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_FAJR_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_DHUHR_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_ASR_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_MAGHRIB_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_ISHA_DEFAULT = "00:00";
    public static final String RAMADAN_TIME_OF_IKAMA_JOMAA_DEFAULT = "00:00";
    public static final int POST_OR_PRE_PRAY_DEFAULT = 0;
    public static final int POST_OR_PRE_DUHA_PRAY_DEFAULT = 15;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT = 10;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT = 25;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT = 20;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT = 20;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT = 10;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT = 20;
    public static final int TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT = 15;
    public static final int ATHKARS_MORNING_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_EVNING_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_FAJER_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_ASER_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_ISHA_PRAYER_DEFAULT = 5;
    public static final int ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT = 5;
    public static final int ARG_TYPE_OF_SHOW_HIJRY = 0;
    public static final int ARG_TYPE_OF_SHOW_GREGORIAN = 1;
    public static final int ARG_TYPE_OF_SHOW_BOTH = 2;
    public static final int ARG_TYPE_OF_NORMAL_SLEEP = 0;
    public static final int ARG_TYPE_OF_SHOW_BLACK_SLEEP = 1;
    public static final int ARG_TYPE_OF_SHOW_BLACK_TIME_SLEEP = 2;
    public static final int POST_OR_PRE_PRAY_TARAWIH_DEFAULT = 0;
    public static final String POST_OR_PRE_PRAY_TAHAJJUD_DEFAULT = "";
    public static final int RAMADAN_DURATION_OF_TARAWIH_DEFAULT = 30;
    public static final int RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT = 30;
    public static final int RAMADAN_POST_OR_PRE_PRAY_DEFAULT = 0;
    public static final int RAMADAN_POST_OR_PRE_DUHA_PRAY_DEFAULT = 15;
    public static final int RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_FAJR_DEFAULT = 25;
    public static final int RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_DHUHR_DEFAULT = 20;
    public static final int RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ASR_DEFAULT = 20;
    public static final int RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_MAGHRIB_DEFAULT = 10;
    public static final int RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_ISHA_DEFAULT = 20;
    public static final int RAMADAN_TIME_BETWEEN_ADAN_AND_IKAMA_JOMAA_DEFAULT = 30;
    public static final int RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT = 15;
    public static final int RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT = 10;
    public static final int RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT = 10;
    public static final int RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT = 6;
    public static final int RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT = 13;
    public static final int RAMADAN_ANNOUNCEMENT_SHOW_TIME_IKAMA_JOMAA_DEFAULT = 11;
    public static final int ANNOUNCEMENT_SHOW_TIME_IKAMA_FAJR_DEFAULT = 10;
    public static final int ANNOUNCEMENT_SHOW_TIME_IKAMA_DHUHR_DEFAULT = 10;
    public static final int ANNOUNCEMENT_SHOW_TIME_IKAMA_ASR_DEFAULT = 10;
    public static final int ANNOUNCEMENT_SHOW_TIME_IKAMA_MAGHRIB_DEFAULT = 5;
    public static final int ANNOUNCEMENT_SHOW_TIME_IKAMA_ISHA_DEFAULT = 10;
    public static final int ANNOUNCEMENT_SHOW_TIME_IKAMA_JOMAA_DEFAULT = 15;
    public static final int ADD_HALF_HOUR_ISHA_DEFAULT = 30;

    public static final int ARG_APP_THEME_CUSTOM_FIREBASE = 14;
    public static final int ATHKARS_FOR_MORNING_AND_EVENING_DEFAULT = 30;
    public static final int RAMADAN_LOCK_DURING_PRAYER_FAJR_DEFAULT = 11;
    public static final int RAMADAN_LOCK_DURING_PRAYER_DHUHR_DEFAULT = 10;
    public static final int RAMADAN_LOCK_DURING_PRAYER_ASR_DEFAULT = 10;
    public static final int RAMADAN_LOCK_DURING_PRAYER_MAGHRIB_DEFAULT = 10;
    public static final int RAMADAN_LOCK_DURING_PRAYER_ISHA_DEFAULT = 11;
    public static final int RAMADAN_LOCK_DURING_PRAYER_JOMAA_DEFAULT = 10;
    public static final int LOCK_DURING_PRAYER_FAJR_DEFAULT = 10;
    public static final int LOCK_DURING_PRAYER_DHUHR_DEFAULT = 10;
    public static final int LOCK_DURING_PRAYER_ASR_DEFAULT = 10;
    public static final int LOCK_DURING_PRAYER_MAGHRIB_DEFAULT = 10;
    public static final int LOCK_DURING_PRAYER_ISHA_DEFAULT = 11;
    public static final int LOCK_DURING_PRAYER_JOMAA_DEFAULT = 10;
    public static final long SECONDS_MILLI_SECOND = 1000L;
    public static final long MINUTES_MILLI_SECOND = 60000L;
    public static final long HOURS_MILLI_SECOND = 3600000L;
    public static final long DAYS_MILLI_SECOND = 86400000L;
    public static final long MONTH_MILLI_SECOND = 2592000000L;
    public static final long YEAR_MILLI_SECOND = 31104000000L;
    //    public static final long THE_INTERVAL_BETWEEN_EACH_IMAGE = 60000L;
    public static final long THE_INTERVAL_BETWEEN_EACH_IMAGE = 1500L;
    public static final int OPTION_OF_TARAWIH_DEFAULT = 1;
    public static final int OPTION_OF_TAHAJJUD_DEFAULT = 1;
    public static final long DURATION_OF_AL_AHADETH_DEFAULT = 0L;
    public static final int DURATION_OF_AZHAR_DEFAULT = 5;
    public static final int DURATION_OF_AZHAR_MORNING_EVENING_DEFAULT = 300000;
    public static final int DURATION_SHOW_OF_AL_AHADETH_DEFAULT = 1;
    public static final int PRAYER_TIME_ANNOUNCEMENT_DEFAULT = 4;
    public static final int PRAYER_TIME_ANNOUNCEMENT_FOR_DUHA = 5;
    public static final int DURATION_OF_FUNERAL_MESSAGES_DEFAULT = 4;//TODO
    public static final long _4_MINUTES_MILLI_SECOND = 4 * MINUTES_MILLI_SECOND;
    public static final long _5_MINUTES_MILLI_SECOND = 5 * MINUTES_MILLI_SECOND;
    public static final long _14_MINUTES_MILLI_SECOND = 14 * MINUTES_MILLI_SECOND;
    public static final long _15_MINUTES_MILLI_SECOND = 15 * MINUTES_MILLI_SECOND;
    public static final long _25_MINUTES_MILLI_SECOND = 25 * MINUTES_MILLI_SECOND;
    public static final boolean IS_ENABLE_ADD_HALF_HOUR_ISHA_DEFAULT = true;
    //    public static final int DURATION_OF_FUNERAL_MESSAGES_DEFAULT = 1;//TODO
    public static final boolean RAMADAN_IS_ENABLE_TARAWIH_BOO = true;
    public static final boolean RAMADAN_IS_ENABLE_TAHAJJUD_BOO = true;
    public static final boolean IS_ENABLE_FUNERAL_MESSAGES_DEFAULT = false;
    public static final boolean IS_ENABLE_SETTINGS_DEFAULT = false;
    public static final boolean DEFAULT_FOR_SHOW_ATHKER = true;
    public static final boolean DEFAULT_FOR_IS_THERE_ATHKER = true;
    public static final boolean IS_ENABLE_JOMAA_DEFAULT = true;
    public static final boolean IS_ENABLE_PRAYER_TIME_ANNOUNCEMENT_DEFAULT = true;
    public static final boolean IS_ENABLE_TIME_BETWEEN_ADAN_AND_IKAMA_DEFAULT = true;
    public static final boolean IS_ENABLE_TIME_OF_IKAMA_DEFAULT = false;
    public static final boolean IS_ENABLE_ANNOUNCEMENT_SHOW_TIME_IKAMA_DEFAULT = true;
    public static final boolean IS_ENABLE_LOCK_DURING_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_FAJER_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_DHUHR_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_ASER_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_MAGHRIB_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_ISHA_PRAYER_DEFAULT = true;
    public static final boolean IS_ENABLE_ATHKARS_AFTER_JOMAA_PRAYER_DEFAULT = true;
    public static String AzkarRef = "azkar/";
    public static Realm realm;

    public ConstantsOfApp() {
    }

    public static String getYEAR() {
        return YEAR;
    }

    public static String getMONTH() {
        return MONTH;
    }

    public static String getDAY() {
        return DAY;
    }

    public static Realm getRealm() {
        return realm;
    }

}

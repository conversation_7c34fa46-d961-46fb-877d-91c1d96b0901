package com.arapeak.alrbea.UI.CustomView;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class SpacesItemDecoration extends RecyclerView.ItemDecoration {
    private final int space;
    private final int typeSpaceTop;

    public SpacesItemDecoration(int space, int typeSpaceTop) {
        this.space = space;
        this.typeSpaceTop = typeSpaceTop;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view,
                               @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        outRect.left = space;
        outRect.right = space;
        outRect.bottom = space;

        // Add top margin only for the first item to avoid double space between items
        switch (typeSpaceTop) {
            case 0:
                outRect.top = 0;
                break;
            case 1:
                outRect.top = space;
                break;
            case 2:
                if (parent.getChildLayoutPosition(view) == 0) {
                    outRect.top = space;
                } else {
                    outRect.top = 0;
                }
                break;
        }
    }
}
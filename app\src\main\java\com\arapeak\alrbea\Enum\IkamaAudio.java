package com.arapeak.alrbea.Enum;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.CUSTOM_IKAMA;

import android.net.Uri;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;

public enum IkamaAudio implements IAudio {
    ALERT,
    ALGAZALI,
    ISAM,
    RASHID,
    HAMAD,//doghriri
    MEDHAT,
    CUSTOM;

    public Uri getUri() {
        return Uri.parse(getPath());
    }

    public String getPath() {
        if (this == CUSTOM)
            return Hawk.get(CUSTOM_IKAMA, "");
        return Utils.getDownloadPath() + "/ikama/" + this.ordinal();
    }

    public String getURL() {
        switch (this) {
            case ALGAZALI:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/IkamaMp3%2Fikama_algazali.mp3?alt=media&token=0cc8c166-15c6-4e25-a52e-6bea4a944a09";
            case ISAM:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/IkamaMp3%2Fhamad_eldugairy.mp3?alt=media&token=f5b3a301-f7f8-4f86-98e7-7d7761e661ab";
            case HAMAD:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/IkamaMp3%2Fisam_bukhari.mp3?alt=media&token=77d7129d-c133-4464-a2ce-3b4a72aeeb27";
            case MEDHAT:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/IkamaMp3%2Fmidhat_ramadan.mp3?alt=media&token=4b121374-279a-43d8-8a7c-c8b3fdcf234d";
            case RASHID:
                return "https://firebasestorage.googleapis.com/v0/b/prayer-996d2.appspot.com/o/IkamaMp3%2Frashid_meshari.mp3?alt=media&token=6519ff7e-0c5c-4a04-b6ba-5df3ab8c3298";
            default:
                return "";
        }
    }

    public String getName() {
        if (this == ALERT)
            return Utils.getString(R.string.audio_alert);
        else {
            String[] values = Utils.getStringArray(R.array.prayer_ikama_sound);
            int index = ordinal() - 1;
            return values[index];
        }
    }

    @Override
    public int getOrdinal() {
        return ordinal();
    }

    public Boolean isOffline() {
        return this == ALERT;
    }

    @Override

    public int getOfflineResource() {
        if (!this.isOffline())
            return 0;

        switch (this) {
            case ALERT:
                return R.raw.a;

        }
        return 0;
    }
}

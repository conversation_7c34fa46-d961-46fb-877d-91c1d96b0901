<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="@dimen/_5sdp"
    tools:layoutDirection="rtl">

    <TextView
        android:id="@+id/country_TextView_CountryViewHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_5sdp"
        android:fontFamily="@font/droid_arabic_kufi_bold"
        android:includeFontPadding="false"
        android:textColor="@android:color/black"
        android:textSize="@dimen/_13sdp"
        android:textStyle="bold"
        tools:text="فلسطين" />

    <View
        android:id="@+id/space_View_CountryViewHolder"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_5sdp"
        android:background="@android:color/darker_gray" />

</LinearLayout>
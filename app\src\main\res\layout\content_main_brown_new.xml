<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".UI.Activity.MainActivity">

    <ImageView
        android:id="@+id/background_ImageView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/img_background_brown_new"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/imageView5"
        style="@style/mosqueIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/colorGrayDark" />

    <LinearLayout
        android:id="@+id/timeNow_LinearLayout_MainActivity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:layoutDirection="ltr"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView5">

        <androidx.appcompat.widget.AppCompatTextView
            app:autoSizeTextType="uniform"
            android:id="@+id/timeNow_TextView_MainActivity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_minus5sdp"
            android:layout_marginBottom="@dimen/_minus5sdp"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_40sdp"
            android:textStyle="bold"
            tools:text="11:25" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/time_seconds"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_minus8sdp"
                android:layout_marginBottom="@dimen/_minus8sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_20sdp"
                tools:text="13" />

            <TextView
                android:id="@+id/timeNowType_TextView_MainActivity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_minus8sdp"
                android:layout_marginBottom="@dimen/_minus8sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:textColor="@color/ambrownnew"
                android:textSize="@dimen/_20sdp"
                tools:text="AM" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/DateNow_TextView_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_40sdp"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/_15sdp"
        android:layout_marginRight="@dimen/_15sdp"
        android:background="@drawable/text_view_date_brown_new"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/timeNow_LinearLayout_MainActivity"
        tools:layoutDirection="rtl">

        <TextView
            android:id="@+id/dateNow_TextView_MainActivity"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_minus10sdp"
            android:layout_marginBottom="@dimen/_minus10sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/datebrownnew"
            android:textDirection="ltr"
            android:textSize="@dimen/_30sdp"
            android:textStyle="bold"
            tools:text="5" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/_5sdp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/datehm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:textColor="@color/datebrownnew"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold"
                tools:text="جمادي الثاني" />

            <TextView
                android:id="@+id/datehy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:textColor="@color/datebrownnew"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold"
                tools:text="1437 هـ" />
        </LinearLayout>

        <TextView
            android:id="@+id/dateNow1_TextView_MainActivity"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_minus10sdp"
            android:layout_marginBottom="@dimen/_minus10sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/datebrownnew"
            android:textDirection="ltr"
            android:textSize="@dimen/_30sdp"
            android:textStyle="bold"
            tools:text="5" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/datem"

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:textColor="@color/datebrownnew"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold"
                tools:text="فبراير" />

            <TextView
                android:id="@+id/datey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:textColor="@color/datebrownnew"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold"
                tools:text="2019" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/m"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5sdp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@+id/DateNow_TextView_MainActivity">

        <TextView
            android:id="@+id/movingMessage_TextView_MainActivity"
            style="@style/MovingText"
            android:fontFamily="@font/droid_arabic_kufi"
            android:textColor="@color/white"
            android:textStyle="bold"
            tools:text="@string/upload_photo_message" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/prayerTimeItem_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/alrabeeaTimes_ImageView_MainActivity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/m">

        <ImageView
            android:id="@+id/container_ImageView_MainActivity1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitCenter"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:alpha="0.5" />
        <!--        layout="@layout/layout_prayer_times_brown_new"-->
        <include
            layout="@layout/layout_prayer_times_brown_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/announcement_include_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/_10sdp"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_marginEnd="@dimen/_10sdp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/alrabeeaTimes_ImageView_MainActivity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/m">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_progress_remaining"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.mikhaellopez.circularprogressbar.CircularProgressBar
                android:id="@+id/progressBar"
                android:layout_width="@dimen/circularProgressBar"
                android:layout_height="@dimen/circularProgressBar"
                android:textColor="@color/white"
                app:cpb_background_progressbar_color="#918F8F"
                app:cpb_background_progressbar_width="3dp"
                app:cpb_progressbar_color="@color/white"
                app:cpb_progressbar_width="@dimen/_3sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@id/progressBar"
                app:layout_constraintEnd_toEndOf="@id/progressBar"
                app:layout_constraintStart_toStartOf="@id/progressBar"
                app:layout_constraintTop_toTopOf="@id/progressBar">

                <TextView
                    android:id="@+id/tv_remaining_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="10:00"
                    android:textColor="@color/white"
                    android:textSize="@dimen/remainingTime" />

                <TextView
                    android:id="@+id/tv_remaining_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/prayerName"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:text="دقائق" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_remainingOnIkama"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/remaining_on_ikama"
            android:textColor="@color/white"
            android:textSize="@dimen/hadithBetweenAdhaanAndIkama"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/what_is_said_between_the_adhaan_and_ikama"
            android:textColor="@color/white"
            android:textSize="@dimen/prayerTime"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"

            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/hadithBetweenAdhaanAndIkama"

            android:autoSizeTextType="uniform"
            android:autoSizeStepGranularity="1sp"
            android:autoSizeMinTextSize="@dimen/_12sdp"
            android:autoSizeMaxTextSize="@dimen/_22sdp"

            tools:text="@string/what_is_said_between_the_adhaan_and_ikama" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif_silent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_60sdp"
            android:src="@drawable/bellsilentline"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="@color/white" />
    </LinearLayout>

    <ImageView
        android:id="@+id/alrabeeaTimes_ImageView_MainActivity"
        style="@style/AlrabeaIcon"
        android:layout_height="@dimen/_30sdp"
        android:layout_marginBottom="@dimen/_6sdp"
        android:src="@drawable/logov2"
        android:scaleY="@fraction/logo_scale_small"
        android:scaleX="@fraction/logo_scale_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:tint="@android:color/white" />

    <LinearLayout
        android:id="@+id/layout_athkar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_athkar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scaleType="fitXY"
            android:src="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/layout_time_athkar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_4sdp">

            <TextView
                android:id="@+id/tv_athkar_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:text="Time"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_icon"
                style="@style/AlrabeaIcon"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_26sdp"
                app:tint="@color/colorblack" />

        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

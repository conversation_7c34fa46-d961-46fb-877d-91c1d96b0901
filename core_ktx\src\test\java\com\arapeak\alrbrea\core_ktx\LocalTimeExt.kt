package com.arapeak.alrbrea.core_ktx

import com.arapeak.alrbrea.core_ktx.ui.utils.formattedHH_SS
import kotlinx.datetime.LocalTime
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner


@RunWith(RobolectricTestRunner::class)
class LocalTimeExt {

    @Before
    fun setUp() {

    }

    @Test
    fun test_timing_formate() {
        val l = LocalTime.parse("15:23")

        println(l.formattedHH_SS())
    }

}
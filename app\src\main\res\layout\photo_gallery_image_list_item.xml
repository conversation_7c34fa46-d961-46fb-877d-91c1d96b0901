<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_5sdp"
    android:clipChildren="true"
    android:clipToPadding="true"
    app:cardBackgroundColor="@android:color/black"
    app:cardCornerRadius="20dp"
    app:cardElevation="3dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#F3F3F3"
        android:clipChildren="true"
        android:clipToPadding="true">

        <ImageView
            android:id="@+id/iv_gallery_photo_list_item"
            android:layout_width="@dimen/_80sdp"
            android:layout_height="@dimen/_140sdp"
            android:scaleType="centerCrop"
            android:src="@drawable/img_background_blue_landscape"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/delete_Button_PhotoGalleryHolder"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_30sdp"
            android:background="@android:color/transparent"
            android:fontFamily="@font/droid_arabic_kufi"
            android:text="@string/delete_photo"
            android:textColor="#E61616"
            android:textSize="@dimen/_14sdp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_gallery_photo_list_item" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    app:contentPadding="20dp"
    tools:cardBackgroundColor="@android:color/black"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/progress_TextView_LinerProgressDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="0"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="50" />

            <ProgressBar
                android:id="@+id/progress_ProgressBar_LinerProgressDialog"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:indeterminate="false"
                android:max="100"
                android:minWidth="200dp"
                android:minHeight="50dp"
                android:progress="0" />
        </LinearLayout>


        <TextView
            android:id="@+id/loading_TextView_LinerProgressDialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:includeFontPadding="false"
            android:text="@string/app_is_updating_and_please_wait_for_updates_to_complete"
            android:textColor="@android:color/black"
            android:textSize="@dimen/contactUsView"
            android:textStyle="bold" />
    </LinearLayout>

</androidx.cardview.widget.CardView>

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import org.jetbrains.annotations.NotNull;

import kotlin.jvm.internal.Intrinsics;


public final class Time_Database extends Time {
    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public Time_Database(double d, boolean z, @NotNull Hour hour) {
        super(d, z, hour);
        Intrinsics.checkNotNullParameter(hour, "hour");
    }

    @Override // com.alawail.prayertimes.moazen.Time
    @NotNull
    public String convertToTime(double myvar, boolean isAM, @NotNull Hour hour24_12) {
        Intrinsics.checkNotNullParameter(hour24_12, "hour24_12");
        int i = (int) myvar;
        int i2 = i / 60;
        int i3 = i - (i2 * 60);
        setM_zone("AM");
        int i4 = 12;
        if (i2 == 12) {
            setM_zone("PM");
        }
        if (i2 > 12) {
            setM_zone("PM");
            if (i2 > 24) {
                i2 %= 24;
                setM_zone("AM");
                if (i2 >= 12) {
                    setM_zone("PM");
                }
            } else if (i2 == 24) {
                setM_zone("AM");
                i2 = hour24_12.type() == Hour.Type.hour12 ? 12 : 0;
            } else if (hour24_12.type() == Hour.Type.hour12) {
                i2 %= 12;
            }
        }
        if (hour24_12.type() != Hour.Type.hour12 || i2 != 0) {
            i4 = i2;
        }
        String a2 = "" + i4;
        String a3 = "" + i3;
        if (a2.length() == 1) {
            a2 = "0".concat(a2);
        }
        if (a3.length() == 1) {
            a3 = "0".concat(a3);
        }
        return a2 + ':' + a3 + ":00 " + getM_zone();
    }
}
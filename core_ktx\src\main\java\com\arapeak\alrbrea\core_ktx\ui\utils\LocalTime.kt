package com.arapeak.alrbrea.core_ktx.ui.utils

import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import kotlinx.datetime.LocalTime
import kotlinx.datetime.toJavaLocalTime
import java.time.format.DateTimeFormatter

fun LocalTime.formattedHH_SS(): String {
    try {
        return DateTimeFormatter.ofPattern("HH:mm").format(this.toJavaLocalTime()) ?: ""
    } catch (e: Exception) {
        logException(e)
    }
    return ""
}
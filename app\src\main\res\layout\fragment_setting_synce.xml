<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:padding="16dp"
    tools:context=".UI.Fragment.settings.content.athkar.content.SettingsAthkarsForMorningAndEveningFragment"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="16dp">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment3"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment4"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment5"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <CheckBox
                android:id="@+id/citationForMorning_CheckBox_AthkarFragment6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/citationForMorning_TextView_AthkarFragment6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:includeFontPadding="false"
                android:text="@string/citation_for_morning"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/citationForMorning_CheckBox_AthkarFragment"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>


        <View
            android:id="@+id/space1_View_AthkarFragment6"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:visibility="invisible"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/citationForMorning_TextView_AthkarFragment" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="30dp"
            android:gravity="center">

            <Button
                android:id="@+id/b"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_green_without_corners_shape"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingStart="20dp"
                android:paddingTop="8dp"
                android:paddingEnd="20dp"
                android:paddingBottom="8dp"
                android:text="مزامنة"
                android:textAlignment="center"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Button
                android:id="@+id/bd"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_red_without_corners_shape"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingStart="20dp"
                android:paddingTop="8dp"
                android:paddingEnd="20dp"
                android:paddingBottom="8dp"
                android:text="الغاء المزامنة"
                android:textAlignment="center"
                android:textColor="@android:color/white"
                android:textSize="14sp" />
        </LinearLayout>

    </LinearLayout>


</ScrollView>
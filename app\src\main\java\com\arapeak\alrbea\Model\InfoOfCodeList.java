package com.arapeak.alrbea.Model;

import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class InfoOfCodeList {

    @Expose
    @SerializedName("info")
    private List<InfoOfCode> infoOfCode;

    @NonNull
    public List<InfoOfCode> getInfoOfCode() {
        return infoOfCode == null ? new ArrayList<InfoOfCode>() : infoOfCode;
    }

    public void setInfoOfCode(List<InfoOfCode> infoOfCode) {
        this.infoOfCode = infoOfCode;
    }
}

package com.arapeak.alrbea.UI.Fragment.settings.content.remote;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbrea.core_ktx.model.remoteaccess.RemoteTool;
import com.arapeak.alrbrea.core_ktx.ui.remoteaccess.RemoteAccessManager;

import java.util.List;

import kotlin.Unit;


public class RemoteAccess extends AlrabeeaTimesFragment {

    private View rootView;
    private RemoteAccessManager manager;
    private RemoteToolsListAdapter adapter;

    public RemoteAccess() {
    }

    public static RemoteAccess newInstance() {
        return new RemoteAccess();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        manager = new RemoteAccessManager(requireContext());

        rootView = inflater.inflate(R.layout.fragment_settings_remote, container, false);

        initView();
        initList();

        return rootView;
    }


    private void initView() {
        RecyclerView rv = rootView.findViewById(R.id.rv_remote_tools);
        rv.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new RemoteToolsListAdapter(requireActivity(), List.of());
        rv.setAdapter(adapter);

        adapter.setOnRefreshTools(() -> {
            manager = new RemoteAccessManager(requireActivity());
            initList();
        });

    }

    private void initList() {
        manager.setOnFirebaseConfigArrived(() -> {
            populateList();

            rootView.findViewById(R.id.pb_remote_settings).setVisibility(View.GONE);

            return Unit.INSTANCE;
        });
    }

    private void populateList() {
        List<RemoteTool> tools = manager.getAsList();
        adapter.setList(tools);
    }

}

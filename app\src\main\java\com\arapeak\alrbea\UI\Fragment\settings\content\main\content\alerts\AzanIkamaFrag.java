package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.alerts;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.SettingsAdapter;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.alerts.content.SelectAzan;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.alerts.content.SelectIkamaAudio;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.List;

public class AzanIkamaFrag extends AlrabeeaTimesFragment implements AdapterCallback {

    private View mainSettingsFragment;
    private RecyclerView settingItemRecyclerView;
    private SettingsAdapter settingsAdapter;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        mainSettingsFragment = inflater.inflate(R.layout.fragment_prayer_times_settings, container, false);

        initView();
        SetParameter();
        SetAction();

        return mainSettingsFragment;
    }

    private void initView() {
        settingItemRecyclerView = mainSettingsFragment.findViewById(R.id.settingItem_RecyclerView_PrayerTimesSettingsFragment);
        settingItemRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));

        settingsAdapter = new SettingsAdapter(getContext(), setupSettings(), this);
    }

    private void SetParameter() {
        settingItemRecyclerView.setAdapter(settingsAdapter);
    }

    private void SetAction() {

    }

    @Override
    public void onItemClick(int position, String tag) {
        switch (position) {
            case 0:
                Utils.loadFragment(new SelectAzan()
                        , getAppCompatActivity()
                        , 0);
                break;
            case 1:
                Utils.loadFragment(new SelectIkamaAudio()
                        , getAppCompatActivity()
                        , 0);
                break;
        }
    }

    private List<SettingAlrabeeaTimes> setupSettings() {
        List<SettingAlrabeeaTimes> settingAlrabeeaTimes = new ArrayList<>();
        String[] generalSettingsTitleArray = getResources().getStringArray(R.array.settings_audio);

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[0]
                , ""
                , R.drawable.noticee));

        settingAlrabeeaTimes.add(new SettingAlrabeeaTimes(generalSettingsTitleArray[1]
                , ""
                , R.drawable.noticee));

        return settingAlrabeeaTimes;
    }
}
package com.arapeak.alrbrea.core_ktx.repo

import android.content.Context
import android.util.Log
import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.data.datastore.ScreensaverSettings
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoElementsEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoSizeEnum

import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverModeEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverTimingConfig
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverTimingEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreensaverToggleEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigDelay
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigInterval
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigPeriod


class ScreenSaverRepo(
    val screensaverSettings: ScreensaverSettings = ScreensaverSettings()
) {
    private val TAG = this.javaClass.simpleName


    fun getCurrentToggle(context: Context): ScreensaverToggleEnum {
        val res = screensaverSettings.getToggle(context)
        Log.e(TAG, "get current Toggle " + res.name)

        return res
    }

    fun updateToggle(context: Context, toggle: ScreensaverToggleEnum) {
        Log.e(TAG, "update current toggle " + toggle.name)
        screensaverSettings.setToggle(context, toggle)
    }

    fun getTogglesNamesLocalized(context: Context): List<String> {
        return ScreensaverToggleEnum.entries.map {
            when (it) {
                ScreensaverToggleEnum.Disabled -> context.getString(R.string.disabled)
                ScreensaverToggleEnum.Enabled -> context.getString(R.string.enabled)
            }
        }
    }


    fun getCurrentMode(context: Context): ScreensaverModeEnum {
        val res = screensaverSettings.getMode(context)
        Log.e(TAG, "get current mode " + res.name)

        return res
    }

    fun updateMode(context: Context, mode: ScreensaverModeEnum) {
        Log.e(TAG, "update current mode " + mode.name)
        screensaverSettings.setMode(context, mode)
    }

    fun getModesNamesLocalized(context: Context): List<String> {
        val disabled = listOf(
            ScreensaverModeEnum.ReduceBrightness
        )

        return ScreensaverModeEnum.entries.filter { it !in disabled }.map {
            when (it) {
                ScreensaverModeEnum.Empty -> context.getString(R.string.blank)
                ScreensaverModeEnum.Info -> context.getString(R.string.blank_with_info2)
                ScreensaverModeEnum.ReduceBrightness -> context.getString(R.string.reduce_brightness)
            }
        }
    }


    fun getInfoSizes(context: Context): ScreenSaverInfoSizeEnum {
        val res = screensaverSettings.getInfoSize(context)
        Log.e(TAG, "get current size " + res.name)

        return res

    }

    fun getInfoElements(context: Context): ScreenSaverInfoElementsEnum {
        val res = screensaverSettings.getInfoElements(context)
        Log.e(TAG, "get current elements " + res.name)

        return res

    }

    fun getElementsNamesLocalized(context: Context): List<String> {
        return ScreenSaverInfoElementsEnum.entries.map {
            when (it) {
                ScreenSaverInfoElementsEnum.TimeOnly -> context.getString(R.string.time_only)
                ScreenSaverInfoElementsEnum.DateTime -> context.getString(R.string.time_date)
//                SSInfoElementsEnum.TextDesign -> context.getString(R.string.text_design)
            }
        }
    }


    fun updateElements(context: Context, infoElementsEnum: ScreenSaverInfoElementsEnum) {
        Log.e(TAG, "update current elements " + infoElementsEnum.name)
        screensaverSettings.setInfoElements(context, infoElementsEnum)
    }

    fun getSizesNamesLocalized(context: Context): List<String> {
        return ScreenSaverInfoSizeEnum.entries.map {
            when (it) {
                ScreenSaverInfoSizeEnum.Small -> context.getString(R.string.small)
                ScreenSaverInfoSizeEnum.Medium -> context.getString(R.string.meduim)
                ScreenSaverInfoSizeEnum.Large -> context.getString(R.string.large)
            }
        }
    }

    fun updateSizes(context: Context, infoSizeEnum: ScreenSaverInfoSizeEnum) {
        Log.e(TAG, "update current size " + infoSizeEnum.name)
        screensaverSettings.setInfoSize(context, infoSizeEnum)
    }


    fun getTiming(context: Context, timing: ScreensaverTimingEnum): Boolean {
        val res = screensaverSettings.getTiming(context, timing)
        Log.e(TAG, "get timing ${timing.name} " + res)

        return res
    }

    fun getAllTimings(context: Context): Map<ScreensaverTimingEnum, Boolean> {
        val res = mutableMapOf<ScreensaverTimingEnum, Boolean>()
        ScreensaverTimingEnum.entries.forEach {
            res[it] = getTiming(context, it)
        }

        return res
    }

    fun updateTiming(context: Context, timing: ScreensaverTimingEnum, value: Boolean) {
        Log.e(TAG, "update timing ${timing.name} " + value)
        screensaverSettings.setTiming(context, timing, value)
    }

    fun getTimingNamesLocalized(context: Context, timing: ScreensaverTimingEnum): String {
        return when (timing) {
            ScreensaverTimingEnum.BetweenPrayers -> context.getString(R.string.between_prayers)
            ScreensaverTimingEnum.AfterDelay -> context.getString(R.string.after_inactivity)
            ScreensaverTimingEnum.Period -> context.getString(R.string.period)
            ScreensaverTimingEnum.DuringPrayers -> context.getString(R.string.during_prayers)
            ScreensaverTimingEnum.Intervals -> context.getString(R.string.repeated)
        }
    }

    fun updateTimingConfig(context: Context, timingConfig: ScreensaverTimingConfig) {
        val save = timingConfig.convertToSave()
        Log.e(TAG, "update timing Config ${save} ")

        when (timingConfig) {
            is TimingConfigDelay -> {
                screensaverSettings.setTimingDelay(context, save)
            }

            is TimingConfigPeriod -> {
                screensaverSettings.setTimingPeriod(context, save)
            }

            is TimingConfigInterval -> {
                screensaverSettings.setTimingInterval(context, save)
            }
        }

    }

    fun getTimingConfigDelay(context: Context): TimingConfigDelay {
        return screensaverSettings.getTimingDelay(context)
    }

    fun getTimingConfigPeriod(context: Context): TimingConfigPeriod {
        return screensaverSettings.getTimingPeriod(context)
    }

    fun getTimingConfigInterval(context: Context): TimingConfigInterval {
        return screensaverSettings.getTimingInterval(context)
    }

    fun getTimingPrayers(context: Context): List<Boolean> {
        val res = screensaverSettings.getTimingPrayers(context)
        Log.e(TAG, "get timing prayers $res")
        return res.map { it == '1' }
    }

    fun updateTimingPrayers(context: Context, list: List<Boolean>) {
        Log.e(TAG, "update Timing Prayers  " + list.joinToString(" "))
        screensaverSettings.setTimingPrayers(context, list.map { if (it) '1' else '0' }.joinToString(""))
    }

    fun getTimingPrayersDelay(context: Context): Int {
        val res = screensaverSettings.getTimingPrayersDelay(context)
        Log.e(TAG, "get timing prayers delay $res")
        return res
    }

    fun updateTimingPrayersDelay(context: Context, delay: Int) {
        Log.e(TAG, "update Timing Prayers delay $delay")
        screensaverSettings.setTimingPrayersDelay(context, delay)
    }

    fun getTimingPrayersPreDelay(context: Context): Int {
        val res = screensaverSettings.getTimingPrayersPreDelay(context)
        Log.e(TAG, "get timing prayers pre delay $res")
        return res
    }

    fun updateTimingPrayersPreDelay(context: Context, delay: Int) {
        Log.e(TAG, "update Timing Prayers pre delay $delay")
        screensaverSettings.setTimingPrayersPreDelay(context, delay)
    }
}
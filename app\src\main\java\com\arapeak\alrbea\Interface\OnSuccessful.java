package com.arapeak.alrbea.Interface;

public abstract class OnSuccessful<T> {
    public void onSuccessful(boolean isSuccessful) {
    }

    public void onSuccessful(boolean isSuccessful, int a, int b, int d) {
    }

    public void onSuccessful(boolean isSuccessful, int a) {
    }

    public void onSuccessful(boolean isSuccessful, String data, String b) {
    }


    public void onSuccessful(boolean isSuccessful, T data) {
    }

    public void onSuccessful(int type, T data) {
    }

}
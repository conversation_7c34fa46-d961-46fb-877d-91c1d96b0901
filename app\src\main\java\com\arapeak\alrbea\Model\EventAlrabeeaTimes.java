package com.arapeak.alrbea.Model;

import android.os.Parcel;
import android.os.Parcelable;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Utils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import io.realm.RealmList;
import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class EventAlrabeeaTimes extends RealmObject implements Parcelable, Comparator<EventAlrabeeaTimes> {

    public static final Creator<EventAlrabeeaTimes> CREATOR = new Creator<EventAlrabeeaTimes>() {
        @Override
        public EventAlrabeeaTimes createFromParcel(Parcel in) {
            return new EventAlrabeeaTimes(in);
        }

        @Override
        public EventAlrabeeaTimes[] newArray(int size) {
            return new EventAlrabeeaTimes[size];
        }
    };
    private static final String TAG = "EventAlrabeeaTimes";
    @PrimaryKey
    private int id;
    private String messageEvent, prayerName, description;
    private String timePrayer;
    private String nameEvent;
    private int durationAfterPray;
    private String startDate, endDate, end_time, start_time;
    private int durationOfEvent;
    private String type;
    private RealmList<PhotoAlrabeeaTimes> photoAlrabeeaTimesList;
    private long timeEventByMilliseconds;
    private boolean isActive;
    private String tagOfPray;
    private String timesNumberOfAppear;
    private int mTransformer;
    private int appearanceAlternatelyMinutes = -1, appearanceAlternatelySeconds = -1, appearanceAlternatelyHours = -1;
    private int photoOneMinutes = -1, photoOneSeconds = -1, photoOneHours = -1, types;

    public EventAlrabeeaTimes() {
    }

    public EventAlrabeeaTimes(int id
            , String nameEvent
            , String messageEvent
            , String prayerName
            , String tagOfPray
            , String timePrayer
            , int durationAfterPray
            , String startDate
            , int durationOfEvent
            , String type
            , boolean isActive) {
        this.id = id;
        this.nameEvent = nameEvent;
        this.messageEvent = messageEvent;
        this.prayerName = prayerName;
        this.tagOfPray = tagOfPray;
        this.timePrayer = timePrayer;
        this.durationAfterPray = durationAfterPray;
        this.startDate = startDate;
        this.durationOfEvent = durationOfEvent;
        this.type = ConstantsOfApp.EVENT_KEY;
        this.type = type;
        this.isActive = isActive;
        timeEventByMilliseconds = getTimeToShowEventMilliseconds();
    }

    public EventAlrabeeaTimes(int id
            , String messageEvent
            , String startDate
            , String endDate
            , String type
            , boolean isActive) {
        this.id = id;
        this.messageEvent = messageEvent;
        this.startDate = startDate;
        this.endDate = endDate;
        this.type = type;
        this.isActive = isActive;
        timeEventByMilliseconds = getTimeToShowEventMilliseconds();

    }

    public EventAlrabeeaTimes(int id
            , List<PhotoAlrabeeaTimes> photoAlrabeeaTimesList
            , String prayerName
            , String tagOfPray
            , String timePrayer
            , int durationAfterPray
            , String startDate
            , int durationOfEvent
            , boolean isActive) {
        this.id = id;
        this.photoAlrabeeaTimesList = new RealmList<>();
        this.photoAlrabeeaTimesList.addAll(photoAlrabeeaTimesList);
        this.messageEvent = "";
        this.prayerName = prayerName;
        this.tagOfPray = tagOfPray;
        this.timePrayer = timePrayer;
        this.durationAfterPray = durationAfterPray;
        this.startDate = startDate;
        this.durationOfEvent = durationOfEvent;
        this.type = ConstantsOfApp.EVENT_IMAGE_KEY;
        this.isActive = isActive;
        timeEventByMilliseconds = getTimeToShowEventMilliseconds();
    }

    public EventAlrabeeaTimes(int id
            , String nameEvent
            , List<PhotoAlrabeeaTimes> photoAlrabeeaTimesList
            , String startDate
            , String start_time
            , String end_time
            , int types
            , int appearanceAlternatelyHours
            , int appearanceAlternatelyMinutes
            , int appearanceAlternatelySeconds
            , int photoOneHours
            , int photoOneMinutes
            , int photoOneSeconds
            , int mTransformer
            , String timesNumberOfAppear
            , boolean isActive) {
        this.id = id;
        this.nameEvent = nameEvent;
        this.photoAlrabeeaTimesList = new RealmList<>();
        this.photoAlrabeeaTimesList.addAll(photoAlrabeeaTimesList);
        this.messageEvent = "";
        this.startDate = startDate;
        this.start_time = start_time;
        this.end_time = end_time;
        this.types = types;
        this.appearanceAlternatelyHours = appearanceAlternatelyHours;
        this.appearanceAlternatelyMinutes = appearanceAlternatelyMinutes;
        this.appearanceAlternatelySeconds = appearanceAlternatelySeconds;
        this.photoOneHours = photoOneHours;
        this.photoOneMinutes = photoOneMinutes;


        this.photoOneSeconds = photoOneSeconds;
        this.mTransformer = mTransformer;
        this.timesNumberOfAppear = timesNumberOfAppear;
        this.type = ConstantsOfApp.EVENT_IMAGE_KEY;
        this.isActive = isActive;
        timeEventByMilliseconds = getTimeToShowEventMilliseconds();
    }

    protected EventAlrabeeaTimes(Parcel in) {
        id = in.readInt();
        messageEvent = in.readString();
        prayerName = in.readString();
        description = in.readString();
        timePrayer = in.readString();
        nameEvent = in.readString();
        durationAfterPray = in.readInt();
        startDate = in.readString();
        endDate = in.readString();
        durationOfEvent = in.readInt();
        type = in.readString();
        timeEventByMilliseconds = in.readLong();
        isActive = in.readByte() != 0;
        tagOfPray = in.readString();
        timesNumberOfAppear = in.readString();
        mTransformer = in.readInt();
        appearanceAlternatelyHours = in.readInt();
        appearanceAlternatelyMinutes = in.readInt();
        appearanceAlternatelySeconds = in.readInt();
        photoOneHours = in.readInt();
        photoOneMinutes = in.readInt();


        photoOneSeconds = in.readInt();
        start_time = in.readString();
        end_time = in.readString();
        types = in.readInt();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMessageEvent() {
        return Utils.getValueWithoutNull(messageEvent);
    }

    public void setMessageEvent(String messageEvent) {
        this.messageEvent = messageEvent;
    }

    public String getDescription() {
        return Utils.getValueWithoutNull(description);
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPrayerName() {
        return Utils.getValueWithoutNull(prayerName);
    }

    public void setPrayerName(String prayerName) {
        this.prayerName = prayerName;
    }

    public String getTimePrayer() {
        return timePrayer;
    }

    public void setTimePrayer(String timePrayer) {
        this.timePrayer = timePrayer;
    }

    public int getDurationAfterPray() {
        return durationAfterPray;
    }

    public void setDurationAfterPray(int durationAfterPray) {
        this.durationAfterPray = durationAfterPray;
    }

    public String getTimeToShowEvent() {
        return Utils.convertTimePrayer(timePrayer, durationAfterPray * ConstantsOfApp.MINUTES_MILLI_SECOND);
    }

    public String getStartDate() {
        return Utils.getValueWithoutNull(startDate);
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return Utils.getValueWithoutNull(endDate);
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public int getTypes() {
        return types;
    }

    public long getTimeToShowEventMilliseconds() {
//        return Utils.convertDateToLongTimestamp(getTimeToShowEvent());
        return Utils.convertDateToLongTimestamp(startDate, getTimeToShowEvent());
    }

    public String getTimeToShowEventWithDate() {
        return Utils.ConvertLongTimestampToDate(getTimeToShowEventMilliseconds());
    }

    public int getDurationOfEvent() {
        return durationOfEvent;
    }

    public void setDurationOfEvent(int durationOfEvent) {
        this.durationOfEvent = durationOfEvent;
    }

    public String getType() {
        return Utils.getValueWithoutNull(type);
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<PhotoAlrabeeaTimes> getPhotoAlrabeeaTimesList() {
        return photoAlrabeeaTimesList == null ? new ArrayList<PhotoAlrabeeaTimes>() : photoAlrabeeaTimesList;
    }

    public void setPhotoAlrabeeaTimesList(RealmList<PhotoAlrabeeaTimes> photoAlrabeeaTimesList) {
        this.photoAlrabeeaTimesList = photoAlrabeeaTimesList;
    }

    public void setPhotoAlrabeeaTimesList(ArrayList<PhotoAlrabeeaTimes> photoAlrabeeaTimesList) {
        this.photoAlrabeeaTimesList = new RealmList<>();

        this.photoAlrabeeaTimesList.addAll(photoAlrabeeaTimesList);
    }

    public RealmList<PhotoAlrabeeaTimes> getPhotoAlrabeeaTimesArrayList() {
        return photoAlrabeeaTimesList == null ? new RealmList<PhotoAlrabeeaTimes>() : photoAlrabeeaTimesList;
    }

    public long getTimeEventByMilliseconds() {
        return timeEventByMilliseconds;
    }

    public void setTimeEventByMilliseconds(long timeEventByMilliseconds) {
        this.timeEventByMilliseconds = timeEventByMilliseconds;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getTagOfPray() {
        return tagOfPray;
    }

    public void setTagOfPray(String tagOfPray) {
        this.tagOfPray = tagOfPray;
    }

    public String getTimesNumberOfAppear() {
        return Utils.getValueWithoutNull(timesNumberOfAppear);
    }

    public void setTimesNumberOfAppear(String timesNumberOfAppear) {
        this.timesNumberOfAppear = timesNumberOfAppear;
    }

    public String getStart_time() {
        return start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public int getmTransformer() {
        return mTransformer;
    }

    public void setmTransformer(int mTransformer) {
        this.mTransformer = mTransformer;
    }

    public String getNameEvent() {
        return Utils.getValueWithoutNull(nameEvent);
    }

    public void setNameEvent(String nameEvent) {
        this.nameEvent = nameEvent;
    }

    public int getAppearanceAlternatelyMinutes() {
        return appearanceAlternatelyMinutes;
    }

    public void setAppearanceAlternatelyMinutes(int appearanceAlternatelyMinutes) {
        this.appearanceAlternatelyMinutes = appearanceAlternatelyMinutes;
    }

    public int getAppearanceAlternatelyHours() {
        return appearanceAlternatelyHours;
    }

    public void setAppearanceAlternatelyHours(int appearanceAlternatelyHours) {
        this.appearanceAlternatelyHours = appearanceAlternatelyHours;
    }

    public int getAppearanceAlternatelySeconds() {
        return appearanceAlternatelySeconds;
    }

    public void setAppearanceAlternatelySeconds(int appearanceAlternatelySeconds) {
        this.appearanceAlternatelySeconds = appearanceAlternatelySeconds;
    }

    public int getphotoOneMinutes() {
        return photoOneMinutes;
    }

    public int getphotoOneHours() {
        return photoOneHours;
    }

    public void setphotoOneMinutes(int photoOneMinutes) {
        this.photoOneMinutes = photoOneMinutes;
    }

    public void setPhotoOneHours(int photoOneHours) {
        this.photoOneHours = photoOneHours;
    }

    public void setPhotoOneSeconds(int photoOneSeconds) {
        this.photoOneSeconds = photoOneSeconds;
    }

    public int getphotoOneSeconds() {
        return photoOneSeconds;
    }

    public long getAppearanceAlternatelyMilliSecond() {
        return (getAppearanceAlternatelyHours() * ConstantsOfApp.HOURS_MILLI_SECOND) + (getAppearanceAlternatelyMinutes() * ConstantsOfApp.MINUTES_MILLI_SECOND)
                + (getAppearanceAlternatelySeconds() * ConstantsOfApp.SECONDS_MILLI_SECOND);

    }


    public long getphotoOneMilliSecond() {
        return (getphotoOneHours() * ConstantsOfApp.HOURS_MILLI_SECOND) + (getphotoOneMinutes() * ConstantsOfApp.MINUTES_MILLI_SECOND
        ) + (getphotoOneSeconds() * ConstantsOfApp.SECONDS_MILLI_SECOND);

    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (obj == this) return true;
        if (!(obj instanceof EventAlrabeeaTimes)) return false;
        EventAlrabeeaTimes eventAlrabeeaTimes = (EventAlrabeeaTimes) obj;
        if (eventAlrabeeaTimes.getId() < 0 || getId() < 0) return false;
        return eventAlrabeeaTimes.getId() == getId();
    }

    @Override
    public int compare(EventAlrabeeaTimes eventAlrabeeaTimes1, EventAlrabeeaTimes eventAlrabeeaTimes2) {
        if (eventAlrabeeaTimes1 == null || eventAlrabeeaTimes2 == null) {
            return 0;
        }
        if (eventAlrabeeaTimes1.getType().equals(eventAlrabeeaTimes2.getType())) {
            return eventAlrabeeaTimes1.getId() - eventAlrabeeaTimes2.getId();
        } else {
            if (eventAlrabeeaTimes1.getType().equals(ConstantsOfApp.EVENT_KEY)) {
                return -1;
            } else {
                return 1;
            }
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(messageEvent);
        dest.writeString(prayerName);
        dest.writeString(description);
        dest.writeString(timePrayer);
        dest.writeString(nameEvent);
        dest.writeInt(durationAfterPray);
        dest.writeString(startDate);
        dest.writeString(endDate);
        dest.writeInt(durationOfEvent);
        dest.writeString(type);
        dest.writeLong(timeEventByMilliseconds);
        dest.writeByte((byte) (isActive ? 1 : 0));
        dest.writeString(tagOfPray);
        dest.writeString(timesNumberOfAppear);
        dest.writeInt(mTransformer);


        dest.writeInt(appearanceAlternatelyHours);
        dest.writeInt(appearanceAlternatelyMinutes);
        dest.writeInt(appearanceAlternatelySeconds);

        dest.writeInt(photoOneHours);
        dest.writeInt(photoOneMinutes);
        dest.writeInt(photoOneSeconds);
        dest.writeString(start_time);
        dest.writeString(end_time);
        dest.writeInt(types);

    }
}

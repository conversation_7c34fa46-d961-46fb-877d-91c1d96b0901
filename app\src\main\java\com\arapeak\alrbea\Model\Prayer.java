package com.arapeak.alrbea.Model;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class Prayer {

    @Expose
    @SerializedName("meta")
    private MetaAlrabeeaTimes meta;
    @Expose
    @SerializedName("date")
    private DateAlrabeeaTimes date;
    @Expose
    @SerializedName("timings")
    private TimingsAlrabeeaTimes timings;

    public MetaAlrabeeaTimes getMeta() {
        return meta;
    }

    public void setMeta(MetaAlrabeeaTimes meta) {
        this.meta = meta;
    }

    public DateAlrabeeaTimes getDate() {
        return date;
    }

    public void setDate(DateAlrabeeaTimes date) {
        this.date = date;
    }

    public TimingsAlrabeeaTimes getTimings() {
        timings.Year = Integer.parseInt(getDate().getGregorian().getYear());
        timings.setMonth(getDate().getGregorian().getMonth().getNumber());
        timings.setDay(Integer.parseInt(getDate().getGregorian().getDay()));
        return timings;
    }

    public void setTimings(TimingsAlrabeeaTimes timings) {
        this.timings = timings;
    }

}

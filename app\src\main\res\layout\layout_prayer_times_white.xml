<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/content_LinearLayout_MainActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <TextView
                style="@style/PrayerTimeLayout.main_white.Start"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:paddingStart="@dimen/_10sdp"
                android:text="@string/adhaan" />

            <TextView
                style="@style/PrayerTimeLayout.main_white.center"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:paddingStart="5dp"
                android:text="@string/prayer" />

            <TextView
                style="@style/PrayerTimeLayout.main_white.End"
                android:layout_marginTop="@dimen/_minus5sdp"
                android:layout_marginBottom="@dimen/_minus5sdp"
                android:paddingEnd="@dimen/_7sdp"
                android:text="@string/ikama" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentFajr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.Start">

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035" />

            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.main_white.center">

                <TextView
                    android:id="@+id/fajr_TextView_MainActivity"
                    style="@style/PrayerTime"
                    android:text="@string/fajr"
                    android:textColor="#504e4e" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_fajr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/fajrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.End">

                <TextView
                    android:id="@+id/fajrATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#128d50"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/fajrATime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#128d50" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.Start">

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#970035"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.main_white.center">

                <TextView
                    android:id="@+id/dhuhr_TextView_MainActivity"
                    style="@style/PrayerTime"
                    android:text="@string/dhuhr"
                    android:textColor="#504e4e" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_dhur"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/dhuhrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.End">

                <TextView
                    android:id="@+id/dhuhrATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#128d50"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/dhuhrATime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#128d50" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentAsr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.Start">

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#970035"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.main_white.center">

                <TextView
                    android:id="@+id/asr_TextView_MainActivity"
                    style="@style/PrayerTime"
                    android:text="@string/asr"
                    android:textColor="#504e4e" />
                <include
                    android:id="@+id/tv_prayer_ikama_time_asr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/asrATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.End">

                <TextView
                    android:id="@+id/asrATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#128d50"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/asrATime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#128d50" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.Start">

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#970035"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.main_white.center">

                <TextView
                    android:id="@+id/maghrib_TextView_MainActivity"
                    style="@style/PrayerTime"
                    android:text="@string/maghrib"
                    android:textColor="#504e4e" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_maghrib"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/maghribATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.End">

                <TextView
                    android:id="@+id/maghribATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#128d50"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/maghribATime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#128d50" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentIsha_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.Start">

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#970035"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035" />
            </LinearLayout>

            <LinearLayout style="@style/PrayerTimeLayout.main_white.center">

                <TextView
                    android:id="@+id/isha_TextView_MainActivity"
                    style="@style/PrayerTime"
                    android:text="@string/isha"
                    android:textColor="#504e4e" />

                <include
                    android:id="@+id/tv_prayer_ikama_time_isha"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ishaATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.End">

                <TextView
                    android:id="@+id/ishaATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#128d50"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/ishaATime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#128d50" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/contentSunrise_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow"
            android:layout_margin="16dp">

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.Start">

                <TextView
                    android:id="@+id/sunriseTimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#970035"
                    android:textSize="@dimen/_20sdp"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/sunriseTime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#970035"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:textSize="@dimen/_20sdp" />
            </LinearLayout>

            <LinearLayout
                style="@style/PrayerTimeLayout.main_white.center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/sunrise_TextView_MainActivity"
                    style="@style/PrayerTime"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:singleLine="true"
                    android:text="@string/duha"
                    android:textColor="#504e4e"
                    android:textSize="@dimen/_20sdp" />
                <!--      <TextView
                          android:text="|"
                          style="@style/PrayerTime"
                          android:textColor="#504e4e"/>
      -->
                <!--                <TextView
                                    android:id="@+id/sunriseduha_TextView_MainActivity"
                                    android:text="@string/duha"
                                    style="@style/PrayerTime"
                                    android:singleLine="true"
                                    android:textColor="#504e4e"/>-->
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.main_white.End">

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:paddingRight="@dimen/_5sdp"
                    android:textColor="#128d50"
                    android:textSize="@dimen/_20sdp"
                    android:visibility="gone"
                    tools:text="م" />

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/PrayerTime.Time"
                    android:textColor="#128d50"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:textSize="@dimen/_20sdp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/remainingDhuhr_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingSunrise_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingFajr_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingAsr_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingMaghrib_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />

    <TextView
        android:id="@+id/remainingIsha_TextView_MainActivity"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:fontFamily="@font/droid_arabic_kufi"
        android:gravity="center"
        android:paddingStart="28dp"
        android:paddingTop="4dp"
        android:paddingEnd="28dp"
        android:paddingBottom="4dp"
        android:textColor="#1A1A1A"
        android:textSize="@dimen/remainingPrayerTime"
        android:visibility="gone"
        app:layout_constrainedHeight="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="percent"
        app:layout_constraintHeight_percent="0.125"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content_LinearLayout_MainActivity"
        app:layout_constraintWidth_percent="0.8"
        tools:text="متبقي ساعة و3 دقائق" />
</LinearLayout>
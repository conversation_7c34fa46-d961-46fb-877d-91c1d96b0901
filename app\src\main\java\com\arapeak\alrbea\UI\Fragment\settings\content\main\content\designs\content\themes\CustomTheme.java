package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.designs.content.themes;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.PorterDuff;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;

import androidx.appcompat.widget.SwitchCompat;

import com.arapeak.alrbea.Enum.UITheme;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.Utils;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.orhanobut.hawk.Hawk;
import com.skydoves.colorpickerview.ColorPickerDialog;
import com.skydoves.colorpickerview.ColorPickerView;
import com.skydoves.colorpickerview.listeners.ColorEnvelopeListener;


public class CustomTheme extends AlrabeeaTimesFragment {

    private final UITheme theme;
    int[] colors;
    Integer logoColor;
    String logoPath = null;
    String[] backgroundPaths = new String[2];
    Dialog loadingDialog;
    private View rootView;
    private ViewGroup[] containers;
    private ViewGroup[] navigators;
    private ImageView iv_logo;
    private Button[] backgroundBtns;
    private ImageView[] backgrounds;
    private ImageView[] colorBtns;
    private Button btn_add_or_remove_logo;
    private Button btn_logo_color;
    private Button btn_logo_color_remove;
    //    private Button btn_alrabea_logo_remove ;
    private Button btn_save;
    private Button btn_reset;
    private SwitchCompat sw_show_ikama;
    private boolean dataChanged = false;
    private boolean showIkama = false;

    public CustomTheme(UITheme theme) {
        this.theme = theme;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        rootView = inflater.inflate(R.layout.fragment_custom_theme, container, false);
        initView();
        setActions();
        loadDefaultValues();
        if (theme != UITheme.CUSTOM_1) {
            for (ViewGroup nav : navigators) {
                nav.setVisibility(View.GONE);
            }
            viewContainer(1);
            rootView.findViewById(R.id.tv_1).setVisibility(View.GONE);
            rootView.findViewById(R.id.tv_2).setVisibility(View.GONE);
        }
        loadingDialog = Utils.initLoadingDialog(getContext());
        return rootView;
    }

    @SuppressLint("SuspiciousIndentation")
    private void setActions() {
        navigators[0].setOnClickListener(v -> viewContainer(0));
        navigators[1].setOnClickListener(v -> viewContainer(1));
        navigators[2].setOnClickListener(v -> viewContainer(2));

        colorBtns[0].setOnClickListener(v -> selectColor(0));
        colorBtns[1].setOnClickListener(v -> selectColor(1));

        backgroundBtns[0].setOnClickListener(v -> pickBackgroundImage(0));
        backgroundBtns[1].setOnClickListener(v -> pickBackgroundImage(1));
        btn_add_or_remove_logo.setOnClickListener(v -> {
            if (logoPath == null || logoPath.isEmpty())
                new ChooserDialog(requireContext())
                        .withFilter(false, false, "png")
                        .withChosenListener((path, pathFile) -> {
//                            Hawk.put("imgbrown",path);
                            dataChanged = true;
                            logoPath = path;
                            loadLogo();
                        })
                        .build()
                        .show();
            else
                removeLogo();
        });
        btn_logo_color.setOnClickListener(i -> {
                    new ColorPickerDialog.Builder(requireContext())
                            .setTitle(getString(R.string.pick_color))
                            .setPreferenceName("MyColorPickerDialogCustomTheme")
                            .setPositiveButton(Utils.getString(R.string.select), (ColorEnvelopeListener) (envelope, fromUser) -> {
                                dataChanged = true;
                                logoColor = envelope.getColor();
                                loadLogo();
                            })
                            .setNegativeButton(Utils.getString(R.string.cancel), (dialogInterface, i2) -> dialogInterface.dismiss())
                            .attachAlphaSlideBar(false)
                            .attachBrightnessSlideBar(true)
                            .setBottomSpace(48) // set a bottom space between the last slidebar and buttons.
                            .setColorPickerView(new ColorPickerView.Builder(requireContext())
                                    .setInitialColor(logoColor == null ? 0xffffff : logoColor)
                                    .setPreferenceName("MyColorPickerDialogCustomTheme2")
                                    .build())
                            .show();
                }
        );
        btn_save.setOnClickListener(i -> save());
        btn_reset.setOnClickListener(i -> reset());

        btn_logo_color_remove.setOnClickListener(i -> {
            dataChanged = true;
            logoColor = null;
            loadLogo();
        });
        sw_show_ikama.setOnClickListener(i -> {
            dataChanged = true;
            showIkama = sw_show_ikama.isChecked();
        });
    }

    private void removeLogo() {
        logoPath = null;
        dataChanged = true;
        loadLogo();
    }

    private void pickBackgroundImage(int index) {
        new ChooserDialog(requireContext())
                .withFilter(false, false, "png")
                .withChosenListener((path, pathFile) -> {
                    dataChanged = true;
                    backgroundPaths[index] = path;
                    loadBackgroundImage(index);
                })
                .build()
                .show();
    }

    private void loadBackgroundImage(int index) {
        if (backgroundPaths[index] == null || backgroundPaths[index].isEmpty())
            backgrounds[index].setImageResource(index == 0 ? R.drawable.theme_custom_1_background_portrait : R.drawable.theme_custom_1_background_landscape);
        else
            backgrounds[index].setImageURI(Uri.parse(backgroundPaths[index]));
    }

    private void loadDefaultValues() {
        String th = theme.name();
        colors = new int[2];
        colors[0] = Hawk.get(th + "_color1", 0xFF494D32);
        colors[1] = Hawk.get(th + "_color2", 0xFFAE7C32);
        setButtonColorBackground(0);
        setButtonColorBackground(1);
        logoPath = Hawk.get("imgbrown", null);
        logoColor = Hawk.get("colorlogo", null);
        backgroundPaths[0] = Hawk.get(th + "_background_portrait", null);
        backgroundPaths[1] = Hawk.get(th + "_background_landscape", null);
        loadLogo();
        loadBackgroundImage(0);
        loadBackgroundImage(1);
        showIkama = Hawk.get(th + "_show_ikama", false);
        sw_show_ikama.setChecked(showIkama);
    }

    private void save() {
        String th = theme.name();

        if (theme == UITheme.CUSTOM_1) {
            Hawk.put(th + "_background_portrait", backgroundPaths[0]);
            Hawk.put(th + "_background_landscape", backgroundPaths[1]);
            Hawk.put(th + "_color1", colors[0]);
            Hawk.put(th + "_color2", colors[1]);
        }
        Hawk.put("imgbrown", logoPath);
        Hawk.put("colorlogo", logoColor);
        Hawk.put(th + "_show_ikama", showIkama);
        dataChanged = false;
        requireActivity().onBackPressed();
    }

    private void reset() {
        String th = theme.name();
        Utils.initConfirmDialog(
                requireContext(),
                0, "تأكيد",
                "سيتم حذف جميع الإعدادات الخاصة بالقالب و إعادته للوضع الإفتراضي\nهل أنت متأكد ؟",
                true,
                true,
                new OnSuccessful() {
                    @Override
                    public void onSuccessful(boolean isSuccessful) {
                        if (isSuccessful) {

                            if (theme == UITheme.CUSTOM_1) {
                                Hawk.delete(th + "_color1");
                                Hawk.delete(th + "_color2");
                                Hawk.delete(th + "_background_portrait");
                                Hawk.delete(th + "_background_landscape");
                            }
                            Hawk.delete("imgbrown");
                            Hawk.delete("colorlogo");
                            Hawk.delete(th + "_show_ikama");
                            dataChanged = false;
                            requireActivity().onBackPressed();
                        }
                        super.onSuccessful(isSuccessful);
                    }
                }
        );
    }

    @Override
    public void onBackPressed() {
        if (!dataChanged)
            requireActivity().onBackPressed();
        else
            Utils.initConfirmDialog(
                    requireContext(),
                    0, Utils.getString(R.string.exitConfirmation),
                    Utils.getString(R.string.exitConfirm),
                    true,
                    true,
                    new OnSuccessful() {
                        @Override
                        public void onSuccessful(boolean isSuccessful) {
                            if (isSuccessful) {
                                requireActivity().onBackPressed();
                            }
                            super.onSuccessful(isSuccessful);
                        }
                    }
            );
    }

    public void loadLogo() {
//        logoPath = Hawk.get("imgbrown",null);
        if (logoPath == null || logoPath.isEmpty()) { // not loaded
            btn_logo_color.setVisibility(View.GONE);
            btn_add_or_remove_logo.setText(getString(R.string.logoch));
            iv_logo.setVisibility(View.GONE);
            btn_logo_color_remove.setVisibility(View.GONE);
        } else {
            btn_logo_color.setVisibility(View.VISIBLE);
            btn_add_or_remove_logo.setText(getString(R.string.unlogoch));
            iv_logo.setVisibility(View.VISIBLE);
            iv_logo.setImageURI(Uri.parse(logoPath));
            if (logoColor != null) {
                btn_logo_color_remove.setVisibility(View.VISIBLE);
                iv_logo.setColorFilter(logoColor, PorterDuff.Mode.SRC_IN);
            } else {
                btn_logo_color_remove.setVisibility(View.GONE);
//                iv_logo.setColorFilter(0xFFFFFFFF, PorterDuff.Mode.MULTIPLY);
                iv_logo.clearColorFilter();
                iv_logo.setImageTintList(null);
            }
//            iv_logo.setColorFilter(logoColor, android.graphics.PorterDuff.Mode.MULTIPLY);
        }
    }

    public void initView() {
        containers = new ViewGroup[3];
        navigators = new ViewGroup[3];
        containers[0] = rootView.findViewById(R.id.layout_backgroundsContainer);
        containers[1] = rootView.findViewById(R.id.layout_logoContainer);
        containers[2] = rootView.findViewById(R.id.layout_colorsContainer);

        navigators[0] = rootView.findViewById(R.id.layout_addBackground);
        navigators[1] = rootView.findViewById(R.id.layout_addLogo);
        navigators[2] = rootView.findViewById(R.id.layout_addColor);

        colorBtns = new ImageView[2];
        colorBtns[0] = rootView.findViewById(R.id.btn_color_1);
        colorBtns[1] = rootView.findViewById(R.id.btn_color_2);

        backgrounds = new ImageView[2];
        backgrounds[0] = rootView.findViewById(R.id.iv_background_portrait);
        backgrounds[1] = rootView.findViewById(R.id.iv_background_landscape);

        backgroundBtns = new Button[2];
        backgroundBtns[0] = rootView.findViewById(R.id.btn_change_background_portrait);
        backgroundBtns[1] = rootView.findViewById(R.id.btn_change_background_landscape);

        btn_add_or_remove_logo = rootView.findViewById(R.id.btn_add_or_remove_logo);
//        btn_alrabea_logo_remove = rootView.findViewById(R.id.btn_alrabea_logo_remove);
        btn_logo_color = rootView.findViewById(R.id.btn_logo_color);
        btn_logo_color_remove = rootView.findViewById(R.id.btn_logo_color_remove);
        iv_logo = rootView.findViewById(R.id.iv_logo);
        btn_save = rootView.findViewById(R.id.btn_save);
        btn_reset = rootView.findViewById(R.id.btn_reset);
        sw_show_ikama = rootView.findViewById(R.id.show_ikama_times_button);
    }

    public void viewContainer(int index) {
        for (int i = 0; i < containers.length; i++) {
            if (index == i) {
                navigators[i].setBackgroundResource(R.drawable.button_gray_without_corners_shape);
                containers[i].setVisibility(View.VISIBLE);
            } else {
                navigators[i].setBackgroundResource(0);
                containers[i].setVisibility(View.GONE);
            }
        }
    }

    private void selectColor(int index) {

        new ColorPickerDialog.Builder(requireContext())
                .setTitle(getString(R.string.pick_color))
                .setPreferenceName("MyColorPickerDialogCustomTheme2")

                .setPositiveButton(Utils.getString(R.string.select), (ColorEnvelopeListener) (envelope, fromUser) -> {
                    dataChanged = true;
                    colors[index] = envelope.getColor();
                    setButtonColorBackground(index);
                })
                .setNegativeButton(Utils.getString(R.string.cancel), (dialogInterface, i2) -> dialogInterface.dismiss())
                .attachAlphaSlideBar(false)
                .attachBrightnessSlideBar(true)
                .setBottomSpace(48) // set a bottom space between the last slidebar and buttons.
                .setColorPickerView(
                        new ColorPickerView.Builder(requireContext())
                                .setInitialColor(colors[index])
                                .setPreferenceName("MyColorPickerDialogCustomTheme2")
                                .build()
                )
                .show();

//        new ColorPickerPopup.Builder(requireContext())
//                .initialColor(colors[index]) // Set initial color
//                .enableBrightness(true) // Enable brightness slider or not
//                .enableAlpha(false) // Enable alpha slider or not
//                .okTitle("إختيار")
//                .cancelTitle("إالغاء")
//                .showIndicator(true)
//                .showValue(false)
//                .build()
//                .show(null, new ColorPickerPopup.ColorPickerObserver() {
//                    @Override
//                    public void onColorPicked(int color) {
//                        dataChanged = true;
//                        colors[index] = color;
//                        setButtonColorBackground(index);
//                    }
//                });
    }

    public void setButtonColorBackground(int index) {
        colorBtns[index].setBackgroundColor(colors[index]);
    }
}
package com.arapeak.alrbea.database.events;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.database.MainDatabase;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.ArrayList;
import java.util.List;

public class EventSqlExecutioner {
    public static List<Event> getAll(SQLiteDatabase db) {
        List<Event> events = new ArrayList<>();
        try (Cursor cursor = db.rawQuery("select * from " + MainDatabase.EVENT, null)) {
            if (cursor.moveToFirst())
                do {
                    events.add(read(cursor));
                } while (cursor.moveToNext());
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return events;
    }

    public static List<Event> getAllEnabled(SQLiteDatabase db) {
        List<Event> events = new ArrayList<>();
        try (Cursor cursor = db.rawQuery("select * from " + MainDatabase.EVENT + " where enabled = 1", null)) {
            if (cursor.moveToFirst())
                do {
                    events.add(read(cursor));
                } while (cursor.moveToNext());
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return events;
    }

    public static Event get(SQLiteDatabase db, int id) {
        try (Cursor cursor = db.rawQuery("select * from " + MainDatabase.EVENT + " where id = " + id, null)) {
            if (cursor.moveToFirst())
                return read(cursor);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return null;
    }

    private static Event read(Cursor cursor) {
        Event event = new Event();
        event.id = cursor.getInt(0);
        event.text = cursor.getString(1);
        event.sCal = new UmmalquraCalendar();
        event.sCal.setTimeInMillis(cursor.getLong(2));
        event.eCal = new UmmalquraCalendar();
        event.eCal.setTimeInMillis(cursor.getLong(3));
        event.enabled = cursor.getInt(4) == 1;
        return event;
    }

    public static boolean add(SQLiteDatabase db, Event event) {
        long id = -1;
        try {
            ContentValues values = new ContentValues();
            values.put("text", event.text);
            values.put("sCal", event.sCal.getTimeInMillis());
            values.put("eCal", event.eCal.getTimeInMillis());
            values.put("enabled", true);
            id = db.insert(MainDatabase.EVENT, null, values);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return id != -1;
    }

    public static boolean update(SQLiteDatabase db, Event event) {
        long id = -1;
        try {
            ContentValues values = new ContentValues();
            values.put("text", event.text);
            values.put("sCal", event.sCal.getTimeInMillis());
            values.put("eCal", event.eCal.getTimeInMillis());
            values.put("enabled", event.enabled);
            id = db.update(MainDatabase.EVENT, values, "id=?", new String[]{event.id + ""});
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return id > 0;
    }

    public static boolean delete(SQLiteDatabase db, Event event) {
        int res = 0;
        try {
            res = db.delete(MainDatabase.EVENT, "id=?", new String[]{event.id + ""});
            //db.execSQL("delete from "+MainDatabase.EVENT+" where id = "+event.id);
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return res > 0;
    }
}

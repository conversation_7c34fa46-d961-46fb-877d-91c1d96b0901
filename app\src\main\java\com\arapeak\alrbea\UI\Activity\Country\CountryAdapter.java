package com.arapeak.alrbea.UI.Activity.Country;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.City;
import com.arapeak.alrbea.Model.Country;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.database.OmanCity;

import java.util.ArrayList;
import java.util.List;

import io.realm.RealmObject;

public class CountryAdapter extends RecyclerView.Adapter<CountryAdapter.CountryViewHolder> {

    public static final String TAG = "CountryAdapter";


    private final Context context;
    private final ArrayList<RealmObject> arrayListItem;
    private final LayoutInflater layoutInflater;
    private final AdapterCallback mCallback;

    public CountryAdapter(Context context
            , ArrayList<RealmObject> arrayListItem
            , AdapterCallback mCallback) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;

        layoutInflater = LayoutInflater.from(this.context);
    }


    @NonNull
    @Override
    public CountryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        View view = layoutInflater.inflate(R.layout.layout_list_item_country, parent, false);

        CountryViewHolder viewHolder = new CountryViewHolder(view);

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull CountryViewHolder savedVoiceViewHolder, int position) {

        savedVoiceViewHolder.onBind(position);
    }

    @Override
    public int getItemCount() {
//        return arrayListItem.size();
        return arrayListItem.size();
    }


    public void add(RealmObject item) {
        if (item == null) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.add(item);
//        notifyItemInserted(lastItemIndex);
        notifyDataSetChanged();
    }

    public void addAll(List<RealmObject> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
//        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public RealmObject getItem(int position) {
        if (position < 0 || position >= getItemCount()) {
            return null;
        }

        return arrayListItem.get(position);
    }


    public void addAllCountries(List<Country> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
//        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void addAllCties(List<City> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
//        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void addAllOmanCities(List<OmanCity> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
//        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void clear() {
        arrayListItem.clear();
        notifyDataSetChanged();
    }

    class CountryViewHolder extends RecyclerView.ViewHolder {

        private final TextView countryTextView;
        private final View spaceView;

        public CountryViewHolder(@NonNull View itemView) {
            super(itemView);

            countryTextView = itemView.findViewById(R.id.country_TextView_CountryViewHolder);
            spaceView = itemView.findViewById(R.id.space_View_CountryViewHolder);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCallback != null) {

                        mCallback.onItemClick(getAdapterPosition(), TAG);
                    }
                }
            });

        }

        @SuppressLint("SetTextI18n")
        public void onBind(final int position) {
            RealmObject realmObject = arrayListItem.get(position);

            if (position + 1 >= getItemCount()) {
                spaceView.setVisibility(View.GONE);
            } else {
                spaceView.setVisibility(View.VISIBLE);
            }

            if (realmObject instanceof Country) {
                Country country = (Country) realmObject;
                countryTextView.setText(country.getName_en() + " " + country.getName_ar());
            } else if (realmObject instanceof City) {
                City city = (City) realmObject;
                countryTextView.setText(city.getCity_name());
            } else if (realmObject instanceof OmanCity) {
                OmanCity city = (OmanCity) realmObject;
                countryTextView.setText(city.city + " " + city.cityname);
            }
        }
    }
}
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- <item>
         <shape>
             <solid android:color="#11000000" />
             <corners android:radius="3.3dp" />
         </shape>

     </item>-->
    <item
        android:bottom="2dp"
        android:gravity="center"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape>
            <solid android:color="#F2F2F2" />
            <corners android:radius="0.5dp" />
            <size
                android:width="32dp"
                android:height="32dp" />

            <padding
                android:bottom="4dp"
                android:left="4dp"
                android:right="4dp"
                android:top="4dp" />

            <!--<stroke
                android:width="@dimen/CheckBoxWidthStroke"
                android:color="@color/checkBoxStroke" />-->
        </shape>
    </item>

    <item android:gravity="center">
        <shape>
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="0.5dp" />
            <size
                android:width="24dp"
                android:height="24dp" />
        </shape>
    </item>

</layer-list>
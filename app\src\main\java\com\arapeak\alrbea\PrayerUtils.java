package com.arapeak.alrbea;

import static com.arapeak.alrbea.AppController.baseContext;

import android.annotation.SuppressLint;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.location.Location;
import android.util.Log;

import com.arapeak.alrbea.Enum.PrayerMethod;
import com.arapeak.alrbea.Model.Hijri_Cal_Tools;
import com.arapeak.alrbea.Model.TimingsAlrabeeaTimes;
import com.arapeak.alrbea.database.OmanCitiesDb;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.arapeak.alrbrea.core_ktx.model.CountriesSupportedEnum;
import com.arapeak.alrbrea.core_ktx.model.prayer.DayPrayers;
import com.arapeak.alrbrea.core_ktx.repo.PrayerTimeRepo;
import com.batoulapps.adhan2.CalculationMethod;
import com.batoulapps.adhan2.CalculationParameters;
import com.batoulapps.adhan2.Coordinates;
import com.batoulapps.adhan2.PrayerTimes;
import com.batoulapps.adhan2.data.DateComponents;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class PrayerUtils {
    private static final Map<String, CalculationMethod> countryMethods;

    static {
        countryMethods = new HashMap<>();
        countryMethods.put("SA", CalculationMethod.UMM_AL_QURA);
        countryMethods.put("PK", CalculationMethod.KARACHI);
        countryMethods.put("AE", CalculationMethod.DUBAI);
        countryMethods.put("SG", CalculationMethod.SINGAPORE);
        countryMethods.put("US", CalculationMethod.NORTH_AMERICA);
        countryMethods.put("CA", CalculationMethod.NORTH_AMERICA);
        countryMethods.put("BD", CalculationMethod.MOON_SIGHTING_COMMITTEE);
        countryMethods.put("IN", CalculationMethod.MOON_SIGHTING_COMMITTEE);
        countryMethods.put("EG", CalculationMethod.EGYPTIAN);
        countryMethods.put("KW", CalculationMethod.KUWAIT);
        countryMethods.put("QA", CalculationMethod.QATAR);
    }

    public static TimingsAlrabeeaTimes getTiming(int year, int month, int day) {
        TimingsAlrabeeaTimes result = _calcTiming(year, month, day);
        if (result != null) {
            result.setMonth(month);
            result.setDay(day);
            result.Year = year;
        }
        return result;
    }

    private static TimingsAlrabeeaTimes _calcTiming(int year, int month, int day) {
        TimingsAlrabeeaTimes result = new TimingsAlrabeeaTimes();
        try {
            Location location = new Location("");
            location.setLatitude(HawkSettings.getLatitude());
            location.setLongitude(HawkSettings.getLongitude());
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

            PrayerMethod calcMethod = HawkSettings.getCurrentPrayerMethod();
            String countryCode = HawkSettings.getCountryCode();
            Log.e("TimingsAlrabeeaTimes", "countryCode " + countryCode);


            switch (calcMethod) {
                case automatic: {

                    switch (countryCode) {
                        case "QA": { // Qatar
                            //Working good
                            result = GetQatarPrayers(year, month, day);
                            break;
                        }
                        case "OM": {
                            //Not tested
                            Hijri_Cal_Tools.calculation(HawkSettings.getLatitude(), HawkSettings.getLatitude2(), HawkSettings.getLongitude(), HawkSettings.getLongitude2(), year, month, day);
                            result.setFajr(Hijri_Cal_Tools.getFajer());
                            result.setSunrise(Hijri_Cal_Tools.getSunRise());
                            result.setDhuhr(Hijri_Cal_Tools.getDhuhur());
                            result.setAsr(Hijri_Cal_Tools.getAsar());
                            result.setMaghrib(Hijri_Cal_Tools.getMagrib());
                            result.setIsha(Hijri_Cal_Tools.getIshaa());
                            break;
                        }
                            /*_case "PK"://pakistani
                            case "AE"://emaraties
                            case "SG"://singapor
                            case "US":
                            case "CA":
                            case "BD"://Bangladesh
                            case "IN"://India
                            case "EG":
                            */
                        case "KW": {
                            PrayerTimeRepo prayerRepo = new PrayerTimeRepo(location, calcMethod.toKtxMethods());

                            DayPrayers dayPrayers = prayerRepo.getPrayerTimes(Calendar.getInstance(), CountriesSupportedEnum.Kuwait, baseContext);

                            result.setFajr(sdf.format(new Date(dayPrayers.getFajr().getTime().toInstant().toEpochMilli())));
                            result.setSunrise(sdf.format(new Date(dayPrayers.getSunrise().getTime().toInstant().toEpochMilli())));
                            result.setDhuhr(sdf.format(new Date(dayPrayers.getDhuhr().getTime().toInstant().toEpochMilli())));
                            result.setAsr(sdf.format(new Date(dayPrayers.getAsr().getTime().toInstant().toEpochMilli())));
                            result.setMaghrib(sdf.format(new Date(dayPrayers.getMaghrib().getTime().toInstant().toEpochMilli())));
                            result.setIsha(sdf.format(new Date(dayPrayers.getIsha().getTime().toInstant().toEpochMilli())));

                            break;
                        }
                        case "YE": {
                            PrayerTimeRepo prayerRepo = new PrayerTimeRepo(location, calcMethod.toKtxMethods());

                            DayPrayers dayPrayers = prayerRepo.getPrayerTimes(Calendar.getInstance(), CountriesSupportedEnum.Yemen, baseContext);

                            result.setFajr(sdf.format(new Date(dayPrayers.getFajr().getTime().toInstant().toEpochMilli())));
                            result.setSunrise(sdf.format(new Date(dayPrayers.getSunrise().getTime().toInstant().toEpochMilli())));
                            result.setDhuhr(sdf.format(new Date(dayPrayers.getDhuhr().getTime().toInstant().toEpochMilli())));
                            result.setAsr(sdf.format(new Date(dayPrayers.getAsr().getTime().toInstant().toEpochMilli())));
                            result.setMaghrib(sdf.format(new Date(dayPrayers.getMaghrib().getTime().toInstant().toEpochMilli())));
                            result.setIsha(sdf.format(new Date(dayPrayers.getIsha().getTime().toInstant().toEpochMilli())));

                            break;
                        }
                        case "BH": {
                            PrayerTimeRepo prayerRepo = new PrayerTimeRepo(location, calcMethod.toKtxMethods());

                            DayPrayers dayPrayers = prayerRepo.getPrayerTimes(Calendar.getInstance(), CountriesSupportedEnum.Bahrain, baseContext);

                            result.setFajr(sdf.format(new Date(dayPrayers.getFajr().getTime().toInstant().toEpochMilli())));
                            result.setSunrise(sdf.format(new Date(dayPrayers.getSunrise().getTime().toInstant().toEpochMilli())));
                            result.setDhuhr(sdf.format(new Date(dayPrayers.getDhuhr().getTime().toInstant().toEpochMilli())));
                            result.setAsr(sdf.format(new Date(dayPrayers.getAsr().getTime().toInstant().toEpochMilli())));
                            result.setMaghrib(sdf.format(new Date(dayPrayers.getMaghrib().getTime().toInstant().toEpochMilli())));
                            result.setIsha(sdf.format(new Date(dayPrayers.getIsha().getTime().toInstant().toEpochMilli())));

                            break;
                        }
                        case "JO": {

                            PrayerTimeRepo prayerRepo = new PrayerTimeRepo(location, calcMethod.toKtxMethods());
                            DayPrayers dayPrayers = prayerRepo.getPrayerTimes(Calendar.getInstance(), CountriesSupportedEnum.Jordan, baseContext);

                            result.setFajr(sdf.format(new Date(dayPrayers.getFajr().getTime().toInstant().toEpochMilli())));
                            result.setSunrise(sdf.format(new Date(dayPrayers.getSunrise().getTime().toInstant().toEpochMilli())));
                            result.setDhuhr(sdf.format(new Date(dayPrayers.getDhuhr().getTime().toInstant().toEpochMilli())));
                            result.setAsr(sdf.format(new Date(dayPrayers.getAsr().getTime().toInstant().toEpochMilli())));
                            result.setMaghrib(sdf.format(new Date(dayPrayers.getMaghrib().getTime().toInstant().toEpochMilli())));
                            result.setIsha(sdf.format(new Date(dayPrayers.getIsha().getTime().toInstant().toEpochMilli())));

                            break;
                        }
                        case "SA": {
                            //Working good
                            PrayerTimeRepo prayerRepo = new PrayerTimeRepo(location, calcMethod.toKtxMethods());

                            DayPrayers dayPrayers = prayerRepo.getPrayerTimes(Calendar.getInstance(), CountriesSupportedEnum.KSA, baseContext);

                            result.setFajr(sdf.format(new Date(dayPrayers.getFajr().getTime().toInstant().toEpochMilli())));
                            result.setSunrise(sdf.format(new Date(dayPrayers.getSunrise().getTime().toInstant().toEpochMilli())));
                            result.setDhuhr(sdf.format(new Date(dayPrayers.getDhuhr().getTime().toInstant().toEpochMilli())));
                            result.setAsr(sdf.format(new Date(dayPrayers.getAsr().getTime().toInstant().toEpochMilli())));
                            result.setMaghrib(sdf.format(new Date(dayPrayers.getMaghrib().getTime().toInstant().toEpochMilli())));
                            result.setIsha(sdf.format(new Date(dayPrayers.getIsha().getTime().toInstant().toEpochMilli())));

                            break;
                        }
                        default: {
//                    result = getTimingsForDate(year,month,day);

                            double latitude = HawkSettings.getLatitude(), longitude = HawkSettings.getLongitude();
                            Coordinates cord = new Coordinates(latitude, longitude);
                            DateComponents dateComponents = new DateComponents(year, month, day);

                            CalculationParameters parameters;
                            if (countryMethods.containsKey(countryCode))
                                parameters = countryMethods.get(countryCode).getParameters();
                            else
                                parameters = CalculationMethod.OTHER.getParameters();
                            PrayerTimes times = new PrayerTimes(cord, dateComponents, parameters);

                            result.setFajr(sdf.format(new Date(times.getFajr().toEpochMilliseconds())));
                            result.setSunrise(sdf.format(new Date(times.getSunrise().toEpochMilliseconds())));
                            result.setDhuhr(sdf.format(new Date(times.getDhuhr().toEpochMilliseconds())));
                            result.setAsr(sdf.format(new Date(times.getAsr().toEpochMilliseconds())));
                            result.setMaghrib(sdf.format(new Date(times.getMaghrib().toEpochMilliseconds())));
                            result.setIsha(sdf.format(new Date(times.getIsha().toEpochMilliseconds())));

                            break;
                        }
                    }

                    break;
                }

                case customCalendar:
                    return getTimingsForDate(year, month, day);

                default: {
                    PrayerTimeRepo prayerRepo = new PrayerTimeRepo(location, calcMethod.toKtxMethods());

                    DayPrayers dayPrayers = prayerRepo.getPrayerTimes(Calendar.getInstance(), CountriesSupportedEnum.KSA, baseContext);

                    result.setFajr(sdf.format(new Date(dayPrayers.getFajr().getTime().toInstant().toEpochMilli())));
                    result.setSunrise(sdf.format(new Date(dayPrayers.getSunrise().getTime().toInstant().toEpochMilli())));
                    result.setDhuhr(sdf.format(new Date(dayPrayers.getDhuhr().getTime().toInstant().toEpochMilli())));
                    result.setAsr(sdf.format(new Date(dayPrayers.getAsr().getTime().toInstant().toEpochMilli())));
                    result.setMaghrib(sdf.format(new Date(dayPrayers.getMaghrib().getTime().toInstant().toEpochMilli())));
                    result.setIsha(sdf.format(new Date(dayPrayers.getIsha().getTime().toInstant().toEpochMilli())));
                    break;
                }
            }


            return result;
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return null;
    }

    public static TimingsAlrabeeaTimes GetQatarPrayers(int year, int month, int day) {
        TimingsAlrabeeaTimes result = new TimingsAlrabeeaTimes();
        try (OmanCitiesDb db = new OmanCitiesDb()) {
            db.openDataBase();
            SQLiteDatabase database = db.getReadableDatabase();

            // Convert day, month, year to the format used in the database
            String date = String.format(Locale.ENGLISH, "%02d-%02d-%04d", day, month, year);

            // Retrieve the prayer timings for the given date
            String query = "SELECT fajr, sunrise, duhr, asr, maghrib, isha FROM qatar_prayers WHERE date = ?";
            Cursor cursor = database.rawQuery(query, new String[]{date});

            if (cursor.moveToFirst()) {
                // Convert the prayer timings to the desired format
                result.setFajr(convertQatarTime(cursor.getString(0)));
                result.setSunrise(convertQatarTime(cursor.getString(1)));
                result.setDhuhr(convertQatarTime(cursor.getString(2)));
                result.setAsr(convertQatarTime(cursor.getString(3)));
                result.setMaghrib(convertQatarTime(cursor.getString(4)));
                result.setIsha(convertQatarTime(cursor.getString(5)));
            }

            cursor.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

        result.setMonth(month);
        result.setDay(day);
        result.Year = year;
        return result;
    }

    @SuppressLint("Range")
    public static TimingsAlrabeeaTimes getTimingsForDate(int year, int month, int day) {
        TimingsAlrabeeaTimes timing = null;
        try (OmanCitiesDb omanCitiesDb = new OmanCitiesDb()) {
            SQLiteDatabase db = omanCitiesDb.getReadableDatabase();
            String[] columns = {"midnight", "imsak", "isha", "maghrib", "sunset", "asr", "dhuhr", "sunrise", "fajr", "month", "day", "year"};
            String selection = "year=? AND month=? AND day=?";
            String[] selectionArgs = {String.valueOf(year), String.valueOf(month), String.valueOf(day)};
            Cursor cursor = db.query("timings", columns, selection, selectionArgs, null, null, null);
            if (cursor.moveToFirst()) {
                timing = new TimingsAlrabeeaTimes();
                timing.setIsha(cursor.getString(cursor.getColumnIndex("isha")));
                timing.setMaghrib(cursor.getString(cursor.getColumnIndex("maghrib")));
                timing.setAsr(cursor.getString(cursor.getColumnIndex("asr")));
                timing.setDhuhr(cursor.getString(cursor.getColumnIndex("dhuhr")));
                timing.setSunrise(cursor.getString(cursor.getColumnIndex("sunrise")));
                timing.setFajr(cursor.getString(cursor.getColumnIndex("fajr")));
                timing.setMonth(cursor.getString(cursor.getColumnIndex("month")));
                timing.setDay(cursor.getString(cursor.getColumnIndex("day")));
                timing.Year = cursor.getInt(cursor.getColumnIndex("year"));
            }
            cursor.close();
            db.close();

        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return timing;
    }

    private static String convertQatarTime(String time) {
        String[] parts = time.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        return String.format(Locale.ENGLISH, "%d:%d", hours, minutes);
    }


}

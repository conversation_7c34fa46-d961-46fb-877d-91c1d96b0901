<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_TextView_screensaver"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="14dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/screensaver_toggle"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Spinner
                android:id="@+id/options_Spinner_screensaver"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_40sdp"
                android:layout_gravity="start"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:dropDownVerticalOffset="40dp"
                android:padding="8dp"
                android:spinnerMode="dropdown"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_TextView_screensaver"
                tools:listitem="@layout/layout_list_item_spinner_text" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="#D1D1D1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/options_Spinner_screensaver" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_ss_mode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            tools:visibility="visible"
            android:visibility="invisible">

            <TextView
                android:id="@+id/title_TextView_screensaver2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="14dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/screensaver"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Spinner
                android:id="@+id/mode_Spinner_screensaver"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_40sdp"
                android:layout_gravity="start"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:dropDownVerticalOffset="40dp"
                android:padding="8dp"
                android:spinnerMode="dropdown"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_TextView_screensaver2"
                tools:listitem="@layout/layout_list_item_spinner_text" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="#D1D1D1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mode_Spinner_screensaver" />


        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_ss_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="14dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/screensaver_info"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Spinner
                android:id="@+id/options_Spinner_screensaver_elements"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_40sdp"
                android:layout_gravity="start"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:dropDownVerticalOffset="40dp"
                android:padding="8dp"
                android:spinnerMode="dropdown"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_TextView_screensaver"
                tools:listitem="@layout/layout_list_item_spinner_text" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="#D1D1D1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/options_Spinner_screensaver" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="14dp"
                android:layout_marginEnd="14dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/text_size"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_11sdp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Spinner
                android:id="@+id/options_Spinner_screensaver_sizes"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_40sdp"
                android:layout_gravity="start"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/without_corners_bottom_50_background_gray"
                android:dropDownVerticalOffset="40dp"
                android:padding="8dp"
                android:spinnerMode="dropdown"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_TextView_screensaver"
                tools:listitem="@layout/layout_list_item_spinner_text" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"

                android:background="#D1D1D1"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/options_Spinner_screensaver" />


        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_ss_timing"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:animateLayoutChanges="true"
            android:background="#FAFAFA"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginEnd="14dp"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:text="@string/screensaver_timing"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_11sdp"
                    android:textStyle="bold"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="14dp"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:lines="2"
                    android:text="@string/screensaver_timing_description"
                    android:textColor="@color/colorBlueMain"
                    android:textSize="@dimen/_8sdp"
                    android:textStyle="normal"
                    app:layout_constraintTop_toTopOf="parent" />
            </LinearLayout>

            <CheckBox
                android:id="@+id/ss_timing_during_prayers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="64dp"
                android:layout_marginEnd="16dp"
                android:button="@drawable/check_box_blue_selector_big"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_12sdp"
                tools:text="التوقيت الهجري الحالي"
                tools:visibility="visible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/during_prayer_desc"
                android:textColor="@color/colorBlueMain"
                android:textSize="@dimen/_8sdp"
                android:textStyle="normal"
                tools:visibility="visible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="32dp"
                android:background="#D1D1D1" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <CheckBox
                    android:id="@+id/ss_timing_prayers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="18dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_weight="1"
                    android:button="@drawable/check_box_blue_selector_big"
                    android:checked="true"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_12sdp"
                    tools:text="التوقيت الهجري الحالي"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/ss_timing_prayers_settings"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="32dp"
                    android:layout_marginEnd="32dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_settings_black_24dp"
                    app:tint="@color/colorBlueMain" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/between_prayer_desc"
                android:textColor="@color/colorBlueMain"
                android:textSize="@dimen/_8sdp"
                android:textStyle="normal"
                tools:visibility="visible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="32dp"
                android:background="#D1D1D1" />


            <LinearLayout
                android:id="@+id/ss_ll_period"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16dp"
                tools:visibility="visible">

                <CheckBox
                    android:id="@+id/ss_timing_period"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="18dp"
                    android:layout_weight="5"

                    android:button="@drawable/check_box_blue_selector_big"
                    android:checked="true"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_12sdp"
                    tools:text="التوقيت الهجري الحالي"
                    tools:visibility="visible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:padding="8dp"
                    android:text="@string/from"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_11sdp"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/time_from_TextView_SubSettingsHolder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/without_corners_20_stroke_1_background_transparent"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:gravity="center"
                    android:minWidth="100dp"
                    android:padding="4dp"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_15sdp"
                    android:textStyle="bold"
                    tools:text="12:44"
                    tools:visibility="visible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:padding="8dp"
                    android:text="@string/to"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_11sdp"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/time_to_TextView_SubSettingsHolder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/without_corners_20_stroke_1_background_transparent"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:gravity="center"
                    android:minWidth="100dp"
                    android:padding="4dp"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_15sdp"
                    android:textStyle="bold"
                    tools:text="12:44"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:id="@+id/ss_timing_period_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/period_desc"
                android:textColor="@color/colorBlueMain"
                android:textSize="@dimen/_8sdp"
                android:textStyle="normal"
                tools:visibility="visible" />

            <View
                android:id="@+id/space_View_ss2hss"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="32dp"
                android:background="#D1D1D1" />

            <LinearLayout
                android:id="@+id/ss_ll_delay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:visibility="gone">

                <CheckBox
                    android:id="@+id/ss_timing_delay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="18dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:layout_weight="5"
                    android:button="@drawable/check_box_blue_selector_big"
                    android:checked="true"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:text="@string/after_delay"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_12sdp"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/addMinusNumber_LinearLayout_SubSettingsHolder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="4dp"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:padding="4dp"
                    android:visibility="visible">
                    <!--            android:visibility="gone">-->

                    <Button
                        android:id="@+id/add_Button_SubSettingsHolder"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:background="@drawable/without_corners_20_background_blue_with_drawable_add_white"
                        android:elevation="0dp"
                        android:gravity="center"
                        android:includeFontPadding="false" />

                    <TextView
                        android:id="@+id/edit_TextView_SubSettingsHolder"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/without_corners_20_background_white_with_stroke_blue"
                        android:fontFamily="@font/droid_arabic_kufi_bold"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:inputType="numberSigned"
                        android:maxLines="1"
                        android:text="0"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_10sdp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/minus_Button_SubSettingsHolder"
                        android:layout_width="@dimen/_20sdp"
                        android:layout_height="@dimen/_20sdp"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/without_corners_20_background_blue_with_drawable_minus_white"
                        android:elevation="0dp"
                        android:gravity="center"
                        android:includeFontPadding="false" />

                </LinearLayout>

                <TextView
                    android:id="@+id/minutes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:padding="8dp"
                    android:text="@string/minutes"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_11sdp"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/delay_desc"
                android:textColor="@color/colorBlueMain"
                android:textSize="@dimen/_8sdp"
                android:textStyle="normal"
                android:visibility="gone" />

            <View
                android:id="@+id/space_View_ss2hses"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="32dp"
                android:background="#D1D1D1" />

            <LinearLayout
                android:id="@+id/ss_ll_interval"

                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/ss_timing_interval"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="18dp"
                        android:layout_marginTop="32dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="32dp"
                        android:button="@drawable/check_box_blue_selector_big"
                        android:checked="true"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_12sdp"
                        tools:text="التوقيت الهجري الحالي"
                        tools:visibility="visible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="240dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="@string/on_for"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_11sdp"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="4dp"
                        android:gravity="center"
                        android:padding="4dp"
                        android:visibility="visible">
                        <!--            android:visibility="gone">-->

                        <Button
                            android:id="@+id/add_Button_SubSettingsHolder1"
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:background="@drawable/without_corners_20_background_blue_with_drawable_add_white"
                            android:elevation="0dp"
                            android:gravity="center"
                            android:includeFontPadding="false" />

                        <TextView
                            android:id="@+id/edit_TextView_SubSettingsHolder1"
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/without_corners_20_background_white_with_stroke_blue"
                            android:fontFamily="@font/droid_arabic_kufi_bold"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:inputType="numberSigned"
                            android:maxLines="1"
                            android:text="0"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/_10sdp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/minus_Button_SubSettingsHolder1"
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/without_corners_20_background_blue_with_drawable_minus_white"
                            android:elevation="0dp"
                            android:gravity="center"
                            android:includeFontPadding="false" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/minutes2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="@string/minutes"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_11sdp"
                        tools:visibility="visible" />


                    <Space
                        android:layout_width="32dp"
                        android:layout_height="32dp" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"

                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="@string/then"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_11sdp"
                        tools:visibility="visible" />

                    <TextView
                        android:layout_width="180dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="@string/off_for"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_11sdp"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="4dp"
                        android:gravity="center"
                        android:padding="4dp"
                        android:visibility="visible">
                        <!--            android:visibility="gone">-->

                        <Button
                            android:id="@+id/add_Button_SubSettingsHolder2"
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:background="@drawable/without_corners_20_background_blue_with_drawable_add_white"
                            android:elevation="0dp"
                            android:gravity="center"
                            android:includeFontPadding="false" />

                        <TextView
                            android:id="@+id/edit_TextView_SubSettingsHolder2"
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/without_corners_20_background_white_with_stroke_blue"
                            android:fontFamily="@font/droid_arabic_kufi_bold"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            android:inputType="numberSigned"
                            android:maxLines="1"
                            android:text="0"
                            android:textColor="@android:color/black"
                            android:textSize="@dimen/_10sdp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/minus_Button_SubSettingsHolder2"
                            android:layout_width="@dimen/_20sdp"
                            android:layout_height="@dimen/_20sdp"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/without_corners_20_background_blue_with_drawable_minus_white"
                            android:elevation="0dp"
                            android:gravity="center"
                            android:includeFontPadding="false" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="center"
                        android:padding="8dp"
                        android:text="@string/minutes"
                        android:textColor="@android:color/black"
                        android:textSize="@dimen/_11sdp"
                        tools:visibility="visible" />


                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/ss_timing_interval_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/droid_arabic_kufi"

                android:text="@string/interval_desc"
                android:textColor="@color/colorBlueMain"
                android:textSize="@dimen/_8sdp"
                android:textStyle="normal"
                tools:visibility="visible" />

            <View
                android:id="@+id/space_View_ss5"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="32dp"
                android:background="#D1D1D1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mode_Spinner_screensaver" />


            <TextView
                android:id="@+id/tv_show_plus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="24dp"
                android:layout_marginTop="32dp"
                android:layout_marginEnd="24dp"
                android:layout_marginBottom="14dp"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:text="@string/show_plus"
                android:textColor="@color/colorBlue"
                android:textSize="@dimen/_12sdp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent" />


        </LinearLayout>


    </LinearLayout>

</ScrollView>
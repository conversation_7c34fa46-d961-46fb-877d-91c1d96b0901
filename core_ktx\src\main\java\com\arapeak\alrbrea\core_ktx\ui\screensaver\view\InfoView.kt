package com.arapeak.alrbrea.core_ktx.ui.screensaver.view

import android.content.Context
import android.view.MotionEvent
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils.logException
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoElementsEnum
import com.arapeak.alrbrea.core_ktx.model.screensaver.ScreenSaverInfoSizeEnum


class InfoView(context: Context, sizeEnum: ScreenSaverInfoSizeEnum, infoEnum: ScreenSaverInfoElementsEnum, val onclick: () -> Unit) : ConstraintLayout(context) {

    val textClock: TextView
    val tvClockType: TextView
    val tvDayName: TextView
    val tvHijri: TextView
    val tvMiladi: TextView
    val tvFuneral: TextView

    private val Small = 32
    private val Meduim = 64
    private val Large = 128


    init {
        inflate(context, R.layout.screensaver_info_view, this)

        textClock = findViewById(R.id.textClock)
        tvClockType = findViewById(R.id.tv_clock_type)
        tvDayName = findViewById(R.id.tv_date_day)
        tvHijri = findViewById(R.id.tv_date_hijri)
        tvMiladi = findViewById(R.id.tv_date_miladi)
        tvFuneral = findViewById(R.id.tv_funeral)


        when (sizeEnum) {
            ScreenSaverInfoSizeEnum.Small -> setSizes(Small)
            ScreenSaverInfoSizeEnum.Medium -> setSizes(Meduim)
            ScreenSaverInfoSizeEnum.Large -> setSizes(Large)
        }

        when (infoEnum) {
            ScreenSaverInfoElementsEnum.TimeOnly -> {
                tvDayName.visibility = GONE
                tvHijri.visibility = GONE
                tvMiladi.visibility = GONE
            }

            ScreenSaverInfoElementsEnum.DateTime -> {
                tvDayName.visibility = VISIBLE
                tvHijri.visibility = VISIBLE
                tvMiladi.visibility = VISIBLE
            }
        }
    }

    private fun setSizes(i: Int) {
        textClock.textSize = i.toFloat() * 1.5f
        tvClockType.textSize = i.toFloat() * 0.4f
        tvDayName.textSize = i.toFloat() * 0.6f
        tvHijri.textSize = i.toFloat() * 0.5f
        tvMiladi.textSize = i.toFloat() * 0.5f
//        tvFuneral.textSize = i.toFloat() * 0.3f
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        try {
            (parent as? ViewGroup)?.removeView(this)
            onclick.invoke()
        } catch (e: Exception) {
            logException(e)
        }
        return true
    }

    fun removeView() {
        try {
            (parent as? ViewGroup)?.removeView(this)
        } catch (e: Exception) {
            logException(e)
        }
    }

    fun updateData(timeText: String, timeType: String?, miladi: String, hijri: String, dayName: String, funeral: String?) {
        try {
            textClock.text = timeText
            tvClockType.text = timeType ?: ""
            tvDayName.text = dayName
            tvHijri.text = hijri
            tvMiladi.text = miladi
            if (funeral != null) {
                val text = context.getString(R.string.there_is_funeral_x, funeral)
                tvFuneral.text = text
                tvFuneral.visibility = VISIBLE
            } else
                tvFuneral.visibility = GONE
        } catch (e: Exception) {
            logException(e)
        }

    }

}
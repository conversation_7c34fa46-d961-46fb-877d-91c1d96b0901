package com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.events;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SwitchCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.events.content.AddEditEventFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.ads.content.events.content.EventViewHolder;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.database.Repositories;
import com.arapeak.alrbea.hawk.HawkSettings;

import java.util.List;

public class EventsFragment extends AlrabeeaTimesFragment {
    RecyclerView rv;
    RecyclerView.Adapter<EventViewHolder> adapter;
    SwitchCompat sw;
    Button btn;
    List<Event> eventList;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View parent = inflater.inflate(R.layout.fragment_events, container, false);
        initView(parent);
        setAction();
        return parent;
    }

    private void initView(View parent) {
        rv = parent.findViewById(R.id.themes_RecyclerView_ThemesFragment);
        sw = parent.findViewById(R.id.enableDisablePhoto_SwitchCompat_AddPhotoFragment);
        btn = parent.findViewById(R.id.btn_add);
    }

    private void setAction() {
        btn.setOnClickListener(this::add);
        eventList = Repositories.getEventDb().getAll();
        adapter = new RecyclerView.Adapter<EventViewHolder>() {
            @NonNull
            @Override
            public EventViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
                LayoutInflater inflater = LayoutInflater.from(requireContext());
                return new EventViewHolder(inflater.inflate(R.layout.list_item_event_view_holder, parent, false));
            }

            @Override
            public void onBindViewHolder(@NonNull EventViewHolder holder, int position) {
                holder.Bind(eventList.get(position));
                holder.itemView.setOnClickListener((i) -> {
                    Utils.loadFragment(new AddEditEventFragment(holder.event)
                            , getAppCompatActivity()
                            , 0);
                    Log.i("Events Fragment", "onBindViewHolder: Fragment opened");
//                    holder.event
                });
            }

            @Override
            public int getItemCount() {
                return eventList.size();
            }
        };
        rv.setAdapter(adapter);

        sw.setChecked(HawkSettings.getEventsEnabled());
        sw.setOnClickListener(i -> {
            HawkSettings.setEventsEnabled(sw.isChecked());
            enableDisableEvents();
        });
        enableDisableEvents();

    }

    private void enableDisableEvents() {
        boolean isEnabled = HawkSettings.getEventsEnabled();
        rv.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
        btn.setVisibility(isEnabled ? View.VISIBLE : View.GONE);
    }

    private void add(View view) {
        Utils.loadFragment(new AddEditEventFragment()
                , getAppCompatActivity()
                , 0);
        Log.i("Events Fragment", "onBindViewHolder: Fragment opened");
    }

}

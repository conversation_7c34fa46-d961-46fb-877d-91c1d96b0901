package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.utils

import java.util.TimeZone


fun getGmtDiff(): Double {
    val currentTimeZone = TimeZone.getDefault()

    val gmtOffset = currentTimeZone.rawOffset

    val gmtDifferenceInHours = gmtOffset / (1000.0 * 60.0 * 60.0)

    val differenceString = when (gmtDifferenceInHours) {
        0.0 -> "GMT"
        else -> {
            val prefix = if (gmtDifferenceInHours > 0) "+" else "-"
            val hours = Math.abs(gmtDifferenceInHours).toString()
            "$prefix$hours hrs"
        }
    }

// Example usage:
    println("GMT Difference: $differenceString")
    return gmtDifferenceInHours
}
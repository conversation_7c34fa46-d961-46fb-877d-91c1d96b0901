package com.arapeak.alrbrea.core_ktx.model.remoteaccess

data class RemoteTool(
    val id: Int,
    val name: String,
    val appPackage: String,
    val fileName: String,
    var downLink: String,
    var status: RemoteToolStatus,
    val iconRes: Int,
) {
    var progress: Int = - 99

}


//sealed class AppUpdateState {
//    data object Init : AppUpdateState()
//    data object Checking : AppUpdateState()
//
//    //check for update
//    data object UpToDate : AppUpdateState()
//
//    //download update
//    data class Downloading(val percentage: Int) : AppUpdateState()
//    data class Downloaded(val path: String) : AppUpdateState()
//
//    //install update
//    data object Refused : AppUpdateState()
//
//    data class Failed(val error: String) : AppUpdateState()
//
//}
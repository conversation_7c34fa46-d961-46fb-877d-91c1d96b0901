package com.arapeak.alrbea;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.DisplayMetrics;

import java.util.Locale;

public class ResourcesLocale {
    private final Context mContext;
    private final AssetManager mAssetManager;
    private final DisplayMetrics mMetrics;
    private final Configuration mConfiguration;
    private final Locale mDefaultLocale;
    private final Locale mTargetLocale;

    public ResourcesLocale(final Context context, final Locale defaultLocale,
                           final Locale targetLocale) {
        this.mContext = context;
        final Resources resources = this.mContext.getResources();
        this.mAssetManager = resources.getAssets();
        this.mMetrics = resources.getDisplayMetrics();
        this.mConfiguration = new Configuration(resources.getConfiguration());
        this.mTargetLocale = targetLocale;
        this.mDefaultLocale = defaultLocale;
    }

    public String getString(final int resourceId) {
        if (mTargetLocale == null) return null;
        mConfiguration.setLocale(mTargetLocale);
        return mContext.createConfigurationContext(mConfiguration).getResources()
                .getString(resourceId);
    }

    public String getString(final int resourceId, int arg) {
        if (mTargetLocale == null) return null;
        mConfiguration.setLocale(mTargetLocale);
        return mContext.createConfigurationContext(mConfiguration).getResources()
                .getString(resourceId, arg);
    }

    public String getString(final int resourceId, int arg, int arg2) {
        if (mTargetLocale == null) return null;
        mConfiguration.setLocale(mTargetLocale);
        return mContext.createConfigurationContext(mConfiguration).getResources()
                .getString(resourceId, arg, arg2);
    }

    public String[] getStringArray(final int resourceId) {
        if (mTargetLocale == null) return null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            mConfiguration.setLocale(mTargetLocale);
            return mContext.createConfigurationContext(mConfiguration).getResources()
                    .getStringArray(resourceId);
        } else {
            mConfiguration.locale = mTargetLocale;
            final String[] resource =
                    new ResourceManager(mAssetManager, mMetrics, mConfiguration).getStringArray(resourceId);
            mConfiguration.locale = mDefaultLocale;  // reset lại locale cũ
            new ResourceManager(mAssetManager, mMetrics, mConfiguration);
            return resource;
        }
    }

    private final class ResourceManager extends Resources {
        public ResourceManager(final AssetManager assets, final DisplayMetrics metrics,
                               final Configuration config) {
            super(assets, metrics, config);
        }

        @Override
        public String getString(final int id, final Object... formatArgs) throws NotFoundException {
            return super.getString(id, formatArgs);
        }

        @Override
        public String getString(final int id) throws NotFoundException {
            return super.getString(id);
        }
    }
}
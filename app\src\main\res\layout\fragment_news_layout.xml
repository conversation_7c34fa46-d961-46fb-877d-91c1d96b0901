<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/confirm_message_ScrollView_CreateMovingMessageFragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true"
    tools:layoutDirection="rtl">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:padding="@dimen/_20sdp">

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/active_SwitchCompat_CreateMovingMessageFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi"
                android:text="@string/enable_news_ticker"
                android:textSize="@dimen/dateNowMain"
                app:buttonTint="@color/colorPrimary"
                app:thumbTint="@android:color/white"
                app:trackTint="@color/colorPrimary" />

            <LinearLayout
                android:id="@+id/layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/moveBetweenImage_TextView_AddPhotoFragment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_20sdp"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:includeFontPadding="false"
                    android:text="@string/ready_texts"
                    android:textColor="#474747"
                    android:textSize="@dimen/dateNowMain" />

                <Spinner
                    android:id="@+id/moveBetweenImage_Spinner_AddPhotoFragment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/_20sdp"
                    android:layout_marginTop="@dimen/_15sdp"
                    android:layout_marginEnd="@dimen/_20sdp"
                    android:background="@drawable/without_corners_bottom_50_background_gray"
                    android:padding="@dimen/_5sdp"
                    android:spinnerMode="dropdown"
                    tools:listitem="@layout/layout_list_item_spinner_text" />

                <TextView
                    android:id="@+id/title_TextView_CreateMovingMessageFragment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:fontFamily="@font/droid_arabic_kufi_bold"
                    android:gravity="center"
                    android:text="@string/the_message_will_be_displayed_inplace_of_date_bar_you_can_back_to_disable_it_in_any_time"
                    android:textColor="@android:color/black"
                    android:textSize="@dimen/_15sdp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/message_EditText_CreateMovingMessageFragment"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_140sdp"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginEnd="@dimen/_10sdp"
                    android:background="@drawable/without_corners_bottom_20_background_gray"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="start|top"
                    android:hint="@string/body_of_message"
                    android:includeFontPadding="false"
                    android:padding="@dimen/_10sdp"
                    android:textColor="@android:color/black"
                    android:textColorHint="@color/colorGrayDarkTwo"
                    android:textSize="@dimen/contactUsView" />

                <LinearLayout
                    android:id="@+id/date_LinearLayout_CreateMovingMessageFragment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20sdp">

                    <Button
                        android:id="@+id/startDate_TextView_CreateMovingMessageFragment"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.43"
                        android:background="@drawable/without_corners_bottom_20_background_gray"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="start|top"
                        android:hint="@string/start_date"
                        android:includeFontPadding="false"
                        android:inputType="none"
                        android:paddingStart="@dimen/_10sdp"
                        android:paddingTop="@dimen/_10sdp"
                        android:paddingEnd="@dimen/_10sdp"
                        android:paddingBottom="@dimen/_10sdp"
                        android:singleLine="true"
                        android:textAlignment="viewStart"
                        android:textColor="@android:color/black"
                        android:textColorHint="#474747"
                        android:textSize="@dimen/contactUsView" />

                    <Button
                        android:id="@+id/endDate_TextView_CreateMovingMessageFragment"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:layout_weight="0.43"
                        android:background="@drawable/without_corners_bottom_20_background_gray"
                        android:drawableEnd="@drawable/ic_calendar"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="start|top"
                        android:hint="@string/end_date"
                        android:includeFontPadding="false"
                        android:inputType="none"
                        android:paddingStart="@dimen/_10sdp"
                        android:paddingTop="@dimen/_10sdp"
                        android:paddingEnd="@dimen/_10sdp"
                        android:paddingBottom="@dimen/_10sdp"
                        android:singleLine="true"
                        android:textAlignment="viewStart"
                        android:textColor="@android:color/black"
                        android:textColorHint="#474747"
                        android:textSize="@dimen/contactUsView" />
                </LinearLayout>
            </LinearLayout>

            <Button
                android:id="@+id/save_Button_CreateMovingMessageFragment"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_35sdp"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_15sdp"
                android:background="@drawable/button_green_without_corners_shape"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingStart="@dimen/_5sdp"
                android:paddingTop="@dimen/_5sdp"
                android:paddingEnd="@dimen/_5sdp"
                android:paddingBottom="@dimen/_5sdp"
                android:text="@string/save"
                android:textAlignment="center"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_14sdp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
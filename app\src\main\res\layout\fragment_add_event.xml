<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:fillViewport="true"
    tools:context=".UI.Fragment.settings.content.ads.content.AddEventFragment"
    tools:layoutDirection="rtl">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_20sdp">

        <LinearLayout
            android:id="@+id/activeCancel_LinearLayout_AddEventFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginEnd="@dimen/_10sdp"
            android:orientation="horizontal"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/cancel_TextView_AddEventFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/cancel"
                android:textColor="@android:color/black"
                android:textSize="@dimen/dateNowMain"
                android:visibility="gone" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/active_SwitchCompat_AddEventFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_5sdp"
                android:checked="true"
                app:buttonTint="@color/colorPrimary"
                app:thumbTint="@android:color/white"
                app:trackTint="@color/colorPrimary" />

            <TextView
                android:id="@+id/active_TextView_AddEventFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_marginEnd="@dimen/_10sdp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/enable_funeral_messages"
                android:textColor="@android:color/black"
                android:textSize="@dimen/dateNowMain" />

        </LinearLayout>

        <TextView
            android:id="@+id/templateMessages_TextView_AddEventFragment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10sdp"
            android:fontFamily="@font/droid_arabic_kufi"
            android:includeFontPadding="false"
            android:text="@string/template_messages"
            android:textColor="@android:color/black"
            android:textSize="@dimen/prayerName"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/activeCancel_LinearLayout_AddEventFragment" />


        <Spinner
            android:id="@+id/templateMessages_Spinner_AddEventFragment"
            android:layout_width="0dp"
            android:layout_height="@dimen/_40sdp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:dropDownVerticalOffset="@dimen/_35sdp"
            android:padding="@dimen/_10sdp"
            android:spinnerMode="dropdown"
            app:layout_constrainedHeight="true"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/templateMessages_TextView_AddEventFragment" />


        <Spinner
            android:id="@+id/prayName_Spinner_AddEventFragment"
            android:layout_width="0dp"
            android:layout_height="@dimen/_40sdp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:background="@drawable/without_corners_bottom_50_background_gray"
            android:dropDownVerticalOffset="@dimen/_35sdp"
            android:padding="@dimen/_10sdp"
            android:spinnerMode="dropdown"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/templateMessages_Spinner_AddEventFragment"
            tools:listitem="@layout/layout_list_item_spinner_text" />


        <Button
            android:id="@+id/save_Button_AddEventFragment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_25sdp"
            android:background="@drawable/without_corners_50_background_blue"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:gravity="center_vertical|center_horizontal"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/_5sdp"
            android:paddingTop="@dimen/_5sdp"
            android:paddingEnd="@dimen/_5sdp"
            android:paddingBottom="@dimen/_5sdp"
            android:text="@string/save"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_16sdp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/prayName_Spinner_AddEventFragment" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
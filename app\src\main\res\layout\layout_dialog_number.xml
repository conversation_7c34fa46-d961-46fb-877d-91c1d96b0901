<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal|top"
    android:layout_margin="@dimen/_20sdp"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:minWidth="@dimen/_250sdp"
    android:orientation="vertical"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="@dimen/_18sdp"
    app:cardElevation="0dp"
    tools:layoutDirection="rtl">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:minWidth="@dimen/_250sdp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"


            android:background="@drawable/layout_gray_without_top_corners"

            android:paddingTop="@dimen/_8sdp"
            android:paddingBottom="@dimen/_8sdp">

            <TextView
                android:id="@+id/title_TextView_ConfirmationDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_weight="2"
                android:fontFamily="@font/droid_arabic_kufi"
                android:includeFontPadding="false"
                android:textColor="#343434"
                android:textSize="@dimen/_15sdp"
                tools:text="سبب الغياب" />

            <ImageView
                android:id="@+id/icon_ImageView_ConfirmationDialog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                tools:srcCompat="@android:drawable/ic_menu_close_clear_cancel" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/_5sdp"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_5sdp"
            android:layout_marginBottom="@dimen/_16sdp"
            android:gravity="center"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/seconds"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textseconds"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/seconds"
                        android:textSize="@dimen/_15sdp" />

                    <NumberPicker
                        android:id="@+id/picksecond"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:theme="@style/AppTheme.Picker" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/minuts"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textminuts"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/minutes"
                        android:textSize="@dimen/_15sdp" />

                    <NumberPicker
                        android:id="@+id/pickminuts"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:theme="@style/AppTheme.Picker" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/hour"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/texthour"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/hours"
                        android:textSize="@dimen/_15sdp" />

                    <NumberPicker
                        android:id="@+id/pickhour"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:theme="@style/AppTheme.Picker" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center">

                <Button
                    android:id="@+id/confirm_Button_ConfirmationDialog"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/_5sdp"
                    android:layout_weight="1"
                    android:background="@drawable/button_green_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:text="@string/ok"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_14sdp" />

                <Button
                    android:id="@+id/cancel_Button_ConfirmationDialog"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/_30sdp"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/_5sdp"
                    android:layout_weight="1"
                    android:background="@drawable/button_red_without_corners_shape"
                    android:fontFamily="@font/droid_arabic_kufi"
                    android:gravity="center"
                    android:text="@string/close"
                    android:textAlignment="center"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_14sdp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</androidx.cardview.widget.CardView>

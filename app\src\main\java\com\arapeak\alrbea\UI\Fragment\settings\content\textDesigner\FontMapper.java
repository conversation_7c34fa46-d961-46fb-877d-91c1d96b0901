package com.arapeak.alrbea.UI.Fragment.settings.content.textDesigner;

import com.arapeak.alrbea.R;
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignFontEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FontMapper {
    private final int defaultFont = R.font.droid_arabic_kufi;
    Map<TextDesignFontEnum, Integer> mapper = new HashMap<>();

    public FontMapper() {
        mapper.put(TextDesignFontEnum.Jassmin, R.font.jassmin_typo);
        mapper.put(TextDesignFontEnum.Alkalami, R.font.alkalami);
        mapper.put(TextDesignFontEnum.Amiri, R.font.amiri_quran);
        mapper.put(TextDesignFontEnum.Haramain, R.font.arfonts_ae_haramain);
        mapper.put(TextDesignFontEnum.Hebah, R.font.arfonts_hebah);

        mapper.put(TextDesignFontEnum.O<PERSON>rah, R.font.arfonts_ostorah);
        mapper.put(TextDesignFontEnum.Bein, R.font.bein_font);
        mapper.put(TextDesignFontEnum.Dinnext, R.font.dinnext_medium);
        mapper.put(TextDesignFontEnum.Kufi, R.font.droid_arabic_kufi);

        mapper.put(TextDesignFontEnum.Dthuluth, R.font.dthuluth_ii);
        mapper.put(TextDesignFontEnum.Frutiger, R.font.frutiger_bold);
        mapper.put(TextDesignFontEnum.Gulzar, R.font.gulzar);
        mapper.put(TextDesignFontEnum.Hasn, R.font.hsn);

        mapper.put(TextDesignFontEnum.Jenine, R.font.jenine);
        mapper.put(TextDesignFontEnum.NotoFufi, R.font.noto_kufi_arabic_regular2);
        mapper.put(TextDesignFontEnum.Qatar, R.font.qatar_2022_bold);
        mapper.put(TextDesignFontEnum.Ruqaa, R.font.qref_ruqaa_ink);

        mapper.put(TextDesignFontEnum.ReemKufi, R.font.reem_kufi);
        mapper.put(TextDesignFontEnum.Teshrin, R.font.teshrin_bold_2);
    }


    public int getFontRes(int font) {
        return getFontRes(TextDesignFontEnum.getEntries().get(font));
    }

    public List<Integer> getFontResList() {
        List<Integer> fontList = new ArrayList<Integer>();
        for (TextDesignFontEnum font : TextDesignFontEnum.getEntries()) {
            fontList.add(getFontRes(font));
        }
        return fontList;
    }


    public int getFontRes(TextDesignFontEnum font) {
        if (mapper.containsKey(font)) {
            return mapper.get(font);
        } else {
            return defaultFont;
        }
    }
}

package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_azan.azan_lib

/**
 * This class is an enumeration of possible prayer and other
 * useful events.
 *
 */
class PrayerTime {
    companion object {
        /**
         * Fajr time
         */
        val FAJR = PrayerTime()

        /**
         * Shurooq time
         */
        val SHUROOQ = PrayerTime()

        /**
         * Thuhr time
         */
        val THUHR = PrayerTime()

        /**
         * Assr time
         */
        val ASSR = PrayerTime()

        /**
         * Maghrib time
         */
        val MAGHRIB = PrayerTime()

        /**
         * Ishaa time
         */
        val ISHAA = PrayerTime()

        /**
         * Imsaak time
         */
        val IMSAAK = PrayerTime()

        /**
         * Next fajr time
         */
        val NEXTFAJR = PrayerTime()
    }
}

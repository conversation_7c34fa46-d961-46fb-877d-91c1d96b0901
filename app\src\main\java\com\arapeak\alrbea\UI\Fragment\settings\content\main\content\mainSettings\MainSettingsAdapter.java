package com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.CustomView.SpinnerAdapter;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.wdullaer.materialdatetimepicker.time.TimePickerDialog;

import java.util.List;

public class MainSettingsAdapter extends RecyclerView.Adapter<MainSettingsAdapter.MainSettingsHolder> {

    public static final String TAG = "PhotoGalleryAdapter";


    private final Context context;
    private final List<SubSettingAlrabeeaTimes> arrayListItem;
    private final LayoutInflater layoutInflater;
    private final SettingsAdapterCallback mCallback;
    private final boolean isShowSpace;

    public MainSettingsAdapter(Context context
            , List<SubSettingAlrabeeaTimes> arrayListItem
            , SettingsAdapterCallback mCallback) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSpace = true;

        layoutInflater = LayoutInflater.from(this.context);
    }

    public MainSettingsAdapter(Context context
            , List<SubSettingAlrabeeaTimes> arrayListItem
            , SettingsAdapterCallback mCallback
            , boolean isShowSpace) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSpace = isShowSpace;

        layoutInflater = LayoutInflater.from(this.context);
    }

    public MainSettingsAdapter(Context context
            , List<SubSettingAlrabeeaTimes> arrayListItem
            , SettingsAdapterCallback mCallback
            , Integer positionDefaultValue) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSpace = true;

        layoutInflater = LayoutInflater.from(this.context);
    }

    public MainSettingsAdapter(Context context
            , List<SubSettingAlrabeeaTimes> arrayListItem
            , SettingsAdapterCallback mCallback
            , String defaultValueString
            , int defaultValueInteger) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSpace = true;

        layoutInflater = LayoutInflater.from(this.context);
    }

    @NonNull
    @Override
    public MainSettingsHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        View view = layoutInflater.inflate(R.layout.layout_list_item_sub_setting, parent, false);

        MainSettingsHolder viewHolder = new MainSettingsHolder(view);

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull MainSettingsHolder savedVoiceViewHolder, int position) {
        savedVoiceViewHolder.onBind(position);
    }

    @Override
    public int getItemCount() {
//        return arrayListItem.size();
        return arrayListItem.size();
    }


    public void add(SubSettingAlrabeeaTimes item) {
        if (item == null)
            return;

        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.add(item);
        notifyItemInserted(lastItemIndex);
//        notifyDataSetChanged();
    }

    public void addAll(List<SubSettingAlrabeeaTimes> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0)
            return;

        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
//        notifyDataSetChanged();
    }

    public void remove(int position) {
        if (position < 0 || position >= getItemCount()) {
            return;
        }
        this.arrayListItem.remove(position);
        notifyItemRemoved(position);
//        notifyDataSetChanged();
    }

    public void removeLastItem() {
        int lastItemIndex = this.arrayListItem.size() - 1;
        if (lastItemIndex < 0) {
            return;
        }
        this.arrayListItem.remove(lastItemIndex);
        notifyItemRemoved(lastItemIndex);
//        notifyDataSetChanged();
    }

    public SubSettingAlrabeeaTimes getItem(int position) {
        if (position < 0 || position >= getItemCount())
            return null;

        return arrayListItem.get(position);
    }

    public SubSettingAlrabeeaTimes getByTag(Object tag) {
        for (SubSettingAlrabeeaTimes item : arrayListItem) {
            if (item.getTag() == tag)
                return item;
        }
        return null;
    }

    public int getPositionByTag(Object tag) {
        for (int i = 0; i < arrayListItem.size(); i++) {
            if (arrayListItem.get(i).getTag() == tag) return i;
        }
        return -1;
    }

    public void notifyItemChangedByTag(Object tag) {
        for (int i = 0; i < arrayListItem.size(); i++)
            if (arrayListItem.get(i).getTag() == tag)
                notifyItemChanged(i);
    }

    public void clear() {
        arrayListItem.clear();
        notifyDataSetChanged();
    }

    public SubSettingAlrabeeaTimes getLastItem() {
        if (getItemCount() == 0)
            return null;

        return arrayListItem.get(arrayListItem.size() - 1);
    }

    public void setItem(int position, SubSettingAlrabeeaTimes subSettingAlrabeeaTimes) {
        if (position < 0 || position >= getItemCount() || subSettingAlrabeeaTimes == null)
            return;

        arrayListItem.set(position, subSettingAlrabeeaTimes);
        notifyItemChanged(position);
    }

    class MainSettingsHolder extends RecyclerView.ViewHolder {
        private static final int PERMISSION_CODE = 103;
        private final TextView titleTextView;
        private final TextView descriptionTextView;
        private final TextView optionTextView;
        private final TextView editPrayerTimeTextView;
        private final TextView addTimeTextView;
        private final RadioGroup optionsRadioGroup;
        private final RadioButton option1AppCompatRadioButton;
        private final RadioButton option2AppCompatRadioButton;
        private final RadioButton option3AppCompatRadioButton;
        private final RadioButton option4AppCompatRadioButton;
        private final Spinner optionsSpinner;
        private final LinearLayout optionsLinearLayout;
        private final LinearLayout viewsLinearLayout;
        private final LinearLayout addMinusNumberLayout;
        private final Switch optionSwitch;
        private final CheckBox optionCheckBox;
        private final Button addPrayerTimeButton;
        private final Button minusPrayerTimeButton;
        private final Button iconsetting;
        private final View spaceView;
        private final Button addButton;
        private final CheckBox isEnableCheckBox;
        private final ViewGroup constraintLayout;
        public int currentSelectedIndex = -1;
        private Dialog loadingDialog;
        private TimePickerDialog timePickerDialog;

        public MainSettingsHolder(@NonNull View itemView) {
            super(itemView);

            titleTextView = itemView.findViewById(R.id.title_TextView_SubSettingsHolder);
            descriptionTextView = itemView.findViewById(R.id.description_TextView_SubSettingsHolder);
            optionsRadioGroup = itemView.findViewById(R.id.options_RadioGroup_SubSettingsHolder);
            option1AppCompatRadioButton = itemView.findViewById(R.id.option1_AppCompatRadioButton_SubSettingsHolder);
            option2AppCompatRadioButton = itemView.findViewById(R.id.option2_AppCompatRadioButton_SubSettingsHolder);
            option3AppCompatRadioButton = itemView.findViewById(R.id.option3_AppCompatRadioButton_SubSettingsHolder);
            option4AppCompatRadioButton = itemView.findViewById(R.id.option4_AppCompatRadioButton_SubSettingsHolder);
            optionsSpinner = itemView.findViewById(R.id.options_Spinner_SubSettingsHolder);
            optionsLinearLayout = itemView.findViewById(R.id.options_LinearLayout_SubSettingsHolder);
            optionSwitch = itemView.findViewById(R.id.option_Switch_SubSettingsHolder);
            optionTextView = itemView.findViewById(R.id.option_TextView_SubSettingsHolder);
            optionCheckBox = itemView.findViewById(R.id.option_CheckBox_SubSettingsHolder);
            viewsLinearLayout = itemView.findViewById(R.id.views_LinearLayout_SubSettingsHolder);
            addButton = itemView.findViewById(R.id.add_Button_SubSettingsHolder);
            isEnableCheckBox = itemView.findViewById(R.id.isEnable_CheckBox_SubSettingsHolder);
            iconsetting = itemView.findViewById(R.id.addPrayerTime_Button_SubSettingsHolderSetting);

            addTimeTextView = itemView.findViewById(R.id.addTime_TextView_SubSettingsHolder);
            addMinusNumberLayout = itemView.findViewById(R.id.addMinusNumber_LinearLayout_SubSettingsHolder);
            addPrayerTimeButton = itemView.findViewById(R.id.addPrayerTime_Button_SubSettingsHolder);
            editPrayerTimeTextView = itemView.findViewById(R.id.editPrayerTime_TextView_SubSettingsHolder);
            minusPrayerTimeButton = itemView.findViewById(R.id.minusPrayerTime_Button_SubSettingsHolder);
            constraintLayout = itemView.findViewById(R.id.constraintLayoutall);

            spaceView = itemView.findViewById(R.id.space_View_SubSettingsHolder);

        }

        public void onBind(final int position) {
            if (position + 1 >= getItemCount() || !isShowSpace) {
                spaceView.setVisibility(View.GONE);
            } else {
                spaceView.setVisibility(View.VISIBLE);
            }

            final SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = arrayListItem.get(position);

            titleTextView.setText(subSettingAlrabeeaTimes.getTitle());

            optionsRadioGroup.setVisibility(View.GONE);
            optionsSpinner.setVisibility(View.GONE);
            optionsLinearLayout.setVisibility(View.GONE);
            optionCheckBox.setVisibility(View.GONE);
            addMinusNumberLayout.setVisibility(View.GONE);
            addButton.setVisibility(View.GONE);
            addTimeTextView.setVisibility(View.GONE);

            isEnableCheckBox.setOnClickListener(i -> {
                if (mCallback != null) {
                    mCallback.onItemClick(ViewsAlrabeeaTimes.ENABLE_BUTTON, position, isEnableCheckBox.isChecked() ? 1 : 0, TAG);
                }
            });


            if (subSettingAlrabeeaTimes.isShowEnableButton()) {
                isEnableCheckBox.setChecked(subSettingAlrabeeaTimes.isEnable());
                isEnableCheckBox.setVisibility(View.VISIBLE);

                if (subSettingAlrabeeaTimes.isEnable()) {
                    viewsLinearLayout.setVisibility(View.VISIBLE);
                    if (subSettingAlrabeeaTimes.getDescription().isEmpty()) {
                        descriptionTextView.setVisibility(View.GONE);
                    } else {
                        descriptionTextView.setVisibility(View.VISIBLE);
                    }
                } else {
                    viewsLinearLayout.setVisibility(View.GONE);
                    descriptionTextView.setVisibility(View.GONE);
                }
            } else {
                isEnableCheckBox.setVisibility(View.GONE);
                if (subSettingAlrabeeaTimes.getDescription().isEmpty()) {
                    descriptionTextView.setVisibility(View.GONE);
                } else {
                    descriptionTextView.setVisibility(View.VISIBLE);
                }
            }

//            spaceView.setVisibility(subSettingAlrabeeaTimes.isShowSpace() ? View.VISIBLE : View.GONE);

            if (subSettingAlrabeeaTimes.isPray()) {
                if (subSettingAlrabeeaTimes.isEnabled()) {
                    constraintLayout.setVisibility(View.VISIBLE);
                    iconsetting.setVisibility(View.VISIBLE);

                } else {
                    constraintLayout.setVisibility(View.GONE);
                    iconsetting.setVisibility(View.GONE);
                }
                if (position == 1 || position == 3 || position == 4 || position == 5 || position == 6 || position == 7)
                    iconsetting.setVisibility(View.GONE);
            }
            iconsetting.setOnClickListener(v -> {
                subSettingAlrabeeaTimes.setisShowSpace(false);
                if (!arrayListItem.get(position + 1).isShowSpace()) {
                    arrayListItem.get(position + 2).setEnabled(true);
                    notifyItemChanged(position + 2);
                }

                arrayListItem.get(position + 1).setEnabled(true);
                notifyItemChanged(position);
                notifyItemChanged(position + 1);
            });

            switch (subSettingAlrabeeaTimes.getViewsAlrabeeaTimes()) {
                case TEXT_VIEW:

//                    if (subSettingAlrabeeaTimes.isEnable()) {
//                        descriptionTextView.setVisibility(View.VISIBLE);
//                    } else {
//                        descriptionTextView.setVisibility(View.GONE);
//                    }

                    titleTextView.setText(subSettingAlrabeeaTimes.getTitle());
                    descriptionTextView.setText(subSettingAlrabeeaTimes.getDescription());
                    if (subSettingAlrabeeaTimes.isEnableCallback()) {
                        itemView.setOnClickListener(v -> {
                            if (mCallback != null) {
                                mCallback.onItemClick(ViewsAlrabeeaTimes.TEXT_VIEW, position, position, TAG);
                            }
                        });
                    }
                    break;
                case RADIO_BUTTON:
                    List<String> descriptionList = subSettingAlrabeeaTimes.getDescriptionList();
                    int size = descriptionList.size();
                    if (size == 0)
                        return;

                    optionsRadioGroup.setVisibility(View.VISIBLE);

                    option1AppCompatRadioButton.setVisibility(View.GONE);
                    option2AppCompatRadioButton.setVisibility(View.GONE);
                    option3AppCompatRadioButton.setVisibility(View.GONE);
                    option4AppCompatRadioButton.setVisibility(View.GONE);


                    option1AppCompatRadioButton.setText(descriptionList.get(0));
                    option1AppCompatRadioButton.setVisibility(View.VISIBLE);

                    if (size > 1) {
                        option2AppCompatRadioButton.setText(descriptionList.get(1));
                        option2AppCompatRadioButton.setVisibility(View.VISIBLE);
                    }
                    if (size > 2) {
                        option3AppCompatRadioButton.setText(descriptionList.get(2));
                        option3AppCompatRadioButton.setVisibility(View.VISIBLE);
                    }

                    if (size > 3) {
                        option4AppCompatRadioButton.setText(descriptionList.get(3));
                        option4AppCompatRadioButton.setVisibility(View.VISIBLE);
                    }

                    if (subSettingAlrabeeaTimes.getPositionCheck() == 3) {
                        option4AppCompatRadioButton.setChecked(true);
                    } else if (subSettingAlrabeeaTimes.getPositionCheck() == 2) {
                        option3AppCompatRadioButton.setChecked(true);
                    } else if (subSettingAlrabeeaTimes.getPositionCheck() == 1) {
                        option2AppCompatRadioButton.setChecked(true);
                    } else if (subSettingAlrabeeaTimes.getPositionCheck() == 0) {
                        option1AppCompatRadioButton.setChecked(true);
                    }
                    if (mCallback != null) {
                        optionsRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
                            int checkedPosition = 0;
                            if (option2AppCompatRadioButton.isChecked())
                                checkedPosition = 1;
                            if (option3AppCompatRadioButton.isChecked())
                                checkedPosition = 2;
                            if (option4AppCompatRadioButton.isChecked())
                                checkedPosition = 3;
                            mCallback.onItemClick(ViewsAlrabeeaTimes.RADIO_BUTTON, position,
                                    checkedPosition, subSettingAlrabeeaTimes.getTitle());
                        });
                    }
                    break;
                case SPINNER:
                    optionsSpinner.setVisibility(View.VISIBLE);
                    SpinnerAdapter<Object> spinnerAdapter = new SpinnerAdapter<>(context, subSettingAlrabeeaTimes.getObjectList());
                    optionsSpinner.setAdapter(spinnerAdapter);
                    int selection = subSettingAlrabeeaTimes.getPositionDefaultValue();
                    optionsSpinner.setSelection(selection);
                    optionsSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                        @Override
                        public void onItemSelected(AdapterView<?> parent, View view, final int subPosition, long id) {
                            if (!(mCallback == null || currentSelectedIndex < 0 || subPosition == currentSelectedIndex)) {
                                mCallback.onItemClick(ViewsAlrabeeaTimes.SPINNER, position, subPosition, subSettingAlrabeeaTimes.getTitle());
                            }
                            currentSelectedIndex = subPosition;

                        }

                        @Override
                        public void onNothingSelected(AdapterView<?> parent) {

                        }
                    });
                    currentSelectedIndex = optionsSpinner.getSelectedItemPosition();

                    break;
                case SWITCH:
                    optionsLinearLayout.setVisibility(View.VISIBLE);

                    optionTextView.setText(subSettingAlrabeeaTimes.getDescription());

                    optionSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                        /*if((isChecked?1:0) == currentSelectedIndex)
                            return;
                        currentSelectedIndex = isChecked?1:0;*/
                        if (mCallback != null) {
                            mCallback.onItemClick(ViewsAlrabeeaTimes.SWITCH,
                                    position, isChecked ? 1 : 0, subSettingAlrabeeaTimes.getTitle());
                        }
                    });
                    break;
                case CHECK_BOX:
                    optionCheckBox.setVisibility(View.VISIBLE);

                    String description = subSettingAlrabeeaTimes.getDescription();

                    optionCheckBox.setText(description);

                    optionCheckBox.setOnCheckedChangeListener((buttonView, isChecked) -> {

                        if (isChecked != subSettingAlrabeeaTimes.isChecked()) {
                            if (mCallback != null) {
                                mCallback.onItemClick(ViewsAlrabeeaTimes.CHECK_BOX,
                                        position, isChecked ? 1 : 0, subSettingAlrabeeaTimes.getTitle());
                            }
                            subSettingAlrabeeaTimes.setChecked(isChecked);
                            arrayListItem.get(position).setChecked(isChecked);
                        }
                    });

                    optionCheckBox.setChecked(subSettingAlrabeeaTimes.isChecked());

                    break;

                case ADD_MINUS_NUMBER:
                    addMinusNumberLayout.setVisibility(View.VISIBLE);

                    final int[] numberPrayerTime = {subSettingAlrabeeaTimes.getPositionDefaultValue()};

                    titleTextView.setText(subSettingAlrabeeaTimes.getTitle());
                    descriptionTextView.setText(subSettingAlrabeeaTimes.getDescription());
                    editPrayerTimeTextView.setText(String.valueOf(numberPrayerTime[0]));

//                    if (subSettingAlrabeeaTimes.getDescription().isEmpty()) {
//                        descriptionTextView.setVisibility(View.GONE);
//                    } else {
//                        descriptionTextView.setVisibility(View.VISIBLE);
//                    }

                    addPrayerTimeButton.setOnClickListener(v -> {
                        ++numberPrayerTime[0];
                        editPrayerTimeTextView.setText(String.valueOf(numberPrayerTime[0]));

                        if (mCallback != null) {
                            mCallback.onItemClick(ViewsAlrabeeaTimes.ADD_MINUS_NUMBER,
                                    position
                                    , numberPrayerTime[0]
                                    , subSettingAlrabeeaTimes.getTitle());
                        }
                    });

                    minusPrayerTimeButton.setOnClickListener(v -> {
                        --numberPrayerTime[0];
                        if (!subSettingAlrabeeaTimes.isMinus() && numberPrayerTime[0] < 0) {
                            numberPrayerTime[0] = 0;
                        }
                        editPrayerTimeTextView.setText(String.valueOf(numberPrayerTime[0]));

                        if (mCallback != null) {
                            mCallback.onItemClick(ViewsAlrabeeaTimes.ADD_MINUS_NUMBER,
                                    position
                                    , numberPrayerTime[0]
                                    , subSettingAlrabeeaTimes.getTitle());
                        }
                    });
                    break;

                case TIME_PICKER:
                    addTimeTextView.setVisibility(View.VISIBLE);

                    final String[] timePrayerTime = {subSettingAlrabeeaTimes.getPositionDefaultValueString()};

                    titleTextView.setText(subSettingAlrabeeaTimes.getTitle());
                    descriptionTextView.setText(subSettingAlrabeeaTimes.getDescription());
//                    addTimeTextView.setText(String.valueOf(timePrayerTime[0]));
                    addTimeTextView.setText(Utils.getTimeWith12(timePrayerTime[0]));
                    addTimeTextView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            int hour = 0;
                            int minute = 0;
                            String timeString = Utils.getTimeWith24(addTimeTextView.getText().toString());
                            if (timeString.split(":").length > 0) {
                                try {
                                    hour = Integer.parseInt(timeString.split(":")[0]);
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);
                                }
                                try {
                                    minute = Integer.parseInt(timeString.split(":")[1]);
                                } catch (Exception e) {
                                    CrashlyticsUtils.INSTANCE.logException(e);

                                }
                            }
                            timePickerDialog = TimePickerDialog
                                    .newInstance((view, hourOfDay, minute1, second) -> {
                                                timePickerDialog.dismiss();
                                                subSettingAlrabeeaTimes
                                                        .setPositionDefaultValueString(
                                                                (hourOfDay < 10 ? "0" + hourOfDay : hourOfDay)
                                                                        + ":" + (minute1 < 10 ? "0" + minute1 : minute1));
                                                addTimeTextView.setText(Utils.getTimeWith12(subSettingAlrabeeaTimes.getPositionDefaultValueString()));
                                                arrayListItem.get(position).setPositionDefaultValueString(subSettingAlrabeeaTimes.getPositionDefaultValueString());
                                                if (mCallback != null) {
                                                    mCallback.onItemClick(ViewsAlrabeeaTimes.TIME_PICKER,
                                                            position
                                                            , position
                                                            , subSettingAlrabeeaTimes.getTitle());
                                                }
                                            }, hour
                                            ,
                                            minute
                                            , false);

                            timePickerDialog.show(((AppCompatActivity) context).getFragmentManager(), context.getString(R.string.time_of_ikama));
                        }
                    });

                    break;
                case BUTTON:
                    descriptionTextView.setVisibility(View.GONE);
                    addButton.setVisibility(View.VISIBLE);
                    titleTextView.setText(subSettingAlrabeeaTimes.getTitle());
//                    descriptionTextView.setText(subSettingAlrabeeaTimes.getDescription());
                    addButton.setText(subSettingAlrabeeaTimes.getDescription());
                    addButton.setOnClickListener(v -> {
                        if (mCallback != null) {
                            mCallback.onItemClick(ViewsAlrabeeaTimes.BUTTON
                                    , position
                                    , 0
                                    , subSettingAlrabeeaTimes.getTitle());
                        }
                    });
                    break;

            }

        }
    }
}
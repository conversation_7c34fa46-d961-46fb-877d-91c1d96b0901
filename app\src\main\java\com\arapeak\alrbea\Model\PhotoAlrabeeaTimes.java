package com.arapeak.alrbea.Model;

import android.os.Parcel;
import android.os.Parcelable;

import com.arapeak.alrbea.Utils;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class PhotoAlrabeeaTimes extends RealmObject implements Parcelable {

    public static final Creator<PhotoAlrabeeaTimes> CREATOR = new Creator<PhotoAlrabeeaTimes>() {
        @Override
        public PhotoAlrabeeaTimes createFromParcel(Parcel in) {
            return new PhotoAlrabeeaTimes(in);
        }

        @Override
        public PhotoAlrabeeaTimes[] newArray(int size) {
            return new PhotoAlrabeeaTimes[size];
        }
    };
    private static final String TAG = "PhotoAlrabeeaTimes";
    private String name;
    @PrimaryKey
    private long id;
    private String imageUrl, imageBase64;
    private int type;

    public PhotoAlrabeeaTimes() {
    }

    protected PhotoAlrabeeaTimes(Parcel in) {
        name = in.readString();
        id = in.readLong();
        imageUrl = in.readString();
        imageBase64 = in.readString();
        type = in.readInt();
    }

    public static String getTAG() {
        return TAG;
    }

    public String getName() {
        return Utils.getValueWithoutNull(name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getType() {
        return type;
    }

    public void setType(int name) {
        this.type = name;
    }

    public String getImageUrl() {
        return Utils.getValueWithoutNull(imageUrl);
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageBase64() {
        return Utils.getValueWithoutNull(imageBase64);
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    @Override
    public String toString() {
        return "PhotoAlrabeeaTimes{" +
                "name='" + name + '\'' +
                ", id=" + id +
                ", imageUrl='" + imageUrl + '\'' +
                ", imageBase64='" + imageBase64 + '\'' +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeLong(id);
        dest.writeString(imageUrl);
        dest.writeString(imageBase64);
    }
}

package com.arapeak.alrbea.database;

import static com.arapeak.alrbea.AppController.baseContext;

import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

public class OmanCitiesDb extends SQLiteOpenHelper {

    private static final int DATABASE_VERSION = 4;
    private static final String DATABASE_NAME = "oman.db";
    public static final String DB_SUB_PATH = "/databases/" + DATABASE_NAME;
    private String APP_DATA_PATH = "";
    private SQLiteDatabase dataBase;

    public OmanCitiesDb() {
        super(baseContext, DATABASE_NAME, null, DATABASE_VERSION);
        APP_DATA_PATH = baseContext.getApplicationInfo().dataDir;
    }

    public static File GetDBFolder() {
        String appDataPath = baseContext.getApplicationInfo().dataDir;
        return new File(appDataPath + "/databases");
    }

    public static File GetDBFile() {
        return new File(GetDBFolder(), "oman.db");
//        String appDataPath = baseContext.getApplicationInfo().dataDir;
//        return new File(appDataPath + "/databases/oman.db");
    }

    public static void CopyDBFromAssets() {
        File dbFile = GetDBFile();
        if (dbFile.exists()) dbFile.delete();

        File dbFolder = GetDBFolder();//Make sure the /databases folder exists
        dbFolder.mkdir();//This can be called multiple times.

        try {
            InputStream inputStream = baseContext.getAssets().open("oman.db");
            OutputStream outputStream = new FileOutputStream(GetDBFile());
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            outputStream.flush();
            outputStream.close();
            inputStream.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
    }

    public static void init() {
        File dbFile = GetDBFile();
        if (dbFile.exists())
            return;
        CopyDBFromAssets();

    }

    public static List<OmanCity> getAllCities() {
        List<OmanCity> result = new ArrayList<>();
        try (OmanCitiesDb db = new OmanCitiesDb()) {
            db.openDataBase();
            db.close();
            SQLiteDatabase database = db.getReadableDatabase();
            String query = "SELECT * FROM cities";
            Cursor cursor = database.rawQuery(query, null);
            if (cursor.moveToFirst()) {
                do {
                    OmanCity city = new OmanCity();
                    city.city = cursor.getString(1);
                    city.cityname = cursor.getString(2);
                    city.lat1 = cursor.getDouble(3);
                    city.lat2 = cursor.getDouble(4);
                    city.long1 = cursor.getDouble(5);
                    city.long2 = cursor.getDouble(6);
                    result.add(city);
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }
        return result;
    }

    public static List<OmanCity> getAllCities(String filter) {
        List<OmanCity> result = new ArrayList<>();
        List<OmanCity> cities = getAllCities();
        for (OmanCity city : cities) {
            if (city.cityname.contains(filter) || city.city.contains(filter))
                result.add(city);
        }
        return result;
    }

    public boolean openDataBase() throws SQLException {
        String mPath = APP_DATA_PATH + DB_SUB_PATH;
        //Note that this method assumes that the db file is already copied in place
        dataBase = SQLiteDatabase.openDatabase(mPath, null, SQLiteDatabase.OPEN_READWRITE);
        return dataBase != null;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {

    }

    @Override
    public synchronized void close() {
        if (dataBase != null) {
            dataBase.close();
        }
        super.close();
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
    }
}

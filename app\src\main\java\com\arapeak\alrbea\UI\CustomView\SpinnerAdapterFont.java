package com.arapeak.alrbea.UI.CustomView;


import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.arapeak.alrbea.Model.PrayerSystemsSchools;
import com.arapeak.alrbea.R;

import java.util.List;
import java.util.Objects;

public class SpinnerAdapterFont<T> extends ArrayAdapter {

    private final List<T> listItem;
    private final List<Integer> fonts;
    private final int layoutResourcesId;

    public SpinnerAdapterFont(@NonNull Context context, List<T> listItem, List<Integer> fonts) {
        super(context, R.layout.layout_list_item_spinner_text, listItem);
        this.layoutResourcesId = R.layout.layout_list_item_spinner_text;
        this.listItem = listItem;
        this.fonts = fonts;
    }

    public SpinnerAdapterFont(@NonNull Context context, int layoutResourcesId, List<T> listItem, List<Integer> fonts) {
        super(context, layoutResourcesId, listItem);
        this.layoutResourcesId = layoutResourcesId;
        this.listItem = listItem;
        this.fonts = fonts;
    }

    @Override
    public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        return getCustomView(position, convertView, parent, true);
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        View view = getCustomView(position, convertView, parent, false);

        return new View(getContext());
    }

    public View getCustomView(int position, View convertView, ViewGroup parent, boolean isHideImageView) {

        LayoutInflater inflater = (LayoutInflater) getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);

        View listItemView = convertView;
        if (listItemView == null) {
            listItemView = Objects.requireNonNull(inflater).inflate(R.layout.layout_list_item_spinner_text, parent, false);
        }

        ImageView arrowImage = listItemView.findViewById(R.id.arrow_image_ImageView_SpinnerAdapter);
        TextView title = listItemView.findViewById(R.id.title_TextView_SpinnerAdapter);
        ImageView icon = listItemView.findViewById(R.id.icon_ImageView_SpinnerAdapter);


        if (getItem(position) instanceof PrayerSystemsSchools) {
            PrayerSystemsSchools prayerSystemsSchools = (PrayerSystemsSchools) listItem.get(position);
            title.setText(prayerSystemsSchools.getName());
        } else {
            title.setText(String.valueOf(listItem.get(position)));
            if (fonts != null && fonts.size() > position) {
                title.setTypeface(ResourcesCompat.getFont(getContext(), fonts.get(position)));
            }
        }

        if (isHideImageView) {
            arrowImage.setVisibility(View.GONE);
        } else {
            arrowImage.setVisibility(View.VISIBLE);
        }


        return listItemView;
    }

    public int indexOf(T object) {
        if (object == null) {
            return -1;
        }
        return listItem.indexOf(object);
    }

    public int getPositionOfItem(String prayerTag, List<String> prayerTagList) {
        if (prayerTag == null || prayerTagList == null || prayerTagList.size() == 0) {
            return -1;
        }
        for (int i = 0; i < prayerTagList.size(); ++i) {
            if (prayerTag.equals(prayerTagList.get(i))) {
                return i;
            }
        }
        return -1;
    }

    public void add(int index, T object) {
        if (index < 0 || index > listItem.size()) {
            return;
        }
        listItem.add(index, object);
        notifyDataSetChanged();
    }

    public void remove(int index) {
        if (index < 0 || index > listItem.size()) {
            return;
        }
        listItem.remove(index);
        notifyDataSetChanged();
    }
}
<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content_CoordinatorLayout_SettingsActivity"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".UI.Activity.SettingsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"
        android:theme="@style/AppTheme.AppBarOverlay"
        android:visibility="gone"
        app:elevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#f1f5f9"
            android:layoutDirection="ltr">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="#f1f5f9"
                android:minHeight="?attr/actionBarSize"
                android:theme="?attr/actionBarTheme"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:navigationIcon="@drawable/ic_back_arrow"
                app:title='  ' />

            <TextView
                android:id="@+id/textView"
                android:layout_width="0dp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginEnd="25dp"
                android:text="Traveling"
                android:textColor="#0E0E0E"
                android:textSize="@dimen/_28sdp"
                app:fontFamily="@font/droid_arabic_kufi_bold"
                app:layout_constrainedHeight="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageView"
                app:layout_constraintTop_toBottomOf="@+id/toolbar" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:text="Start a new Journey"
                android:textColor="#9E9A9A"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imageView"
                app:layout_constraintTop_toBottomOf="@+id/textView" />

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_marginStart="30dp"
                android:visibility="gone"
                app:layout_constraintEnd_toStartOf="@+id/textView"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/toolbar"
                app:srcCompat="@drawable/setting"
                app:tint="@color/colorPrimary" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <include
        android:id="@+id/container_include_SettingsActivity"
        layout="@layout/activity_settings" />


</androidx.coordinatorlayout.widget.CoordinatorLayout>
package com.arapeak.alrbea.APIs;

import android.content.Context;
import android.util.Log;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.RetryPolicy;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.androidnetworking.AndroidNetworking;
import com.androidnetworking.common.Priority;
import com.androidnetworking.error.ANError;
import com.androidnetworking.interfaces.DownloadListener;
import com.androidnetworking.interfaces.DownloadProgressListener;
import com.androidnetworking.interfaces.JSONObjectRequestListener;
import com.arapeak.alrbea.AppController;
import com.arapeak.alrbea.Enum.PrayerMethod;
import com.arapeak.alrbea.Interface.OnCompleteListener;
import com.arapeak.alrbea.Interface.OnProgress;
import com.arapeak.alrbea.Model.AppInfo;
import com.arapeak.alrbea.Model.InfoOfCode;
import com.arapeak.alrbea.Model.PrayFileInfo;
import com.arapeak.alrbea.Model.PrayerApi;
import com.arapeak.alrbea.Model.ResponseErrors;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.arapeak.alrbea.hawk.HawkSettings;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.downloader.Error;
import com.downloader.OnCancelListener;
import com.downloader.OnDownloadListener;
import com.downloader.OnPauseListener;
import com.downloader.OnProgressListener;
import com.downloader.OnStartOrResumeListener;
import com.downloader.PRDownloader;
import com.downloader.Progress;
import com.downloader.Status;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.orhanobut.hawk.Hawk;

import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;


public class AlrabeeaTimesRequests {

    public static final String TAG = "AlrabeeaTimesRequests";
    private static int downloadId;

    public static void checkAndUseActivationCode(final Context context
            , String url
            , final String activationCode
            , final String deviceId
            , final OnCompleteListener<InfoOfCode, String> OnCompleteListener) {

        if (context == null || Utils.getValueWithoutNull(url).isEmpty() || Utils.getValueWithoutNull(activationCode).isEmpty()
                || Utils.getValueWithoutNull(deviceId).isEmpty()) {
            return;
        }


        AndroidNetworking.post(url)
                .addBodyParameter("code", activationCode.trim())
                .addBodyParameter("device_id", deviceId.trim())
                .addHeaders("Accept", "application/json")
                .setPriority(Priority.HIGH)
                .build()
                .getAsJSONObject(new JSONObjectRequestListener() {
                    @Override
                    public void onResponse(JSONObject response) {
                        try {
                            String responseString = Utils.getValueWithoutNull(response.toString());

                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();
                            InfoOfCode infoOfCode = mGson.fromJson(responseString, InfoOfCode.class);
                            if (infoOfCode.getCode().isEmpty()) {
                                OnCompleteListener.onFail(infoOfCode.getMessage().isEmpty()
                                        ? context.getString(R.string.incorrect_activate_code)
                                        : infoOfCode.getMessage());

                            } else {
                                OnCompleteListener.onSuccess(infoOfCode);
                            }
                        } catch (Exception e) {
                            OnCompleteListener.onFail(context.getString(R.string.error_activatting));
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }

                    @Override
                    public void onError(ANError error) {

                        if ((error.getErrorCode() == 400 || error.getErrorCode() == 404)
                                && !Utils.getValueWithoutNull(error.getResponse().toString()).isEmpty()
                                && error.getErrorBody().contains("message")) {
                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();

                            InfoOfCode infoOfCode = mGson.fromJson(error.getErrorBody(), InfoOfCode.class);
                            OnCompleteListener.onFail(infoOfCode.getMessage());

                        } else {
                            OnCompleteListener.onFail(context.getString(R.string.incorrect_activate_code));
                        }
                        CrashlyticsUtils.INSTANCE.logException(error);
                    }
                });

    }

    public static void contactUs(final Context context
            , String url
            , final String name
            , final String email
            , final String phone
            , final String subject
            , final String text
            , final OnCompleteListener<String, Object> OnCompleteListener) {

        if (context == null
                || Utils.getValueWithoutNull(url).isEmpty()
                || Utils.getValueWithoutNull(name).isEmpty()
                || Utils.getValueWithoutNull(email).isEmpty()
                || Utils.getValueWithoutNull(phone).isEmpty()
                || Utils.getValueWithoutNull(subject).isEmpty()
                || Utils.getValueWithoutNull(text).isEmpty()) {
            Log.e(TAG, "contactUs There is A problem");
            return;
        }

        Log.e(TAG, "contactUs url: " + url);
        Log.e(TAG, "contactUs name: " + name);
        Log.e(TAG, "contactUs email: " + email);
        Log.e(TAG, "contactUs phone: " + phone);
        Log.e(TAG, "contactUs subject: " + subject);
        Log.e(TAG, "contactUs text: " + text);

        AndroidNetworking.post(url)
                .addBodyParameter("name", name.trim())
                .addBodyParameter("email", email.trim())
                .addBodyParameter("phone", phone.trim())
                .addBodyParameter("subject", subject.trim())
                .addBodyParameter("text", text.trim())
                .addHeaders("Accept", "application/json")
                .setPriority(Priority.HIGH)
                .build()
                .getAsJSONObject(new JSONObjectRequestListener() {
                    @Override
                    public void onResponse(JSONObject response) {
                        Log.d(TAG, "onResponse: " + response);
                        try {
                            String responseString = Utils.getValueWithoutNull(response.toString());

                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();
                            ResponseErrors responseErrors = mGson.fromJson(responseString, ResponseErrors.class);

                            OnCompleteListener.onSuccess(responseErrors.getMessage());
                        } catch (Exception e) {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }

                    @Override
                    public void onError(ANError error) {
                        Log.d(TAG, "contactUs onErrorResponse: " + error.getMessage()
                                + "\t" + error.getStackTrace() + "\t" + error.getErrorCode());

                        Log.d(TAG, "error.getResponse(): " + error.getResponse());
                        Log.d(TAG, "error.getErrorBody(): " + error.getErrorBody());
                        Log.d(TAG, "error.getErrorDetail(): " + error.getErrorDetail());

                        if ((error.getErrorCode() == 422)
                                && !Utils.getValueWithoutNull(error.getResponse().toString()).isEmpty()
                                && (error.getErrorBody().contains("errors") || error.getErrorBody().contains("message"))) {
                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();

                            ResponseErrors responseErrors = mGson.fromJson(error.getErrorBody(), ResponseErrors.class);
                            if (responseErrors.isErrorsNotNull()) {
                                OnCompleteListener.onFail(responseErrors.getErrors());
                            } else {
                                OnCompleteListener.onFail(responseErrors.getMessage());
                            }

                        } else {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                        }
                        CrashlyticsUtils.INSTANCE.logException(error);
                    }
                });
    }

    public static void getPrayerTimesWithDefault(String year
            , String url
            , final OnCompleteListener<PrayerApi, String> OnCompleteListener) {

        final double latitude = HawkSettings.getLatitude();
        final double longitude = HawkSettings.getLongitude();
        final PrayerMethod prayerMethod = HawkSettings.getCurrentPrayerMethod();
//        final PrayerSystemsSchools prayerSystemsSchools2 = Hawk.get(ConstantsOfApp.PRAYER_METHOD_KEY, null);
        final int adjustHijriDate = Hawk.get(ConstantsOfApp.ADJUST_HIJRI_DATE_KEY, 0);

//                , prayerSystemsSchools == null ? 4 : prayerSystemsSchools.getValue()
        getPrayerTimesThisYear(url
                , latitude
                , longitude
                , prayerMethod == null ? 0 : prayerMethod.value
                , adjustHijriDate
                , year
                , true
                , OnCompleteListener);
    }

    public static void getPrayerTimesThisYear(String url
            , double latitude
            , double longitude
            , int prayerSystemsSchoolsValue
            , int adjustHijriDate
            , String year
            , boolean isAnnual
            , final OnCompleteListener<PrayerApi, String> OnCompleteListener) {


        url += "?latitude=" + latitude + "&longitude=" + longitude
                + "&method=" + (prayerSystemsSchoolsValue/**/)
                + "&adjustment=" + adjustHijriDate
                + "&year=" + year
                + "&annual=" + isAnnual;

        Log.e(TAG, "getPrayerTimesThisYear url: " + url);
        Log.e(TAG, "getPrayerTimesThisYear latitude: " + latitude);
        Log.e(TAG, "getPrayerTimesThisYear longitude: " + longitude);
        Log.e(TAG, "getPrayerTimesThisYear prayerSystemsSchoolsValue: " + prayerSystemsSchoolsValue);
        Log.e(TAG, "getPrayerTimesThisYear adjustHijriDate: " + adjustHijriDate);
        Log.e(TAG, "getPrayerTimesThisYear year: " + year);
        Log.e(TAG, "getPrayerTimesThisYear isAnnual: " + isAnnual);

        if (Utils.getValueWithoutNull(url).isEmpty()) {
            Log.e(TAG, "getPrayerTimesThisYear There is A problem");
            return;
        }

        RequestQueue queue = Volley.newRequestQueue(AppController.baseContext);

        StringRequest stringRequest = new StringRequest(Request.Method.GET
                , url,
                response -> {
                    Log.d(TAG, "onResponse: " + response);
                    try {
                        GsonBuilder builder = new GsonBuilder();
                        Gson mGson = builder.create();
                        PrayerApi prayerApi
                                = mGson.fromJson(response, PrayerApi.class);
                        Log.d(TAG, "onResponse: " + response);
                        OnCompleteListener.onSuccess(prayerApi);
                    } catch (Exception e) {
                        OnCompleteListener.onFail(Utils.getString(R.string.incorrect_activate_code));
                        CrashlyticsUtils.INSTANCE.logException(e);
                    }
                }, error -> {
            Log.d(TAG, "getPrayerTimesThisYear onErrorResponse: " + error.getMessage()
                    + "\t" + error.getStackTrace() + "\t" + (error.networkResponse != null ? error.networkResponse.statusCode : -1));
            CrashlyticsUtils.INSTANCE.logException(error);

            OnCompleteListener.onFail(Utils.getString(R.string.try_again));

            Log.e(TAG, "Error: " + error.getMessage());

        }) {
           /* @Override
            protected Map<String, String> getParams() throws AuthFailureError {
                Map<String, String> params = new HashMap<>();

                params.put("latitude", "" + latitude);
                params.put("longitude", "" + longitude);
                params.put("method", "" + "" + (prayerSystemsSchools == null ? 4 : prayerSystemsSchools.getValue()));
                params.put("year", "" + year);
                params.put("annual", "" + true);

                return params;
            }*/

            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                Map<String, String> params = new HashMap<>();

//                params.put("Accept", "application/json");
                return params;
            }
        };

        int socketTimeout = 60000;//30 seconds - change to what you want
        RetryPolicy policy = new DefaultRetryPolicy(socketTimeout,
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT);
        stringRequest.setRetryPolicy(policy);
        stringRequest.setShouldCache(false);
        queue.add(stringRequest);

    }

    public static void downloadApkFileByAndroidNetworking(final Context mContext
//            , final String url
            , AppInfo appInfo
            , final OnCompleteListener<String, Object> onCompleteListener
            , final OnProgress onProgress, int downloadIdd) {

        if (mContext == null || appInfo == null || Utils.getValueWithoutNull(appInfo.getLink()).isEmpty()) {
            Log.e(TAG, "downloadApkFile There is A problem");
            return;
        }

        String filePath = Utils.getOutputFiles(mContext).getAbsolutePath();
        final String fileName = "AlrabeeaTimes.apk";

        final long[] lastBytesDownloaded = {0};
        final long[] speedOfDownloadFile = {0};

        String appName = mContext.getString(R.string.app_name) + appInfo.getVersion() + ".apk";
        String[] appUrl = appInfo.getLink().split("/");
        if (appInfo.getLink().endsWith("apk") && appUrl.length > 0) {
            appName = appUrl[appUrl.length - 1];
        }
        Log.d(TAG, "downloadApkFile appName: " + appName.trim());

        File file = Utils.getOutputFilesForApp(mContext);
        if (file != null) {
            filePath = file.getAbsolutePath();
        }
        final String finalAppName = appName;
        final String finalFilePath = filePath;

        Log.e(TAG, "downloadApkFile appInfo.getLink: " + appInfo.getLink());
        Log.e(TAG, "downloadApkFile filePath: " + filePath);
        Log.e(TAG, "downloadApkFile fileName: " + fileName);


// Setting timeout globally for the download network requests:


        //     downloadId =Hawk.get("idown",0);

        if (Status.RUNNING == PRDownloader.getStatus(downloadId)) {
            PRDownloader.pause(downloadId);
            return;
        }


        // onProgress.setIndeterminate(true);


        if (Status.PAUSED == PRDownloader.getStatus(downloadId)) {
            PRDownloader.resume(downloadId);
            return;
        }

        int finalDownloadId = downloadId;

        downloadId = PRDownloader.download(appInfo.getLink().trim(), filePath.trim(), appName.trim())
                .build()
                .setOnStartOrResumeListener(new OnStartOrResumeListener() {
                    @Override
                    public void onStartOrResume() {
                        //  onProgress.setIndeterminate(false);
                    }
                })
                .setOnPauseListener(new OnPauseListener() {
                    @Override
                    public void onPause() {

                    }
                })
                .setOnCancelListener(new OnCancelListener() {
                    @Override
                    public void onCancel() {
                        PRDownloader.pause(downloadId);
                    }
                })
                .setOnProgressListener(new OnProgressListener() {
                    @Override
                    public void onProgress(Progress progress) {
                        long progressPercent = progress.currentBytes * 100 / progress.totalBytes;

                        onProgress.onProgress((int) progressPercent);
                    }
                })
                .start(new OnDownloadListener() {
                    @Override
                    public void onDownloadComplete() {
                        try {
//                            String responseString = Utils.getValueWithoutNull(response.toString());
                            Log.d(TAG, "downloadApkFileByAndroidNetworking filePath: " + finalFilePath);
                            Log.d(TAG, "downloadApkFileByAndroidNetworking finalAppName: " + finalAppName);
                            onCompleteListener.onSuccess(finalFilePath + "/" + finalAppName);
                            Hawk.put("idown", 0);
                        } catch (Exception e) {

                            PRDownloader.pause(downloadId);
                            onCompleteListener.onFail(mContext.getString(R.string.incorrect_activate_code));
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }

                    @Override
                    public void onError(Error error) {


                        PRDownloader.pause(downloadId);

                        //   Hawk.get("idown",0)
                        //Log.e(TAG, "downloadApkFileByAndroidNetworking anError.getResponse().message(): " + error.getResponse().message());
                        onCompleteListener.onFail(mContext.getString(R.string.try_again));
                        // error.printStackTrace();
                    }
                });
        Hawk.put("idown", downloadId);

 /*       AndroidNetworking.download(appInfo.getLink().trim(), filePath.trim(), appName.trim())
                .addHeaders("Accept", "application/json")
                .setPriority(Priority.HIGH)
                .build()
                .setDownloadProgressListener(new DownloadProgressListener() {
                    @Override
                    public void onProgress(final long bytesDownloaded, final long totalBytes) {
                        speedOfDownloadFile[0] = bytesDownloaded - lastBytesDownloaded[0];
                        lastBytesDownloaded[0] = bytesDownloaded;
                        Log.d(TAG, "downloadApkFile totalBytes: " + totalBytes
                                + "\n, bytesDownloaded: " + bytesDownloaded
                                + "\n, (bytesDownloaded * 100.0 / totalBytes): " + (bytesDownloaded * 100.0 / totalBytes)
                                + "\n, speedOfDownloadFile: " + speedOfDownloadFile[0]);

                        new Handler(mContext.getMainLooper())
                                .post(new Runnable() {
                                    @Override
                                    public void run() {
                                        int progress = (int) (bytesDownloaded * 100.0 / totalBytes);
                                        onProgress.onProgress(progress);
//                                        if (progress >= 99) {
//                                            new Handler(mContext.getMainLooper()).postDelayed(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    Log.d(TAG, "downloadApkFileByAndroidNetworking filePath: " + filePath);
//                                                    Log.d(TAG, "downloadApkFileByAndroidNetworking finalAppName: " + finalAppName);
//                                                    onCompleteListener.onSuccess(filePath + "/" + finalAppName);
//                                                }
//                                            }, 1000);
//                                        }

                                    }
                                });
                    }
                })
                .startDownload(new DownloadListener() {
                    @Override
                    public void onDownloadComplete() {
//                        Log.d(TAG, "downloadApkFileByAndroidNetworking onResponse: " + response);
                        try {
//                            String responseString = Utils.getValueWithoutNull(response.toString());
                            Log.d(TAG, "downloadApkFileByAndroidNetworking filePath: " + finalFilePath);
                            Log.d(TAG, "downloadApkFileByAndroidNetworking finalAppName: " + finalAppName);
                            onCompleteListener.onSuccess(finalFilePath + "/" + finalAppName);
                        } catch (Exception e) {
                            onCompleteListener.onFail(mContext.getString(R.string.incorrect_activate_code));
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onError(ANError anError) {
//                        Log.e(TAG, "downloadApkFileByAndroidNetworking anError.getResponse().message(): " + anError.getResponse().message());
                        onCompleteListener.onFail(mContext.getString(R.string.try_again));
                        anError.printStackTrace();

                    }
                });
*/
//                .getAsJSONObject(new JSONObjectRequestListener() {
//                    @Override
//                    public void onResponse(JSONObject response) {
//                        Log.d(TAG, "downloadApkFileByAndroidNetworking onResponse: " + response);
//                        try {
//                            String responseString = Utils.getValueWithoutNull(response.toString());
//                            onCompleteListener.onSuccess(filePath + "/" + fileName);
//                        } catch (Exception e) {
//                            onCompleteListener.onFail(mContext.getString(R.string.incorrect_activate_code));
//                            e.printStackTrace();
//                        }
//                    }
//
//                    @Override
//                    public void onError(ANError error) {
//                        Log.d(TAG, "downloadApkFile onErrorResponse: " + error.getMessage()
//                                + "\t" + error.getStackTrace() + "\t" + error.getErrorCode());
//
//                        Log.d(TAG, "downloadApkFile error.getResponse(): " + error.getResponse());
//                        Log.d(TAG, "downloadApkFile error.getErrorBody(): " + error.getErrorBody());
//                        Log.d(TAG, "downloadApkFile error.getErrorDetail(): " + error.getErrorDetail());
//
//                        if ((error.getErrorCode() == 400 || error.getErrorCode() == 404)
//                                && !Utils.getValueWithoutNull(error.getResponse().toString()).isEmpty()
//                                && error.getErrorBody().contains("message")) {
//                            onCompleteListener.onFail(mContext.getString(R.string.incorrect_activate_code));
//
//                        } else {
//                            onCompleteListener.onFail(mContext.getString(R.string.incorrect_activate_code));
//                        }
//                        error.printStackTrace();
//                    }
//                });

    }


    public static void downloadTextFileByAndroidNetworking(final Context mContext
            , String url
            , final String fileName
            , final OnCompleteListener<String, Object> onCompleteListener) {

        url += "/" + fileName;
        Log.e(TAG, "downloadTextFileByAndroidNetworking url: " + url);
        Log.e(TAG, "downloadTextFileByAndroidNetworking fileName: " + fileName);

        if (mContext == null) {
            Log.e(TAG, "downloadTextFileByAndroidNetworking There is A problem");
            return;
        }

        String filePath = Utils.getOutputFiles(mContext).getAbsolutePath();
//        final String fileName = Utils.getValueWithoutNull(nameFile).isEmpty() ? "Alrbea.txt" : nameFile;

        final long[] lastBytesDownloaded = {0};
        final long[] speedOfDownloadFile = {0};


        File file = Utils.getOutputFilesForApp(mContext);
        if (file != null) {
            filePath = file.getAbsolutePath();
        }
        final String finalFilePath = filePath;

        Log.e(TAG, "downloadTextFileByAndroidNetworking filePath: " + filePath);

        AndroidNetworking.download(url, filePath.trim(), fileName)
                .addHeaders("Accept", "application/json")
                .setPriority(Priority.HIGH)
                .build()
                .setDownloadProgressListener(new DownloadProgressListener() {
                    @Override
                    public void onProgress(final long bytesDownloaded, final long totalBytes) {
                        speedOfDownloadFile[0] = bytesDownloaded - lastBytesDownloaded[0];
                        lastBytesDownloaded[0] = bytesDownloaded;
                        Log.d(TAG, "downloadTextFileByAndroidNetworking totalBytes: " + totalBytes
                                + "\n, bytesDownloaded: " + bytesDownloaded
                                + "\n, (bytesDownloaded * 100.0 / totalBytes): " + (bytesDownloaded * 100.0 / totalBytes)
                                + "\n, speedOfDownloadFile: " + speedOfDownloadFile[0]);
                    }
                })
                .startDownload(new DownloadListener() {
                    @Override
                    public void onDownloadComplete() {
                        try {
                            Log.d(TAG, "downloadTextFileByAndroidNetworking filePath: " + finalFilePath);
                            Log.d(TAG, "downloadTextFileByAndroidNetworking finalAppName: " + fileName);
                            onCompleteListener.onSuccess(finalFilePath + "/" + fileName);
                        } catch (Exception e) {
                            onCompleteListener.onFail(mContext.getString(R.string.incorrect_activate_code));
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }

                    @Override
                    public void onError(ANError anError) {
                        Log.e(TAG, "downloadTextFileByAndroidNetworking anError.getResponse().message(): " + anError.getResponse().message());
                        onCompleteListener.onFail(mContext.getString(R.string.try_again));
                        CrashlyticsUtils.INSTANCE.logException(anError);

                    }
                });

    }

    public static void getAppInfo(final Context context
            , String url
            , final OnCompleteListener<AppInfo, Object> OnCompleteListener) {

        if (context == null
                || Utils.getValueWithoutNull(url).isEmpty()) {
            Log.e(TAG, "getAppInfo There is A problem");
            return;
        }

        Log.e(TAG, "getAppInfo url: " + url);

        AndroidNetworking.get(url)
                .addHeaders("Accept", "application/json")
                .setPriority(Priority.HIGH)
                .build()
                .getAsJSONObject(new JSONObjectRequestListener() {
                    @Override
                    public void onResponse(JSONObject response) {
                        Log.d(TAG, "onResponse: " + response);
                        try {
                            String responseString = Utils.getValueWithoutNull(response.toString());

                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();
                            AppInfo appInfo = mGson.fromJson(responseString, AppInfo.class);

                            OnCompleteListener.onSuccess(appInfo);
                        } catch (Exception e) {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }

                    @Override
                    public void onError(ANError error) {
                        if (error == null) {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                            return;
                        }
                        Log.d(TAG, "getAppInfo onErrorResponse: " + error.getMessage()
                                + "\t" + error.getStackTrace() + "\t" + error.getErrorCode());

                        Log.d(TAG, "getAppInfo error.getResponse(): " + error.getResponse());
                        Log.d(TAG, "getAppInfo error.getErrorBody(): " + error.getErrorBody());
                        Log.d(TAG, "getAppInfo error.getErrorDetail(): " + error.getErrorDetail());

                        if (error.getErrorCode() == 422
                                && !Utils.getValueWithoutNull(error.getResponse().toString()).isEmpty()
                                && (error.getErrorBody().contains("errors") || error.getErrorBody().contains("message"))) {
                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();

                            ResponseErrors responseErrors = mGson.fromJson(error.getErrorBody(), ResponseErrors.class);
                            if (responseErrors.isErrorsNotNull()) {
                                OnCompleteListener.onFail(responseErrors.getErrors());
                            } else {
                                OnCompleteListener.onFail(responseErrors.getMessage());
                            }

                        } else {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                        }
                        CrashlyticsUtils.INSTANCE.logException(error);
                    }
                });
    }

    public static void getPrayFileInfo(final Context context
            , String url
            , final OnCompleteListener<PrayFileInfo, Object> OnCompleteListener) {

        if (context == null
                || Utils.getValueWithoutNull(url).isEmpty()) {
            Log.e(TAG, "getPrayFileInfo There is A problem");
            return;
        }

        Log.e(TAG, "getPrayFileInfo url: " + url);

        AndroidNetworking.get(url)
                .addHeaders("Accept", "application/json")
                .setPriority(Priority.HIGH)
                .build()
                .getAsJSONObject(new JSONObjectRequestListener() {
                    @Override
                    public void onResponse(JSONObject response) {
                        Log.d(TAG, "onResponse: " + response);
                        try {
                            String responseString = Utils.getValueWithoutNull(response.toString());

                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();
                            PrayFileInfo prayFileInfo = mGson.fromJson(responseString, PrayFileInfo.class);

                            OnCompleteListener.onSuccess(prayFileInfo);
                        } catch (Exception e) {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                            CrashlyticsUtils.INSTANCE.logException(e);
                        }
                    }

                    @Override
                    public void onError(ANError error) {
                        if (error == null) {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                            return;
                        }
                        Log.d(TAG, "getPrayFileInfo onErrorResponse: " + error.getMessage()
                                + "\t" + error.getStackTrace() + "\t" + error.getErrorCode());

                        Log.d(TAG, "getPrayFileInfo error.getResponse(): " + error.getResponse());
                        Log.d(TAG, "getPrayFileInfo error.getErrorBody(): " + error.getErrorBody());
                        Log.d(TAG, "getPrayFileInfo error.getErrorDetail(): " + error.getErrorDetail());

                        if (error.getErrorCode() == 422
                                && !Utils.getValueWithoutNull(error.getResponse().toString()).isEmpty()
                                && (error.getErrorBody().contains("errors") || error.getErrorBody().contains("message"))) {
                            GsonBuilder builder = new GsonBuilder();
                            Gson mGson = builder.create();

                            ResponseErrors responseErrors = mGson.fromJson(error.getErrorBody(), ResponseErrors.class);
                            if (responseErrors.isErrorsNotNull()) {
                                OnCompleteListener.onFail(responseErrors.getErrors());
                            } else {
                                OnCompleteListener.onFail(responseErrors.getMessage());
                            }

                        } else {
                            OnCompleteListener.onFail(context.getString(R.string.there_is_a_problem));
                        }
                        CrashlyticsUtils.INSTANCE.logException(error);
                    }
                });
    }
}

<?xml version="1.0" encoding="utf-8"?><!--
**
** Copyright 2008, The Android Open Source Project
** Copyright 2013, <PERSON>@gmail.com
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Layout of time picker-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layoutDirection="ltr"
        android:orientation="horizontal">

        <!-- hour -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/hour"
                android:textAppearance="?android:attr/textAppearanceMedium" />

            <NumberPicker
                android:id="@+id/hour"
                android:layout_width="70dip"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true" />
        </LinearLayout>

        <!-- minute -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/minute"
                android:textAppearance="?android:attr/textAppearanceMedium" />

            <NumberPicker
                android:id="@+id/minute"
                android:layout_width="70dip"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dip"
                android:focusable="true"
                android:focusableInTouchMode="true" />
        </LinearLayout>

        <!-- seconds -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/second"
                android:textAppearance="?android:attr/textAppearanceMedium" />

            <NumberPicker
                android:id="@+id/seconds"
                android:layout_width="70dip"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dip"
                android:focusable="true"
                android:focusableInTouchMode="true" />
        </LinearLayout>


    </LinearLayout>

    <!-- AM / PM -->
    <Button
        android:id="@+id/amPm"
        style="?android:attr/textAppearanceLargeInverse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dip"
        android:layout_marginTop="43dip"
        android:paddingStart="20dip"
        android:paddingEnd="20dip"
        android:textColor="@android:color/primary_text_light_nodisable"
        android:visibility="gone" />

</LinearLayout>

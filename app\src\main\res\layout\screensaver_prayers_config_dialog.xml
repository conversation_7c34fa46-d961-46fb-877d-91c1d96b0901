<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="0dp"
    android:minWidth="620dp"
    android:minHeight="600dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"

        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="14dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:text="@string/between_prayers_times"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_10sdp"
            android:textStyle="bold" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:lines="1"
            android:text="@string/between_prayer_desc"
            android:textColor="@color/colorBlueMain"
            android:textSize="@dimen/_7sdp"
            android:textStyle="normal" />


        <CheckBox
            android:id="@+id/ss_prayers_after_fajr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="16dp"
            android:button="@drawable/check_box_blue_selector_big"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/after_fajr_until_sunrise"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/ss_prayers_after_sunrise"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="16dp"
            android:button="@drawable/check_box_blue_selector_big"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/after_sunrise_until_dhuhr"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/ss_prayers_after_dhur"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="16dp"
            android:button="@drawable/check_box_blue_selector_big"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/after_dhur_until_asr"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/ss_prayers_after_asr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="16dp"
            android:button="@drawable/check_box_blue_selector_big"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/after_asr_until_maghreb"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/ss_prayers_after_maghreb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="16dp"
            android:button="@drawable/check_box_blue_selector_big"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/after_maghreb_until_isha"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/ss_prayers_after_isha"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="16dp"
            android:button="@drawable/check_box_blue_selector_big"
            android:checked="true"
            android:fontFamily="@font/droid_arabic_kufi"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/after_isha_until_fajr"
            android:textColor="@android:color/black"
            android:textSize="@dimen/_11sdp"
            tools:visibility="visible" />


        <LinearLayout
            android:id="@+id/addMinusNumber_LinearLayout_SubSettingsHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="24dp"
            android:foregroundGravity="center"
            android:gravity="start"
            android:padding="4dp"
            android:visibility="visible">
            <!--            android:visibility="gone">-->

            <TextView
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:padding="8dp"
                android:text="@string/activate_after"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                tools:visibility="visible" />

            <Button
                android:id="@+id/ss_add_Button_SubSettingsHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:background="@drawable/without_corners_20_background_blue_with_drawable_add_white"
                android:elevation="0dp"
                android:gravity="center"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/ss_edit_TextView_SubSettingsHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginStart="8dp"
                android:background="@drawable/without_corners_20_background_white_with_stroke_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:inputType="numberSigned"
                android:maxLines="1"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/ss_minus_Button_SubSettingsHolder"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginStart="8dp"
                android:background="@drawable/without_corners_20_background_blue_with_drawable_minus_white"
                android:elevation="0dp"
                android:gravity="center"
                android:includeFontPadding="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:padding="8dp"
                android:text="@string/minutes"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:text="@string/between_prayer_timer_desc"
            android:textColor="@color/colorBlueMain"
            android:textSize="@dimen/_7sdp"
            android:textStyle="normal" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginTop="44dp"
            android:layout_marginEnd="24dp"
            android:gravity="start"
            android:padding="4dp">
            <!--            android:visibility="gone">-->

            <TextView
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:padding="8dp"
                android:text="@string/desactivate_after"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                tools:visibility="visible" />

            <Button
                android:id="@+id/ss_add_Button_SubSettingsHolder2"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:background="@drawable/without_corners_20_background_blue_with_drawable_add_white"
                android:elevation="0dp"
                android:gravity="center"
                android:includeFontPadding="false" />

            <TextView
                android:id="@+id/ss_edit_TextView_SubSettingsHolder2"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginStart="8dp"
                android:background="@drawable/without_corners_20_background_white_with_stroke_blue"
                android:fontFamily="@font/droid_arabic_kufi_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:inputType="numberSigned"
                android:maxLines="1"
                android:text="0"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/ss_minus_Button_SubSettingsHolder2"
                android:layout_width="@dimen/_20sdp"
                android:layout_height="@dimen/_20sdp"
                android:layout_marginStart="8dp"
                android:background="@drawable/without_corners_20_background_blue_with_drawable_minus_white"
                android:elevation="0dp"
                android:gravity="center"
                android:includeFontPadding="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/droid_arabic_kufi"
                android:gravity="center"
                android:padding="8dp"
                android:text="@string/minutes"
                android:textColor="@android:color/black"
                android:textSize="@dimen/_10sdp"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/droid_arabic_kufi_bold"
            android:text="@string/between_prayer_timer_stop_desc"
            android:textColor="@color/colorBlueMain"
            android:textSize="@dimen/_7sdp"
            android:textStyle="normal" />


        <Button
            android:id="@+id/btn_dismmis"
            android:layout_width="@dimen/_120sdp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="64dp"
            android:background="@drawable/button_gray_without_corners_shape"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:padding="8dp"
            android:text="@string/close"
            android:textColor="@color/bluelett"
            android:textSize="@dimen/_11sdp"
            android:textStyle="bold" />

    </LinearLayout>

</ScrollView>


package com.arapeak.alrbrea.core_ktx.data.prayer.ptp_noor.model;


import android.app.Activity;

import java.io.Serializable;

public class City implements Serializable {
    public static String YEAR_MULTI_REPEAT_FIRST_LOOP = "2";
    public static String YEAR_MULTI_REPEAT_LAST = "4";
    public static String YEAR_ONE_REPEAT_LOOP = "3";
    public static String YEAR_ONE_REPEAT_LOOP_UAE = "1";
    public String name = null;
    public String id = null;
    public boolean is_Makkah_default_city = false;
    public String hour12_24 = null;
    public boolean isPrayerTimes_Expression = false;
    public IkametDB ikametDB = new IkametDB();
    public AzanDB azanDB = new AzanDB();
    public AzanShift azanShift = new AzanShift(this);
    //    public SeasonCity seasonCity = new SeasonCity();
//    public Exp exp = new Exp();
    public Actual actual = new Actual();
    public FixedIshaMagrib fixedIshaMagrib = new FixedIshaMagrib();
    public Ikama ikama = new Ikama();
    public Country country = new Country();

    /* loaded from: classes.dex */
    public class Actual {
        public boolean isSaudi_Actual_Settings = true;
        public boolean isQatar_Actual_Settings = true;
        public boolean isMorocco_Actual_Settings = true;
        public boolean isArab_Emirates_Actual_Settings = true;
        public boolean isTurkey_Actual_Settings = true;
        public boolean isIraq_Actual_Settings = true;

        public Actual() {
        }

        public Boolean getArab_Emirates_Actual_Settings() {
            return true;
        }

        public String getArab_Emirates_Actual_SettingsStr() {
            return "Arab_Emirates_Actual_Settings";
        }

        public Boolean getIraq_Actual_Settings() {
            return true;
        }

        public String getIraq_Actual_SettingsStr() {
            return "Iraq_Actual_Settings";
        }

        public Boolean getMorocco_Actual_Settings() {
            return true;
        }

        public String getMorocco_Actual_SettingsStr() {
            return "Morocco_Actual_Settings";
        }

        public Boolean getQatar_Actual_Settings() {
            return true;
        }

        public String getQatar_Actual_SettingsStr() {
            return "Qatar_Actual_Settings";
        }

        public Boolean getSaudi_Actual_Settings() {
            return true;
        }

        public String getSaudi_Actual_SettingsStr() {
            return "Saudi_Actual_Settings";
        }

        public Boolean getTurkey_Actual_Settings() {
            return true;
        }

        public String getTurkey_Actual_SettingsStr() {
            return "Turkey_Actual_Settings";
        }
    }

    /* loaded from: classes.dex */
    public class AzanDB {
        public String year_type = "-1";
        public int year_min = 0;
        public int year_max = 0;
        public String actualDate = Preference.DEFAULT_CITY_ACTUAL_DATE;

        public AzanDB() {
        }

        public String getCityActualDate() {
            return "cityActualDate";
        }

        public String get_year_max() {
            return "year_max";
        }

        public String get_year_min() {
            return "year_min";
        }

        public String get_year_type() {
            return "year_type";
        }

    }

    /* loaded from: classes.dex */
    public class AzanShift {
        public int shiftPrayerAsr;
        public int shiftPrayerDuhr;
        public int shiftPrayerFajr;
        public int shiftPrayerIsha;
        public int shiftPrayerMagrib;
        public int shiftPrayerShuruq;

        public AzanShift(City city) {
        }

        public String getShiftPrayerAsrPreferen() {
            return "shiftPrayerAsr_preferen";
        }

        public String getShiftPrayerDuhrPreferen() {
            return "shiftPrayerDuhr_preferen";
        }

        public String getShiftPrayerFajrPreferen() {
            return "shiftPrayerFajr_preferen";
        }

        public String getShiftPrayerIshaPreferen() {
            return "shiftPrayerIsha_preferen";
        }

        public String getShiftPrayerMagribPreferen() {
            return "shiftPrayerMagrib_preferen";
        }

        public String getShiftPrayerShuruqPreferen() {
            return "shiftPrayerShuruq_preferen";
        }
    }

    /* loaded from: classes.dex */
    public class FixedIshaMagrib {
        public boolean isIsha_Magrib_Between = false;
        public String Isha_Magrib_Between_value_minutes = "90";

        public FixedIshaMagrib() {
        }

        public Boolean getIsha_Magrib_Between() {
            return true;
        }

        public String getIsha_Magrib_BetweenStr() {
            return "Isha_Magrib_Between";
        }

//        public String getIsha_Magrib_Between_value_minutes() {
//            City city = City.this;
//            return "" + MyTool.strToInt(PreferenceManager.getDefaultSharedPreferences(city.context).getString(getIsha_Magrib_Between_value_minutesStr(), city.context.getString(R.string.Isha_Magrib_Between_value_minutes)), MyTool.strToInt(city.context.getString(R.string.Isha_Magrib_Between_value_minutes), 90));
//        }

        public String getIsha_Magrib_Between_value_minutesStr() {
            return "Isha_Magrib_Between_value_minutes";
        }

//        public void setIsha_Magrib_Between(boolean z) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putBoolean(getIsha_Magrib_BetweenStr(), z);
//            edit.apply();
//        }
//
//        public void setIsha_Magrib_Between_value_minutes(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getIsha_Magrib_Between_value_minutesStr(), str);
//            edit.apply();
//        }
    }

    /* loaded from: classes.dex */
    public class Ikama {
        public int iqamaPrayerAsr;
        public int iqamaPrayerDhuhr;
        public int iqamaPrayerFajr;
        public int iqamaPrayerIsha;
        public int iqamaPrayerJomo3a;
        public int iqamaPrayerMaghrib;
        public int iqamaPrayerShuruq;
        public int timeAsrHour;
        public int timeAsrMinute;
        public int timeDuhrHour;
        public int timeDuhrMinute;
        public int timeFajrHour;
        public int timeFajrMinute;
        public int timeIshaHour;
        public int timeIshaMinute;
        public int timeJomo3aHour;
        public int timeJomo3aMinute;
        public int timeMagribHour;
        public int timeMagribMinute;
        public int timeShuruqHour;
        public int timeShuruqMinute;

        public Ikama() {
        }

        public String getIkamaPrayerAsrPreferen() {
            return "ikamaPrayerAsr_preferen";
        }

        public String getIkamaPrayerDuhrPreferen() {
            return "ikamaPrayerDuhr_preferen";
        }

        public String getIkamaPrayerFajrPreferen() {
            return "ikamaPrayerFajr_preferen";
        }

        public String getIkamaPrayerIshaPreferen() {
            return "ikamaPrayerIsha_preferen";
        }

        public String getIkamaPrayerJomo3aPreferen() {
            return "ikamaPrayerJomo3a_preferen";
        }

        public String getIkamaPrayerMagribPreferen() {
            return "ikamaPrayerMagrib_preferen";
        }

        public String getIkamaPrayerShuruqPreferen() {
            return "ikamaPrayerShuruq_preferen";
        }


        public String getIsha_ikamaStr() {
            return "Isha_ikama";
        }

        public String getTimeAsrHourPreferen() {
            return "timeAsrHour_preferen";
        }

        public String getTimeAsrMinutePreferen() {
            return "timeAsrMinute_preferen";
        }

        public String getTimeDuhrHourPreferen() {
            return "timeDuhrHour_preferen";
        }

        public String getTimeDuhrMinutePreferen() {
            return "timeDuhrMinute_preferen";
        }

        public String getTimeFajrHourPreferen() {
            return "timeFajrHour_preferen";
        }

        public String getTimeFajrMinutePreferen() {
            return "timeFajrMinute_preferen";
        }

        public String getTimeIshaHourPreferen() {
            return "timeIshaHour_preferen";
        }

        public String getTimeIshaMinutePreferen() {
            return "timeIshaMinute_preferen";
        }

        public String getTimeJomo3aHourPreferen() {
            return "timeJomo3aHour_preferen";
        }

        public String getTimeJomo3aMinutePreferen() {
            return "timeJomo3aMinute_preferen";
        }

        public String getTimeMagribHourPreferen() {
            return "timeMagribHour_preferen";
        }

        public String getTimeMagribMinutePreferen() {
            return "timeMagribMinute_preferen";
        }

        public String getTimeShuruqHourPreferen() {
            return "timeShuruqHour_preferen";
        }

        public String getTimeShuruqMinutePreferen() {
            return "timeShuruqMinute_preferen";
        }

        public String get_ikamaTimeAsr_preferen() {
            return "ikamaTimeAsr_preferen";
        }

        public String get_ikamaTimeDuhr_preferen() {
            return "ikamaTimeDuhr_preferen";
        }

        public String get_ikamaTimeFajr_preferen() {
            return "ikamaTimeFajr_preferen";
        }

        public String get_ikamaTimeIsha_preferen() {
            return "ikamaTimeIsha_preferen";
        }

        public String get_ikamaTimeJomo3a_preferen() {
            return "ikamaTimeJomo3a_preferen";
        }

        public String get_ikamaTimeMagrib_preferen() {
            return "ikamaTimeMagrib_preferen";
        }

        public String get_ikamaTimeShuruq_preferen() {
            return "ikamaTimeShuruq_preferen";
        }


    }

    /* loaded from: classes.dex */
    public class IkametDB {

        public int year_min = 0;
        public int year_max = 0;
        /* renamed from: a */
        private String f3708a = "-1";
        private String b = Preference.DEFAULT_CITY_ACTUAL_DATE;

        public IkametDB() {
        }

        public String getActualDate() {
            return this.b;
        }

        public String getCityActualDate() {
            return "ikametCityActualDate";
        }


        public String getYear_type() {
            return this.f3708a;
        }

        public String get_year_max() {
            return "ikamet_year_max";
        }

        public String get_year_min() {
            return "ikamet_year_min";
        }

        public String get_year_type() {
            return "ikamet_year_type";
        }

        public String getCityName() {
            return "cityName";
        }

        public String getCityNo() {
            return "cityNo";
        }

        public String getCountryName() {
            return "countryName";
        }

        public String getCountryNo() {
            return "countryNo";
        }

        public String getIs_Makkah_default_city() {
            return "is_Makkah_default_city";
        }

        public String getPrayerTimesExpression() {
            return "PrayerTimes_Expression";
        }

        public void setActualValue() {
//        FixedIshaMagrib fixedIshaMagrib = this.fixedIshaMagrib;
//        fixedIshaMagrib.isIsha_Magrib_Between = fixedIshaMagrib.getIsha_Magrib_Between().booleanValue();
//        FixedIshaMagrib fixedIshaMagrib2 = this.fixedIshaMagrib;
//        fixedIshaMagrib2.Isha_Magrib_Between_value_minutes = fixedIshaMagrib2.getIsha_Magrib_Between_value_minutes();
//        Actual actual = this.actual;
//        actual.isSaudi_Actual_Settings = actual.getSaudi_Actual_Settings().booleanValue();
//        Actual actual2 = this.actual;
//        actual2.isQatar_Actual_Settings = actual2.getQatar_Actual_Settings().booleanValue();
//        Actual actual3 = this.actual;
//        actual3.isMorocco_Actual_Settings = actual3.getMorocco_Actual_Settings().booleanValue();
//        Actual actual4 = this.actual;
//        actual4.isArab_Emirates_Actual_Settings = actual4.getArab_Emirates_Actual_Settings().booleanValue();
//        Actual actual5 = this.actual;
//        actual5.isTurkey_Actual_Settings = actual5.getTurkey_Actual_Settings().booleanValue();
//        Actual actual6 = this.actual;
//        actual6.isIraq_Actual_Settings = actual6.getIraq_Actual_Settings().booleanValue();
        }

        public void setAllValueDB(City city) {
//        this.name = city.name;
//        this.id = city.id;
//        Country country = this.country;
            Country country2 = city.country;
            country.name = country2.name;
            country.id = country2.id;
//        AzanDB azanDB = this.azanDB;
            AzanDB azanDB2 = city.azanDB;
            azanDB.year_type = azanDB2.year_type;
            azanDB.year_min = azanDB2.year_min;
            azanDB.year_max = azanDB2.year_max;
//        this.ikametDB.f3708a = city.ikametDB.f3708a;
//        IkametDB ikametDB = this.ikametDB;
            IkametDB ikametDB2 = city.ikametDB;
            ikametDB.year_min = ikametDB2.year_min;
            ikametDB.year_max = ikametDB2.year_max;
        }

        public void setExpValue() {
//        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(this.context);
//        Exp exp = this.exp;
//        exp.calc_method = defaultSharedPreferences.getString(exp.getCalcMethod(), Preference.DEFAULT_CALC_METHOD);
//        Exp exp2 = this.exp;
//        exp2.calcMethod = exp2.calc_method;
//        exp2.mazhab = defaultSharedPreferences.getString(exp2.getMazhab(), Preference.DEFAULT_MAZHAB);
//        Exp exp3 = this.exp;
//        exp3.fajrAngle = defaultSharedPreferences.getString(exp3.getFajrAngleStr(), "18");
//        Exp exp4 = this.exp;
//        exp4.ishaAngle = defaultSharedPreferences.getString(exp4.getIshaAngleStr(), "17");
//        Exp exp5 = this.exp;
//        exp5.timezone_preferen = exp5.get_timezone_preferen();
//        Exp exp6 = this.exp;
//        exp6.high_latitude = exp6.get_high_latitude();
//        Exp exp7 = this.exp;
//        exp7.latitude_preferen = exp7.get_latitude_preferen();
//        Exp exp8 = this.exp;
//        exp8.longitude_preferen = exp8.get_longitude_preferen();
        }

        public void setGeneralValue() {
//        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(this.context);
//        this.name = defaultSharedPreferences.getString(getCityName(), Preference.DEFAULT_CITY_NAME);
//        this.id = defaultSharedPreferences.getString(getCityNo(), Preference.DEFAULT_CITY_ID);
//        this.is_Makkah_default_city = defaultSharedPreferences.getBoolean(getIs_Makkah_default_city(), true);
//        this.country.name = defaultSharedPreferences.getString(getCountryName(), Preference.DEFAULT_COUNTRY_NAME);
//        this.country.id = defaultSharedPreferences.getString(getCountryNo(), Preference.DEFAULT_COUNTRY_ID);
//        try {
//            Exp exp = this.exp;
//            exp.timeZone = defaultSharedPreferences.getFloat(exp.getTimeZone(), Preference.DEFAULT_TIMEZONE.intValue());
//        } catch (Exception e) {
//            this.exp.timeZone = defaultSharedPreferences.getInt(r2.getTimeZone(), Preference.DEFAULT_TIMEZONE.intValue());
//            e.printStackTrace();
//        }
//        Exp exp2 = this.exp;
//        exp2.latitude = defaultSharedPreferences.getString(exp2.getLatitude(), Preference.DEFAULT_LATITUDE);
//        Exp exp3 = this.exp;
//        exp3.longitude = defaultSharedPreferences.getString(exp3.getLongitude(), Preference.DEFAULT_LONGITUDE);
//        this.isPrayerTimes_Expression = isPrayerTimes_Expression();
//        this.hour12_24 = defaultSharedPreferences.getString("hour", Preference.DEFAULT_HOUR_12_24);
//        AzanDB azanDB = this.azanDB;
//        azanDB.actualDate = defaultSharedPreferences.getString(azanDB.getCityActualDate(), Preference.DEFAULT_CITY_ACTUAL_DATE);
//        AzanDB azanDB2 = this.azanDB;
//        azanDB2.year_type = defaultSharedPreferences.getString(azanDB2.get_year_type(), "4");
//        AzanDB azanDB3 = this.azanDB;
//        azanDB3.year_min = defaultSharedPreferences.getInt(azanDB3.get_year_min(), 2010);
//        AzanDB azanDB4 = this.azanDB;
//        azanDB4.year_max = defaultSharedPreferences.getInt(azanDB4.get_year_max(), 2030);
//        if (!isPrayerTimes_Expression()) {
//            IkametDB ikametDB = this.ikametDB;
//            ikametDB.b = defaultSharedPreferences.getString(ikametDB.getCityActualDate(), Preference.DEFAULT_CITY_ACTUAL_DATE);
//            IkametDB ikametDB2 = this.ikametDB;
//            ikametDB2.f3708a = defaultSharedPreferences.getString(ikametDB2.get_year_type(), "4");
//            IkametDB ikametDB3 = this.ikametDB;
//            ikametDB3.year_min = defaultSharedPreferences.getInt(ikametDB3.get_year_min(), 2010);
//            IkametDB ikametDB4 = this.ikametDB;
//            ikametDB4.year_max = defaultSharedPreferences.getInt(ikametDB4.get_year_max(), 2030);
//        }
//        c.b(new StringBuilder("setGeneralValue year_type00 "), this.ikametDB.f3708a, "year_max");
        }

        public void setIqamaPrayerValue() {
//        SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(this.context);
//        Ikama ikama = this.ikama;
//        ikama.iqamaPrayerFajr = MyTool.getIntPrefVal(R.string.val_ikamaPrayerFajr, ikama.getIkamaPrayerFajrPreferen(), this.context);
//        Ikama ikama2 = this.ikama;
//        ikama2.iqamaPrayerShuruq = MyTool.getIntPrefVal(R.string.val_ikamaPrayerShuruq, ikama2.getIkamaPrayerShuruqPreferen(), this.context);
//        Ikama ikama3 = this.ikama;
//        ikama3.iqamaPrayerDhuhr = MyTool.getIntPrefVal(R.string.val_ikamaPrayerDuhr, ikama3.getIkamaPrayerDuhrPreferen(), this.context);
//        Ikama ikama4 = this.ikama;
//        ikama4.iqamaPrayerAsr = MyTool.getIntPrefVal(R.string.val_ikamaPrayerAsr, ikama4.getIkamaPrayerAsrPreferen(), this.context);
//        Ikama ikama5 = this.ikama;
//        ikama5.iqamaPrayerMaghrib = MyTool.getIntPrefVal(R.string.val_ikamaPrayerMagrib, ikama5.getIkamaPrayerMagribPreferen(), this.context);
//        Ikama ikama6 = this.ikama;
//        ikama6.iqamaPrayerIsha = MyTool.getIntPrefVal(R.string.val_ikamaPrayerIsha, ikama6.getIkamaPrayerIshaPreferen(), this.context);
//        Ikama ikama7 = this.ikama;
//        ikama7.iqamaPrayerJomo3a = MyTool.getIntPrefVal(R.string.val_ikamaPrayerJomo3a, ikama7.getIkamaPrayerJomo3aPreferen(), this.context);
//        Ikama ikama8 = this.ikama;
//        ikama8.timeJomo3aHour = defaultSharedPreferences.getInt(ikama8.getTimeJomo3aHourPreferen(), -1);
//        Ikama ikama9 = this.ikama;
//        ikama9.timeJomo3aMinute = defaultSharedPreferences.getInt(ikama9.getTimeJomo3aMinutePreferen(), -1);
//        Ikama ikama10 = this.ikama;
//        ikama10.timeIshaHour = defaultSharedPreferences.getInt(ikama10.getTimeIshaHourPreferen(), -1);
//        Ikama ikama11 = this.ikama;
//        ikama11.timeIshaMinute = defaultSharedPreferences.getInt(ikama11.getTimeIshaMinutePreferen(), -1);
//        Ikama ikama12 = this.ikama;
//        ikama12.timeMagribHour = defaultSharedPreferences.getInt(ikama12.getTimeMagribHourPreferen(), -1);
//        Ikama ikama13 = this.ikama;
//        ikama13.timeMagribMinute = defaultSharedPreferences.getInt(ikama13.getTimeMagribMinutePreferen(), -1);
//        Ikama ikama14 = this.ikama;
//        ikama14.timeAsrHour = defaultSharedPreferences.getInt(ikama14.getTimeAsrHourPreferen(), -1);
//        Ikama ikama15 = this.ikama;
//        ikama15.timeAsrMinute = defaultSharedPreferences.getInt(ikama15.getTimeAsrMinutePreferen(), -1);
//        Ikama ikama16 = this.ikama;
//        ikama16.timeDuhrHour = defaultSharedPreferences.getInt(ikama16.getTimeDuhrHourPreferen(), -1);
//        Ikama ikama17 = this.ikama;
//        ikama17.timeDuhrMinute = defaultSharedPreferences.getInt(ikama17.getTimeDuhrMinutePreferen(), -1);
//        Ikama ikama18 = this.ikama;
//        ikama18.timeShuruqHour = defaultSharedPreferences.getInt(ikama18.getTimeShuruqHourPreferen(), -1);
//        Ikama ikama19 = this.ikama;
//        ikama19.timeShuruqMinute = defaultSharedPreferences.getInt(ikama19.getTimeShuruqMinutePreferen(), -1);
//        Ikama ikama20 = this.ikama;
//        ikama20.timeFajrHour = defaultSharedPreferences.getInt(ikama20.getTimeFajrHourPreferen(), -1);
//        Ikama ikama21 = this.ikama;
//        ikama21.timeFajrMinute = defaultSharedPreferences.getInt(ikama21.getTimeFajrMinutePreferen(), -1);
        }

        public void setShiftPrayerValue() {
//        AzanShift azanShift = this.azanShift;
//        azanShift.shiftPrayerFajr = MyTool.getIntPrefVal(R.string.val_shiftPrayer, azanShift.getShiftPrayerFajrPreferen(), this.context);
//        AzanShift azanShift2 = this.azanShift;
//        azanShift2.shiftPrayerShuruq = MyTool.getIntPrefVal(R.string.val_shiftPrayer, azanShift2.getShiftPrayerShuruqPreferen(), this.context);
//        AzanShift azanShift3 = this.azanShift;
//        azanShift3.shiftPrayerDuhr = MyTool.getIntPrefVal(R.string.val_shiftPrayer, azanShift3.getShiftPrayerDuhrPreferen(), this.context);
//        AzanShift azanShift4 = this.azanShift;
//        azanShift4.shiftPrayerAsr = MyTool.getIntPrefVal(R.string.val_shiftPrayer, azanShift4.getShiftPrayerAsrPreferen(), this.context);
//        AzanShift azanShift5 = this.azanShift;
//        azanShift5.shiftPrayerMagrib = MyTool.getIntPrefVal(R.string.val_shiftPrayer, azanShift5.getShiftPrayerMagribPreferen(), this.context);
//        AzanShift azanShift6 = this.azanShift;
//        azanShift6.shiftPrayerIsha = MyTool.getIntPrefVal(R.string.val_shiftPrayer, azanShift6.getShiftPrayerIshaPreferen(), this.context);
        }

//    public void setPrayerTimes_Expression(boolean z) {
//        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(this.context).edit();
//        edit.putBoolean(getPrayerTimesExpression(), z);
//        edit.apply();
//    }

        public void updateCity() {
//        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(this.context).edit();
//        setCityName(this.name);
//        setCityNo(this.id);
//        edit.putBoolean(getIs_Makkah_default_city(), this.is_Makkah_default_city);
//        setCountryName(this.country.name);
//        setCountryNo(this.country.id);
//        Exp exp = this.exp;
//        exp.setLongitude(exp.longitude);
//        Exp exp2 = this.exp;
//        exp2.setLatitude(exp2.latitude);
//        Exp exp3 = this.exp;
//        exp3.setTimeZone(exp3.timeZone);
//        setPrayerTimes_Expression(this.isPrayerTimes_Expression);
//        AzanDB azanDB = this.azanDB;
//        azanDB.setYearType(azanDB.year_type);
//        AzanDB azanDB2 = this.azanDB;
//        azanDB2.setYearMin(azanDB2.year_min);
//        AzanDB azanDB3 = this.azanDB;
//        azanDB3.setYearMax(azanDB3.year_max);
//        AzanDB azanDB4 = this.azanDB;
//        azanDB4.setCityActualDate(azanDB4.actualDate);
//        IkametDB ikametDB = this.ikametDB;
//        ikametDB.setYearType(ikametDB.f3708a);
//        IkametDB ikametDB2 = this.ikametDB;
//        ikametDB2.setYearMin(ikametDB2.year_min);
//        IkametDB ikametDB3 = this.ikametDB;
//        ikametDB3.setYearMax(ikametDB3.year_max);
//        IkametDB ikametDB4 = this.ikametDB;
//        ikametDB4.setCityActualDate(ikametDB4.b);
//        c.b(new StringBuilder("updateCity year_type00 "), this.ikametDB.f3708a, "year_max");
//        Exp exp4 = this.exp;
//        exp4.set_calc_method(exp4.calc_method);
//        Exp exp5 = this.exp;
//        exp5.set_mazhab(exp5.mazhab);
//        Exp exp6 = this.exp;
//        exp6.setFajrAngle(exp6.fajrAngle);
//        Exp exp7 = this.exp;
//        exp7.setIshaAngle(exp7.ishaAngle);
//        Exp exp8 = this.exp;
//        exp8.set_timezone_preferen(exp8.timezone_preferen);
//        Exp exp9 = this.exp;
//        exp9.set_high_latitude(exp9.high_latitude);
//        Exp exp10 = this.exp;
//        exp10.set_latitude_preferen(exp10.latitude_preferen);
//        Exp exp11 = this.exp;
//        exp11.set_longitude_preferen(exp11.longitude_preferen);
//        edit.putBoolean(this.fixedIshaMagrib.getIsha_Magrib_BetweenStr(), this.fixedIshaMagrib.isIsha_Magrib_Between);
//        FixedIshaMagrib fixedIshaMagrib = this.fixedIshaMagrib;
//        fixedIshaMagrib.setIsha_Magrib_Between_value_minutes(fixedIshaMagrib.Isha_Magrib_Between_value_minutes);
//        edit.putBoolean(this.actual.getSaudi_Actual_SettingsStr(), this.actual.isSaudi_Actual_Settings);
//        edit.putBoolean(this.actual.getQatar_Actual_SettingsStr(), this.actual.isQatar_Actual_Settings);
//        edit.putBoolean(this.actual.getMorocco_Actual_SettingsStr(), this.actual.isMorocco_Actual_Settings);
//        edit.putBoolean(this.actual.getArab_Emirates_Actual_SettingsStr(), this.actual.isArab_Emirates_Actual_Settings);
//        edit.putBoolean(this.actual.getIraq_Actual_SettingsStr(), this.actual.isIraq_Actual_Settings);
//        edit.putBoolean(this.actual.getTurkey_Actual_SettingsStr(), this.actual.isTurkey_Actual_Settings);
//        SeasonCity seasonCity = this.seasonCity;
//        seasonCity.setSeason(seasonCity.season);
//        SeasonCity seasonCity2 = this.seasonCity;
//        seasonCity2.setSessionSummerMonth(Integer.valueOf(seasonCity2.seasonSummerMonth));
//        SeasonCity seasonCity3 = this.seasonCity;
//        seasonCity3.setSessionWinterMonth(Integer.valueOf(seasonCity3.seasonWinterMonth));
//        SeasonCity seasonCity4 = this.seasonCity;
//        seasonCity4.setSessionSummerDay(Integer.valueOf(seasonCity4.seasonSummerDay));
//        SeasonCity seasonCity5 = this.seasonCity;
//        seasonCity5.setSessionWinterDay(Integer.valueOf(seasonCity5.seasonWinterDay));
//        SeasonCity seasonCity6 = this.seasonCity;
//        seasonCity6.setAuto_DaylightTime(seasonCity6.auto_DaylightTime);
//        edit.putString(this.azanShift.getShiftPrayerFajrPreferen(), this.azanShift.shiftPrayerFajr + "");
//        edit.putString(this.azanShift.getShiftPrayerShuruqPreferen(), this.azanShift.shiftPrayerShuruq + "");
//        edit.putString(this.azanShift.getShiftPrayerDuhrPreferen(), this.azanShift.shiftPrayerDuhr + "");
//        edit.putString(this.azanShift.getShiftPrayerAsrPreferen(), this.azanShift.shiftPrayerAsr + "");
//        edit.putString(this.azanShift.getShiftPrayerMagribPreferen(), this.azanShift.shiftPrayerMagrib + "");
//        edit.putString(this.azanShift.getShiftPrayerIshaPreferen(), this.azanShift.shiftPrayerIsha + "");
//        edit.apply();
        }

        public void savePrayerTime() {
        }

        public void setPrayerTimeValue() {
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        /* loaded from: classes.dex */
        public final class a implements Runnable {

            /* renamed from: a */
            final /* synthetic */ Activity f3710a;

            a(Activity activity) {
                this.f3710a = activity;
            }

            @Override // java.lang.Runnable
            public final void run() {
//            Activity activity = this.f3710a;
//            Preference preference = new Manager(activity).getPreference();
//            preference.fetchCurrentPreferences();
//            City city = new City();
//            City.copyCity(city, preference.city);
//            City.copyCity(preference.city, preference.city_2);
//            City.copyCity(preference.city_2, city);
//            preference.city.updateCity();
//            preference.city_2.updateCity();
//            PrayerTimesApplication.Need_createAlarmPrayerTime = true;
//            PrayerTimesApplication.RequiredRestartOnResume_MainActivity = true;
//            try {
//                Intent launchIntentForPackage = activity.getPackageManager().getLaunchIntentForPackage(activity.getPackageName());
//                launchIntentForPackage.addFlags(67108864);
//                activity.startActivity(launchIntentForPackage);
//                activity.finish();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            preference.setMust_request_random(true);
            }
        }

        /* loaded from: classes.dex */
        public class Exp {
            public String calcMethod;
            public String fajrAngle;
            public String ishaAngle;
            public String latitude;
            public String longitude;
            public String mazhab = Preference.DEFAULT_MAZHAB;
            public String calc_method = null;
            public String timezone_preferen = "3";
            public int high_latitude = 0;
            public String latitude_preferen = "21.430000";
            public String longitude_preferen = "39.819999";
            public float timeZone = -999.0f;

            public Exp() {
            }

            public String getCalcMethod() {
                return "calc_method";
            }

//        public String getFajrAngle() {
//            return PreferenceManager.getDefaultSharedPreferences(City.this.context).getString(getFajrAngleStr(), "18");
//        }

            public String getFajrAngleStr() {
                return "FajrAngle";
            }

            public String getHighLatitude() {
                return "high_latitude";
            }

//        public String getIshaAngle() {
//            return PreferenceManager.getDefaultSharedPreferences(City.this.context).getString(getIshaAngleStr(), "17");
//        }

            public String getIshaAngleStr() {
                return "IshaAngle";
            }

            public String getLatitude() {
                return "latitude";
            }

            public String getLatitudePreferen() {
                return "latitude_preferen";
            }

            public String getLongitude() {
                return "longitude";
            }

            public String getLongitudePreferen() {
                return "longitude_preferen";
            }

            public String getMazhab() {
                return "mazhab";
            }

            public String getTimeZone() {
                return "timeZone";
            }

            public String getTimezonePreferen() {
                return "timezone_preferen";
            }

            /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
//        public int get_high_latitude() {
//            char c;
//            String string = PreferenceManager.getDefaultSharedPreferences(City.this.context).getString(getHighLatitude(), "OneSeventh");
//            string.getClass();
//            switch (string.hashCode()) {
//                case -1997483328:
//                    if (string.equals("AngleBased")) {
//                        c = 0;
//                        break;
//                    }
//                    c = 65535;
//                    break;
//                case -1605771568:
//                    if (string.equals("MidNight")) {
//                        c = 1;
//                        break;
//                    }
//                    c = 65535;
//                    break;
//                case -308976101:
//                    if (string.equals("OneSeventh")) {
//                        c = 2;
//                        break;
//                    }
//                    c = 65535;
//                    break;
//                case 2433880:
//                    if (string.equals("None")) {
//                        c = 3;
//                        break;
//                    }
//                    c = 65535;
//                    break;
//                default:
//                    c = 65535;
//                    break;
//            }
//            switch (c) {
//                case 0:
//                    return 2;
//                case 1:
//                    return 1;
//                case 2:
//                default:
//                    return 0;
//                case 3:
//                    return -1;
//            }
//        }
//
//        public String get_latitude_preferen() {
//            return PreferenceManager.getDefaultSharedPreferences(City.this.context).getString(getLatitudePreferen(), "21.430000");
//        }
//
//        public String get_longitude_preferen() {
//            return PreferenceManager.getDefaultSharedPreferences(City.this.context).getString(getLongitudePreferen(), "39.819999");
//        }
//
//        public String get_timezone_preferen() {
//            return PreferenceManager.getDefaultSharedPreferences(City.this.context).getString(getTimezonePreferen(), ExifInterface.GPS_MEASUREMENT_3D);
//        }

//        public void setFajrAngle(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getFajrAngleStr(), str);
//            edit.apply();
//        }
//
//        public void setIshaAngle(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getIshaAngleStr(), str);
//            edit.apply();
//        }
//
//        public void setLatitude(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getLatitude(), str);
//            edit.apply();
//        }
//
//        public void setLongitude(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getLongitude(), str);
//            edit.apply();
//        }
//
//        public void setTimeZone(float f2) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putFloat(getTimeZone(), f2);
//            edit.apply();
//            MyTool.MyLog("timezone22", "timeZone " + f2);
//        }
//
//        public void set_calc_method(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getCalcMethod(), str);
//            edit.apply();
//        }
//
//        public void set_high_latitude(int i) {
//            String str = "OneSeventh";
//            if (i != 0) {
//                if (i != 1) {
//                    if (i != 2) {
//                        if (i == 3) {
//                            str = "None";
//                        }
//                    } else {
//                        str = "AngleBased";
//                    }
//                } else {
//                    str = "MidNight";
//                }
//            }
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getHighLatitude(), str);
//            edit.apply();
//        }
//
//        public void set_latitude_preferen(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getLatitudePreferen(), str);
//            edit.apply();
//        }
//
//        public void set_longitude_preferen(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getLongitudePreferen(), str);
//            edit.apply();
//        }
//
//        public void set_mazhab(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getMazhab(), str);
//            edit.apply();
//        }
//
//        public void set_timezone_preferen(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getTimezonePreferen(), str);
//            edit.apply();
//            MyTool.MyLog("timezone22", "timezone_preferen " + str);
//        }
//
//        public void set_calc_method(CalcMethod calcMethod) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getCalcMethod(), calcMethod.calenderString());
//            edit.apply();
//        }
        }

        /* loaded from: classes.dex */
        public class SeasonCity {
            public boolean auto_DaylightTime = false;
            public String season = null;
            public int seasonSummerDay;
            public int seasonSummerMonth;
            public int seasonWinterDay;
            public int seasonWinterMonth;

            public SeasonCity() {
            }

//        public Boolean getAuto_DaylightTime() {
//            return Boolean.valueOf(PreferenceManager.getDefaultSharedPreferences(City.this.context).getBoolean(getAuto_DaylightTimeStr(), false));
//        }

            public String getAuto_DaylightTimeStr() {
                return "Auto_DaylightTime";
            }

            public String getSeason() {
                return "season";
            }

            public String getSeasonSummerDay() {
                return "seasonSummerDay";
            }

            public String getSeasonSummerMonth() {
                return "seasonSummerMonth";
            }

            public String getSeasonWinterDay() {
                return "seasonWinterDay";
            }

            public String getSeasonWinterMonth() {
                return "seasonWinterMonth";
            }

//        public void setAuto_DaylightTime(boolean z) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putBoolean(getAuto_DaylightTimeStr(), z);
//            edit.apply();
//        }

//        public void setSeason(String str) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getSeason(), str);
//            MyTool.MyLog("setSeason", "1 " + str + " " + getSeason());
//            MyTool.MyLog("m_season--", "setSeason 22 " + str + " " + getSeason());
//            edit.apply();
//        }

//        public void setSessionSummerDay(Integer num) {
//            com.alawail.prayertimes.manager.a.b(num, PreferenceManager.getDefaultSharedPreferences(City.this.context).edit(), getSeasonSummerDay());
//        }
//
//        public void setSessionSummerMonth(Integer num) {
//            com.alawail.prayertimes.manager.a.b(num, PreferenceManager.getDefaultSharedPreferences(City.this.context).edit(), getSeasonSummerMonth());
//        }
//
//        public void setSessionWinterDay(Integer num) {
//            com.alawail.prayertimes.manager.a.b(num, PreferenceManager.getDefaultSharedPreferences(City.this.context).edit(), getSeasonWinterDay());
//        }
//
//        public void setSessionWinterMonth(Integer num) {
//            com.alawail.prayertimes.manager.a.b(num, PreferenceManager.getDefaultSharedPreferences(City.this.context).edit(), getSeasonWinterMonth());
//        }
//
//        public void setSeason() {
//            SharedPreferences defaultSharedPreferences = PreferenceManager.getDefaultSharedPreferences(City.this.context);
//            this.auto_DaylightTime = defaultSharedPreferences.getBoolean(getAuto_DaylightTimeStr(), false);
//            this.season = defaultSharedPreferences.getString(getSeason(), Preference.DEFAULT_SEASON);
//            this.seasonSummerMonth = defaultSharedPreferences.getInt(getSeasonSummerMonth(), Preference.DEFAULT_SEASON_SUMMER_MONTH.intValue());
//            this.seasonWinterMonth = defaultSharedPreferences.getInt(getSeasonWinterMonth(), Preference.DEFAULT_SEASON_WINTER_MONTH.intValue());
//            this.seasonSummerDay = defaultSharedPreferences.getInt(getSeasonSummerDay(), Preference.DEFAULT_SEASON_SUMMER_DAY.intValue());
//            this.seasonWinterDay = defaultSharedPreferences.getInt(getSeasonWinterDay(), Preference.DEFAULT_SEASON_WINTER_DAY.intValue());
//        }
//
//        public void setSeason(Season season) {
//            SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(City.this.context).edit();
//            edit.putString(getSeason(), season.seasonString());
//            MyTool.MyLog("setSeason", "2 " + season.seasonString() + " " + getSeason());
//            MyTool.MyLog("m_season--", "setSeason 2 " + season.seasonString() + " " + getSeason());
//            edit.apply();
//            this.season = season.seasonString();
//        }
        }
    }
}

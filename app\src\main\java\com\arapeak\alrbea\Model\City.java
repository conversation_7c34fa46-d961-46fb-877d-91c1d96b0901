package com.arapeak.alrbea.Model;

import io.realm.RealmObject;

public class City extends RealmObject {
    private Long id;
    private String city_name;
    private Long population;
    private Double latitude;
    private Double longitude;
    private Long timezone;
    private String country_id;
    private String country_name;
    private Long has_data;
    private Long default_mazhab;
    private Long default_calc_method;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCity_name() {
        return city_name;
    }

    public void setCity_name(String city_name) {
        this.city_name = city_name;
    }

    public Long getPopulation() {
        return population;
    }

    public void setPopulation(Long population) {
        this.population = population;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Long getTimezone() {
        return timezone;
    }

    public void setTimezone(Long timezone) {
        this.timezone = timezone;
    }

    public String getCountry_id() {
        return country_id;
    }

    public void setCountry_id(String country_id) {
        this.country_id = country_id;
    }

    public String getCountry_name() {
        return country_name;
    }

    public void setCountry_name(String country_name) {
        this.country_name = country_name;
    }

    public Long getHas_data() {
        return has_data;
    }

    public void setHas_data(Long has_data) {
        this.has_data = has_data;
    }

    public Long getDefault_mazhab() {
        return default_mazhab;
    }

    public void setDefault_mazhab(Long default_mazhab) {
        this.default_mazhab = default_mazhab;
    }

    public Long getDefault_calc_method() {
        return default_calc_method;
    }

    public void setDefault_calc_method(Long default_calc_method) {
        this.default_calc_method = default_calc_method;
    }


}

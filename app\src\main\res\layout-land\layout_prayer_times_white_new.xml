<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:layoutDirection="rtl"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/contentFajr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.white_new"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="horizontal">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/fajr_TextView_MainActivity"
                        style="@style/TimeTextView.white_new.TimeNameAR"
                        android:text="الفجر" />

                    <TextView
                        style="@style/TimeTextView.white_new.TimeNameEN"
                        android:textSize="@dimen/_14sdp"
                        android:text="Fajr" />
                </LinearLayout>
                <include
                    android:id="@+id/tv_prayer_ikama_time_fajr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side"
                android:layoutDirection="ltr">


                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.Time" />

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.TimeType" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.white_new"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="horizontal">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivity"
                        style="@style/TimeTextView.white_new.TimeNameAR"
                        android:text="الظهر" />

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivityE"
                        style="@style/TimeTextView.white_new.TimeNameEN"
                        android:textSize="@dimen/_14sdp"

                        android:text="Dhuhr" />
                </LinearLayout>
                <include
                    android:id="@+id/tv_prayer_ikama_time_dhur"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side"
                android:layoutDirection="ltr">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.Time" />

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.TimeType" />


            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentAsr_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.white_new"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="horizontal">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/asr_TextView_MainActivity"
                        style="@style/TimeTextView.white_new.TimeNameAR"
                        android:text="العصر" />

                    <TextView
                        android:textSize="@dimen/_14sdp"
                        style="@style/TimeTextView.white_new.TimeNameEN"
                        android:text="Asr" />
                </LinearLayout>
                <include
                    android:id="@+id/tv_prayer_ikama_time_asr"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side"
                android:layoutDirection="ltr">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.Time" />

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.TimeType" />


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.white_new"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="horizontal">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/maghrib_TextView_MainActivity"
                        style="@style/TimeTextView.white_new.TimeNameAR"
                        android:text="المغرب" />

                    <TextView
                        style="@style/TimeTextView.white_new.TimeNameEN"
                        android:textSize="@dimen/_14sdp"
                        android:text="Maghrib" />
                </LinearLayout>
                <include
                    android:id="@+id/tv_prayer_ikama_time_maghrib"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side"
                android:layoutDirection="ltr">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.Time" />

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.TimeType" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentIsha_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.white_new"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="horizontal">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/isha_TextView_MainActivity"
                        style="@style/TimeTextView.white_new.TimeNameAR"
                        android:text="العشاء" />

                    <TextView
                        style="@style/TimeTextView.white_new.TimeNameEN"
                        android:textSize="@dimen/_14sdp"
                        android:text="Isha" />
                </LinearLayout>
                <include
                    android:id="@+id/tv_prayer_ikama_time_isha"
                    layout="@layout/textview_circle_grey"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side"
                android:layoutDirection="ltr">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.Time" />

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.TimeType" />


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/contentSunrise_LinearLayout_MainActivity"
        style="@style/LinearLayoutPrayerTimeRow.white_new"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="4dp"
        android:orientation="vertical">

        <LinearLayout style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.Side"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity"
                        style="@style/TimeTextView.white_new.TimeNameAR"
                        android:text="الضحى"
                        android:layout_marginTop="0dp"
                        android:layout_marginBottom="0dp"
                        android:textSize="@dimen/_18sdp" />

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivityE"
                        style="@style/TimeTextView.white_new.TimeNameEN"
                        android:layout_marginTop="0dp"

                        android:layout_marginBottom="0dp"
                        android:text="Duha"
                        android:textSize="@dimen/_12sdp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseATime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.Side"
                android:layoutDirection="ltr">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.Time"
                    android:layout_marginTop="0dp"
                    android:layout_marginBottom="0dp"
                    android:textSize="@dimen/_20sdp" />

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/TimeTextView.white_new.TimeType"
                    android:textSize="@dimen/_10sdp" />


            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


</LinearLayout>
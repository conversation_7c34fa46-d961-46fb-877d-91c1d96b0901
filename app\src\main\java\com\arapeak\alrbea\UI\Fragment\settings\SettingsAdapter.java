package com.arapeak.alrbea.UI.Fragment.settings;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Model.SettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.List;

public class SettingsAdapter extends RecyclerView.Adapter<SettingsAdapter.SettingViewHolder> {

    public static final String TAG = "SettingsAdapter";


    private final Context context;
    private final List<SettingAlrabeeaTimes> arrayListItem;
    private final LayoutInflater layoutInflater;
    private final AdapterCallback mCallback;

    public SettingsAdapter(Context context
            , List<SettingAlrabeeaTimes> arrayListItem
            , AdapterCallback mCallback) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;

        layoutInflater = LayoutInflater.from(this.context);
    }


    @NonNull
    @Override
    public SettingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        View view = layoutInflater.inflate(R.layout.layout_list_item_setting, parent, false);

        SettingViewHolder viewHolder = new SettingViewHolder(view);

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull SettingViewHolder savedVoiceViewHolder, int position) {

        savedVoiceViewHolder.onBind(position);
    }

    @Override
    public int getItemCount() {
        return arrayListItem.size();
    }


    public void add(SettingAlrabeeaTimes item) {
        if (item == null) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.add(item);
        notifyItemInserted(lastItemIndex);
        notifyDataSetChanged();
    }

    public void addAll(List<SettingAlrabeeaTimes> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public void clear() {
        arrayListItem.clear();
        notifyDataSetChanged();
    }

    public SettingAlrabeeaTimes getItem(int position) {
        if (position < 0 || position >= getItemCount()) {
            return null;
        }
        return arrayListItem.get(position);
    }

    class SettingViewHolder extends RecyclerView.ViewHolder {

        private final LinearLayout contentLayout;
        private final CardView iconCardView;
        private final ImageView iconImageView;
        private final LinearLayout titleDescriptionLinearLayout;
        private final TextView titleTextView;
        private final ImageLoader imageLoader;
        private final TextView isNew;
        private TextView descriptionTextView;
        private View spaceView;

        public SettingViewHolder(@NonNull View itemView) {
            super(itemView);

            contentLayout = itemView.findViewById(R.id.content_LinearLayout_SettingViewHolder);
            iconCardView = itemView.findViewById(R.id.icon_CardView_SettingViewHolder);
            iconImageView = itemView.findViewById(R.id.train);
            titleDescriptionLinearLayout = itemView.findViewById(R.id.titleDescription_LinearLayout_SettingViewHolder);
            titleTextView = itemView.findViewById(R.id.title_TextView_SettingViewHolder);
            isNew = itemView.findViewById(R.id.isNew_TextView_SettingViewHolder);
            //  descriptionTextView = itemView.findViewById(R.id.description_TextView_SettingViewHolder);
            // spaceView = itemView.findViewById(R.id.space_View_SettingViewHolder);
            imageLoader = ImageLoader.getInstance();
        }

        public void onBind(final int position) {

            final SettingAlrabeeaTimes settingAlrabeeaTimes = arrayListItem.get(position);

            titleTextView.setText(settingAlrabeeaTimes.getTitle());

            if (isNew != null) {
                if (settingAlrabeeaTimes.getIsNew()) {
                    isNew.setVisibility(View.VISIBLE);
                } else {
                    isNew.setVisibility(View.GONE);
                }
            }
            //   descriptionTextView.setText(settingAlrabeeaTimes.getDescription());

            //  if (descriptionTextView.getText().toString().trim().isEmpty()) {
            //       descriptionTextView.setVisibility(View.GONE);
            //   } else {
//                descriptionTextView.setVisibility(View.VISIBLE);
            //  }
            try {
                if (settingAlrabeeaTimes.getIconResourcesId() <= 0) {
                    iconCardView.setVisibility(View.GONE);
                } else {

//                    imageLoader.displayImage("drawable://"+settingAlrabeeaTimes.getIconResourcesId() , iconImageView);
//                    Picasso.get().load(settingAlrabeeaTimes.getIconResourcesId()).into(iconImageView);
                    iconImageView.setImageResource(settingAlrabeeaTimes.getIconResourcesId());
                }
            } catch (Exception e) {
                Log.e(TAG, "Error: " + e.getMessage());
                iconCardView.setVisibility(View.GONE);
                CrashlyticsUtils.INSTANCE.logException(e);
            }

            if (position + 1 >= getItemCount()) {
                //     spaceView.setVisibility(View.GONE);
            } else {
//                spaceView.setVisibility(View.VISIBLE);
            }


            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCallback != null) {
                        mCallback.onItemClick(getAdapterPosition(), settingAlrabeeaTimes.getTitle());
                    }
                }
            });
        }
    }
}
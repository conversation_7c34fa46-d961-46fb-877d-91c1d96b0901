package com.arapeak.alrbrea.core_ktx

import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigDelay
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigInterval
import com.arapeak.alrbrea.core_ktx.model.screensaver.TimingConfigPeriod
import kotlinx.datetime.LocalTime
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@RunWith(RobolectricTestRunner::class)
class TimingConfigTest {

    @Before
    fun setUp() {

    }

    @Test
    fun test_timing_config_1() {

        val l = TimingConfigDelay(30)

        Assert.assertEquals(l.validate().first, true)
        Assert.assertEquals(l.convertToSave(), "30")



        l.parseFromSave("400")

        Assert.assertEquals(l.validate().first, true)
        Assert.assertEquals(l.getDelayMinutes(), 400)

    }


    @Test
    fun test_timing_config_2() {
        val from = LocalTime(hour = 12, minute = 30)
        val to = LocalTime(hour = 17, minute = 30)

        val l = TimingConfigPeriod(from, to)
        val l2 = TimingConfigPeriod(to, from)
        val l3 = TimingConfigPeriod(to, to)

        Assert.assertEquals(l.validate().first, true)
        Assert.assertEquals(l2.validate().first, false)
        Assert.assertEquals(l3.validate().first, false)


        Assert.assertEquals(l.convertToSave(), "12:30-17:30")



        l.parseFromSave("12:45-19:30")
//        l2.parseFromSave("08:10-02:00")

        Assert.assertEquals(l.validate().first, true)
        Assert.assertEquals(l2.validate().first, true)
        Assert.assertEquals(l.getFrom(), LocalTime(hour = 12, minute = 45))
        Assert.assertEquals(l.getTo(), LocalTime(hour = 19, minute = 30))

    }


    @Test
    fun test_timing_config_3() {

        val l = TimingConfigInterval(30, 5)

        Assert.assertEquals(l.validate().first, true)
        Assert.assertEquals(l.convertToSave(), "30-5")



        l.parseFromSave("34-3")

        Assert.assertEquals(l.validate().first, true)
        Assert.assertEquals(l.getOnIntervalMinutes(), 34)
        Assert.assertEquals(l.getOffIntervalMinutes(), 3)

    }
}
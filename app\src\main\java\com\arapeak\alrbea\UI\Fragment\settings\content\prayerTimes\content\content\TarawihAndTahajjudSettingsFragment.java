package com.arapeak.alrbea.UI.Fragment.settings.content.prayerTimes.content.content;

import static com.arapeak.alrbea.APIs.ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.MINUTES_MILLI_SECOND;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.OPTION_OF_TAHAJJUD_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.OPTION_OF_TAHAJJUD_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.OPTION_OF_TARAWIH_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.OPTION_OF_TARAWIH_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.PRAYER_TIME_ANNOUNCEMENT_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TAHAJJUD_KEY_H;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_DEFAULT;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_DURATION_OF_TARAWIH_KEY_H;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_IS_ENABLE_TAHAJJUD_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.RAMADAN_IS_ENABLE_TARAWIH_KEY;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TAHAJJUD_PRAYER_TIME_HOUR;
import static com.arapeak.alrbea.APIs.ConstantsOfApp.TAHAJJUD_PRAYER_TIME_MINUTE;

import android.app.TimePickerDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.APIs.ConstantsOfApp;
import com.arapeak.alrbea.Enum.PrayerType;
import com.arapeak.alrbea.Enum.ViewsAlrabeeaTimes;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Interface.PrayerTime;
import com.arapeak.alrbea.Interface.SettingsAdapterCallback;
import com.arapeak.alrbea.Model.SubSettingAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.UI.Activity.SettingsActivity;
import com.arapeak.alrbea.UI.CustomView.AlrabeeaTimesFragment;
import com.arapeak.alrbea.UI.Fragment.settings.content.main.content.mainSettings.MainSettingsAdapter;
import com.arapeak.alrbea.Utils;
import com.orhanobut.hawk.Hawk;

import java.util.ArrayList;
import java.util.Calendar;
//import static com.arapeak.alrbea.UI.Activity.MainActivity.timingsAlrabeeaTimes;

public class TarawihAndTahajjudSettingsFragment extends AlrabeeaTimesFragment implements SettingsAdapterCallback {

    public static final String NAME_OF_PRAYER_ARG = "nameOfPrayer";
    public static final String IS_RAMADAN_SETTINGS_ARG = "isRamadanSettings";
    private static final String TAG = "Tar&TahSettingsFragment";
    private View tarawihAndTahajjud;
    private AppCompatCheckBox isEnableCheckBox;
    private RadioGroup optionOfTarawihAndTahajjudRadioGroup;
    private AppCompatRadioButton activationOfTheDeclarationOfPrayerAppCompatRadioButton;
    private AppCompatRadioButton enableAndStopGalleryAppCompatRadioButton;
    private RecyclerView settingItemRecyclerView;

    private MainSettingsAdapter mainSettingsAdapter;
    private String nameOfPrayer;
    private boolean isTarawih;
    private boolean isRamadanSettings;
//    private TimingsAlrabeeaTimes timingsAlrabeeaTimes;


    public TarawihAndTahajjudSettingsFragment() {

    }

    public static TarawihAndTahajjudSettingsFragment newInstance(String nameOfPrayer, boolean isRamadanSettings) {

        Bundle args = new Bundle();

        TarawihAndTahajjudSettingsFragment fragment = new TarawihAndTahajjudSettingsFragment();
        args.putString(NAME_OF_PRAYER_ARG, nameOfPrayer);
        args.putBoolean(IS_RAMADAN_SETTINGS_ARG, isRamadanSettings);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            nameOfPrayer = getArguments().getString(NAME_OF_PRAYER_ARG, "");
            isRamadanSettings = getArguments().getBoolean(IS_RAMADAN_SETTINGS_ARG, false);
            isTarawih = nameOfPrayer.equalsIgnoreCase(getString(R.string.tarawih));
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        PrayerTime.EnableRamadanForTesting = true;
        tarawihAndTahajjud = inflater.inflate(R.layout.fragment_tarawih_and_tahajjud_settings, container, false);
        initView();
        SetParameter();
        SetAction();

        return tarawihAndTahajjud;
    }

    @Override
    public void onDestroy() {
        PrayerTime.EnableRamadanForTesting = false;
        super.onDestroy();
    }

    private void initView() {
        isEnableCheckBox = tarawihAndTahajjud.findViewById(R.id.isEnable_CheckBox_TarawihAndTahajjudSettingsFragment);
        optionOfTarawihAndTahajjudRadioGroup = tarawihAndTahajjud.findViewById(R.id.optionOfTarawihAndTahajjud_RadioGroup_TarawihAndTahajjudSettingsFragment);
        activationOfTheDeclarationOfPrayerAppCompatRadioButton = tarawihAndTahajjud.findViewById(R.id.activationOfTheDeclarationOfPrayer_AppCompatRadioButton_TarawihAndTahajjudSettingsFragment);
        enableAndStopGalleryAppCompatRadioButton = tarawihAndTahajjud.findViewById(R.id.enableAndStopGallery_AppCompatRadioButton_TarawihAndTahajjudSettingsFragment);
        settingItemRecyclerView = tarawihAndTahajjud.findViewById(R.id.settingItem_RecyclerView_TarawihAndTahajjudSettingsFragment);

        mainSettingsAdapter = new MainSettingsAdapter(getContext(), new ArrayList<SubSettingAlrabeeaTimes>(), this);

        TextView title = tarawihAndTahajjud.findViewById(R.id.tv_prayer_time_title);
        title.setText(nameOfPrayer);

//        timingsAlrabeeaTimes = Hawk.get(TIMINGS_ALRABEEA_TIMES_NOW_KEY);
//        timingsAlrabeeaTimes = MainActivity.timingsAlrabeeaTimes;
    }

    private void SetParameter() {
        setTitle();
        settingItemRecyclerView.setAdapter(mainSettingsAdapter);

        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes;

//        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(""+ (isTarawih ?
//                getString(R.string.current_prayer_time) +  Utils.getTimeWith12( timingsAlrabeeaTimes.getIshaWithAdjustmentTaraw(true))+" " + "\n "+
//                        getString(R.string.current_tarawih)+""+timingsAlrabeeaTimes.getTimeBetweenAdanAndIkamaToIshaAll(true)
//: Utils.getTimeWith12(Hawk.get(RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME,POST_OR_PRE_PRAY_TAHAJJUD_DEFAULT)))
        long tarawihTime = PrayerType.Tarawih.prayerTime.getAzanTime();
        long tahajjudTime = PrayerType.Tahajjud.prayerTime.getAzanTime();
        long ishaDuration = (PrayerType.Isha.prayerTime.getTimeUntilUnlockAthkar() - PrayerType.Isha.prayerTime.getAzanTime()) / MINUTES_MILLI_SECOND;
//        long tarawihPostAfterIshaDuration = PrayerType.Isha.prayerTime.getTimeUntilUnlockAthkar();
        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.current_prayer_time) +
                (isTarawih
                        ? Utils.getTimeWith12(Utils.getTimeFormatted(tarawihTime, "hh:mm a")) + "\n"
                        + getString(R.string.current_tarawih) + ishaDuration

                        : Utils.getTimeFormatted(tahajjudTime, "hh:mm a"))
                , ViewsAlrabeeaTimes.BUTTON
                , "تعديل"
                , true);
        subSettingAlrabeeaTimes.setShowEnableButton(false);
        mainSettingsAdapter.add(subSettingAlrabeeaTimes);


        subSettingAlrabeeaTimes = new SubSettingAlrabeeaTimes(getString(R.string.declaration_of_prayer) + "\n" +
                getString(R.string.the_time_it_takes_to_finish_prayer) + ":" + Hawk.get(isTarawih
                        ? RAMADAN_DURATION_OF_TARAWIH_KEY
                        : RAMADAN_DURATION_OF_TAHAJJUD_KEY
                , isTarawih
                        ? RAMADAN_DURATION_OF_TARAWIH_DEFAULT
                        : RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT)
                , ViewsAlrabeeaTimes.BUTTON
                , "تعديل"


                , true);
        subSettingAlrabeeaTimes.setShowEnableButton(false);

        mainSettingsAdapter.add(subSettingAlrabeeaTimes);

        isEnableCheckBox.setChecked(Hawk.get(isTarawih
                        ? RAMADAN_IS_ENABLE_TARAWIH_KEY
                        : RAMADAN_IS_ENABLE_TAHAJJUD_KEY
                , IS_ENABLE_SETTINGS_DEFAULT));

        if (isEnableCheckBox.isChecked()) {
            optionOfTarawihAndTahajjudRadioGroup.setVisibility(View.VISIBLE);
        } else {
            optionOfTarawihAndTahajjudRadioGroup.setVisibility(View.GONE);
        }

        if (isTarawih) {
            activationOfTheDeclarationOfPrayerAppCompatRadioButton
                    .setText(R.string.activation_of_the_declaration_of_tarawih_prayer);
            int optionOfTarawihAndTahajjud = Hawk.get(OPTION_OF_TARAWIH_KEY, OPTION_OF_TARAWIH_DEFAULT);
            if (optionOfTarawihAndTahajjud == 1) {
                activationOfTheDeclarationOfPrayerAppCompatRadioButton.setChecked(true);
            } else {
                enableAndStopGalleryAppCompatRadioButton.setChecked(true);
            }
        } else {
            activationOfTheDeclarationOfPrayerAppCompatRadioButton
                    .setText(R.string.activation_of_the_declaration_of_tahajjud_prayer);

            int optionOfTarawihAndTahajjud = Hawk.get(OPTION_OF_TAHAJJUD_KEY, OPTION_OF_TAHAJJUD_DEFAULT);
            if (optionOfTarawihAndTahajjud == 1) {
                activationOfTheDeclarationOfPrayerAppCompatRadioButton.setChecked(true);
            } else {
                enableAndStopGalleryAppCompatRadioButton.setChecked(true);
            }
        }
        enableAndStopGalleryAppCompatRadioButton
                .setText(R.string.enable_and_stop_gallery);


    }

    private void SetAction() {
        isEnableCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    optionOfTarawihAndTahajjudRadioGroup.setVisibility(View.VISIBLE);
                } else {
                    optionOfTarawihAndTahajjudRadioGroup.setVisibility(View.GONE);
                }
                Hawk.put(isTarawih
                                ? RAMADAN_IS_ENABLE_TARAWIH_KEY
                                : RAMADAN_IS_ENABLE_TAHAJJUD_KEY
                        , isChecked);
                Hawk.delete(isTarawih ? OPTION_OF_TARAWIH_KEY : OPTION_OF_TAHAJJUD_KEY);
            }
        });

        optionOfTarawihAndTahajjudRadioGroup.setOnCheckedChangeListener((group, checkedId) -> Hawk.put(isTarawih ? OPTION_OF_TARAWIH_KEY
                        : OPTION_OF_TAHAJJUD_KEY
                , checkedId == activationOfTheDeclarationOfPrayerAppCompatRadioButton.getId()
                        ? 1
                        : 2));
    }

    private void setTitle() {
        if (nameOfPrayer.equalsIgnoreCase(getString(R.string.tarawih))) {
//            if (Utils.isLandscape()) {
//                SettingsLandscapeActivity.setTextTite(getString(R.string.tarawih_prayer));
//            } else {
//                SettingsActivity.setTextTite(getString(R.string.tarawih_prayer));
//            }
            SettingsActivity.setTextTite(getString(R.string.tarawih_prayer));
        } else if (nameOfPrayer.equalsIgnoreCase(getString(R.string.tahajjud))) {
//            if (Utils.isLandscape()) {
//                SettingsLandscapeActivity.setTextTite(getString(R.string.tahajjud_prayer));
//            } else {
//                SettingsActivity.setTextTite(getString(R.string.tahajjud_prayer));
//            }
            SettingsActivity.setTextTite(getString(R.string.tahajjud_prayer));
        }
    }

    @Override
    public void onItemClick(ViewsAlrabeeaTimes viewsAlrabeeaTimes, int position, int subPosition, String tag) {
        SubSettingAlrabeeaTimes subSettingAlrabeeaTimes = mainSettingsAdapter.getItem(position);
        int delayOFTarawih = getDelayOFTarawih();
        switch (position) {
            case 0:
                if (isTarawih) {

                    Utils.initConfirmDialogt(getAppCompatActivity()
                            , 0
                            , getString(R.string.prayer_time_define)
                            , "تعديل"
                            , true
                            , true
                            , new OnSuccessful() {
                                @Override
                                public void onSuccessful(boolean isSuccessful) {
                                    if (isSuccessful) {

                                        long tarawihTime = PrayerType.Tarawih.prayerTime.getAzanTime();
                                        long ishaDuration = (PrayerType.Isha.prayerTime.getAzanTime() - PrayerType.Isha.prayerTime.getTimeUntilUnlockAthkar()) / MINUTES_MILLI_SECOND;

                                        subSettingAlrabeeaTimes.setTitle(Utils.getTimeWith12(Utils.getTimeFormatted(tarawihTime, "hh:mm a")) + "\n"
                                                + getString(R.string.current_tarawih) + ishaDuration);
//                                        subSettingAlrabeeaTimes.setTitle(  getString(R.string.current_prayer_time)+""+ Utils.getTimeWith12( timingsAlrabeeaTimes.getIshaWithAdjustmentTaraw(true))+" " + "\n "+
//                                                getString(R.string.current_tarawih)+""+timingsAlrabeeaTimes.getTimeBetweenAdanAndIkamaToIshaAll(true));
                                        mainSettingsAdapter.setItem(position, subSettingAlrabeeaTimes);

                                    }
                                }
                            });
                } else {

                    Calendar mcurrentTime = Calendar.getInstance();
//                    int hour = mcurrentTime.get(Calendar.HOUR_OF_DAY);
//                    int minute = mcurrentTime.get(Calendar.MINUTE);
                    int hour = Hawk.get(TAHAJJUD_PRAYER_TIME_HOUR, 0);
                    int minute = Hawk.get(TAHAJJUD_PRAYER_TIME_MINUTE, 0);
                    TimePickerDialog mTimePicker;
                    mTimePicker = new TimePickerDialog(getContext(), (timePicker, selectedHour, selectedMinute) -> {

                        Hawk.put(TAHAJJUD_PRAYER_TIME_HOUR, selectedHour);
                        Hawk.put(TAHAJJUD_PRAYER_TIME_MINUTE, selectedMinute);
//                        Hawk.put(RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY_TIME, selectedHour + ":" + selectedMinute);
                        subSettingAlrabeeaTimes.setTitle(getString(R.string.prayer_time_define) + "\n" + getString(R.string.current_prayer_time) + Utils.getTimeWith12(selectedHour + ":" + selectedMinute)
                        );
                        //  subSettingAlrabeeaTimes.setPositionDefaultValue(subPosition);
                        mainSettingsAdapter.setItem(0, subSettingAlrabeeaTimes);
                        // eReminderTime.setText( selectedHour + ":" + selectedMinute);
                    }, hour, minute, false);//Yes 24 hour time
                    mTimePicker.setTitle("Select Time");
                    mTimePicker.show();
                    //   Hawk.put(RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY, subPosition);


                }

                break;
            case 1:
                if (isTarawih) {
                    Utils.initConfirmDialogNumberRamadan(getAppCompatActivity()
                            , 0
                            , getString(R.string.prayer_time_define)
                            , "تعديل",
                            Hawk.get(RAMADAN_DURATION_OF_TARAWIH_KEY_H, 0), Hawk.get(RAMADAN_DURATION_OF_TARAWIH_KEY, 30), 0
                            , true
                            , true
                            , new OnSuccessful() {
                                @Override
                                public void onSuccessful(boolean isSuccessful, int h, int m, int s) {
                                    if (isSuccessful) {
                                        Hawk.put(
                                                RAMADAN_DURATION_OF_TARAWIH_KEY_H
                                                , h);
                                        Hawk.put(
                                                RAMADAN_DURATION_OF_TARAWIH_KEY
                                                , m);
                                        subSettingAlrabeeaTimes.setTitle(getString(R.string.declaration_of_prayer) + "\n" +
                                                getString(R.string.the_time_it_takes_to_finish_prayer) + ":" + Hawk.get(isTarawih
                                                        ? RAMADAN_DURATION_OF_TARAWIH_KEY
                                                        : RAMADAN_DURATION_OF_TAHAJJUD_KEY
                                                , isTarawih
                                                        ? RAMADAN_DURATION_OF_TARAWIH_DEFAULT
                                                        : RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT));
                                        mainSettingsAdapter.setItem(position, subSettingAlrabeeaTimes);
                                        //    subSettingAlrabeeaTimes.setTitle(getString(R.string.prayer_time_define) + "\n" + getDescription()
                                        //  );
                                        //  subSettingAlrabeeaTimes.setDescription(getDescription());
                                        // mainSettingsAdapter.setItem(position, subSettingAlrabeeaTimes);

                                    }
                                }
                            });
                } else {
                    Utils.initConfirmDialogNumberRamadan(getAppCompatActivity()
                            , 0
                            , getString(R.string.prayer_time_define)
                            , "تعديل",
                            Hawk.get(RAMADAN_DURATION_OF_TAHAJJUD_KEY_H, 0), Hawk.get(RAMADAN_DURATION_OF_TAHAJJUD_KEY, 30), 0
                            , true
                            , true
                            , new OnSuccessful() {
                                @Override
                                public void onSuccessful(boolean isSuccessful, int h, int m, int s) {
                                    if (isSuccessful) {
                                        Hawk.put(
                                                RAMADAN_DURATION_OF_TAHAJJUD_KEY_H
                                                , h);
                                        Hawk.put(
                                                RAMADAN_DURATION_OF_TAHAJJUD_KEY
                                                , m);

                                        subSettingAlrabeeaTimes.setTitle(getString(R.string.declaration_of_prayer) + "\n" +
                                                getString(R.string.the_time_it_takes_to_finish_prayer) + ":" + Hawk.get(isTarawih
                                                        ? RAMADAN_DURATION_OF_TARAWIH_KEY
                                                        : RAMADAN_DURATION_OF_TAHAJJUD_KEY
                                                , isTarawih
                                                        ? RAMADAN_DURATION_OF_TARAWIH_DEFAULT
                                                        : RAMADAN_DURATION_OF_TAHAJJUD_DEFAULT));
                                        mainSettingsAdapter.setItem(position, subSettingAlrabeeaTimes);

                                    }
                                }
                            });
                }

                break;
        }
    }

  /*  private String getDescription() {
        int delayOFTarawih = getDelayOFTarawih();
        int delayOFTarawihHawk = Hawk.get(RAMADAN_POST_OR_PRE_TO_TARAWIH_KEY, POST_OR_PRE_PRAY_TARAWIH_DEFAULT);


        return isTarawih ?
                getString(R.string.current_prayer_time)+":"+ timingsAlrabeeaTimes.getIshaWithAdjustmentTaraw(true)+" " + "\n "+
                        getString(R.string.current_tarawih)+""+timingsAlrabeeaTimes.getTimeBetweenAdanAndIkamaToIshaAll(true)
                :  getString(R.string.current_prayer_time).trim() + " "
                + Utils.getTimeWith12(  Hawk.get(RAMADAN_POST_OR_PRE_TO_TAHAJJUD_KEY
                , POST_OR_PRE_PRAY_TAHAJJUD_DEFAULT));
    }*/

    private int getDelayOFTarawih() {
        Object[] objects = Utils.putPrayKey(getContext(), ConstantsOfApp.ISHA_KEY, isRamadanSettings);

        int delayOFTarawih = ConstantsOfApp.DURATION_OF_AZHAR_DEFAULT;
        if (objects == null) {
            Log.e(TAG, "objects: null");
            return delayOFTarawih;
        }
        String nameOfPrayerKey = (String) objects[0];
        String postOrPreToPrayKey = (String) objects[1];
        int timeBetweenAdanAndIkamaDefault = (int) objects[2];
        String isEnablePostOrPrePrayKey = (String) objects[3];
        String isEnableTimeBetweenAdanAndIkamaKey = (String) objects[4];

        if (Hawk.get(getRamadanKey() + ConstantsOfApp.IS_ENABLE_KEY
                        + ConstantsOfApp.PRAYER_TIME_ANNOUNCEMENT_KEY
                        + nameOfPrayerKey
                , ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT)) {
            delayOFTarawih += PRAYER_TIME_ANNOUNCEMENT_DEFAULT;
        }
        if (Hawk.get(isEnablePostOrPrePrayKey, ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT)) {
            delayOFTarawih += Hawk.get(postOrPreToPrayKey,
                    isRamadanSettings
                            ? ConstantsOfApp.RAMADAN_POST_OR_PRE_PRAY_DEFAULT
                            : ConstantsOfApp.POST_OR_PRE_PRAY_DEFAULT);
        }

        if (Hawk.get(isEnableTimeBetweenAdanAndIkamaKey, ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT)) {
            delayOFTarawih += Hawk.get(getRamadanKey()
                    + ConstantsOfApp.TIME_BETWEEN_ADAN_AND_IKAMA_KEY
                    + nameOfPrayerKey, timeBetweenAdanAndIkamaDefault);
        }
        if (Hawk.get(getRamadanKey()
                        + ConstantsOfApp.IS_ENABLE_KEY
                        + ConstantsOfApp.LOCK_DURING_PRAYER_KEY
                        + nameOfPrayerKey
                , ConstantsOfApp.IS_ENABLE_SETTINGS_DEFAULT)) {
            delayOFTarawih += Hawk.get(getRamadanKey()
                            + ConstantsOfApp.LOCK_DURING_PRAYER_KEY
                            + nameOfPrayerKey
                    , Utils.getTimeToFinishThePrayer(getAppCompatActivity(), ConstantsOfApp.ISHA_KEY, isRamadanSettings));
        }
        return delayOFTarawih;
    }

    public String getRamadanKey() {
        return isRamadanSettings ? ConstantsOfApp.RAMADAN_KEY : "";
    }

}

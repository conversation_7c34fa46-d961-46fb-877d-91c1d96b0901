<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"
        android:minSdkVersion="33" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_VIDEO"
        android:minSdkVersion="33" />
    <uses-permission
        android:name="android.permission.READ_MEDIA_AUDIO"
        android:minSdkVersion="33" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" /> <!-- <uses-permission android:name="android.permission.GET_TASKS" /> -->
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <application
        android:name=".AppController"
        android:allowBackup="true"
        android:extractNativeLibs="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppThemeNoActionBar"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".beshr.ui.BeshrActivity"
            android:exported="false" />
        <!--
            android:configChanges="orientation|keyboardHidden|screenSize"

            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize"
        -->
        <meta-data
            android:name="io.sentry.dsn"
            android:value="https://<EMAIL>/6628579" />
        <!--
 Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
           We recommend adjusting this value in production.
        -->
        <meta-data
            android:name="io.sentry.traces.sample-rate"
            android:value="1.0" />
        <meta-data
            android:name="io.sentry.anr.enable"
            android:value="false" />
        <meta-data
            android:name="io.sentry.anr.timeout-interval-millis"
            android:value="2000" />
        <!--             android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize" -->
        <activity
            android:name=".UI.Activity.SplashScreen"
            android:clearTaskOnLaunch="true"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:stateNotNeeded="true"
            android:windowSoftInputMode="stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!--             android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize" -->
        <activity
            android:name=".UI.Activity.InitialSetupActivity"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="stateHidden"></activity>
        <activity
            android:name=".UI.Activity.MainActivity"
            android:clearTaskOnLaunch="true"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:stateNotNeeded="true"
            android:theme="@style/SplashTheme"></activity> <!-- <activity android:name=".AmbilWarnaDemoPreferenceActivity" /> -->
        <activity android:name=".UI.Activity.Country.CountryActivity" />
        <!--                     android:screenOrientation="portrait" -->
        <activity android:name=".UI.Activity.SettingsActivity" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="cvio.api_key_id"
            android:value="4NVhEFFGL8NJHTiBJ01A2A" />
        <!--
                <provider
                    android:name="androidx.core.content.FileProvider"
                    android:authorities="com.arapeak.alrbea.fileprovider"
                    android:exported="false"
                    android:grantUriPermissions="true">
                    <meta-data
                        android:name="android.support.FILE_PROVIDER_PATHS"
                        android:resource="@xml/my_provider" /> &lt;!&ndash; Create this XML file in the res/xml directory &ndash;&gt;
                </provider>
        -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/my_provider" />
        </provider> <!-- <provider -->
        <!-- android:name="androidx.core.content.FileProvider" -->
        <!-- android:authorities="${applicationId}.provider" -->
        <!-- android:exported="false" -->
        <!-- android:grantUriPermissions="true"> -->
        <!-- <meta-data -->
        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
        <!-- android:resource="@xml/my_provider" /> -->
        <!-- </provider> -->
        <!-- <provider -->
        <!-- android:name="com.werb.pickphotoview.provider.PickProvider" -->
        <!-- android:authorities="${applicationId}.provider" -->
        <!-- android:exported="false" -->
        <!-- android:grantUriPermissions="true"> -->
        <!-- <meta-data -->
        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
        <!-- android:resource="@xml/my_provider"/> -->
        <!-- </provider> -->
        <receiver
            android:name=".UI.Activity.mainActivityExt.RefreshReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="APP_REFRESH_BROADCAST" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".BroadcastReceiver.BootUpReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".BroadcastReceiver.AlarmReceiver"
            android:enabled="true"
            android:exported="false" />

        <service
            android:name=".Service.AppMonitorService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="systemExempted" />
    </application>

</manifest>
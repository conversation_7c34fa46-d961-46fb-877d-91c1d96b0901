package com.arapeak.alrbrea.core_ktx.ui.utils

import android.content.Context
import android.os.Environment
import android.util.Log
import com.arapeak.alrbrea.core_ktx.R
import java.io.File

fun downloadUpdate(context: Context, url: String, version: String, onFail: (String) -> Unit, onSuccess: (String) -> Unit, onProgress: (Int) -> Unit) {
    val TAG = "AppUpdater"

    if (url.isBlank())
        return onFail("Invalid URL")

    var filePath = getOutputFiles(context)?.absolutePath
    val fileName = "AlrabeeaTimes.apk"

    val lastBytesDownloaded = longArrayOf(0)
    val speedOfDownloadFile = longArrayOf(0)

    var appName = context.getString(R.string.app_name) + version + ".apk"
    val appUrl: Array<String> = url.split("/".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()

    if (url.endsWith("apk") && appUrl.isNotEmpty()) {
        appName = appUrl[appUrl.size - 1]
    }
    Log.d(TAG, "downloadApkFile appName: " + appName.trim { it <= ' ' })


    val file = getOutputFilesForApp(context)
    if (file != null) {
        filePath = file.absolutePath
    }
    val finalAppName = appName
    val finalFilePath = filePath !!

}


fun getOutputFiles(context: Context): File? {
    val mediaStorageDir = File(
        Environment.getExternalStorageDirectory()
            .toString() + "/Android/data/"
                + context.packageName
                + "/AlrabeeaTimes/Files"
    )

    if (! mediaStorageDir.exists()) {
        if (! mediaStorageDir.mkdirs()) {
            return null
        }
    }
    val mediaFile = File(mediaStorageDir.path + File.separator)
    return mediaFile
}

fun getOutputFilesForApp(context: Context): File? {

    val mediaStorageDir = File(
        Environment.getExternalStorageDirectory()
            .toString() + "/Android/data/"
                + context.packageName
                + "/AlrabeeaTimes/Files"
    )

    if (! mediaStorageDir.exists()) {
        if (! mediaStorageDir.mkdirs()) {
            return null
        }
    }
    val mediaFile = File(mediaStorageDir.path)
    return mediaFile
}
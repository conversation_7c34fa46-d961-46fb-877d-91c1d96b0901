package com.arapeak.alrbea.UI.uicontainers;

import android.app.Activity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.arapeak.alrbea.R;

import android.app.Activity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.arapeak.alrbea.R; // Adjust if your R file is elsewhere

public class PrayerUILayouts {
    // Main prayer display containers
    public final ViewGroup prayerTimeContainer;
    public final ViewGroup announcementContainer;
    public final ViewGroup athkarContainer; // Though AthkarManager will primarily use AthkarUILayouts

    // General prayer UI elements
    public final TextView remainingPrayerTextView;
    public final TextView azanTextView, prayerTextView, ikamaTextView; // Headers

    // Fajr
    public final LinearLayout contentFajrLayout;
    public final TextView fajrTextView, fajrTimeTextView, fajrTimeTypeTextView, fajrATimeTextView, fajrATimeTypeTextView, remainingFajrTextView;
    public final LinearLayout fajrTimeLinearLayout, fajrATimeLinearLayout;
    public final ImageView imagefajrView;
    public final TextView tvIkamaDelayFajr;


    // Sunrise/Duha (handle dual nature)
    public final LinearLayout contentSunriseLayout;
    public final TextView sunriseTextView, sunriseTextViewe, sunriseTimeTextView, sunriseTimeTypeTextView, sunriseATimeTextView, sunriseATimeTypeTextView, remainingSunriseTextView;
    public final LinearLayout sunriseTimeLinearLayout, sunriseATimeLinearLayout;
    public final ImageView imageSunriseView;
    public final View tvSunriseEnglishName; // The specific R.id.sunrise_TextView_MainActivity_en if different from sunriseTextViewe

    // Dhuhr/Jomaa
    public final LinearLayout contentDhuhrLayout;
    public final TextView dhuhrTextView, dhuhrTextViewe, dhuhrTimeTextView, dhuhrTimeTypeTextView, dhuhrATimeTextView, dhuhrATimeTypeTextView, remainingDhuhrTextView;
    public final LinearLayout dhuhrTimeLinearLayout, dhuhrATimeLinearLayout;
    public final ImageView imagedhuhrView;
    public final TextView tvIkamaDelayDhur;

    // Asr
    public final LinearLayout contentAsrLayout;
    public final TextView asrTextView, asrTimeTextView, asrTimeTypeTextView, asrATimeTextView, asrATimeTypeTextView, remainingAsrTextView;
    public final LinearLayout asrTimeLinearLayout, asrATimeLinearLayout;
    public final ImageView imageasrView;
    public final TextView tvIkamaDelayAsr;

    // Maghrib
    public final LinearLayout contentMaghribLayout;
    public final TextView maghribTextView, maghribTimeTextView, maghribTimeTypeTextView, maghribATimeTextView, maghribATimeTypeTextView, remainingMaghribTextView;
    public final LinearLayout maghribTimeLinearLayout, maghribATimeLinearLayout;
    public final ImageView imagemaghribView;
    public final TextView tvIkamaDelayMaghreb;

    // Isha
    public final LinearLayout contentIshaLayout;
    public final TextView ishaTextView, ishaTimeTextView, ishaTimeTypeTextView, ishaATimeTextView, ishaATimeTypeTextView, remainingIshaTextView;
    public final LinearLayout ishaTimeLinearLayout, ishaATimeLinearLayout;
    public final ImageView imageishaView;
    public final TextView tvIkamaDelayIsha;


    public PrayerUILayouts(Activity activity) {
        // Find all views, handling potential nulls if a theme doesn't have a view
        prayerTimeContainer = activity.findViewById(R.id.prayerTimeItem_include_MainActivity);
        announcementContainer = activity.findViewById(R.id.announcement_include_MainActivity);
        athkarContainer = activity.findViewById(R.id.layout_athkar); // Belongs more to AthkarUILayouts but shown for structure

        remainingPrayerTextView = activity.findViewById(R.id.remainingPrayer_TextView_MainActivity);
        azanTextView = activity.findViewById(R.id.azan_TextView_MainActivity);
        prayerTextView = activity.findViewById(R.id.prayer_TextView_MainActivity);
        ikamaTextView = activity.findViewById(R.id.ikama_TextView_MainActivity);

        // Fajr
        contentFajrLayout = activity.findViewById(R.id.contentFajr_LinearLayout_MainActivity);
        fajrTextView = activity.findViewById(R.id.fajr_TextView_MainActivity);
        fajrTimeTextView = activity.findViewById(R.id.fajrTime_TextView_MainActivity);
        fajrTimeTypeTextView = activity.findViewById(R.id.fajrTimeType_TextView_MainActivity);
        fajrATimeTextView = activity.findViewById(R.id.fajrATime_TextView_MainActivity);
        fajrATimeTypeTextView = activity.findViewById(R.id.fajrATimeType_TextView_MainActivity);
        remainingFajrTextView = activity.findViewById(R.id.remainingFajr_TextView_MainActivity);
        fajrTimeLinearLayout = activity.findViewById(R.id.fajrTime_LinearLayout_MainActivity);
        fajrATimeLinearLayout = activity.findViewById(R.id.fajrATime_LinearLayout_MainActivity);
        imagefajrView = activity.findViewById(R.id.imageFajr_View_MainActivity);
        tvIkamaDelayFajr = activity.findViewById(R.id.tv_prayer_ikama_time_fajr);

        // Sunrise/Duha
        contentSunriseLayout = activity.findViewById(R.id.contentSunrise_LinearLayout_MainActivity);
        sunriseTextView = activity.findViewById(R.id.sunrise_TextView_MainActivity);
        sunriseTextViewe = activity.findViewById(R.id.sunrise_TextView_MainActivityE); // English name
        sunriseTimeTextView = activity.findViewById(R.id.sunriseTime_TextView_MainActivity);
        sunriseTimeTypeTextView = activity.findViewById(R.id.sunriseTimeType_TextView_MainActivity);
        sunriseATimeTextView = activity.findViewById(R.id.sunriseATime_TextView_MainActivity); // Often for Duha
        sunriseATimeTypeTextView = activity.findViewById(R.id.sunriseATimeType_TextView_MainActivity);
        remainingSunriseTextView = activity.findViewById(R.id.remainingSunrise_TextView_MainActivity);
        sunriseTimeLinearLayout = activity.findViewById(R.id.sunriseTime_LinearLayout_MainActivity);
        sunriseATimeLinearLayout = activity.findViewById(R.id.sunriseATime_LinearLayout_MainActivity);
        imageSunriseView = activity.findViewById(R.id.imageSunrise_View_MainActivity);
        tvSunriseEnglishName = activity.findViewById(R.id.sunrise_TextView_MainActivity_en);


        // Dhuhr
        contentDhuhrLayout = activity.findViewById(R.id.contentDhuhr_LinearLayout_MainActivity);
        dhuhrTextView = activity.findViewById(R.id.dhuhr_TextView_MainActivity);
        dhuhrTextViewe = activity.findViewById(R.id.dhuhr_TextView_MainActivityE); // English name
        dhuhrTimeTextView = activity.findViewById(R.id.dhuhrTime_TextView_MainActivity);
        dhuhrTimeTypeTextView = activity.findViewById(R.id.dhuhrTimeType_TextView_MainActivity);
        dhuhrATimeTextView = activity.findViewById(R.id.dhuhrATime_TextView_MainActivity);
        dhuhrATimeTypeTextView = activity.findViewById(R.id.dhuhrATimeType_TextView_MainActivity);
        remainingDhuhrTextView = activity.findViewById(R.id.remainingDhuhr_TextView_MainActivity);
        dhuhrTimeLinearLayout = activity.findViewById(R.id.dhuhrTime_LinearLayout_MainActivity);
        dhuhrATimeLinearLayout = activity.findViewById(R.id.dhuhrATime_LinearLayout_MainActivity);
        imagedhuhrView = activity.findViewById(R.id.imageDhuhr_View_MainActivity);
        tvIkamaDelayDhur = activity.findViewById(R.id.tv_prayer_ikama_time_dhur);

        // Asr
        contentAsrLayout = activity.findViewById(R.id.contentAsr_LinearLayout_MainActivity);
        asrTextView = activity.findViewById(R.id.asr_TextView_MainActivity);
        asrTimeTextView = activity.findViewById(R.id.asrTime_TextView_MainActivity);
        asrTimeTypeTextView = activity.findViewById(R.id.asrTimeType_TextView_MainActivity);
        asrATimeTextView = activity.findViewById(R.id.asrATime_TextView_MainActivity);
        asrATimeTypeTextView = activity.findViewById(R.id.asrATimeType_TextView_MainActivity);
        remainingAsrTextView = activity.findViewById(R.id.remainingAsr_TextView_MainActivity);
        asrTimeLinearLayout = activity.findViewById(R.id.asrTime_LinearLayout_MainActivity);
        asrATimeLinearLayout = activity.findViewById(R.id.asrATime_LinearLayout_MainActivity);
        imageasrView = activity.findViewById(R.id.imageAsr_View_MainActivity);
        tvIkamaDelayAsr = activity.findViewById(R.id.tv_prayer_ikama_time_asr);

        // Maghrib
        contentMaghribLayout = activity.findViewById(R.id.contentMaghrib_LinearLayout_MainActivity);
        maghribTextView = activity.findViewById(R.id.maghrib_TextView_MainActivity);
        maghribTimeTextView = activity.findViewById(R.id.maghribTime_TextView_MainActivity);
        maghribTimeTypeTextView = activity.findViewById(R.id.maghribTimeType_TextView_MainActivity);
        maghribATimeTextView = activity.findViewById(R.id.maghribATime_TextView_MainActivity);
        maghribATimeTypeTextView = activity.findViewById(R.id.maghribATimeType_TextView_MainActivity);
        remainingMaghribTextView = activity.findViewById(R.id.remainingMaghrib_TextView_MainActivity);
        maghribTimeLinearLayout = activity.findViewById(R.id.maghribTime_LinearLayout_MainActivity);
        maghribATimeLinearLayout = activity.findViewById(R.id.maghribATime_LinearLayout_MainActivity);
        imagemaghribView = activity.findViewById(R.id.imageMaghrib_View_MainActivity);
        tvIkamaDelayMaghreb = activity.findViewById(R.id.tv_prayer_ikama_time_maghrib);

        // Isha
        contentIshaLayout = activity.findViewById(R.id.contentIsha_LinearLayout_MainActivity);
        ishaTextView = activity.findViewById(R.id.isha_TextView_MainActivity);
        ishaTimeTextView = activity.findViewById(R.id.ishaTime_TextView_MainActivity);
        ishaTimeTypeTextView = activity.findViewById(R.id.ishaTimeType_TextView_MainActivity);
        ishaATimeTextView = activity.findViewById(R.id.ishaATime_TextView_MainActivity);
        ishaATimeTypeTextView = activity.findViewById(R.id.ishaATimeType_TextView_MainActivity);
        remainingIshaTextView = activity.findViewById(R.id.remainingIsha_TextView_MainActivity);
        ishaTimeLinearLayout = activity.findViewById(R.id.ishaTime_LinearLayout_MainActivity);
        ishaATimeLinearLayout = activity.findViewById(R.id.ishaATime_LinearLayout_MainActivity);
        imageishaView = activity.findViewById(R.id.imageIsha_View_MainActivity);
        tvIkamaDelayIsha = activity.findViewById(R.id.tv_prayer_ikama_time_isha);
    }
}
package com.arapeak.alrbea.database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.arapeak.alrbea.Enum.IslamicEvent;
import com.arapeak.alrbea.Model.Event;
import com.arapeak.alrbea.database.events.EventSqlExecutioner;
import com.github.msarhan.ummalqura.calendar.UmmalquraCalendar;

import java.util.ArrayList;
import java.util.List;

public class MainDatabase extends SQLiteOpenHelper {

    public static final String DATABASE_NAME = "contactsManager";
    public static final String PHOTO_GALLERY = "photo_gallery";
    public static final String EVENT = "event";
    public static final String PHOTO_GALLERY_IMAGE = "photo_gallery_image";
    private static final int DATABASE_VERSION = 5;
    private final String createEventTable = "CREATE TABLE " + EVENT + " ("
            + "id INTEGER PRIMARY KEY AUTOINCREMENT ,"
            + "text TEXT ,"
            + "sCal INTEGER ,"
            + "eCal INTEGER ,"
            + "enabled INTEGER default 1 )";

    public MainDatabase(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
        //3rd argument to be passed is CursorFactory instance
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        String command = "CREATE TABLE " + PHOTO_GALLERY + " ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT ,"
                + " name TEXT )";
        db.execSQL(command);
        command = "CREATE TABLE " + PHOTO_GALLERY_IMAGE + " ("
                + "id INTEGER PRIMARY KEY AUTOINCREMENT ,"
                + "gallery_id INTEGER,"
                + " imageData TEXT)";
        db.execSQL(command);
        initEventTable(db);
    }

    void initEventTable(SQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS " + EVENT);
        db.execSQL(createEventTable);
        for (Event event : getAllEvents())
            EventSqlExecutioner.add(db, event);
    }

    private List<Event> getAllEvents() {
        ArrayList<Event> events = new ArrayList<>();

        for (IslamicEvent ev : IslamicEvent.values()) {
            Event event = new Event();
            if (ev == IslamicEvent.THE_NIGHT_OF_DECREE) {
                for (int i = 20; i < 28; i += 2) {
                    event = new Event();
                    event.text = ev.getName();

                    event.sCal = new UmmalquraCalendar();
                    event.sCal.set(UmmalquraCalendar.MONTH, ev.sMonth);
                    event.sCal.set(UmmalquraCalendar.DAY_OF_MONTH, i);
                    event.sCal.set(UmmalquraCalendar.HOUR_OF_DAY, 18);
                    event.sCal.set(UmmalquraCalendar.MINUTE, 0);
                    event.sCal.set(UmmalquraCalendar.SECOND, 0);

                    event.eCal = new UmmalquraCalendar();
                    event.eCal.set(UmmalquraCalendar.MONTH, ev.sMonth);
                    event.eCal.set(UmmalquraCalendar.DAY_OF_MONTH, i + 1);
                    event.eCal.set(UmmalquraCalendar.HOUR_OF_DAY, 4);
                    event.eCal.set(UmmalquraCalendar.MINUTE, 0);
                    event.eCal.set(UmmalquraCalendar.SECOND, 0);

                    event.enabled = true;
                    events.add(event);
                }
                continue;
            }
            event.text = ev.getName();

            event.sCal = new UmmalquraCalendar();
            event.sCal.set(UmmalquraCalendar.MONTH, ev.sMonth);
            event.sCal.set(UmmalquraCalendar.DAY_OF_MONTH, ev.sDay);
            event.sCal.set(UmmalquraCalendar.HOUR_OF_DAY, 0);
            event.sCal.set(UmmalquraCalendar.MINUTE, 0);
            event.sCal.set(UmmalquraCalendar.SECOND, 0);

            event.eCal = new UmmalquraCalendar();
            event.eCal.setTimeInMillis(event.sCal.getTimeInMillis());
            if (ev == IslamicEvent.FIRST_DAYS_OF_EID_AL_FITR)
                event.eCal.add(UmmalquraCalendar.DAY_OF_MONTH, 3);
            else if (ev == IslamicEvent.SIX_FROM_SHAWWAL) {
                event.eCal.add(UmmalquraCalendar.DAY_OF_MONTH, 23);
            } else
                event.eCal.add(UmmalquraCalendar.DAY_OF_MONTH, 1);

            event.enabled = true;
            events.add(event);
        }

        return events;
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        if (newVersion >= 3 && newVersion <= 5) initEventTable(db);
        else {
            db.execSQL("DROP TABLE IF EXISTS " + PHOTO_GALLERY);
            db.execSQL("DROP TABLE IF EXISTS " + PHOTO_GALLERY_IMAGE);
            onCreate(db);
        }
    }
}

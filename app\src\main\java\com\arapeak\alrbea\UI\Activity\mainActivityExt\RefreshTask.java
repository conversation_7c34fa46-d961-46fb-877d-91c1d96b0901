package com.arapeak.alrbea.UI.Activity.mainActivityExt;

import static com.arapeak.alrbrea.core_ktx.ui.utils.ContextExtKt.isActivityInForeground;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.util.Log;

import com.arapeak.alrbea.UI.Activity.MainActivity;
import com.arapeak.alrbrea.core_ktx.data.analytics.CrashlyticsUtils;

import java.util.Calendar;

public class RefreshTask {

    public void setUp(Context context) {
        try {
            Log.d("RefreshTask", "setUp refresh task");

            Intent intent = new Intent(context, RefreshReceiver.class);
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context,
                    0,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT);

// Set the desired time for the task (e.g., 8:00 AM)
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

// Schedule the task to repeat daily
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

            alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    calendar.getTimeInMillis(),
                    pendingIntent);


        } catch (Exception e) {
            CrashlyticsUtils.INSTANCE.logException(e);
        }

    }


    public void scheduleDailyTask(Context context, Handler handler, int hourOfDay, int minuteOfDay) {
        Calendar calendar = Calendar.getInstance();
        long now = calendar.getTimeInMillis();

        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
        calendar.set(Calendar.MINUTE, minuteOfDay);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // Calculate the delay until the next execution
        long delay = calendar.getTimeInMillis() - now;
        if (delay < 0) {
            // If the desired time has already passed today, schedule for tomorrow
            delay += AlarmManager.INTERVAL_DAY;
        }

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.d("RefreshReceiver", "Receive refresh task");
                if (isActivityInForeground(context, MainActivity.class)) {
                    Log.i("RefreshReceiver", "Activity is foreground restarting");
                    Intent restartIntent = new Intent(context, MainActivity.class);
                    restartIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    context.startActivity(restartIntent);
                }
            }
        }, delay);
    }

}

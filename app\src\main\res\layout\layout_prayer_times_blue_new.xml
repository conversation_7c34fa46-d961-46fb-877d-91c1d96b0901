<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentPrayerItem_ConstraintLayout_MainActivity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical"
    tools:background="@color/colorblack">

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.blue_new"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/contentFajr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_new.Right"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/fajr_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new.TimeNameAR"
                        android:text="الفجر" />

                    <TextView
                        style="@style/TimeTextView.blue_new.TimeNameEN"
                        android:text="Fajr" />
                </LinearLayout>
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_fajr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/fajrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">


                <TextView
                    android:id="@+id/fajrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.Time" />

                <TextView
                    android:id="@+id/fajrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.TimeType" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/remainingFajr_TextView_MainActivity"
            style="@style/TimeTextView.blue_new.TimeRemain"
            android:visibility="visible" />
    </LinearLayout>

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.blue_new"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/contentDhuhr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_new.Right"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new.TimeNameAR"
                        android:text="الظهر" />

                    <TextView
                        android:id="@+id/dhuhr_TextView_MainActivityE"
                        style="@style/TimeTextView.blue_new.TimeNameEN"
                        android:text="Dhuhr" />
                </LinearLayout>
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_dhur"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/dhuhrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/dhuhrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.Time" />

                <TextView
                    android:id="@+id/dhuhrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.TimeType" />


            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingDhuhr_TextView_MainActivity"
            style="@style/TimeTextView.blue_new.TimeRemain" />
    </LinearLayout>

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.blue_new"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/contentAsr_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_new.Right"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/asr_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new.TimeNameAR"
                        android:text="العصر" />

                    <TextView
                        style="@style/TimeTextView.blue_new.TimeNameEN"
                        android:text="Asr" />
                </LinearLayout>
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_asr"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/asrTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/asrTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.Time" />

                <TextView
                    android:id="@+id/asrTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.TimeType" />


            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/remainingAsr_TextView_MainActivity"
            style="@style/TimeTextView.blue_new.TimeRemain" />
    </LinearLayout>

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.blue_new"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/contentMaghrib_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_new.Right"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/maghrib_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new.TimeNameAR"
                        android:text="المغرب" />

                    <TextView
                        style="@style/TimeTextView.blue_new.TimeNameEN"
                        android:text="Maghrib" />
                </LinearLayout>
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_maghrib"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/maghribTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/maghribTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.Time" />

                <TextView
                    android:id="@+id/maghribTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.TimeType" />


            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingMaghrib_TextView_MainActivity"
            style="@style/TimeTextView.blue_new.TimeRemain" />
    </LinearLayout>

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.blue_new"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/contentIsha_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_new.Right"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/isha_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new.TimeNameAR"
                        android:text="العشاء" />

                    <TextView
                        style="@style/TimeTextView.blue_new.TimeNameEN"
                        android:text="Isha" />
                </LinearLayout>
            </LinearLayout>

            <include
                android:id="@+id/tv_prayer_ikama_time_isha"
                layout="@layout/textview_circle_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|end"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/ishaTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left">

                <TextView
                    android:id="@+id/ishaTime_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.Time" />

                <TextView
                    android:id="@+id/ishaTimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.TimeType" />


            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/remainingIsha_TextView_MainActivity"
            style="@style/TimeTextView.blue_new.TimeRemain" />
    </LinearLayout>

    <LinearLayout
        style="@style/LinearLayoutPrayerTimeRow.blue_new"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:id="@+id/contentSunrise_LinearLayout_MainActivity"
            style="@style/LinearLayoutPrayerTimeRow">

            <LinearLayout
                style="@style/PrayerTimeLayout.blue_new.Right"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:gravity="end"
                android:orientation="vertical">

                <LinearLayout
                    style="@style/LinearLayoutPrayerTimeRow"
                    android:layout_width="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/sunrise_TextView_MainActivity"
                        style="@style/TimeTextView.blue_new.TimeNameAR"
                        android:layout_marginStart="@dimen/_4sdp"
                        android:layout_marginEnd="@dimen/_4sdp"
                        android:text="الضحى"
                        android:textSize="@dimen/_20sdp" />

                    <TextView
                        style="@style/TimeTextView.blue_new.TimeNameEN"
                        android:text="Duha"
                        android:textSize="@dimen/_12sdp" />
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/sunriseTime_LinearLayout_MainActivity"
                style="@style/PrayerTimeLayout.blue_new.Left"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:gravity="center_vertical|end">

                <TextView
                    android:id="@+id/sunriseATime_TextView_MainActivity"

                    style="@style/TimeTextView.blue_new.Time"
                    android:textSize="@dimen/_24sdp" />

                <TextView
                    android:id="@+id/sunriseATimeType_TextView_MainActivity"
                    style="@style/TimeTextView.blue_new.TimeType"

                    android:textSize="@dimen/_12sdp" />


            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/remainingSunrise_TextView_MainActivity"
            style="@style/TimeTextView.blue_new.TimeRemain"
            android:textSize="@dimen/_10sdp"
            tools:visibility="visible" />
    </LinearLayout>


</LinearLayout>
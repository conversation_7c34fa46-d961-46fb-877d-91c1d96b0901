package com.arapeak.alrbea.UI.Fragment.settings.content.photoGallery;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.arapeak.alrbea.Interface.AdapterCallback;
import com.arapeak.alrbea.Interface.OnSuccessful;
import com.arapeak.alrbea.Model.PhotoAlrabeeaTimes;
import com.arapeak.alrbea.R;
import com.arapeak.alrbea.Utils;
import com.nostra13.universalimageloader.core.DisplayImageOptions;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.util.List;

public class PhotoGalleryAdapter extends RecyclerView.Adapter<PhotoGalleryAdapter.PhotoGalleryHolder> {

    public static final String TAG = "PhotoGalleryAdapter";
    public static final String SETTINGS_BUTTON_TAG = "settingsButton";
    public static final String DELETE_BUTTON_TAG = "deleteButton";
    private final Context context;
    private final List<PhotoAlrabeeaTimes> arrayListItem;
    private final LayoutInflater layoutInflater;
    private final AdapterCallback mCallback;
    private final boolean isImageFullScreen;
    private final boolean isShowProgressBar;
    private final boolean isShowSettingsLayout;
    public Thread d;

    public PhotoGalleryAdapter(Context context
            , List<PhotoAlrabeeaTimes> arrayListItem
            , AdapterCallback mCallback
            , boolean isShowSettingsLayout
            , boolean isShowProgressBar) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSettingsLayout = isShowSettingsLayout;
        this.isShowProgressBar = isShowProgressBar;
        isImageFullScreen = false;

        layoutInflater = LayoutInflater.from(this.context);
    }

    public PhotoGalleryAdapter(Context context
            , List<PhotoAlrabeeaTimes> arrayListItem
            , AdapterCallback mCallback
            , boolean isShowSettingsLayout
            , boolean isShowProgressBar
            , boolean isImageFullScreen) {
        this.context = context;
        this.arrayListItem = arrayListItem;
        this.mCallback = mCallback;
        this.isShowSettingsLayout = isShowSettingsLayout;
        this.isShowProgressBar = isShowProgressBar;
        this.isImageFullScreen = isImageFullScreen;

        layoutInflater = LayoutInflater.from(this.context);
    }

    @NonNull
    @Override
    public PhotoGalleryHolder onCreateViewHolder(@NonNull ViewGroup parent, int i) {
        View view = layoutInflater.inflate(R.layout.layout_list_item_photo, parent, false);

        PhotoGalleryHolder viewHolder = new PhotoGalleryHolder(view);

        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull PhotoGalleryHolder savedVoiceViewHolder, int position) {
        //savedVoiceViewHolder.photoImageView.setImageBitmap(null);
        savedVoiceViewHolder.onBind(position);
    }

    @Override
    public int getItemCount() {
        return arrayListItem.size();
    }


    public void add(PhotoAlrabeeaTimes item) {
        if (item == null) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.add(item);
        notifyItemInserted(lastItemIndex);
        notifyDataSetChanged();
    }

    public void remove(int position) {
        if (position < 0 || position >= getItemCount()) {
            return;
        }
        this.arrayListItem.remove(position);
        notifyItemRemoved(position);
        notifyDataSetChanged();
    }

    public void addAll(List<PhotoAlrabeeaTimes> arrayListItem) {
        if (arrayListItem == null || arrayListItem.size() == 0) {
            return;
        }
        int lastItemIndex = this.arrayListItem.size();
        this.arrayListItem.addAll(arrayListItem);
        notifyItemRangeInserted(lastItemIndex, arrayListItem.size());
        notifyDataSetChanged();
    }

    public PhotoAlrabeeaTimes getItem(int position) {
        if (position < 0 || position >= getItemCount()) {
            return null;
        }
        return arrayListItem.get(position);
    }

    public void clear() {
        arrayListItem.clear();
        notifyDataSetChanged();
    }

    public PhotoAlrabeeaTimes getLastItem() {
        if (getItemCount() == 0) {
            return null;
        }

        return arrayListItem.get(arrayListItem.size() - 1);
    }

    public void setItem(int position, PhotoAlrabeeaTimes subSettingAlrabeeaTimes) {
        if (position < 0 || position >= getItemCount() || subSettingAlrabeeaTimes == null) {
            return;
        }
        arrayListItem.set(position, subSettingAlrabeeaTimes);

        notifyItemChanged(position);
    }

    class PhotoGalleryHolder extends RecyclerView.ViewHolder {

        private final ImageView photoImageView;
        private final Button settingsButton;
        private final Button deleteButton;
        private final View spaceView;
        private final FrameLayout progressBarFrameLayout;
        private final DisplayImageOptions options;

        public PhotoGalleryHolder(@NonNull View itemView) {
            super(itemView);

            photoImageView = itemView.findViewById(R.id.photo_ImageView_PhotoGalleryHolder);
            settingsButton = itemView.findViewById(R.id.settings_Button_PhotoGalleryHolder);
            deleteButton = itemView.findViewById(R.id.delete_Button_PhotoGalleryHolder);
            spaceView = itemView.findViewById(R.id.space_View_PhotoGalleryHolder);
            progressBarFrameLayout = itemView.findViewById(R.id.progressBar_FrameLayout_PhotoGalleryHolder);

            options = new DisplayImageOptions.Builder()

                    .cacheInMemory(true)
                    .cacheOnDisk(true)
                    .considerExifParams(true)

                    .build();

        }


        public void onBind(final int position) {
            final PhotoAlrabeeaTimes photoAlrabeeaTimes = arrayListItem.get(position);

            if (isShowProgressBar) {
                progressBarFrameLayout.setVisibility(View.VISIBLE);
            } else {
                progressBarFrameLayout.setVisibility(View.GONE);
            }

            if (isImageFullScreen) {
                photoImageView.getLayoutParams().height = RecyclerView.LayoutParams.MATCH_PARENT;
                photoImageView.setMinimumHeight((int) context.getResources().getDimension(R.dimen.height_image_item));
            } else {
                photoImageView.getLayoutParams().height = (int) context.getResources().getDimension(R.dimen.height_image_item);
            }

            if (isShowSettingsLayout) {
                settingsButton.setVisibility(View.VISIBLE);
                deleteButton.setVisibility(View.VISIBLE);
                spaceView.setVisibility(View.VISIBLE);
            } else {
                settingsButton.setVisibility(View.GONE);
                deleteButton.setVisibility(View.VISIBLE);
                spaceView.setVisibility(View.GONE);
            }

            photoImageView.requestLayout();
            if (!photoAlrabeeaTimes.getImageBase64().isEmpty()) {
                //  ImageLoader imageLoader = ImageLoader.getInstance();
                // ImageSize targetSize = new ImageSize(1920, 1080);
                //   final Bitmap bitmap =imageLoader.loadImageSync( photoAlrabeeaTimes.getImageBase64(),targetSize,options);

                //   final File image = DiskCacheUtils.findInCache(bitmap, imageLoader.getDiskCache())

                // photoImageView.setImageBitmap(bitmap);

                System.out.println("ssss");
                System.out.println(photoAlrabeeaTimes.getImageUrl());
                ImageLoader imageLoader = ImageLoader.getInstance();
                imageLoader.displayImage("file://" + photoAlrabeeaTimes.getImageUrl(), photoImageView, options);
                progressBarFrameLayout.setVisibility(View.GONE);


        /*        new Thread(new Runnable() {
                    public void run() {
                        if (bitmap == null) {
                            return;
                        }


                        photoImageView.post(new Runnable() {
                            public void run() {
                                photoImageView.setImageBitmap(bitmap);
                            }});





                    }}).start();
*/
//                new Thread(new Runnable() {
//                    @Override
//                    public void run() {
//                        final Bitmap bitmap = Utils.convertImageBase64ToBitmapImage(photoAlrabeeaTimes.getImageBase64());
//
//                        if (bitmap == null) {
//                            return;
//                        }
//
//                        new Handler(context.getMainLooper()).post(new Runnable() {
//                            @Override
//                            public void run() {
//                                photoImageView.setImageBitmap(bitmap);
//                                progressBarFrameLayout.setVisibility(View.GONE);
//                            }
//                        });
//                    }
//                }).start();


            } else if (!photoAlrabeeaTimes.getImageUrl().isEmpty()) {
                Utils.setPhotoWithCallBack(context
                        , photoImageView
                        , photoAlrabeeaTimes.getImageUrl()
                        , new OnSuccessful() {
                            @Override
                            public void onSuccessful(boolean isSuccessful) {
                                if (isSuccessful) {
                                    progressBarFrameLayout.setVisibility(View.GONE);
                                }
                            }
                        });
            }

            settingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCallback != null) {
                        mCallback.onItemClick(position, SETTINGS_BUTTON_TAG);
                    }
                }
            });
            deleteButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    remove(position);
                }
            });
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mCallback != null) {
                        mCallback.onItemClick(position, TAG);
                    }
                }
            });
        }
    }
}
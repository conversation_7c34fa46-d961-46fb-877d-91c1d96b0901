<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_92sdp"
    android:layout_margin="@dimen/_5sdp"
    android:clipChildren="true"
    android:clipToPadding="true"
    app:cardCornerRadius="@dimen/_5sdp"
    app:cardElevation="@dimen/_1sdp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F3F3F3"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:padding="@dimen/_5sdp">

        <TextView
            android:id="@+id/gallery_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/droid_arabic_kufi"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/gallery_name"
            android:textColor="#01A0C6"
            android:textSize="@dimen/_14sdp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="@dimen/_10sdp"
            android:layout_marginRight="@dimen/_10sdp"
            android:background="#D1D1D1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gallery_name" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            app:layout_constrainedHeight="true"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gallery_name">

            <LinearLayout
                android:id="@+id/linear_layout_gallery_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="start"
                        android:includeFontPadding="false"
                        android:text="@string/photo_count"
                        android:textColor="#01A0C6"
                        android:textSize="@dimen/_14sdp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/gallery_image_count"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="start"
                        android:includeFontPadding="false"
                        android:text="18"
                        android:textColor="#01A0C6"
                        android:textSize="@dimen/_14sdp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="start"
                        android:includeFontPadding="false"
                        android:text="@string/gallery_type"
                        android:textColor="#01A0C6"
                        android:textSize="@dimen/_14sdp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/gallery_image_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10sdp"
                        android:fontFamily="@font/droid_arabic_kufi"
                        android:gravity="start"
                        android:includeFontPadding="false"
                        android:text="@string/normal"
                        android:textColor="#01A0C6"
                        android:textSize="@dimen/_14sdp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>

            <RadioButton
                android:id="@+id/gallery_enabled_switch"
                android:layout_width="@dimen/_90sdp"
                android:layout_height="match_parent"
                android:checked="true"
                android:fontFamily="@font/droid_arabic_kufi"
                android:layoutDirection="ltr"
                android:text="@string/not_enabled"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/_14sdp"
                android:textStyle="bold" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
package com.arapeak.alrbrea.core_ktx.data.analytics

import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber

object CrashlyticsUtils {
    init {
        Timber.plant(Timber.DebugTree())
    }

    fun logException(throwable: Throwable) {
        try {
            Timber.e(throwable)
            FirebaseCrashlytics.getInstance().recordException(throwable)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}
package com.arapeak.alrbrea.core_ktx.repo

import android.content.Context
import android.util.Log
import com.arapeak.alrbrea.core_ktx.R
import com.arapeak.alrbrea.core_ktx.data.datastore.TextDesignerSettings
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignFontEnum
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignPosition
import com.arapeak.alrbrea.core_ktx.model.textdesign.TextDesignToggleEnum
import com.arapeak.alrbrea.core_ktx.ui.textdesign.TextDesignSizes

class TextDesignRepo(
    val settings: TextDesignerSettings = TextDesignerSettings()
) {
    private val TAG = this.javaClass.simpleName


    fun resetAll(context: Context) {
        Log.e(TAG, "update reset all")
        settings.resetAll(context)
    }

    fun getToggle(context: Context): TextDesignToggleEnum {
        val res = settings.getToggle(context)
        Log.e(TAG, "get Toggle $res")

        return res
    }

    fun updateToggle(context: Context, toggle: TextDesignToggleEnum) {
        Log.e(TAG, "update toggle $toggle")
        settings.setToggle(context, toggle)
    }

    fun getTogglesNamesLocalized(context: Context): List<String> {
        return TextDesignToggleEnum.entries.map {
            when (it) {
                TextDesignToggleEnum.Disabled -> context.getString(R.string.disabled)
                TextDesignToggleEnum.Enabled -> context.getString(R.string.enabled)
            }
        }
    }

    fun getTwoLinesNamesLocalized(context: Context): List<String> {
        return listOf(
            context.getString(R.string.oneLine),
            context.getString(R.string.twoLines)
        )
    }


    fun getTwoLines(context: Context): Boolean {
        val res = settings.isTwoLines(context)
        Log.e(TAG, "get TwoLines $res")

        return res
    }

    fun updateTwoLines(context: Context, toggle: Boolean) {
        Log.e(TAG, "update TwoLines $toggle")
        settings.setIsTwoLines(context, toggle)
    }

    fun getFont(context: Context, secondLine: Boolean = false): TextDesignFontEnum {
        val res = settings.getFont(context, secondLine)
        Log.e(TAG, "get font ${res.name} Line : ${if (secondLine) 2 else 1}")

        return res
    }

    fun updateFont(context: Context, font: TextDesignFontEnum, secondLine: Boolean = false) {
        Log.e(TAG, "update font ${font.name} Line : ${if (secondLine) 2 else 1}")
        settings.setFont(context, font, secondLine)
    }

    fun getFontNamesLocalized(context: Context): List<String> {
        return TextDesignFontEnum.entries.mapIndexed { index, textDesignFontEnum ->
            "${context.getString(R.string.font_type_number)}$index"
        }
    }


    fun getColor(context: Context, secondLine: Boolean = false): Int {
        val res = settings.getColor(context, secondLine)
        Log.e(TAG, "get color ${res} Line : ${if (secondLine) 2 else 1}")

        return res
    }

    fun updateColor(context: Context, color: Int, secondLine: Boolean = false) {
        Log.e(TAG, "update color ${color} Line : ${if (secondLine) 2 else 1}")
        settings.setColor(context, color, secondLine)
    }


    fun getText(context: Context, secondLine: Boolean = false): String {
        val res = settings.getText(context, secondLine)
        Log.e(TAG, "get text ${res} Line : ${if (secondLine) 2 else 1}")

        return res
    }

    fun updateText(context: Context, text: String, secondLine: Boolean = false) {
        Log.e(TAG, "update text ${text} Line : ${if (secondLine) 2 else 1}")
        settings.setText(context, text, secondLine)
    }


    fun getSize(context: Context, secondLine: Boolean = false): Int {
        val res = settings.getSize(context, secondLine)
        Log.e(TAG, "get size ${res} Line : ${if (secondLine) 2 else 1}")

        return res
    }

    fun getSizeParsed(context: Context, secondLine: Boolean = false): Float {
        val res = getSize(context, secondLine)
        val sizes = TextDesignSizes().textSizes;

        return if (res < sizes.size)
            context.resources.getDimension(sizes[res])
        else
            context.resources.getDimension(sizes.last())
    }

    fun updateSize(context: Context, size: Int, secondLine: Boolean = false) {
        Log.e(TAG, "update size ${size} Line : ${if (secondLine) 2 else 1}")
        val sizes = TextDesignSizes().textSizes;

        when {
            size <= 0 -> {
                settings.setSize(context, 0, secondLine)
            }

            size >= sizes.size -> {
                settings.setSize(context, sizes.size - 1, secondLine)
            }

            else -> {
                settings.setSize(context, size, secondLine)
            }
        }

    }


    fun getPosition(context: Context, secondLine: Boolean = false): TextDesignPosition {
        val res = settings.getPosition(context, secondLine)
        Log.e(TAG, "get position ${res} Line : ${if (secondLine) 2 else 1}")

        return res
    }

    fun updatePosition(context: Context, p: TextDesignPosition, secondLine: Boolean = false) {
        Log.e(TAG, "update position ${p} Line : ${if (secondLine) 2 else 1}")
        settings.setPosition(context, p, secondLine)
    }

    fun getDuringPrayersSetting(context: Context): Boolean {
        val res = settings.getShowDuringPrayers(context)
        Log.e(TAG, "get ShowDuringPrayers ${res} ")

        return res
    }

    fun updateDuringPrayersSetting(context: Context, p: Boolean) {
        Log.e(TAG, "update ShowDuringPrayers ${p} ")
        settings.setShowDuringPrayers(context, p)
    }
}